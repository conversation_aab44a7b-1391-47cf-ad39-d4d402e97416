const web = [
    {
        path: "/",
        component: () => import("../layouts/layout.js").then(m => m.default),
        children: [
            {
                path: "home",
                name: "home",
                meta:{
                    jp: "HomePage"
                },
                component: () => import("../pages/home.js").then(m => m.default)
            },
            {
                path: "login",
                name: "login",
                meta:{
                    jp: "ログイン"
                },
                component: () => import("../pages/login.js").then(m => m.default)
            },
            {
                path: "signup",
                name: "signup",
                meta:{
                    jp: "新規作成"
                },
                component: () => import("../pages/signup.js").then(m => m.default)
            },
            {
                path: "opportunities/:id/detail",
                name: "preview_project",
                meta:{
                    jp:"案件情報閲覧"
                },
                component: () => import("../pages/preview_project.js").then(m => m.default)
            },
            {
                path: "edit_companyprofile",
                name: "edit_companyprofile",
                meta:{
                    jp:"基本情報編集"
                },
                component: () => import("../pages/edit_companyprofile.js").then(m => m.default)
            },
            {
                path: "opportunities/:id/manage/duplicate",
                name: "opportunities_manage_duplicate",
                meta:{
                    jp:"案件情報修正"
                },
                component: () => import("../pages/opportunities/manage/New.js").then(m => m.default)
            },
            {
                path: "/opportunity_manage_condition/search",
                name: "opportunities_manage_condition_search",
                meta:{
                    jp:"案件情報修正"
                },
                component: () => import("../pages/opportunities/manage/Search.js").then(m => m.default)
            },
            {
                path: "/opportunity_search_condition/search",
                name: "opportunities_search_condition_search",
                meta:{
                    jp:"案件情報修正"
                },
                component: () => import("../pages/opportunities/Search.js").then(m => m.default)
            },
            {
                path: "opportunities/:id/manage/edit",
                name: "opportunities_manage_edit",
                meta:{
                    jp:"案件情報修正"
                },
                component: () => import("../pages/opportunities/manage/Edit.js").then(m => m.default)
            },
            {
                path: "select_plan",
                name: "select_plan",
                meta:{
                    jp:"プラン一覧"
                },
                component: () => import("../pages/select_plan.js").then(m => m.default)
            },
            {
                path: "opportunities/manage/index",
                name: "opportunities_manage_index",
                meta:{
                    jp:"案件情報一覧"
                },
                component: () => import("../pages/opportunities/manage/Index.js").then(m => m.default)
            },
            {
                path: "opportunities/manage/delete",
                name: "opportunities_manage_delete",
                meta:{
                    jp:"案件情報削除"
                },
                component: () => import("../pages/opportunities/manage/Delete.js").then(m => m.default)
            },
            {
                path: "opportunities/manage/new",
                name: "opportunities_manage_new",
                meta:{
                    jp:"案件情報作成"
                },
                component: () => import("../pages/opportunities/manage/New.js").then(m => m.default)
            },
            {
                path: "/users/manage/users",
                name: "users_manage_users",
                meta:{
                    jp:"企業管理者一覧"
                },
                component: () => import("../pages/users/manage/Users.js").then(m => m.default)
            },
            {
                path: "/mypage",
                name: "mypage",
                meta:{
                    jp:"マイページ"
                },
                component: () => import("../pages/users/mypage.js").then(m => m.default)
            },
            {
                path: "opportunities/active",
                name: "opportunities_active",
                meta:{
                    jp:"案件を探す"
                },
                component: () => import("../pages/opportunities/Active.js").then(m => m.default)
            },
            {
                path: "ERP",
                name: "ERP",
                meta:{
                    jp:"パッケージ導入コンサルタント"
                },
                component: () => import("../pages/ERP.js").then(m => m.default)
            },
            {
                path: "IT",
                name: "IT",
                meta:{
                    jp:"ITコンサルタント"
                },
                component: () => import("../pages/IT.js").then(m => m.default)
            },
            {
                path: "/resumes/active",
                name: "人気の人財を探す_PMO",
                meta:{
                    jp:"人気の人財を探す_PMO"
                },
                component: () => import("../pages/resumes/active.js").then(m => m.default)
            },
            {
                path: "/resume_search_condition/search",
                name: "resume_search",
                meta:{
                    jp:"人気の人財を探す_PMO"
                },
                component: () => import("../pages/resumes/search.js").then(m => m.default)
            },
            {
                path: "business_system",
                name: "人気の人財を探す_業務システム開発",
                meta:{
                    jp:"人気の人財を探す_業務システム開発"
                },
                component: () => import("../pages/business_system.js").then(m => m.default)
            },
            {
                path: "ERP_package",
                name: "ERP・パッケージ導入コンサルタント",
                meta:{
                    jp:"ERP・パッケージ導入コンサルタント"
                },
                component: () => import("../pages/ERP_package.js").then(m => m.default)
            },
            {
                path: "design_server",
                name: "サーバー設計・構築",
                meta:{
                    jp:"サーバー設計・構築"
                },
                component: () => import("../pages/design_server.js").then(m => m.default)
            },
            {
                path: "PM_PL",
                name: "PM_PL",
                meta:{
                    jp:"PM_PL"
                },
                component: () => import("../pages/PM_PL.js").then(m => m.default)
            },
            {
                path: "resumes/manage/index",
                name: "resumes",
                meta:{
                    jp:"resumes"
                },
                component: () => import("../pages/resumes.js").then(m => m.default)
            },
            {
                path: "resumes/:id/detail",
                name: "resumes_detail",
                meta:{
                    jp:"resumes_detail"
                },
                component: () => import("../pages/resume_detail.js").then(m => m.default), props: true,
            },
            {
                path: "resumes/manage/new",
                name: "resumes_manage_new",
                meta:{
                    jp:"人財登録"
                },
                component: () => import("../pages/resumes/manage/new_resume.js").then(m => m.default)
            },
            {
                path: "resumes/:id/manage/duplicate",
                name: "resumes_manage_duplicate",
                meta:{
                    jp:"人財登録"
                },
                component: () => import("../pages/resumes/manage/new_resume.js").then(m => m.default), props: true,
            },
            {
                path: "resumes/:id/manage/edit",
                name: "resumes_manage_edit",
                meta:{
                    jp:"人財編集"
                },
                component: () => import("../pages/resumes/manage/edit_resume.js").then(m => m.default), props: true,
            },
            {
                path: "mypage/plan",
                name: "mypage/plan",
                meta:{
                    jp:"クレジット支払い"
                },
                component: () => import("../pages/new_payment.js").then(m => m.default)
            },
            {
                path: "payment/result",
                name: "payment_result",
                meta:{
                    jp:"決済結果"
                },
                component: () => import("../pages/payment_result.js").then(m => m.default)
            },
            {
                path: "nyukai/plan",
                name: "nyukai_plan",
                meta:{
                    jp:"入会"
                },
                component: () => import("../pages/nyukai_payment.js").then(m => m.default)
            },
            {
                path: "our_services/talent",
                name: "our_services_talent",
                meta:{
                    jp:"our_services_talent"
                },
                component: () => import("../pages/services_talent.js").then(m => m.default)
            },
            {
                path: "our_services/project",
                name: "our_services_project",
                meta:{
                    jp:"our_services_project"
                },
                component: () => import("../pages/new_resume.js").then(m => m.default)
            },
            {
                path: "resumes/edit",
                name: "resumes_edit",
                meta:{
                    jp:"resumes_edit"
                },
                component: () => import("../pages/resumes/manage/edit_resume.js").then(m => m.default)
            },
            {
                path: "credit_payments/new",
                name: "credit_payments_new",
                meta:{
                    jp:"credit_payments_new"
                },
                component: () => import("../pages/new_payment.js").then(m => m.default)
            },
            {
                path: "messages/select_opportunities_for_scout",
                name: "messages_select_opportunities_for_scout",
                meta:{
                    jp:"messages_select_opportunities_for_scout"
                },
                component: () => import("../pages/messages/opportunities_scout.js").then(m => m.default)

            },
            {
                path: "our_services/talent",
                name: "our_services_talent",
                meta:{
                    jp:"our_services_talent"
                },
                component: () => import("../pages/services_talent.js").then(m => m.default)
            },
            {
                path: "our_services/project",
                name: "our_services_project",
                meta:{
                    jp:"our_services_project"
                },
                component: () => import("../pages/services_project.js").then(m => m.default)
            },
            {
                path: "users/password/new",
                name: "users_password_new",
                meta:{
                    jp:"users_password_new"
                },
                component: () => import("../pages/users/password/password_new.js").then(m => m.default)
            },
            {
                path: "users/password/edit",
                name: "users_password_edit",
                meta:{
                    jp:"users_password_edit"
                },
                component: () => import("../pages/users/password/password_edit.js").then(m => m.default)
            },
            {
                path: "users/edit",
                name: "users_edit",
                meta:{
                    jp:"users_edit"
                },
                component: () => import("../pages/users/manage/user_edit.js").then(m => m.default)
            },
            {
                path: "personal_rating/:id",
                name: "personal_rating",
                meta:{
                    jp:"personal_rating"
                },
                component: () => import("../pages/personnel_evaluation.js").then(m => m.default)
            },
            {
                path: "interview_result/new",
                name: "interview_result_new",
                meta:{
                    jp:"interview_result_new"
                },
                component: () => import("../pages/interview_result_new.js").then(m => m.default)
            },
            {
                path: "interview_result/:id/view",
                name: "interview_result_view",
                meta:{
                    jp:"interview_result_view"
                },
                component: () => import("../pages/interview_result_view.js").then(m => m.default)
            },
            {
                path: "/:opp_id/:res_id/schedules",
                name: "schedules",
                meta:{
                    jp:"schedules"
                },
                component: () => import("../pages/schedules.js").then(m => m.default),
            },
            {
                path: "resumes/:id/file_upload",
                name: "resumes_file_upload",
                meta:{
                    jp:"resumes_file_upload"
                },
                component: () => import("../pages/resumes/manage/file_upload.js").then(m => m.default)
            },
            {
                path: "company/:id/detail",
                name: "company_detail",
                meta:{
                    jp:"会社詳細"
                },
                component: () => import("../pages/company/company_detail.js").then(m => m.default), props: true,
            },
            {
                path: "/company/:id/list_opportunities",
                name: "company_list_opportunities",
                meta:{
                    jp:"会社詳細/案件一覧"
                },
                component: () => import("../pages/company/company_list_opp.js").then(m => m.default), props: true,
            },
            {
                path: "/company/:id/list_resumes",
                name: "company_list_resumes",
                meta:{
                    jp:"会社詳細/人財一覧"
                },
                component: () => import("../pages/company/company_list_res.js").then(m => m.default), props: true,
            },
            {
                path: "messages/:opp_id/select_resumes_for_apply",
                name: "messages_select_resumes_for_apply",
                meta:{
                    jp:"messages_select_resumes_for_apply"
                },
                component: () => import("../pages/messages/resumes_apply.js").then(m => m.default), props: true
            },
            {
                path: "our_services/userguide",
                name: "our_services_userguide",
                meta:{
                    jp:"ご利用ガイドライン"
                },
                component: () => import("../pages/our_services/userguide.js").then(m => m.default), props: true
            },
            {
                path: "our_services/agreement",
                name: "our_services_agreement",
                meta:{
                    jp:"利用規約"
                },
                component: () => import("../pages/our_services/agreement.js").then(m => m.default), props: true
            },
            {
                path: "news_managements/:id",
                name: "news_managements_details",
                meta:{
                    jp:"運営からのお知らせ詳細"
                },
                component: () => import("../pages/news/details.js").then(m => m.default), props: true
            },
            {
                path: "plan",
                name: "plan",
                meta:{
                    jp:"運営からのお知らせ詳細"
                },
                component: () => import("../pages/plan/plan.js").then(m => m.default), props: true
            },
             {
                path: "FAQ",
                name: "FAQ",
                meta:{
                    jp:"運営からのお知らせ詳細"
                },
                component: () => import("../pages/FAQ.js").then(m => m.default), props: true
            },
            {
                path: "guideline",
                name: "guideline",
                meta: {
                    jp: "運営からのお知らせ詳細"
                },
                component: () => import("../pages/guideline.js").then(m => m.default), props: true
            },
            {
                path: "bookmark_list",
                name: "bookmark_list",
                meta:{
                    jp:"運営からのお知らせ詳細"
                },
                component: () => import("../pages/bookmark_list.js").then(m => m.default), props: true
            },
            {
                path: "account/wait_active",
                name: "account_wait_active",
                meta:{
                    jp:"会員登録"
                },
                component: () => import("../pages/users/account/wait_active.js").then(m => m.default)
            },
            {
                path: "account/verify_active",
                name: "account_verify_active",
                meta:{
                    jp:"登録完了"
                },
                component: () => import("../pages/users/account/verify_active.js").then(m => m.default)
            },
            {
                path: "registration_data",
                name: "registration_data",
                meta:{
                    jp:"案件・人財登録"
                },
                component: () => import("../pages/registration_data.js").then(m => m.default)
            },
            {
               path: "setting_gmail",
               name: "setting_gmail",
               meta:{
                   jp:"運営からのお知らせ詳細"
               },
               component: () => import("../pages/setttingmail.js").then(m => m.default), props: true
           },
           {
              path: "request_success/:type",
              name: "request_success",
              meta:{
                  jp:"運営からのお知らせ詳細"
              },
              component: () => import("../pages/request_success.js").then(m => m.default), props: true
          },
          {
            path: "matching_request/:type",
            name: "matching_request",
            meta:{
                jp:"コンシェルジュサービス/案件・人財"
            },
            component: () => import("../pages/matching_request.js").then(m => m.default)
        },
          {
             path: "request_select",
             name: "request_select",
             meta:{
                 jp:"コンシェルジュサービス"
             },
             component: () => import("../pages/request_select.js").then(m => m.default), props: true
         },
         {
            path: "data_management",
            name: "data_management",
            meta:{
                jp:"データ管理"
            },
            redirect: "/opportunities/manage/index"
        },
         {
            path: "contact_new",
            name: "contact_new",
            meta: {
                jp: "新しいお問い合わせ"
            },
            component: () => import("../pages/contact_new.js").then(m => m.default)
        },
        {
            path: "users/profile/edit_email",
            name: "users_profile_edit_email",
            meta:{
                jp:"メールアドレス"
            },
            component: () => import("../pages/users/profile/change_mail.js").then(m => m.default)
        },
        {
            path: "users/profile/temp_change_mail",
            name: "users_profile_temp_change_mail",
            meta:{
                jp:"メールアドレス"
            },
            component: () => import("../pages/users/profile/temp_change_mail.js").then(m => m.default)
        },
        {
            path: "users/profile/edit_password",
            name: "users_profile_edit_password",
            meta:{
                jp:"パスワード"
            },
            component: () => import("../pages/users/profile/change_pass.js").then(m => m.default)
        },
        {
            path: "/plant_out",
            name: "plant_out",
            meta:{
                jp:"メールアドレス"
            },
            component: () => import("../pages/plan/plant_out.js").then(m => m.default)
        },
        {
            path: "companies/manage/edit",
            name: "companies_manage_edit",
            meta:{
                jp:"会社データ"
            },
            component: () => import("../pages/company/company_edit.js").then(m => m.default), props: true,
        },
        {
            path: "/our_services/Law",
            name: "our_services_Law",
            meta:{
                jp:"特定商取引に基づく表記"
            },
            component: () => import("../pages/our_services/Law.js").then(m => m.default)
        },
        {
            path: "/contact_success",
            name: "contact_success",
            meta:{
                jp:"特定商取引に基づく表記"
            },
            component: () => import("../pages/contact_success.js").then(m => m.default)
        },
        {
            path: "plan/plant_out",
            name: "users_delete_account",
            meta:{
                jp:"退会"
            },
            component: () => import("../pages/users/account/delete_account.js").then(m => m.default)
        },
        {
            path: "mypage/plan",
            name: "change_plan",
            meta:{
                jp:"プラン変更"
            },
            component: () => import("../pages/plan/change_plan.js").then(m => m.default)
        },
        {
            path: "gratitude",
            name: "gratitude",
            meta:{
                jp:"退会"
            },
            component: () => import("../pages/gratitude.js").then(m => m.default)
        }
        ]
    },
    {
        path: "/notfound",
        component: () => import("../pages/pagenotfound.js").then(m => m.default),
    },
    {
        path: "/:pathMatch(.*)*",
        redirect: "/notfound"
    }
]

export default web