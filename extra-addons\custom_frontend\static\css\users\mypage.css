/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {
    /* Base styles */
    *, ::after, ::before {
        box-sizing: border-box;
    }

    /* Typography */
    h1 {
        margin: 0 0 .5rem;
        font-weight: 300;
        font-size: 2.5rem;
        line-height: 1.2;
    }


    /* Links */
    a {
        color: #1072e9;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #1072e9;
        text-decoration: none;
    }

    a:hover img {
        opacity: .7;
    }

    /* Layout */
    .container-fluid {
        width: 100%;
        padding: 0 15px;
        margin: 0 auto;
    }

    /* Grid system - giữ nguyên không thay đổi vì ảnh hưởng layout */
    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
    }

    /* <PERSON><PERSON><PERSON> col classes giữ nguyên */
    .col-12,
    .col-3,
    .col-4,
    .col-6,
    .col-8,
    .col-9,
    .col-md-4,
    .col-md-8,
    .col-xl-12,
    .col-xl-3,
    .col-xl-9 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 768px) {
        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }
    }

    @media (min-width: 1200px) {
        .col-xl-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-xl-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }

        .col-xl-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    /* Card styles */
    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    }

    .card-body {
        flex: 1 1 auto;
        min-height: 1px;
        padding: 1.25rem;
    }

    .align-text-bottom {
        vertical-align: text-bottom !important;
    }

    .bg-white {
        background-color: #fff !important;
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-none {
            display: none !important;
        }

        .d-md-table-row {
            display: table-row !important;
        }

        .d-md-table-cell {
            display: table-cell !important;
        }
    }

    @media (min-width: 1200px) {
        .d-xl-none {
            display: none !important;
        }

        .d-xl-block {
            display: block !important;
        }
    }

    .flex-row-reverse {
        flex-direction: row-reverse !important;
    }

    .flex-wrap {
        flex-wrap: wrap !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mr-1 {
        margin-right: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .pr-0,
    .px-0 {
        padding-right: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .pl-1 {
        padding-left: 0.25rem !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .pr-2 {
        padding-right: 0.5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .py-3 {
        padding-top: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .px-4 {
        padding-right: 1.5rem !important;
    }

    .px-4 {
        padding-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .mx-auto {
        margin-right: auto !important;
    }

    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 576px) {
        .pr-sm-3 {
            padding-right: 1rem !important;
        }
    }

    @media (min-width: 768px) {
        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .mb-md-3 {
            margin-bottom: 1rem !important;
        }

        .pb-md-0 {
            padding-bottom: 0 !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    @media (min-width: 1200px) {
        .mb-xl-0 {
            margin-bottom: 0 !important;
        }

        .mb-xl-2 {
            margin-bottom: 0.5rem !important;
        }

        .mb-xl-4 {
            margin-bottom: 1.5rem !important;
        }

        .py-xl-3 {
            padding-top: 1rem !important;
        }

        .py-xl-3 {
            padding-bottom: 1rem !important;
        }
    }

    .text-nowrap {
        white-space: nowrap !important;
    }

    .text-left {
        text-align: left !important;
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media print {
        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        thead {
            display: table-header-group;
        }

        img,
        tr {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }
    }

    .white-text {
        color: #fff !important;
    }

    .stylish-color {
        background-color: #1072e9 !important;
    }

    :disabled {
        pointer-events: none !important;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .collapsible-body {
        display: none;
    }

    .font-small {
        font-size: .9rem;
    }

    table th {
        font-size: .9rem;
        font-weight: 400;
    }

    table td {
        font-size: .9rem;
        font-weight: 300;
    }

    .collapsible .active .rotate-icon {
        transition: all 150ms ease-in 0s;
        transform: rotate(180deg);
    }

    .bold {
        font-weight: 500;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-default {
        font-size: .875rem !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-xxl {
        font-size: 2rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .white-text {
        color: #fff;
    }

    .default-color-text {
        color: rgba(0, 0, 0, 0.87);
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-3-text {
        color: #d5d9db;
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .p-0 {
        padding: 0 !important;
    }

    .pr-0,
    .px-0 {
        padding-right: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .pl-1 {
        padding-left: .25rem !important;
    }

    .mr-1 {
        margin-right: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .pr-2 {
        padding-right: .5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .py-3 {
        padding-top: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .px-4 {
        padding-right: 1.5rem !important;
    }

    .px-4 {
        padding-left: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .mb-7 {
        margin-bottom: 3rem !important;
    }

    @media (min-width: 576px) {
        .pr-sm-3 {
            padding-right: 1rem !important;
        }
    }

    @media (min-width: 768px) {
        .pb-md-0 {
            padding-bottom: 0 !important;
        }

        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .mb-md-3 {
            margin-bottom: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    @media (min-width: 1200px) {
        .mb-xl-0 {
            margin-bottom: 0 !important;
        }

        .mb-xl-2 {
            margin-bottom: .5rem !important;
        }

        .py-xl-3 {
            padding-top: 1rem !important;
        }

        .py-xl-3 {
            padding-bottom: 1rem !important;
        }

        .mb-xl-4 {
            margin-bottom: 1.5rem !important;
        }
    }

    .gap-4 {
        -moz-column-gap: 1.5rem !important;
        column-gap: 1.5rem !important;
        row-gap: 1.5rem !important;
    }

    .promotion-banner-width {
        width: calc((100% - 24px)/2);
    }

    .max-width-1136 {
        max-width: 1136px !important;
    }

    @media (max-width: 359px) {
        .font-middle {
            font-size: 0.7rem !important;
        }

        .filter-label {
            font-size: 0.7rem !important;
        }

        .pagination-btn {
            font-size: 0.6rem !important;
        }

        .select-wrapper.mdb-select.anavi-select {
            width: 140px !important;
        }

        .select-wrapper.anavi-select input.select-dropdown {
            font-size: 0.7rem !important;
        }
    }

    @media (min-width: 360px) and (max-width: 400px) {
        .font-middle {
            font-size: 0.8rem !important;
        }

        .filter-label {
            font-size: 0.8rem !important;
        }

        .pagination-btn {
            font-size: 0.8rem !important;
        }

        .select-wrapper.mdb-select.anavi-select {
            width: 150px !important;
        }

        .select-wrapper.anavi-select input.select-dropdown {
            font-size: 0.8rem !important;
        }
    }

    @media (min-width: 768px) and (max-width: 1199px) {
        .max-width-752-for-tb {
            max-width: 752px !important;
        }

        .only-pc {
            display: none !important;
        }
    }

    @media (max-width: 768px) {
        .only-pc {
            display: none !important;
        }
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    body a:hover img {
        opacity: .7;
    }

    .bg-grey-3 {
        background-color: #d5d9db;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    main {
        margin-top: 8%;
    }

    main .grabient {
        min-height: 50vh;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 9.25rem;
        }
    }

    .title {
        background-color: #1072e9 !important;
        background-size: cover;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    :focus {
        outline: 0;
    }

    ul {
        list-style: none;
        padding: 0;
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    @media (min-width: 768px) and (max-width: 1200px) {
        .border-tb-right {
            border: none !important;
            border-right: 1px solid #e6eaec !important;
        }
    }

    .activity-table .keep-length {
        min-width: 5rem;
    }

    @media screen and (max-width: 768px) {
        .activity-table tbody th {
            width: 100% !important;
        }

        .activity-table tbody td {
            width: 33% !important;
        }
    }

    .ticket-img {
        display: inline-block;
        vertical-align: middle;
        width: 34px;
        height: 31px;
        background: center/contain no-repeat url(https://assign-navi.jp/assets/img/common/ticket-cbb477538264d409b702f5a9862d0fb8dc1357e45df3242638acece6edc713b0.png);
    }

    .right-arrow {
        position: relative;
        padding-right: 16px;
    }

    .right-arrow::after {
        content: "";
        margin: auto;
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        border-top: 2px solid #1072e9;
        border-right: 2px solid #1072e9;
        transform: rotate(45deg);
    }

    /* Table styles */
    .activity-table {
        margin-top: 15px;
    }

    .activity-table th,
    .activity-table td {
        padding: 12px;
        vertical-align: middle;
        height: 48px;
        width: auto !important;
    }

    .activity-table th {
        background-color: #4b515d;
        color: #fff;
        font-weight: normal;
        font-size: .9rem;
    }

    .activity-table td {
        border-bottom: 1px solid #e6eaec;
        font-size: .9rem;
        font-weight: 300;
    }

    /* Pagination styles - gộp các styles trùng lặp */
    .pagination .page-item .page-link {
        font-size: 1rem;
        border-radius: 50% !important;
        width: 3rem;
        height: 3rem;
        padding: .875rem 0;
        text-align: center;
        color: #212529;
        background-color: transparent;
        border: 0;
        outline: 0;
        transition: all .3s linear;
    }

    .pagination .page-item .page-link:hover {
        background-color: #cfd8dc;
    }

    .pagination .page-item.active .page-link {
        background-color: #546e7a;
        color: #fff;
    }

    /* Media queries - gộp các breakpoints trùng lặp */
    @media (max-width: 767px) {
        .col-12 {
            flex: 0 0 96% !important;
            max-width: 96% !important;
        }
        .activity-table tbody th {
            width: 100% !important;
        }
        .activity-table tbody td {
            width: 33% !important;
        }
    }

    /* Thêm styles cho table wrapper */
    .table-wrapper {
        position: relative;
        width: 100%;
        margin-bottom: 15px;
    }

    @media screen and (max-width: 767px) {
        .table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Style cho scrollbar */
        .table-wrapper::-webkit-scrollbar {
            height: 6px;
        }

        .table-wrapper::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .table-wrapper::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #666;
        }

        /* Sửa lại cách hiển thị của bảng trên mobile */
        .table-wrapper .activity-table {
            min-width: 800px; /* Đảm bảo có thể scroll */
            display: table !important; /* Force table display */
        }

        .table-wrapper .activity-table tr {
            display: table-row !important;
        }

        .table-wrapper .activity-table th,
        .table-wrapper .activity-table td {
            display: table-cell !important;
            white-space: nowrap;
        }

        /* Ẩn các label mobile */
        .table-wrapper .activity-table .d-block.d-md-none {
            display: none !important;
        }

        /* Hiện lại header của bảng */
        .table-wrapper .activity-table .d-none.d-md-table-row {
            display: table-row !important;
        }
    }

    /* Thêm class để phân biệt với các table-wrapper khác */
    .table-wrapper.scrollable {
        overflow-x: auto;
    }

    /* Style cho các bảng trong table-wrapper */
    .table-wrapper.scrollable .activity-table {
        margin-bottom: 0;
    }

}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 5vh;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

.tooltip {
    z-index: 1000
}

.tooltip-inner {
    text-align: left;
    max-width: 200px
}

@media (min-width: 576px) {
    .tooltip-inner {
        text-align: left !important;
        max-width: 400px !important;
    }
}

.baner-img {
    -o-object-fit: cover;
    object-fit: cover;
    border: 1px solid #d5d9db;
    height: 140px;
}

@media (min-width: 501px) {
    .baner-img {
        height: 230px;
    }
}

@media (min-width: 1200px) {
    .baner-img {
        height: 150px;
    }
}

/* Workflow Tables Styles */
.workflow-section {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16);
    padding: 20px;
}

.workflow-section .font-middle {
    font-size: 1rem;
    margin-bottom: 0;
}

/* Filter Section */
.filter-section {
    display: flex;
    align-items: center;
}

.filter-label {
    color: rgba(84, 110, 122, 0.87);
    margin-right: 10px;
    font-size: 0.9rem;
}

.filter-select {
    padding: 4px 8px;
    border: 1px solid #d5d9db;
    border-radius: 4px;
    font-size: 0.9rem;
    background-color: white;
    color: rgba(84, 110, 122, 0.87);
    min-width: 150px;
}

/* Workflow Links */
.workflow-link {
    color: #1072e9;
    text-decoration: none;
    cursor: pointer;
}

.workflow-link:hover {
    color: #1072e9;
    text-decoration: underline !important;
}

/* CSV Export link */
.workflow-section a[href*="csv"] {
    color: #1072e9;
    text-decoration: none;
}

.workflow-section a[href*="csv"]:hover {
    color: #1072e9;
    text-decoration: underline;
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
    margin-top: 15px;
}

.pagination-btn {
    padding: 8px 16px;
    background-color: #fff;
    border: 1px solid #d5d9db;
    border-radius: 4px;
    color: rgba(84, 110, 122, 0.87);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.pagination-btn:hover {
    background-color: #f5f5f5;
}

.pagination-btn:disabled {
    opacity: 0.5;
    background-color: #e9ecef;
    border-color: #d5d9db;
    color: #6c757d;
    cursor: not-allowed;
}

/* Empty row style */
.activity-table tr.border-bottom td {
    border-bottom: 1px solid #e6eaec;
}

.filter-section .mb-5 {
    margin: 0px !important;
    padding: 0px !important;
}

.filter-section .select-dropdown {
    margin: 0px !important;
    padding: 0px !important;
}

.activity-table th,
.activity-table td {
    width: auto !important;
}

/*! CSS Used from: https://assign-navi.jp/assets/application-5c62c0bf10dc71ffa28617d13da03c81eeb71d47f2b1b1f657b8b6641e347cff.css ; media=screen */
@media screen{
    *,::after,::before{box-sizing:border-box;}
    a{color:#007bff;text-decoration:none;background-color:transparent;}
    a:hover{color:#0056b3;text-decoration:underline;}
    .page-link{position:relative;display:block;padding:.5rem .75rem;margin-left:-1px;line-height:1.25;color:#007bff;background-color:#fff;border:1px solid #dee2e6;}
    .page-link:hover{z-index:2;color:#0056b3;text-decoration:none;background-color:#e9ecef;border-color:#dee2e6;}
    .page-link:focus{z-index:3;outline:0;box-shadow:0 0 0 0.2rem rgba(0,123,255,0.25);}
    .page-item.active .page-link{z-index:3;color:#fff;background-color:#007bff;border-color:#007bff;}
    @media print{
    *,::after,::before{text-shadow:none!important;box-shadow:none!important;}
    a:not(.btn){text-decoration:underline;}
    }
    :disabled{pointer-events:none!important;}
    a{color:#007bff;text-decoration:none;cursor:pointer;transition:all .2s ease-in-out;}
    a:hover{color:#0056b3;text-decoration:none;transition:all .2s ease-in-out;}
    a:disabled:hover{color:#007bff;}
    .waves-effect{position:relative;overflow:hidden;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
    a.waves-effect{display:inline-block;}
    .pagination .page-item.active .page-link{color:#fff;background-color:#4285f4;border-radius:.25rem;box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);transition:all .2s linear;}
    .pagination .page-item.active .page-link:hover{background-color:#4285f4;}
    .pagination .page-item .page-link{font-size:.9rem;color:#212529;background-color:rgba(0,0,0,0);border:0;outline:0;transition:all .3s linear;}
    .pagination .page-item .page-link:hover{background-color:#eee;border-radius:.25rem;transition:all .3s linear;}
    .pagination .page-item .page-link:focus{background-color:rgba(0,0,0,0);box-shadow:none;}
    :focus{outline:0;}
    .pagination .page-item .page-link{font-size:1rem;border-radius: 50% !important;;width:3rem;height:3rem;padding:.875rem 0;text-align:center;}
    .pagination .page-item .page-link:hover{border-radius: 50% !important;;background-color:#cfd8dc;}
    .pagination .page-item .page-link:focus{box-shadow:none;}
    .pagination .page-item.active .page-link{border-radius:50% !important;background-color:#546e7a;}
    .pagination .page-item.active .page-link:hover{background-color:#546e7a;}
    }

    .clamp-1 {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .disable {
        opacity: 0.5;
        pointer-events: none;
    }

    /* Ngăn scroll ngang cho toàn bộ trang */
    body {
        overflow-x: hidden !important;
        width: 100%;
        position: relative;
    }

    /* Chỉ cho phép scroll trong table-wrapper */
    .table-wrapper {
        position: relative;
        width: 100%;
        margin-bottom: 15px;
        background: #fff;
    }

    @media screen and (max-width: 767px) {
        /* Giữ scroll cho table nhưng giới hạn width */
        .table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            max-width: 100%;
        }

        /* Đảm bảo container không bị overflow */
        .container-fluid {
            overflow-x: hidden;
            width: 100%;
            padding-left: 0;
            padding-right: 0;
        }

        /* Điều chỉnh row để tránh scroll ngang */
        .row {
            margin-left: 0;
            margin-right: 0;
        }

        /* Điều chỉnh columns để tránh padding dư */
        .col-12, .col-md-4, .col-xl-3, .col-xl-9 {
            padding-left: 10px;
            padding-right: 10px;
        }
    }

/* Tab styles for mypage */
.mypage-tabs {
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    width: 100%;
}

/* Style for container that replaced the title - background removed */
.container-fluid .mypage-tabs {
    background-size: cover;
}

/* Adjust tab container in the new position */
.container-fluid .tab-container {
    margin-bottom: 0;
    padding-top: 5px;
    padding-bottom: 5px;
}

/* Left alignment for tabs */
.mypage-tabs.left-aligned {
    justify-content: flex-start;
}

.mypage-tabs.left-aligned .tab-container {
    justify-content: flex-start;
}

.tab-container {
    display: flex;
    width: 90%;
    max-width: 3000px;
    overflow: hidden;
    justify-content: center;
    gap: 0.5%;
}

.tab-button {
    height: 5vh;
    width: 17.5%; /* Fixed width instead of percentage */
    border: 2px solid #4472C4;
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0;
    margin-right: 4px; /* Add space between tabs */
    cursor: pointer;
    margin-top: 4px !important;
}

.tab-button.active {
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    color: #fff;
    border-color: #4472C4 !important;
}

.tab-content {
    display: none;
    width: 100%;
}

.tab-content.active {
    display: block;
}

/* Styles for tables in side-by-side layout */
.tables-flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    width: 100%;
    max-width: none;
    overflow: visible;
}

.table-section {
    flex: 1;
    overflow: visible;
    display: flex;
    flex-direction: column;
}

/* Đảm bảo cả hai bảng có chiều cao bằng nhau */
.table-section .table-wrapper {
    flex: 1;
    min-height: 200px; /* Chiều cao tối thiểu cho bảng */
}

/* Đảm bảo các phần header và pagination có chiều cao giống nhau */
.table-section .d-flex {
    min-height: 40px;
    display: flex;
    align-items: center;
}

.table-section .pagination-controls {
    min-height: 50px;
}

/* Đảm bảo các header của bảng được căn chỉnh ngang bằng nhau */
.tables-flex-container .table-section:first-child .d-flex,
.tables-flex-container .table-section:last-child .d-flex {
    height: 40px;
    margin-bottom: 15px;
}

.table-wrapper {
    width: 100%;
    overflow-x: auto;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16);
}

.activity-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.activity-table th, .activity-table td {
    padding: 8px 12px;
    text-align: center;
    vertical-align: middle;
    height: 48px;
    line-height: 1.5;
}

/* Đảm bảo tất cả các dòng có chiều cao bằng nhau */
.activity-table tr {
    height: 48px !important;
    box-sizing: border-box;
}

/* Đặc biệt cho dòng tổng kết */
.activity-table tr.bg-grey-3 {
    height: 48px !important;
}

/* Đảm bảo các bảng có cùng chiều cao */
.tables-flex-container .table-section:first-child .table-wrapper,
.tables-flex-container .table-section:last-child .table-wrapper {
    height: calc(48px * 3 + 48px); /* 3 dòng dữ liệu + 1 dòng header */
}

/* Đảm bảo các dòng trống không bị co lại */
.activity-table tr:empty {
    height: 48px !important;
    display: table-row;
}

.activity-table tr:empty td {
    height: 48px !important;
    border-bottom: 1px solid #e6eaec;
}

.activity-table th {
    background-color: #1072e9;
    color: #fff;
    font-weight: normal;
    font-size: 1rem;
}

.activity-table td {
    border-bottom: 1px solid #e6eaec;
    font-size: .9rem;
    font-weight: 300;
}

.activity-table th.text-left, .activity-table td.text-left {
    text-align: left;
}

/* Ensure tables don't shrink too much */
.row > .col-12 {
    padding-left: 0;
    padding-right: 0;
}

/* Container styles */
.max-width-1136 {
    max-width: 1136px !important;
}

.max-width-752-for-tb {
    max-width: 100% !important;
}

/* Responsive styles */
@media (max-width: 767px) {
    .tab-container {
        flex-direction: row;
        width: 100%;
        gap: 2px;
    }

    .tab-button {
        padding: 8px 2px; /* Reduce horizontal padding */
        font-size: 13px; /* Slightly reduce font size */
        background: #f4f4f4;
        margin-left: 0.5%;
        height: 30px;
    }

    /* Adjust tab container in the new position for mobile */
    .container-fluid .tab-container {
        width: 100%;
        padding-top: 3px;
        padding-bottom: 3px;
    }

    /* Left alignment for mobile */
    .mypage-tabs.left-aligned {
        padding-left: 5px;
    }

    .mypage-tabs.left-aligned .tab-container {
        width: 100%;
        margin-left: -5px;
        margin-top: 75px;
    }

    .tab-button {
        width: 48%; /* Adjust width for mobile */
        margin-right: 5px;
    }

    .tables-flex-container {
        flex-direction: column;
    }
}

@media (max-width: 764px){
    .table-section {
        margin-left: 5.3% !important;
        width: 98% !important;
    }
}

/* Ẩn chỉ text "絞り込み:" trên mobile */
@media (max-width: 767px) {
    .filter-label {
        display: none !important;
    }
}

  
/* iPad Pro responsive styles */
@media screen and (min-width: 1024px) and (max-width: 1279px) {
    .tab-button {
        height: 3vh;
    }

}
  
  /* Desktop large screens (min 1280px) responsive styles */
@media screen and (min-width: 1280px) {
}

