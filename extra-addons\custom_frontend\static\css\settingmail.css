/* <PERSON><PERSON>y chỉnh radio button khi được chọn */
input[type="radio"].form-check-input:checked {
    background-color: #00b0f0; /* <PERSON><PERSON><PERSON> x<PERSON>h dư<PERSON> */
    border-color: #00b0f0;
}

/* <PERSON><PERSON><PERSON> bảo hình dạng tròn và bo viền */
input[type="radio"].form-check-input {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #ccc;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}

.container-fluid {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

input[type="radio"].form-check-input:focus {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: none !important;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}


/* Inner dot khi được chọn */
input[type="radio"].form-check-input:checked::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 10px;
    height: 10px;
    background-color: #00b0f0;
    border-radius: 50%;
}

@media screen and (min-width: 375px) and (max-width: 390px) {
    .position-relative {
        margin-top: -1rem !important;
    }

    /* iPhone 12 Pro responsive styles for setting-row */
    .setting-row {
        margin-bottom: 20px !important;
    }

    .setting-row .d-flex {
        flex-direction: column !important;
        align-items: flex-start !important;
        padding-left: 15px !important;
    }

    .setting-row .setting-label {
        margin-bottom: 15px !important;
        margin-right: 0 !important;
        font-size: 16px !important;
        font-weight: bold !important;
        width: 100% !important;
    }

    .setting-row .form-check {
        margin-left: 0 !important;
        margin-right: 20px !important;
        padding-left: 0 !important;
        margin-bottom: 10px !important;
    }

    .setting-row .form-check-inline {
        display: block !important;
        margin-bottom: 15px !important;
    }

    .setting-row .form-check-input {
        margin-right: 8px !important;
    }

    .setting-row .form-check-label {
        font-size: 14px !important;
        margin-left: 5px !important;
        white-space: nowrap !important;
    }

    /* Container adjustments for mobile */
    .mail-settings-form {
        padding: 15px !important;
    }

    /* Radio button group styling */
    .radio-group-mobile {
        display: flex !important;
        flex-direction: column !important;
        gap: 15px !important;
        width: 100% !important;
        margin-top: 10px !important;
    }

    .radio-option-mobile {
        display: flex !important;
        align-items: center !important;
        padding: 10px 15px !important;
        border: 1px solid #ddd !important;
        border-radius: 8px !important;
        background-color: #f9f9f9 !important;
        transition: all 0.3s ease !important;
    }

    .radio-option-mobile:hover {
        background-color: #e9ecef !important;
        border-color: #007bff !important;
    }

    .radio-option-mobile input[type="radio"] {
        margin-right: 10px !important;
        transform: scale(1.2) !important;
    }

    .radio-option-mobile label {
        margin: 0 !important;
        font-size: 16px !important;
        cursor: pointer !important;
        flex: 1 !important;
    }

}
