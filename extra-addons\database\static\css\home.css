/*! CSS Used from: https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css */
*,
::after,
::before {
    box-sizing: border-box;
}

article,
section {
    display: block;
}

h2,
h3 {
    margin-top: 0;
    margin-bottom: .5rem;
}

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

ul {
    margin-top: 0;
    margin-bottom: 1rem;
}

a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
    -webkit-text-decoration-skip: objects;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

img {
    vertical-align: middle;
    border-style: none;
}

label {
    display: inline-block;
    margin-bottom: .5rem;
}

input {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

input {
    overflow: visible;
}

input[type=radio] {
    box-sizing: border-box;
    padding: 0;
}

h2,
h3 {
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

@media screen and (prefers-reduced-motion:reduce) {
    .btn {
        transition: none;
    }
}

.btn:focus,
.btn:hover {
    text-decoration: none;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.btn:disabled {
    opacity: .65;
}

.btn:not(:disabled):not(.disabled) {
    cursor: pointer;
}

.badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem;
}

.badge:empty {
    display: none;
}

.bg-white {
    background-color: #fff !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

.d-inline-block {
    display: inline-block !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: -ms-flexbox !important;
    display: flex !important;
}

.d-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
}

.flex-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}

.flex-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
}

.justify-content-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
}

.justify-content-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
}

.align-items-center {
    -ms-flex-align: center !important;
    align-items: center !important;
}

.align-items-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
}

.align-self-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
}

@media (min-width:576px) {
    .flex-sm-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important;
    }
}

@media (min-width:768px) {
    .flex-md-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important;
    }
}

.position-relative {
    position: relative !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

.mr-1 {
    margin-right: .25rem !important;
}

.mb-2 {
    margin-bottom: .5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mx-auto {
    margin-right: auto !important;
}

.mx-auto {
    margin-left: auto !important;
}

@media (min-width:768px) {
    .mx-md-0 {
        margin-right: 0 !important;
    }

    .mx-md-0 {
        margin-left: 0 !important;
    }
}

.text-left {
    text-align: left !important;
}

.text-center {
    text-align: center !important;
}

.text-white {
    color: #fff !important;
}

@media print {

    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important;
    }

    a:not(.btn) {
        text-decoration: underline;
    }

    img {
        page-break-inside: avoid;
    }

    h2,
    h3,
    p {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }

    .badge {
        border: 1px solid #000;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/css/common.css?ver=002 */
article,
section {
    display: block;
}

video {
    display: inline-block;
    vertical-align: baseline;
}

a {
    background-color: transparent;
}

a:active {
    outline: 0;
}

img {
    border: 0;
}

.font-small {
    font-size: 0.75rem !important;
    line-height: 1.8;
}

.font-default {
    font-size: 0.875rem !important;
    line-height: 1.8;
}

.font-middle {
    font-size: 1rem !important;
    line-height: 1.8 !important;
}

.font-large {
    font-size: 1.125rem !important;
    line-height: 1.8 !important;
}

.font-extralarge {
    font-size: 1.25rem !important;
    line-height: 1.6;
}

.font-xxl {
    font-size: 2rem !important;
    line-height: 1.5;
}

.bold {
    font-weight: 700;
}

.default-color-text {
    color: #1072e9;
}

.custom-sub-text {
    color: #3479CA;
}

.custom-grey-6-text {
    color: #455965;
}

.custom-grey-7-text {
    color: #2A3942;
}

.bg-main-green {
    background-color: #1072e9;
}

.bg-sub-blue {
    background-color: #3479CA;
}

.bg-white {
    background-color: #fff;
}

.bg-introgray {
    background-color: #FAFAFA;
}

.bg-lightgreen {
    background-color: #EAF8F7;
}

.d-flex {
    display: flex;
}

.flex-1 {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0%;
}

.text-center {
    text-align: center !important;
}

.mt-6 {
    margin-top: 2.5rem !important;
}

.mb-6 {
    margin-bottom: 2.5rem !important;
}

.py-6 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
}

.mb-12px {
    margin-bottom: 0.75rem !important;
}

.mb-20px {
    margin-bottom: 1.25rem !important;
}

.mx-28px {
    margin-right: 1.75rem !important;
    margin-left: 1.75rem !important;
}

.mb-28px {
    margin-bottom: 1.75rem !important;
}

.p-28px {
    padding: 1.75rem !important;
}

.px-28px {
    padding-right: 1.75rem !important;
    padding-left: 1.75rem !important;
}

.w-90px {
    width: 90px;
}

.h-90px {
    height: 90px;
}

.max-w-300 {
    max-width: 300px;
}

.max-w-320 {
    max-width: 320px;
}

.max-w-604 {
    max-width: 604px;
}

.btn-w-300 {
    width: 300px;
}

.gap-20px {
    gap: 1.25rem !important;
}

.gap-28px {
    gap: 1.75rem !important;
}

.rounded-8 {
    border-radius: 8px;
}

.rounded-40 {
    border-radius: 40px;
}

input {
    color: inherit;
    font: inherit;
    margin: 0;
}

input {
    line-height: normal;
    outline: none;
}

input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}

a.btn {
    display: block;
    padding: 13px;
    text-decoration: none;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 3px;
    transition: 200ms ease-in;
}

.btn.btn-fill {
    background-color: #3479CA;
    color: #fff;
}

.btn.btn-fill.btn-fill-green {
    background-color: #1072e9;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%);
}

.btn.btn-outline {
    border: 1px solid #3479CA;
    color: #3479CA;
    background-color: #fff;
}

.btn.btn-outline.btn-outline-green {
    border: 1px solid #1072e9 !important;
    color: #1072e9 !important;
    background-color: #fff !important;
    box-shadow: 0px 1px 2px 1px rgba(0, 0, 0, 0.12);
    filter: drop-shadow(0px 0px 8px rgba(0, 0, 0, 0.1));
}

.btn.btn-outline.btn-outline-green:hover {
    color: #1072e9;
}

a.btn:hover {
    opacity: .6;
}

.btn.btn-fill:hover {
    color: #fff;
}

.btn.btn-outline:hover {
    color: #3479CA;
}

br {
    letter-spacing: 0 !important;
}

img {
    -ms-interpolation-mode: bicubic;
}

ul {
    padding: 0;
}

li {
    list-style: none;
}

a {
    color: inherit;
    text-decoration: none;
}

h2,
h3,
p,
ul {
    margin-top: 0;
    margin-bottom: 0;
}

* {
    vertical-align: top;
}

* {
    filter: inherit;
}

p,
li {
    -ms-line-break: strict;
    line-break: strict;
    -ms-word-break: break-strict;
    word-break: break-strict;
    word-wrap: break-word;
}

h2,
h3,
p,
ul,
li {
    line-height: 1;
}

a {
    color: #141414;
}

.notouch a:hover {
    color: #141414;
    text-decoration: none;
}

* {
    box-sizing: border-box;
}

img {
    width: auto;
    max-width: 100%;
    height: auto;
}

@media screen and (min-width: 1024px) {
    img {
        width: 100%;
        height: auto;
    }
}

#contact.section {
    padding-top: 60px;
    padding-bottom: 60px;
    background-color: #EAF8F7;
    background-image: url(https://assign-navi.jp/assets/img/common/bg_conv_area_circle.png);
    background-size: 987px, auto, contain;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    z-index: 0;
}

#contact.section::before {
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    content: '';
    width: 740px;
    height: 194px;
    z-index: -1;
    background-image: url(https://assign-navi.jp/assets/img/common/bg_conv_area_left.png);
    background-size: 740px, auto, contain;
}

#contact.section::after {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    content: '';
    width: 375px;
    height: 101px;
    z-index: -1;
    background-image: url(https://assign-navi.jp/assets/img/common/bg_conv_area_right.png);
    background-size: 375px, auto, contain;
    mix-blend-mode: multiply;
}

#contact.section .sec_title {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1.6;
    text-align: center;
}

#contact.section .sub_title {
    margin-bottom: 2.5rem;
    font-size: 0.875rem;
    line-height: 1.8;
}

#contact.section .conversion-btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1.75rem;
}

#contact.section .conversion-btn-wrapper .btn {
    width: 100%;
    max-width: 350px;
    font-size: 1.125rem;
    line-height: 1.8;
    padding: 13px;
    margin: 0;
}

@media (max-width: 767px) {
    #contact.section {
        padding-right: 20px;
        padding-left: 20px;
    }

    #contact.section .sub_title {
        margin-bottom: 1.25rem;
    }

    #contact.section .conversion-btn-wrapper {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    #contact.section::before {
        width: 518px;
        height: 135px;
        background-size: 518px, auto, contain;
    }

    #contact.section::after {
        width: 390px;
        height: 101px;
        background-size: 390px, auto, contain;
    }
}

@media screen and (min-width: 768px) {
    .notouch a {
        transition: 400ms ease;
    }

    .notouch #topics.section .tag_wrap li a {
        transition: border 400ms ease-in-out, color 400ms ease-in-out;
    }

    .notouch #topics.section .tag_wrap li a:hover {
        border-color: #c8c8c8;
        color: #62d1be;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/css/layout.css?ver=002 */
#topics.section {
    position: relative;
    background-color: #fafafa;
}

#topics.section .inner {
    position: relative;
    padding-top: 180px;
    padding-bottom: 180px;
}

#topics.section .tag_wrap {
    width: calc(980 / 1280 * 100%);
    max-width: 1600px;
    margin: 40px auto -15px auto;
}

#topics.section .tag_wrap ul {
    display: flex;
    flex-wrap: wrap;
}

#topics.section .tag_wrap li {
    margin-bottom: 15px;
    margin-right: 10px;
}

#topics.section .tag_wrap li a {
    display: inline-block;
    padding: 11px 14px;
    font-size: 16px;
    text-decoration: none;
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
    #topics.section .tag_wrap {
        width: calc(944 / 1024 * 100%);
    }
}

@media screen and (max-width: 767px) {
    #topics.section {
        padding-bottom: 100px;
    }

    #topics.section .inner {
        width: auto;
        padding-top: 98px;
        padding-bottom: 110px;
        padding-right: 23px;
        padding-left: 23px;
    }

    #topics.section .tag_wrap {
        width: auto;
        margin-top: 30px;
        margin-bottom: -9px;
        padding: 0;
    }

    #topics.section .tag_wrap li {
        margin-bottom: 9px;
    }

    #topics.section .tag_wrap li a {
        padding: 11px 14px;
        border-radius: 4px;
        font-size: 16px;
    }
}

.category-sub-title-wrap {
    padding-top: 16px;
    padding-bottom: 40px;
}

/*! CSS Used from: https://assign-navi.jp/assets/css/top.css?ver=002 */
.top article.main_section {
    margin-top: 60px;
}

@media (min-width: 768px) {
    .top article.main_section {
        margin-top: 80px;
    }
}

#hero.section .inner {
    padding: 80px 0 200px;
}

#hero.section::before {
    position: absolute;
    content: "";
    display: block;
    width: 522px;
    height: 214px;
    background-image: url(https://assign-navi.jp/assets/img/common/bg-wave-left.png);
    background-repeat: no-repeat;
    background-size: contain;
    z-index: -1;
}

#hero.section::after {
    position: absolute;
    top: 90vh;
    right: 0;
    content: "";
    display: block;
    width: 529px;
    height: 320px;
    background-image: url(https://assign-navi.jp/assets/img/common/bg-wave-right.png);
    background-position: right;
    background-repeat: no-repeat;
    background-size: contain;
    z-index: -1;
}

#hero.section video {
    position: absolute;
    top: 80px;
    right: -180px;
    max-width: 1536px;
    width: 80vw;
    z-index: -2;
}

#hero.section h2 {
    font-size: 3rem;
    line-height: 1.6;
    font-weight: bold;
}

#hero.section .service-achievement-wrap .separate {
    display: block;
    width: 1px;
    background-color: #D5D9DB;
}

@media (min-width: 768px) {
    #hero.section .title-content-wrapper {
        min-width: 29.875rem;
        margin-left: 10vw;
    }
}

@media screen and (max-width: 767px) {
    #hero.section video {
        width: 130%;
        top: 380px;
        left: 50%;
        transform: translateX(-50%);
    }

    #hero.section::before {
        display: none;
    }

    #hero.section::after {
        width: 370px;
        top: 750px;
    }

    #hero.section .inner {
        padding: 60px 20px 160px;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
    }

    #hero.section h2 {
        font-size: 2.125rem;
        line-height: 1.5;
        white-space: nowrap;
    }

    #hero.section .btn-wrapper {
        padding-top: 300px;
    }
}

#topics.section {
    background-color: initial;
}

#topics.section .inner {
    max-width: 980px;
    padding-top: 0;
    padding-bottom: 3.75rem;
}

#topics.section .tabs {
    margin-top: 50px;
    margin: 0 auto;
    padding-top: 3.125rem;
    position: relative;
}

#topics.section .tab-item {
    cursor: pointer;
    position: absolute;
    top: 0;
    width: 15rem;
    height: 3.25rem;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-weight: bold;
    text-align: center;
    transition: all 0.2s ease;
}

#topics.section .tab-item[for="talent"] {
    left: calc(50% - 0.5rem);
    transform: translateX(-100%);
}

#topics.section .tab-item[for="project"] {
    right: calc(50% - 0.5rem);
    transform: translateX(100%);
}

#topics.section .tab-item span {
    font-size: 1.125rem;
    line-height: 1.8;
}

#topics.section .tab-item .tab-border {
    position: relative;
    width: 100%;
    height: 3px;
    background-color: #E6EAEC;
    margin-top: 0.75rem;
}

#topics.section .tab-item:hover {
    opacity: 0.75;
}

#topics.section input[name="tab-item"] {
    display: none;
}

#topics.section .tab-content {
    display: none;
    width: 980px;
    margin-top: 45px;
    padding: 2.5rem 3.75rem;
    clear: both;
    overflow: hidden;
    background-color: #EAF8F7;
    border-radius: 8px;
}

#topics.section #talent:checked~#talent-content,
#topics.section #project:checked~#project-content {
    display: block;
    animation-name: fadeInAnime;
    animation-duration: 1s;
}

#topics.section .tabs input:checked+.tab-item {
    color: #1072e9;
}

#topics.section .tabs input:checked+.tab-item .tab-border {
    border-bottom: 3px solid #1072e9;
}

#topics.section .tabs input:checked+.tab-item .tab-border::after {
    content: "";
    display: block;
    width: 14px;
    height: 10px;
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-top: 10px solid #1072e9;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
}

#topics.section .tag_wrap {
    width: initial;
    max-width: initial;
    margin: initial;
}

#topics.section .tag_wrap .freelance-category-wrap .category-sub-title-wrap {
    padding: initial;
}

#topics.section .tag_wrap .freelance-category-wrap .category-sub-title-wrap li {
    margin-right: 1rem;
    margin-bottom: 1rem;
}

#topics.section .tag_wrap .freelance-category-wrap .category-sub-title-wrap li a {
    padding: 0.25rem 1.25rem;
    font-size: 0.875rem;
    line-height: 1.8;
    border-radius: 4px;
    background-color: #fff;
    transition: 200ms ease-in;
}

#topics.section .tag_wrap .freelance-category-wrap .category-sub-title-wrap li a:hover {
    opacity: 0.6;
    color: #141414;
    background-color: #fff;
    box-shadow: 0px 1px 20px 1px rgba(0, 0, 0, 0.25);
    transition: 200ms ease-in, box-shadow 400ms ease-out;
}

#topics.section .tag_wrap .category-title-wrapper .category-title-border {
    max-width: 380px;
    height: 1px;
    display: block;
    background-color: #D5D9DB;
    border-radius: 10px;
}

@media screen and (max-width: 767px) {
    #topics.section {
        padding-bottom: 0px;
    }

    #topics.section .inner {
        padding: 0px;
    }

    #topics.section .tab-item {
        max-width: 10.437rem;
    }

    #topics.section .tab-content {
        width: auto;
        padding: 2.5rem 1.25rem;
    }
}

#feature.section {
    padding: 3.75rem 1.25rem;
    background-color: #FAFAFA;
}

#feature.section .inner {
    max-width: 980px;
    margin: 0 auto;
}

#feature.section .inner a:hover {
    opacity: .6;
}

#feature.section .inner .feature-content-wrapper {
    gap: 2.5rem;
    padding-bottom: 3.75rem;
}

#feature.section .inner .feature-img {
    max-width: 300px;
}

#feature.section .inner .text-with-separate {
    display: flex;
    align-items: center;
    justify-content: center;
}

#feature.section .inner .text-with-separate::before,
#feature.section .inner .text-with-separate::after {
    content: '';
    display: block;
    height: 1px;
    flex-grow: 1;
    background-color: #D5D9DB;
}

#feature.section .inner .text-with-separate::before {
    margin-right: 1.75rem;
}

#feature.section .inner .text-with-separate::after {
    margin-left: 1.75rem;
}

#feature.section .inner ul {
    max-width: 800px;
    margin: 0 auto;
}

#feature.section .inner li {
    width: 100%;
}

#feature.section .inner .guide a {
    display: block;
    position: relative;
    height: 100%;
    background-color: #ffffff;
    border-radius: 5px;
    text-decoration: none;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
}

#feature.section .inner .guide a:hover {
    opacity: 0.6;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.25);
}

#feature.section .inner .guide a:before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% auto;
}

#feature.section .inner li.talent a,
#feature.section .inner li.project a {
    padding-top: calc(516 / 820 * 100%);
}

#feature.section .inner li.talent a:before {
    background-image: url(https://assign-navi.jp/assets/img/top/find_illust_talent.png);
}

#feature.section .inner li.project a:before {
    background-image: url(https://assign-navi.jp/assets/img/top/find_illust_project.png);
}

#feature.section .inner .text {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    text-align: center;
    font-size: 1.125rem;
    font-weight: 700;
    letter-spacing: 0.025em;
}

#feature.section .inner li .text {
    padding: 0 calc(24 / 412 * 100%) calc(24 / 412 * 100%) calc(24 / 412 * 100%);
}

#feature.section .feature-description a {
    color: #1072e9;
}

#feature.section .feature-description a:hover {
    color: #1072e9;
    opacity: .6;
}

@media (max-width: 767px) {
    .feature-content-wrapper {
        align-items: center;
    }

    #feature.section .inner .text-with-separate::before,
    #feature.section .inner .text-with-separate::after {
        display: none;
    }

    #feature.section .inner .feature-content-wrapper {
        gap: 3.75rem;
    }
}

#ticket.section {
    padding: 3.75rem 1.25rem;
    background-color: #fff;
}

#ticket.section .inner {
    padding: 0;
    max-width: 980px;
    margin: 0 auto;
}

#ticket.section .inner .ticket-description-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2.5rem;
}

#ticket.section .inner .ticket-img {
    width: 92px;
}

#ticket.section .inner .triangle {
    width: 15px;
    height: 30px;
    border-top: 15px solid transparent;
    border-left: 15px solid #738A97;
    border-bottom: 15px solid transparent;
}

#ticket.section .inner .gra_triangle {
    width: 250px;
    height: 40px;
    background-image: url(https://assign-navi.jp/assets/img/common/gra_triangle.png);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
}

#ticket.section .inner .badge {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
}

#ticket.section .plus_circle {
    width: 28px;
    height: 28px;
    background-image: url(https://assign-navi.jp/assets/img/top/plus_circle_icon.png);
    position: absolute;
    top: calc(50% - 14px);
    left: -14px;
}

@media (max-width: 800px) {
    #ticket.section .inner .ticket-description-wrapper {
        flex-direction: column;
        gap: 1.75rem;
    }

    #ticket.section .inner .triangle {
        transform: rotate(90deg);
    }

    #ticket.section .inner .through-year-wrapper {
        max-width: 294px;
    }
}

@media (max-width: 767px) {
    #ticket.section .plus_circle {
        top: -14px;
        left: calc(50% - 14px);
    }
}

#event.section {
    background-color: #FAFAFA;
    padding: 3.75rem 1.25rem;
}

#event.section .inner {
    max-width: 800px;
    margin: auto;
}

#event.section .inner .event-content-wrapper {
    gap: 6.25rem;
}

@media (max-width: 767px) {
    #event.section .inner .event-content-wrapper {
        gap: 0;
    }

    #event.section .inner .md-mb-60px {
        margin-bottom: 3.75rem;
    }

    #event.section .inner .md-max-w-350px {
        max-width: 350px;
    }
}

#event.section .inner .font-subtitle {
    font-size: 1.3rem;
    line-height: 1.6;
    font-weight: bold;
    margin: 1rem;
    text-align: center;
}

#event.section .inner a.btn {
    padding: 10px;
    font-size: 18px;
    line-height: 1.8;
}

#information.section {
    border-top: #E6EAEC solid 1px;
}

#information.section .inner {
    margin: auto;
    padding: 0;
    width: auto;
}

#information.section .information-panel-link-wrapper {
    transition: 400ms ease;
}

#information.section .information-panel-link-wrapper:hover {
    opacity: .6;
    text-decoration: none;
}

#information.section .information-panel {
    padding: 1.75rem;
    gap: 12px;
    box-shadow: none;
    border-radius: 0px;
}

#information.section .information-panel .information-panel-img {
    width: 60px;
    height: 60px;
}

#information.section a h3 {
    color: #00A79C;
}

#information.section a p {
    color: #141414;
    text-align: center;
}

@media screen and (min-width: 768px) {
    #information.section .information-panel-border {
        border-right: #E6EAEC solid 1px;
    }

    #information.section .information-panel-link-wrapper {
        width: 50%;
    }
}

@media screen and (max-width: 767px) {
    #information.section {
        padding-bottom: 0px;
    }

    #information.section .information-panel-border {
        border-bottom: #E6EAEC solid 1px;
    }
}

#contact.section {
    position: relative;
    padding: 3.75rem 1.25rem;
}

/*! CSS Used from: https://assign-navi.jp/assets/css/header.css?ver=002 */
.btn {
    margin: 0.375rem;
    color: inherit;
    text-transform: uppercase;
    word-wrap: break-word;
    white-space: normal;
    cursor: pointer;
    border: 0;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    padding: 0.84rem 2.14rem;
    font-size: .81rem;
}

/*! CSS Used keyframes */
@keyframes fadeInAnime {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}