/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  figure,
  footer,
  header,
  main,
  nav,
  section {
    display: block;
  }

  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    background-color: #fff;
  }

  [tabindex="-1"]:focus:not(:focus-visible) {
    outline: 0 !important;
  }

  h1,
  h4 {
    margin-top: 0;
    margin-bottom: .5rem;
  }

  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  dl,
  ul {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  ul ul {
    margin-bottom: 0;
  }

  dt {
    font-weight: 700;
  }

  dd {
    margin-bottom: .5rem;
    margin-left: 0;
  }

  a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
  }

  a:hover {
    color: #0056b3;
    text-decoration: underline;
  }

  figure {
    margin: 0 0 1rem;
  }

  img {
    vertical-align: middle;
    border-style: none;
  }

  label {
    display: inline-block;
    margin-bottom: .5rem;
  }

  button {
    border-radius: 0;
  }

  button:focus:not(:focus-visible) {
    outline: 0;
  }

  button,
  input,
  textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  button,
  input {
    overflow: visible;
  }

  button {
    text-transform: none;
  }

  [type=button],
  [type=submit],
  button {
    -webkit-appearance: button;
  }

  [type=button]:not(:disabled),
  [type=submit]:not(:disabled),
  button:not(:disabled) {
    cursor: pointer;
  }

  input[type=checkbox],
  input[type=radio] {
    box-sizing: border-box;
    padding: 0;
  }

  textarea {
    overflow: auto;
    resize: vertical;
  }

  h1,
  h4 {
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
  }

  h1 {
    font-size: 2.5rem;
  }

  h4 {
    font-size: 1.5rem;
  }

  .small {
    font-size: 80%;
    font-weight: 400;
  }

  .container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .col,
  .col-11,
  .col-12,
  .col-6,
  .col-lg-3,
  .col-lg-8,
  .col-md-10,
  .col-md-4,
  .col-md-5,
  .col-md-6 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
  }

  .col {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  .col-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-11 {
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  @media (min-width: 768px) {
    .col-md-4 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    .col-md-5 {
      flex: 0 0 41.666667%;
      max-width: 41.666667%;
    }

    .col-md-6 {
      flex: 0 0 50%;
      max-width: 50%;
    }

    .col-md-10 {
      flex: 0 0 83.333333%;
      max-width: 83.333333%;
    }
  }

  @media (min-width: 992px) {
    .col-lg-3 {
      flex: 0 0 25%;
      max-width: 25%;
    }

    .col-lg-8 {
      flex: 0 0 66.666667%;
      max-width: 66.666667%;
    }
  }

  .form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .form-control {
      transition: none;
    }
  }

  .form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .form-control::placeholder {
    color: #6c757d;
    opacity: 1;
  }

  .form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
  }

  textarea.form-control {
    height: auto;
  }

  .form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem;
  }

  .form-check-input {
    position: absolute;
    margin-top: .3rem;
    margin-left: -1.25rem;
  }

  .form-check-input:disabled~.form-check-label {
    color: #6c757d;
  }

  .form-check-label {
    margin-bottom: 0;
  }

  .form-inline {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
  }

  .form-inline .form-check {
    width: 100%;
  }

  @media (min-width: 576px) {
    .form-inline label {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0;
    }

    .form-inline .form-check {
      display: flex;
      align-items: center;
      justify-content: center;
      width: auto;
      padding-left: 0;
    }

    .form-inline .form-check-input {
      position: relative;
      flex-shrink: 0;
      margin-top: 0;
      margin-right: .25rem;
      margin-left: 0;
    }
  }

  .btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .btn {
      transition: none;
    }
  }

  .btn:hover {
    color: #212529;
    text-decoration: none;
  }

  .btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .btn:disabled {
    opacity: .65;
  }

  .btn:not(:disabled):not(.disabled) {
    cursor: pointer;
  }

  .btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem;
  }

  .btn-sm {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem;
  }

  .btn-block {
    display: block;
    width: 100%;
  }

  .dropdown {
    position: relative;
  }

  .custom-control {
    position: relative;
    z-index: 1;
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5rem;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .custom-control-input {
    position: absolute;
    left: 0;
    z-index: -1;
    width: 1rem;
    height: 1.25rem;
    opacity: 0;
  }

  .custom-control-input:checked~.custom-control-label::before {
    color: #fff;
    border-color: #007bff;
    background-color: #007bff;
  }

  .custom-control-input:focus~.custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .custom-control-input:focus:not(:checked)~.custom-control-label::before {
    border-color: #80bdff;
  }

  .custom-control-input:not(:disabled):active~.custom-control-label::before {
    color: #fff;
    background-color: #b3d7ff;
    border-color: #b3d7ff;
  }

  .custom-control-input:disabled~.custom-control-label {
    color: #6c757d;
  }

  .custom-control-input:disabled~.custom-control-label::before {
    background-color: #e9ecef;
  }

  .custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
  }

  .custom-control-label::before {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    background-color: #fff;
    border: #adb5bd solid 1px;
  }

  .custom-control-label::after {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background: 50%/50% 50% no-repeat;
  }

  .custom-checkbox .custom-control-label::before {
    border-radius: .25rem;
  }

  .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
  }

  .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(0, 123, 255, 0.5);
  }

  .custom-control-label::before {
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .custom-control-label::before {
      transition: none;
    }
  }

  .card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: .25rem;
  }

  .card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem;
  }

  .badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem;
  }

  .badge-danger {
    color: #fff;
    background-color: #dc3545;
  }

  .progress {
    display: flex;
    height: 1rem;
    overflow: hidden;
    line-height: 0;
    font-size: .75rem;
    background-color: #e9ecef;
    border-radius: .25rem;
  }

  .close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
  }

  .close:hover {
    color: #000;
    text-decoration: none;
  }

  .close:not(:disabled):not(.disabled):focus,
  .close:not(:disabled):not(.disabled):hover {
    opacity: .75;
  }

  button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
  }

  .modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
  }

  .modal-dialog {
    position: relative;
    width: auto;
    margin: .5rem;
    pointer-events: none;
  }

  .modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
  }

  .modal-dialog-centered::before {
    display: block;
    height: calc(100vh - 1rem);
    height: -webkit-min-content;
    height: -moz-min-content;
    height: min-content;
    content: "";
  }

  .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: .3rem;
    outline: 0;
  }

  .modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(.3rem - 1px);
    border-top-right-radius: calc(.3rem - 1px);
  }

  .modal-header .close {
    padding: 1rem 1rem;
    margin: -1rem -1rem -1rem auto;
  }

  .modal-title {
    margin-bottom: 0;
    line-height: 1.5;
  }

  .modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
  }

  @media (min-width: 576px) {
    .modal-dialog {
      max-width: 500px;
      margin: 1.75rem auto;
    }

    .modal-dialog-centered {
      min-height: calc(100% - 3.5rem);
    }

    .modal-dialog-centered::before {
      height: calc(100vh - 3.5rem);
      height: -webkit-min-content;
      height: -moz-min-content;
      height: min-content;
    }
  }

  @media (min-width: 992px) {
    .modal-lg {
      max-width: 800px;
    }
  }

  .border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
  }

  .d-none {
    display: none !important;
  }

  .d-inline-block {
    display: inline-block !important;
  }

  .d-block {
    display: block !important;
  }

  @media (min-width: 768px) {
    .d-md-block {
      display: block !important;
    }
  }

  @media (min-width: 1200px) {
    .d-xl-none {
      display: none !important;
    }

    .d-xl-inline-block {
      display: inline-block !important;
    }

    .d-xl-block {
      display: block !important;
    }
  }

  .align-items-center {
    align-items: center !important;
  }

  .float-right {
    float: right !important;
  }

  .fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
  }

  .shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  }

  .w-100 {
    width: 100% !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .mt-0,
  .my-0 {
    margin-top: 0 !important;
  }

  .mb-0,
  .my-0 {
    margin-bottom: 0 !important;
  }

  .mt-1 {
    margin-top: 0.25rem !important;
  }

  .mr-1 {
    margin-right: 0.25rem !important;
  }

  .mb-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-1 {
    margin-left: 0.25rem !important;
  }

  .mt-2 {
    margin-top: 0.5rem !important;
  }

  .mb-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-2 {
    margin-left: 0.5rem !important;
  }

  .mt-3,
  .my-3 {
    margin-top: 1rem !important;
  }

  .mr-3 {
    margin-right: 1rem !important;
  }

  .mb-3,
  .my-3 {
    margin-bottom: 1rem !important;
  }

  .ml-3 {
    margin-left: 1rem !important;
  }

  .mr-4 {
    margin-right: 1.5rem !important;
  }

  .mb-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-5 {
    margin-bottom: 3rem !important;
  }

  .p-0 {
    padding: 0 !important;
  }

  .px-0 {
    padding-right: 0 !important;
  }

  .pb-0 {
    padding-bottom: 0 !important;
  }

  .pl-0,
  .px-0 {
    padding-left: 0 !important;
  }

  .pt-1 {
    padding-top: 0.25rem !important;
  }

  .p-2 {
    padding: 0.5rem !important;
  }

  .pt-2,
  .py-2 {
    padding-top: 0.5rem !important;
  }

  .pb-2,
  .py-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-2 {
    padding-left: 0.5rem !important;
  }

  .py-3 {
    padding-top: 1rem !important;
  }

  .pr-3,
  .px-3 {
    padding-right: 1rem !important;
  }

  .py-3 {
    padding-bottom: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .p-4 {
    padding: 1.5rem !important;
  }

  .py-4 {
    padding-top: 1.5rem !important;
  }

  .py-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-4 {
    padding-left: 1.5rem !important;
  }

  .pt-5 {
    padding-top: 3rem !important;
  }

  .mr-auto,
  .mx-auto {
    margin-right: auto !important;
  }

  .ml-auto,
  .mx-auto {
    margin-left: auto !important;
  }

  @media (min-width: 768px) {
    .ml-md-3 {
      margin-left: 1rem !important;
    }

    .pr-md-3 {
      padding-right: 1rem !important;
    }

    .py-md-4 {
      padding-top: 1.5rem !important;
    }

    .px-md-4 {
      padding-right: 1.5rem !important;
    }

    .py-md-4 {
      padding-bottom: 1.5rem !important;
    }

    .px-md-4 {
      padding-left: 1.5rem !important;
    }
  }

  .text-right {
    text-align: right !important;
  }

  .text-center {
    text-align: center !important;
  }

  @media (min-width: 768px) {
    .text-md-center {
      text-align: center !important;
    }
  }

  .font-weight-bold {
    font-weight: 700 !important;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }

    a:not(.btn) {
      text-decoration: underline;
    }

    img {
      page-break-inside: avoid;
    }

    p {
      orphans: 3;
      widows: 3;
    }

    body {
      min-width: 992px !important;
    }

  }

  .pink.lighten-2 {
    background-color: #ff6388 !important;
  }

  .pink {
    background-color: #ff285b !important;
  }

  .black-text {
    color: #000 !important;
  }

  .white {
    background-color: #fff !important;
  }

  .white-text {
    color: #fff !important;
  }

  .primary-color-dark {
    background-color: #0d47a1 !important;
  }

  :disabled {
    pointer-events: none !important;
  }

  a {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    transition: all .2s ease-in-out;
  }

  a:hover {
    color: #0056b3;
    text-decoration: none;
    transition: all .2s ease-in-out;
  }

  a:disabled:hover {
    color: #007bff;
  }

  body {
    font-family: "Roboto", sans-serif;
    font-weight: 300;
  }

  h1,
  h4 {
    font-weight: 300;
  }

  .font-small {
    font-size: .9rem;
  }

  .waves-effect {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  a.waves-effect,
  a.waves-light {
    display: inline-block;
  }

  .btn {
    margin: .375rem;
    color: inherit;
    text-transform: uppercase;
    word-wrap: break-word;
    white-space: normal;
    cursor: pointer;
    border: 0;
    border-radius: .25rem;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    padding: .84rem 2.14rem;
    font-size: .81rem;
  }

  .btn:hover,
  .btn:focus,
  .btn:active {
    outline: 0;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn.btn-block {
    margin: inherit;
  }

  .btn.btn-lg {
    padding: 1rem 2.4rem;
    font-size: .94rem;
  }

  .btn.btn-sm {
    padding: .5rem 1.6rem;
    font-size: .64rem;
  }

  .btn:disabled:hover,
  .btn:disabled:focus,
  .btn:disabled:active {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .btn[class*=btn-outline-] {
    padding-top: .7rem;
    padding-bottom: .7rem;
  }

  .btn.btn-sm[class*=btn-outline-] {
    padding-top: .38rem;
    padding-bottom: .38rem;
  }

  .btn-default {
    color: #fff !important;
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
  }

  .btn-default:hover {
    color: #fff;
    background-color: #61b8f7;
  }

  .btn-default:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-default:focus,
  .btn-default:active {
    background-color: #005650;
  }

  .btn-default:not([disabled]):not(.disabled):active {
    background-color: #005650 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-default:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-default {
    color: #1072e9 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border: 2px solid #1072e9 !important;
  }

  .btn-outline-default:hover,
  .btn-outline-default:focus,
  .btn-outline-default:active,
  .btn-outline-default:active:focus {
    color: #1072e9 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #1072e9 !important;
  }

  .btn-outline-default:not([disabled]):not(.disabled):active {
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #1072e9 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-default:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-deep-orange {
    color: #fff !important;
    background-color: #1072e9 !important;
  }

  .btn-deep-orange:hover {
    color: #fff;
    background-color: #ff8d42;
  }

  .btn-deep-orange:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-deep-orange:focus,
  .btn-deep-orange:active {
    background-color: #002060;
  }

  .btn-deep-orange:not([disabled]):not(.disabled):active {
    background-color: #002060 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-deep-orange:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-blue-grey {
    color: #78909c !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border: 2px solid #78909c !important;
  }

  .btn-outline-blue-grey:hover,
  .btn-outline-blue-grey:focus,
  .btn-outline-blue-grey:active,
  .btn-outline-blue-grey:active:focus {
    color: #78909c !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #78909c !important;
  }

  .btn-outline-blue-grey:not([disabled]):not(.disabled):active {
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #78909c !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-blue-grey:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .card {
    font-weight: 400;
    border: 0;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .badge-pill {
    padding-right: .5rem;
    padding-left: .5rem;
    border-radius: 10rem;
  }

  .badge-danger {
    color: #fff !important;
    background-color: #ff285b !important;
  }

  .modal-dialog .modal-content {
    border: 0;
    border-radius: .25rem;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .modal-dialog .modal-content .modal-header {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
  }

  .modal {
    padding-right: 0 !important;
  }

  footer.page-footer {
    bottom: 0;
    color: #fff;
  }

  footer.page-footer .container-fluid {
    width: auto;
  }

  footer.page-footer a {
    color: #fff;
  }

  button,
  html [type=button],
  [type=submit] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .form-check-input:not(:checked),
  .form-check-input:checked {
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }

  .form-check-input[type=radio]:not(:checked)+label,
  .form-check-input[type=radio]:checked+label {
    position: relative;
    display: inline-block;
    height: 1.5625rem;
    padding-left: 28px;
    line-height: 1.5625rem;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: .28s ease;
  }

  .form-check-input[type=radio]+label:before,
  .form-check-input[type=radio]+label:after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 18px;
    height: 18px;
    margin: 4px;
    content: "";
    transition: .28s ease;
  }

  .form-check-input[type=radio]:not(:checked)+label:before,
  .form-check-input[type=radio]:not(:checked)+label:after,
  .form-check-input[type=radio]:checked+label:before,
  .form-check-input[type=radio]:checked+label:after {
    border-radius: 50%;
  }

  .form-check-input[type=radio]:not(:checked)+label:before,
  .form-check-input[type=radio]:not(:checked)+label:after {
    border: 2px solid #d5d9db;
  }

  .form-check-input[type=radio]:not(:checked)+label:after {
    transform: scale(0);
  }

  .form-check-input[type=radio]:checked+label:before {
    border: 2px solid rgba(0, 0, 0, 0);
  }

  .form-check-input[type=radio]:checked+label:after {
    border: 2px solid #1072e9;
  }

  .form-check-input[type=radio]:checked+label:after {
    background-color: #1072e9;
  }

  .form-check-input[type=radio]:checked+label:after {
    transform: scale(1.02);
  }

  .form-check-input[type=radio]:disabled:not(:checked)+label:before,
  .form-check-input[type=radio]:disabled:checked+label:before {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0.46);
  }

  .form-check-input[type=radio]:focus+label:before {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  [type=checkbox]:not(:checked),
  [type=checkbox]:checked {
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }

  button:focus {
    outline: 0 !important;
  }

  .drag-target {
    position: fixed;
    top: 0;
    z-index: 998;
    width: 10px;
    height: 100%;
  }

  .drag-target {
    position: fixed;
    top: 0;
    z-index: 998;
    width: 10px;
    height: 100%;
  }

  .md-progress {
    position: relative;
    display: block;
    width: 100%;
    height: .25rem;
    margin-bottom: 1rem;
    overflow: hidden;
    background-color: #eee;
    box-shadow: none;
  }

  .md-progress .indeterminate {
    background-color: #90caf9;
  }

  .md-progress .indeterminate:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    content: "";
    background-color: inherit;
    -webkit-animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
    animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
    will-change: left, right;
  }

  .md-progress .indeterminate:after {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    content: "";
    background-color: inherit;
    -webkit-animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
    animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
    -webkit-animation-delay: 1.15s;
    animation-delay: 1.15s;
    will-change: left, right;
  }

  .bold {
    font-weight: 500;
  }

  .ex-bold {
    font-weight: 700 !important;
  }

  .font-default {
    font-size: .875rem !important;
  }

  .font-middle {
    font-size: 1rem !important;
  }

  .font-extralarge {
    font-size: 1.25rem !important;
  }

  .font-small {
    font-size: .75rem !important;
  }

  .white-text {
    color: #fff;
  }

  .custom-grey-text {
    color: rgba(84, 110, 122, 0.87);
  }

  .custom-grey-6-text {
    color: #455965;
  }

  .default-main-color {
    color: #1072e9;
  }

  .p-0 {
    padding: 0 !important;
  }

  .px-0 {
    padding-right: 0 !important;
  }

  .pb-0 {
    padding-bottom: 0 !important;
  }

  .pl-0,
  .px-0 {
    padding-left: 0 !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .mt-0,
  .my-0 {
    margin-top: 0 !important;
  }

  .mb-0,
  .my-0 {
    margin-bottom: 0 !important;
  }

  .pt-1 {
    padding-top: .25rem !important;
  }

  .mt-1 {
    margin-top: .25rem !important;
  }

  .mr-1 {
    margin-right: .25rem !important;
  }

  .mb-1 {
    margin-bottom: .25rem !important;
  }

  .ml-1 {
    margin-left: .25rem !important;
  }

  .p-2 {
    padding: .5rem !important;
  }

  .pt-2,
  .py-2 {
    padding-top: .5rem !important;
  }

  .pb-2,
  .py-2 {
    padding-bottom: .5rem !important;
  }

  .pl-2 {
    padding-left: .5rem !important;
  }

  .mt-2 {
    margin-top: .5rem !important;
  }

  .mb-2 {
    margin-bottom: .5rem !important;
  }

  .ml-2 {
    margin-left: .5rem !important;
  }

  .py-3 {
    padding-top: 1rem !important;
  }

  .pr-3,
  .px-3 {
    padding-right: 1rem !important;
  }

  .py-3 {
    padding-bottom: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .mt-3,
  .my-3 {
    margin-top: 1rem !important;
  }

  .mr-3 {
    margin-right: 1rem !important;
  }

  .mb-3,
  .my-3 {
    margin-bottom: 1rem !important;
  }

  .ml-3 {
    margin-left: 1rem !important;
  }

  .p-4 {
    padding: 1.5rem !important;
  }

  .py-4 {
    padding-top: 1.5rem !important;
  }

  .py-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-4 {
    padding-left: 1.5rem !important;
  }

  .mr-4 {
    margin-right: 1.5rem !important;
  }

  .mb-4 {
    margin-bottom: 1.5rem !important;
  }

  .pt-5 {
    padding-top: 2rem !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  .mt-6 {
    margin-top: 2.5rem !important;
  }

  .mb-7 {
    margin-bottom: 3rem !important;
  }

  @media (min-width: 576px) {
    .mr-sm-8 {
      margin-right: 3.5rem !important;
    }
  }

  @media (min-width: 768px) {
    .pr-md-3 {
      padding-right: 1rem !important;
    }

    .ml-md-3 {
      margin-left: 1rem !important;
    }

    .py-md-4 {
      padding-top: 1.5rem !important;
    }

    .px-md-4 {
      padding-right: 1.5rem !important;
    }

    .py-md-4 {
      padding-bottom: 1.5rem !important;
    }

    .px-md-4 {
      padding-left: 1.5rem !important;
    }
  }

  body {
    font-family: none;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
    font-size: .875rem;
    background-color: #eee;
    color: rgba(0, 0, 0, 0.87);
    word-wrap: break-word;
    word-break: break-all;
    transition: color .3s ease 0s;
    height: auto;
  }

  body a {
    color: #1072e9;
  }

  body a:hover {
    color: #1072e9;
  }

  body a:hover img {
    opacity: .7;
  }

  .material-icons {
    vertical-align: bottom;
    margin-right: 15px;
  }

  .vertical-sub {
    vertical-align: sub;
  }

  .vertical-middle {
    vertical-align: middle !important;
  }

  .vertical-text-bottom {
    vertical-align: text-bottom;
  }

  @media (min-width: 768px) {
    .text-md-nowrap {
      white-space: nowrap;
    }
  }

  .z-2 {
    z-index: 2;
  }

  .btn {
    line-height: 1;
    text-transform: none;
  }

  .btn:hover,
  .btn:active,
  .btn:focus {
    opacity: .7;
  }

  .btn[class*=btn-outline-]:hover,
  .btn[class*=btn-outline-]:active,
  .btn[class*=btn-outline-]:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    outline: 0;
    opacity: 1;
  }

  .btn.btn-sm {
    padding: .5rem 1rem;
    font-size: .875rem;
  }

  .badge-pill {
    padding: .1rem .5rem;
    color: #fff;
  }

  .inline-unit-label {
    position: absolute;
    top: 1.2em;
  }

  body header a {
    color: rgba(0, 0, 0, 0.87);
    overflow: visible;
  }

  body header a:hover,
  body header a:hover .material-icons {
    opacity: .7;
    text-decoration: none;
  }

  body header #login_link,
  body header #registration_link {
    width: 95px;
    height: 32px;
    font-weight: 700;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
  }

  body header #login_link {
    line-height: .8;
  }

  body header #registration_link {
    line-height: 1;
  }

  body header .header-search-menu {
    font-weight: 500;
  }

  body header .header-search-menu a {
    color: #2a3942;
  }

  body header .header-search-menu span:hover {
    opacity: .7;
  }

  body header .navbar-nav {
    flex-direction: row;
  }

  body header .icon-wrapper {
    position: relative;
    float: left;
  }

  body header .icon-wrapper i {
    width: 28px;
    height: 28px;
    font-size: 28px;
    text-align: center;
    vertical-align: middle;
    color: #455965;
  }

  @media (min-width: 768px) {
    body header .icon-wrapper i {
      width: 32px;
      height: 32px;
      font-size: 32px;
    }
  }

  body header .link_area {
    font-size: 13px;
    line-height: 1;
  }

  body header .phone-area label {
    font-size: 18px;
  }

  body header .phone-icon {
    width: 17px;
    height: 17px;
    display: inline-block;
    margin: 0 4px 3px 0;
  }

  body header .phone-reception-time {
    font-weight: 500;
    line-height: normal;
  }

  body header .phone-outside-reception-hours {
    font-family: "YakuHanJP";
    font-feature-settings: "palt" 1;
  }

  body header .sp-phone-area {
    display: block;
    text-align: center;
    background-color: #eaf8f7;
  }

  body header .sp-phone-invitation-message {
    font-weight: 700;
    margin-bottom: 0;
    white-space: nowrap;
  }

  body header .sp-phone-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    margin: 0 4px 6px 0;
    vertical-align: bottom;
  }

  body header .sp-phone-number {
    margin-bottom: 0;
    font-size: 1.5rem;
    line-height: normal;
    white-space: nowrap;
  }

  body header .sp-phone-reception-time {
    color: #2a3942;
    font-weight: 500;
    line-height: normal;
  }

  body header .sp-phone-outside-reception-hours {
    color: #2a3942;
    font-family: "YakuHanJP";
    font-feature-settings: "palt" 1;
  }

  body header .sp-phone-area .tel-btn {
    font-size: 1.5rem;
    line-height: normal;
  }

  body header .line-height-normal {
    line-height: 1.6;
  }

  body header .line-height-mini {
    line-height: .7;
  }

  body header .roboto {
    font-family: "Roboto";
  }

  body header .header-color {
    color: #2a3942;
  }

  @media screen and (min-width: 481px) {
    body header .tel-btn {
      display: none;
    }
  }

  @media screen and (max-width: 767px) {
    body header .navbar.scrolling-navbar {
      padding: 12px 16px;
      height: 100px;
    }
    .slogan {
      width: 40vw;
  }
  }

  @media (max-width: 480px) {
    body header .text-phone-number {
      display: none;
    }
  }

  main .grabient {
    min-height: 50vh;
  }

  main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar) {
    position: relative;
    line-height: 1.8;
    font-weight: 700 !important;
  }

  main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
    content: "";
    background-color: #455965;
    display: block;
    height: 100%;
    width: .428rem;
    position: absolute;
    left: -1.285rem;
  }

  @media (max-width: 767px) {
    main {
      margin-top: 64px;
    }

    main .sp_sides_uniter {
      margin: 0 1px;
    }

    main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
      left: -0.8125rem !important;
    }
  }

  .title {

    background-color: #1072e9 !important;


    background-size: cover;
  }

  .title h1 {
    color: #fff;
  }

  @media (max-width: 767px) {
    .title h1 {
      font-size: 1.8rem;
    }
  }

  footer {
    width: 100%;
    height: auto;
    background-color: #78909c;
  }

  :focus {
    outline: 0;
  }

  .form-control:focus {
    box-shadow: none;
    border-color: #1072e9;
  }

  input.form-control[type="text"],
  input.form-control[type="email"],
  input.form-control[type="password"] {
    border: none !important;
    border-bottom: 1px solid #1072e9 !important;
    border-radius: 0 !important;
    background-color: #eee !important;
    color: rgba(0, 0, 0, 0.87) !important;
    height: inherit !important;
    padding: .75rem .75rem !important;
    cursor: text !important;
  }


  textarea.form-control {
    border-color: #1072e9;
  }

  .custom-checkbox label[class*=custom-control-label] {
    cursor: pointer;
  }

  .custom-control-label::before {
    width: 1.125rem;
    height: 1.125rem;
    background-color: rgba(0, 0, 0, 0);
    border: 2px solid #d5d9db;
    top: 3px;
    box-shadow: none !important;
  }

  .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: #1072e9;
    border-color: #1072e9;
  }

  .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background: url(https://assign-navi.jp/assets/img/common/check_white.png);
    background-size: contain;
    left: -1.4rem;
    top: 3px;
  }

  .custom-control-input:focus:not(:checked)~.custom-control-label::before {
    border-color: #d5d9db;
  }

  .modal {
    opacity: .5;
  }

  .modal-header {
    padding: 2rem;
    border-bottom: none;
  }

  .modal-body {
    padding: 0 2rem 2rem;
  }

  @media (max-width: 767px) {
    .modal-header {
      padding: 2rem 1rem;
      border-bottom: none;
    }

    .modal-body {
      padding: 0 1rem 2rem;
    }
  }

  ul,
  dl {
    list-style: none;
    padding: 0;
  }

  .account-side-scrollbar {
    overflow-y: scroll;
    height: 100vh;
    border-left: 2px solid #9da9b2;
  }

  .accordion_close {
    cursor: pointer;
  }

  .progress-zindex {
    z-index: 1050;
  }

  .progress {
    border-radius: 1.25rem;
  }

  @media (max-width: 768px) {
    .progress {
      width: 263px;
      margin: 0 auto;
    }
  }

  .border-bottom {
    border-bottom: 1px solid #e6eaec !important;
  }

  @media (max-width: 767px) {
    .btn-outline-default:hover {
      border-color: #1072e9 !important;
      background-color: inherit !important;
      color: #1072e9 !important;
    }
  }

  @media (max-width: 767px) {
    .btn-outline-blue-grey:hover {
      border-color: #78909c !important;
      background-color: inherit !important;
      color: #78909c !important;
    }
  }

  .form-check-input[type=radio]:focus+label:before {
    border-color: custom-(#d5d9db);
    box-shadow: none;
  }

  .form-check-input[type=radio]:not(:checked)+label,
  .form-check-input[type=radio]:checked+label {
    height: initial;
  }

  .message-validator {
    color: #dc3545;
    text-align: center;
  }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
  }
}

/*! CSS Used from: Embedded */
.karte-r,
.karte-r div,
.karte-r img,
.karte-r i,
.karte-r figure,
.karte-r section {
  animation-delay: 0;
  animation-direction: normal;
  animation-duration: 0;
  animation-fill-mode: none;
  animation-iteration-count: 1;
  animation-name: none;
  animation-play-state: running;
  animation-timing-function: ease;
  backface-visibility: visible;
  background: 0;
  background-attachment: scroll;
  background-clip: border-box;
  background-color: transparent;
  background-image: none;
  background-origin: padding-box;
  background-position: 0 0;
  background-position-x: 0;
  background-position-y: 0;
  background-repeat: repeat;
  background-size: auto auto;
  border: 0;
  border-style: none;
  border-width: medium;
  border-color: inherit;
  border-bottom: 0;
  border-bottom-color: inherit;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-style: none;
  border-bottom-width: medium;
  border-collapse: separate;
  border-image: none;
  border-left: 0;
  border-left-color: inherit;
  border-left-style: none;
  border-left-width: medium;
  border-radius: 0;
  border-right: 0;
  border-right-color: inherit;
  border-right-style: none;
  border-right-width: medium;
  border-spacing: 0;
  border-top: 0;
  border-top-color: inherit;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top-style: none;
  border-top-width: medium;
  bottom: auto;
  box-shadow: none;
  box-sizing: content-box;
  caption-side: top;
  clear: none;
  clip: auto;
  color: inherit;
  columns: auto;
  column-count: auto;
  column-fill: balance;
  column-gap: normal;
  column-rule: medium none currentColor;
  column-rule-color: currentColor;
  column-rule-style: none;
  column-rule-width: none;
  column-span: 1;
  column-width: auto;
  content: normal;
  counter-increment: none;
  counter-reset: none;
  cursor: auto;
  direction: ltr;
  display: inline;
  empty-cells: show;
  float: none;
  font-size: 100%;
  font-size: medium;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  height: auto;
  hyphens: none;
  left: auto;
  letter-spacing: normal;
  line-height: normal;
  list-style: none;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: disc;
  margin: 0;
  margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
  margin-top: 0;
  max-height: none;
  max-width: none;
  min-height: 0;
  min-width: 0;
  opacity: 1;
  orphans: 0;
  outline: 0;
  outline-color: invert;
  outline-style: none;
  outline-width: medium;
  overflow: visible;
  overflow-x: visible;
  overflow-y: visible;
  padding: 0;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
  page-break-after: auto;
  page-break-before: auto;
  page-break-inside: auto;
  perspective: none;
  perspective-origin: 50% 50%;
  position: static;
  quotes: '\201C' '\201D' '\2018' '\2019';
  right: auto;
  tab-size: 8;
  table-layout: auto;
  text-align: inherit;
  text-align-last: auto;
  text-decoration: none;
  text-decoration-color: inherit;
  text-decoration-line: none;
  text-decoration-style: solid;
  text-indent: 0;
  text-shadow: none;
  text-transform: none;
  top: auto;
  transform: none;
  transform-style: flat;
  transition: none;
  transition-delay: 0s;
  transition-duration: 0s;
  transition-property: none;
  transition-timing-function: ease;
  unicode-bidi: normal;
  vertical-align: baseline;
  visibility: visible;
  white-space: normal;
  widows: 0;
  width: auto;
  word-spacing: normal;
  z-index: auto;
}

.karte-r div,
.karte-r figure,
.karte-r section {
  display: block;
}

#karte-c .karte-animated {
  -webkit-animation-duration: 0.5s;
  -moz-animation-duration: 0.5s;
  -o-animation-duration: 0.5s;
  -ms-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  -ms-animation-fill-mode: both;
  animation-fill-mode: both;
}

#karte-c .karte-fadeIn {
  -webkit-animation-name: karte-fadeIn;
  -moz-animation-name: karte-fadeIn;
  -o-animation-name: karte-fadeIn;
  -ms-animation-name: karte-fadeIn;
  animation-name: karte-fadeIn;
}

#karte-c .karte-widget {
  position: fixed;
  z-index: 2147483640;
}

#karte-c .karte-widget__content {
  box-sizing: border-box;
  max-height: 100%;
  max-width: 100%;
}

#karte-c .karte-widget.karte-widget--right {
  right: 0;
}

#karte-c .karte-widget.karte-widget--bottom {
  bottom: 0;
}

/*! CSS Used from: Embedded */
label[for=learn_about_this_site_field0],
label[for=learn_about_this_site_field1],
label[for=learn_about_this_site_field2],
label[for=learn_about_this_site_field3] {
  margin: 0 3.75rem .5rem 0;
}

/*! CSS Used from: Embedded */
._karte-g__a9Kb_ ._krt-icon-close05__a9Kb_ {
  font-family: 'icomoon_close05' !important;
  speak: none;
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

._karte-g__a9Kb_ ._krt-icon-close05__a9Kb_:before {
  font-family: 'icomoon_close05' !important;
  content: '\E900';
}

._karte-g__a9Kb_ img {
  max-width: 100%;
  vertical-align: bottom;
}

._karte-g__a9Kb_ button {
  cursor: pointer;
}

._karte-g__a9Kb_ button * {
  cursor: inherit;
}

._karte-g__a9Kb_ ._wrapper__a9Kb_ {
  position: relative;
  text-align: left;
  font-size: 12px;
  line-height: 1.66667;
}

._karte-g__a9Kb_ ._wrapper__a9Kb_ * {
  box-sizing: border-box;
  white-space: inherit;
  font-size: inherit;
  line-height: inherit;
}

._karte-g__a9Kb_ ._btn-close__a9Kb_ {
  position: absolute;
  z-index: 1;
  border-radius: 50%;
  line-height: 1;
  transition: .25s;
  display: inline-block;
  margin: 0;
  border: solid 1px transparent;
  vertical-align: middle;
  text-decoration: none;
  text-align: center;
  top: 7px;
  right: 7px;
  padding: 6px;
  background: 0;
  color: #999;
}

._karte-g__a9Kb_ ._btn-close__a9Kb_ ._icon-close__a9Kb_ {
  display: inline-block;
  vertical-align: top;
}

._karte-g__a9Kb_ ._btn-close__a9Kb_:hover,
._karte-g__a9Kb_ ._btn-close__a9Kb_:focus {
  opacity: .75;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

._karte-g__a9Kb_ ._card__a9Kb_ {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 94.4vh;
  overflow-y: auto;
  background: #fff no-repeat 50%;
  background-size: cover;
  color: #333;
  background-color: inherit;
}

._karte-g__a9Kb_ ._card__a9Kb_.__edged__a9Kb_ {
  border-radius: 0;
}

._karte-g__a9Kb_ ._card-body__a9Kb_ {
  padding: 16px 16px 0 16px;
}

._karte-g__a9Kb_ ._card-body__a9Kb_::after {
  display: block;
  content: "";
  position: relative;
  z-index: -1;
}

._karte-g__a9Kb_ ._card-body__a9Kb_ {
  position: relative;
  overflow: inherit;
}

._karte-g__a9Kb_ ._card-image__a9Kb_ {
  margin: 0 auto;
  text-align: center;
}

/*! CSS Used from: Embedded */
#karte-9329326.karte-widget.karte-widget--bottom.karte-widget--right {
  bottom: 16px;
}

@media screen and (max-width: 640px) {
  #karte-9329326.karte-widget.karte-widget--bottom.karte-widget--right {
    bottom: 16px;
  }
}

/*! CSS Used keyframes */
@-webkit-keyframes indeterminate {
  0% {
    right: 100%;
    left: -35%;
  }

  60% {
    right: -90%;
    left: 100%;
  }

  100% {
    right: -90%;
    left: 100%;
  }
}

@keyframes indeterminate {
  0% {
    right: 100%;
    left: -35%;
  }

  60% {
    right: -90%;
    left: 100%;
  }

  100% {
    right: -90%;
    left: 100%;
  }
}

@-moz-keyframes karte-fadeIn {
  0% {
    opacity: 0;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    -ms-filter: none;
    filter: none;
  }
}

@-webkit-keyframes karte-fadeIn {
  0% {
    opacity: 0;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    -ms-filter: none;
    filter: none;
  }
}

@keyframes karte-fadeIn {
  0% {
    opacity: 0;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
    filter: alpha(opacity=0);
  }

  100% {
    opacity: 1;
    -ms-filter: none;
    filter: none;
  }
}

/*! CSS Used fontfaces */
@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 300;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 300;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
  font-family: "Noto Sans Japanese";
  font-style: normal;
  font-weight: 300;
  font-display: auto;
  src: local("Noto Sans CJK JP Regular"), local("NotoSansCJKjp-Regular"), local("NotoSansJP-Regular"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff) format("woff");
}

@font-face {
  font-family: "Noto Sans Japanese";
  font-style: normal;
  font-weight: 700;
  src: local("Noto Sans CJK JP Bold"), local("NotoSansCJKjp-Bold"), local("NotoSansJP-Bold"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff) format("woff");
}

@font-face {
  font-family: MySansSerif;
  font-weight: normal;
  font-display: auto;
  src: local("HelveticaNeue"), local("Helvetica Neue"), local("Helvetica"), local("Arial");
}

@font-face {
  font-family: MySansSerif;
  font-weight: 700;
  font-display: auto;
  src: local("HelveticaNeueBold"), local("HelveticaNeue-Bold"), local("Helvetica Neue Bold"), local("HelveticaBold"), local("Helvetica-Bold"), local("Helvetica Bold"), local("Arial Bold");
}

@font-face {
  font-family: MySansSerif;
  font-weight: 900;
  font-display: auto;
  src: local("HelveticaNeueBlack"), local("HelveticaNeue-Black"), local("Helvetica Neue Black"), local("HelveticaBlack"), local("Helvetica-Black"), local("Helvetica Black"), local("Arial Black");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 100;
  font-display: auto;
  src: local("HiraginoSans-W1"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 200;
  font-display: auto;
  src: local("HiraginoSans-W2"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 300;
  font-display: auto;
  src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 400;
  font-display: auto;
  src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 500;
  font-display: auto;
  src: local("HiraginoSans-W5"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 600;
  src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 700;
  font-display: auto;
  src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 800;
  font-display: auto;
  src: local("HiraginoSans-W7"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 900;
  font-display: auto;
  src: local("HiraginoSans-W8"), local("Hiragino Sans");
}

@font-face {
  font-family: MyYugo;
  font-weight: normal;
  font-display: auto;
  src: local("YuGothic-Medium"), local("Yu Gothic Medium"), local("YuGothic-Regular");
}

@font-face {
  font-family: MyYugo;
  font-weight: bold;
  font-display: auto;
  src: local("YuGothic-Bold"), local("Yu Gothic");
}

@font-face {
  font-family: "YakuHanJP";
  font-style: normal;
  font-weight: 500;
  src: url(https://assign-navi.jp/assets/font/YakuHanJP-Medium.woff) format("woff");
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

@font-face {
  font-family: 'icomoon_close05';
  src: url('https://templates-cf.karte.io/fonts/close05.woff') format('woff');
  font-weight: normal;
}

/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  label {
    display: inline-block;
    margin-bottom: .5rem;
  }

  input {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  input {
    overflow: visible;
  }

  .form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .form-control {
      transition: none;
    }
  }

  .form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .form-control::placeholder {
    color: #6c757d;
    opacity: 1;
  }

  .form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
  }

  .badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem;
  }

  .badge-danger {
    color: #fff;
    background-color: #dc3545;
  }

  .d-inline-block {
    display: inline-block !important;
  }

  .ml-2 {
    margin-left: 0.5rem !important;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  .mb-5 {
    margin-bottom: 3rem !important;
  }

  .mx-auto {
    margin-right: auto !important;
  }

  .mx-auto {
    margin-left: auto !important;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }
  }

  .pink.lighten-2 {
    background-color: #ff6388 !important;
  }

  .pink {
    background-color: #ff285b !important;
  }

  :disabled {
    pointer-events: none !important;
  }

  .font-small {
    font-size: .9rem;
  }

  .badge-pill {
    padding-right: .5rem;
    padding-left: .5rem;
    border-radius: 10rem;
  }

  .badge-danger {
    color: #fff !important;
    background-color: #ff285b !important;
  }

  .font-middle {
    font-size: 1rem !important;
  }

  .font-small {
    font-size: .75rem !important;
  }

  .ml-2 {
    margin-left: .5rem !important;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  .badge-pill {
    padding: .1rem .5rem;
    color: #fff;
  }

  main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar) {
    position: relative;
    line-height: 1.8;
    font-weight: 700 !important;
    margin-left: 13px;
  }

  main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
    content: "";
    background-color: #455965;
    display: block;
    height: 100%;
    width: .428rem;
    position: absolute;
    left: -1.285rem;
  }

  @media (max-width: 767px) {
    main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
      left: -0.8125rem !important;
    }
  }

  :focus {
    outline: 0;
  }

  .form-control:focus {
    box-shadow: none;
    border-color: #1072e9;
  }

  input.form-control[type=email] {
    border: none;
    border-bottom: 1px solid #1072e9;
    border-radius: 0;
    background-color: #eee;
    color: rgba(0, 0, 0, 0.87);
    height: inherit;
    padding: .75rem .75rem;
    cursor: text;
  }
}

input.form-control[type="email"],
input.form-control[type="password"] {
  border: none !important;
  border-bottom: 1px solid #1072e9 !important;
  border-radius: 0 !important;
  background-color: #eee !important;
  color: rgba(0, 0, 0, 0.87) !important;
  height: inherit !important;
  padding: .75rem .75rem !important;
  cursor: text !important;
}

/*! CSS Used from: https://aishe.jp/wp-content/themes/aishe/css/style.css ; media=all */
@media all {

  div,
  span {
    margin: 0;
    border: 0;
    outline: 0;
    vertical-align: baseline;
    background: transparent;
  }


  *,
  *:before,
  *:after {
    box-sizing: border-box;
  }

  .progressbar {
    font-weight: bold;
  }

  @media (max-width: 575px) {
    .block-sm {
      display: block !important;
    }
  }

  .none {
    display: none;
  }

  @media (max-width: 767px) {
    .none-md {
      display: none !important;
    }
  }

  .mb-30 {
    margin-bottom: 3rem !important;
  }

  .progressbar {
    display: flex;
    /* flex-wrap: wrap; */
    justify-content: center;
  }

  .progressbar .item {
    padding: 10px 20px;
    font-weight: bold;
    width: 15vw;
    display: inline-block;
    text-align: center;
    margin-right: 10px;
    background: #F4F5F9;
    color: #1A1A1A;
  }

  .progressbar .item:first-child {
    clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%);
  }

  .progressbar .item:not(:first-child) {
    clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%, 10% 50%);
    margin-left: -30px;
  }

  .progressbar .item:last-child {
    clip-path: polygon(0% 0%, 90% 0%, 90% 50%, 90% 100%, 0% 100%, 10% 50%);
    margin-left: -30px;
  }


  @media (max-width: 767px) {
    .progressbar .item {
      font-size: 10px;
      line-height: 1.4;
      padding: 10px 0;
    }
  }


  .progressbar .item .step {
    font-size: 1.4rem;
  }

  .progressbar .item.active {
    z-index: 1;
    background: #1072e9;
    color: #fff;
  }


  .btn-prev {
    background-color: #333 !important;
    color: #fff !important;
  }

  .btn-next {
    background-color: #1072e9 !important;
    color: #fff !important;
  }

  .btn:focus {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12) !important;
    border: none;
    outline: none;
    opacity: 1;
  }

  .consultation__btn {
    display: flex;
    justify-content: center;

  }
}

/*! CSS Used from: https://aishe.jp/wp-content/themes/aishe/css/style.css ; media=all */
@media all {
  span {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    vertical-align: baseline;
    background: transparent;
  }

  *,
  *:before,
  *:after {
    box-sizing: border-box;
  }

  .color-white {
    color: #fff !important;
  }

  .bg-sub {
    background: #E63278;
  }

  .bold {
    font-weight: bold;
  }

  .gap-10 {
    gap: 1rem !important;
  }
}

.text-danger.text-left.mt-3 {
  font-weight: 400 !important;
}

#app {
  margin-top: 80px !important;
}

.header-bg {
  background-position: center;
  background-size: cover;
}

footer.page-footer {
  background-image: url('/custom_frontend/static/img/bg_footer.png') !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  padding-bottom: 15vw !important;
}

.home-footer {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  .home-logo {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
          width: 10rem;
      }
  }
  .home-directive {
      max-width: 100% !important;
  }
}

.rowtt {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
.col-6tt {
  flex: 0 0 50%;
  max-width: 50%;
  padding-right: 15px;
  padding-left: 15px;
}
@media screen and (max-width: 1200px) {

  .rowtt {
    display: block;
    padding-left: 15px;
  }
  .col-6tt {
    max-width: 96%;
  }
  div,
  span {
    padding: 0px;
  }
}

@media screen and (max-width: 700px) {
  .progressbar .item {
    width: 25vw;
  }
  div,
  span {
    padding: 0px;
  }

}

/* iPhone 12 Pro responsive styles */
@media screen and (max-width: 390px) {
  .font-extralarge {
    font-size: 1rem !important;
  }

  .progressbar .item .step {
    font-size: 1rem;
  }
}

@media screen and (min-width: 1024px) {
  .breadcrumbs {
      margin-top: -0% !important;
      margin-bottom: 0px !important;
  }
}