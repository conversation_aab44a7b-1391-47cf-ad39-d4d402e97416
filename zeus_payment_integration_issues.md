# Zeus Payment Integration - Phân tích vấn đề và giải pháp

## Thông tin cấu hình <PERSON>
```
IPコード（clientip）　  ：2019002213
認証キー（ZKEY）：    b6d065489489de0c0518a245f8c926f7a5766d5b
サイト名　　             ：Verticallimit株式会社（テスト）
対応決済手段　　　　　　：クレジットカード決済

＜クレジットカード決済＞
・接続方式　　　　　　：SecureAPI
・セキュリティコード　　　：利用する
・３Dセキュア　　　　　：利用する
・売上処理方法　　       ：即時決済
・継続決済対応　　　　：利用する

※（ユーザへの）決済完了メール：利用する
※決済結果通知メール送信先アドレス：<EMAIL>
```

## Vấn đề 1: Giao dịch không hiển thị trong trang quản lý Zeus

### Nguyên nhân:
1. **Thiếu thông tin giao dịch trong request PayReq**:
   - Trong phương thức `create_zeus_payreq_xml()` (file: `extra-addons/custom_frontend/controllers/creditcard.py`, dòng 76-90), không có thông tin về số tiền và order_id:

```python
def create_zeus_payreq_xml(self, xid):
    root = ET.Element("request")
    root.set("service", "secure_link_3d")
    root.set("action", "payment")
    xid_elem = ET.SubElement(root, "xid")
    xid_elem.text = xid
    print_am = ET.SubElement(root, "print_am")
    print_am.text = "yes"
    print_addition_value = ET.SubElement(root, "print_addition_value")
    print_addition_value.text = "yes"
    return ET.tostring(root, encoding='utf-8', method='xml')
```

2. **Không lưu trữ kết quả thanh toán vào database**:
   - Sau khi nhận được response thành công từ Zeus, code không lưu thông tin giao dịch vào database (file: `extra-addons/custom_frontend/controllers/creditcard.py`, dòng 171-179):

```python
if pay_status == 'success':
    return {
        'success': True,
        'message': '3D Secure OK, AuthReq and PayReq sent',
        'MD': md,
        'PaRes': pares,
        'authreq_response': auth_response,
        'payreq_response': pay_response
    }
```

### Giải pháp:

1. **Cập nhật phương thức `create_zeus_payreq_xml()`**:
```python
def create_zeus_payreq_xml(self, xid, amount=None, order_id=None):
    root = ET.Element("request")
    root.set("service", "secure_link_3d")
    root.set("action", "payment")

    xid_elem = ET.SubElement(root, "xid")
    xid_elem.text = xid

    # Thêm thông tin số tiền nếu có
    if amount:
        amount_elem = ET.SubElement(root, "amount")
        amount_elem.text = str(amount)

    # Thêm thông tin order_id nếu có
    if order_id:
        order_id_elem = ET.SubElement(root, "order_id")
        order_id_elem.text = order_id

    print_am = ET.SubElement(root, "print_am")
    print_am.text = "yes"

    print_addition_value = ET.SubElement(root, "print_addition_value")
    print_addition_value.text = "yes"

    return ET.tostring(root, encoding='utf-8', method='xml')
```

2. **Lưu thông tin thanh toán vào database**:
```python
def save_payment_to_database(self, response, order_id, amount):
    try:
        # Parse XML response
        root = ET.fromstring(response)
        status = root.find('.//status').text
        order_number = root.find('.//order_number').text if root.find('.//order_number') is not None else ''

        # Lấy thông tin user từ session hoặc request
        user_id = session.get('user_id')
        plan = session.get('plan')

        # Lưu thông tin thanh toán vào database
        payment_history = request.env['vit.payment_history'].sudo().create({
            'user_id': user_id,
            'plan': plan,
            'amount': amount,
            'payment_date': datetime.now(),
            'payment_method': 'credit_card',
            'status': 'success' if status == 'success' else 'failed',
            'payment_type': session.get('payment_type', 'registration'),
            'order_id': order_id,
            'transaction_id': order_number
        })

        _logger.info(f"Payment saved to database with ID: {payment_history.id}")
        return True
    except Exception as e:
        _logger.error(f"Error saving payment to database: {str(e)}")
        return False
```

## Vấn đề 2: Lỗi "outside 008" khi gọi API EnrolRes

### Nguyên nhân:
Theo thông tin từ Zeus, lỗi 008 có nghĩa là thẻ không hỗ trợ 3D Secure:
```
「code：008」は、ブランドが対応しておりませんので、加盟店に第三者利用によるチャージバックリスクが発生いたします。
```

Đây không phải là lỗi kỹ thuật mà là thông báo rằng thẻ đang sử dụng (có thể là thẻ Discover hoặc AMEX) không hỗ trợ xác thực 3D Secure.

### Giải pháp:

1. **Xử lý lỗi 008 để tiếp tục thanh toán mà không cần 3D Secure**:
```python
def handle_enrol_response(self, response):
    root = ET.fromstring(response)
    status = root.find('.//status').text

    if status == 'outside':
        code = root.find('.//code').text
        if code == '008':
            # Lỗi 008: Thẻ không hỗ trợ 3D Secure (Discover, AMEX)
            # Zeus cho phép tiếp tục thanh toán mà không cần 3D Secure, nhưng có rủi ro chargeback
            _logger.warning("Card brand does not support 3D Secure (code 008). Proceeding to direct payment.")
            return self.proceed_to_direct_payment()

    # Xử lý các trường hợp khác...
```

2. **Triển khai phương thức thanh toán trực tiếp**:
```python
def proceed_to_direct_payment(self):
    # Lấy thông tin thanh toán từ session hoặc database
    token_key = session.get('token_key')
    amount = session.get('amount')
    order_id = session.get('order_id')

    # Tạo XML request cho PayReq trực tiếp (bỏ qua Auth)
    xml_pay = self.create_direct_payreq_xml(token_key, amount, order_id)

    # Gửi request
    url = "https://secure2-sandbox.cardservice.co.jp/cgi-bin/secure/api.cgi"
    pay_status, pay_response = self.send_zeus_request(xml_pay, url)

    if pay_status == 'success':
        # Xử lý thanh toán thành công
        # Lưu thông tin thanh toán vào database
        self.save_payment_to_database(pay_response, order_id, amount)
        return {'success': True, 'message': 'Payment successful', 'response': pay_response}
    else:
        # Xử lý thanh toán thất bại
        return {'success': False, 'message': 'Payment failed', 'response': pay_response}
```

## Vấn đề 3: Sự không nhất quán trong URL API và thông tin merchant

### Nguyên nhân:
1. **Sự không nhất quán giữa các URL API được sử dụng trong code**:

   - **Trong cấu hình mặc định** (file: `extra-addons/database/models/vit_models.py`, dòng 909):
   ```python
   'api_url': 'https://linkpt.cardservice.co.jp/cgi-bin/token/charge.cgi',
   ```

   - **Trong phương thức `zeus_3ds_callback()`** (file: `extra-addons/custom_frontend/controllers/creditcard.py`, dòng 161):
   ```python
   url = "https://secure2-sandbox.cardservice.co.jp/cgi-bin/secure/api.cgi"
   ```

   - **Trong file JavaScript** (file: `extra-addons/custom_frontend/static/js/pages/nyukai_payment.js`, dòng 178):
   ```javascript
   script.src = "https://secure2-sandbox.cardservice.co.jp/api/token/2.0/zeus_token_cvv2.js";
   ```

2. **Sai thông tin merchant**:
   - Cấu hình mặc định sử dụng giá trị test không hợp lệ (file: `extra-addons/database/models/vit_models.py`, dòng 906-908):
   ```python
   'merchant_id': 'TEST_MERCHANT',  # Cần thay bằng 2019002213
   'service_id': 'TEST_SERVICE',  # Không bắt buộc cho tích hợp API cơ bản, có thể bỏ trống
   'secret_key': 'TEST_SECRET_KEY',  # Cần thay bằng b6d065489489de0c0518a245f8c926f7a5766d5b
   ```
   - Những giá trị này không khớp với thông tin merchant thực tế được Zeus cung cấp:
   ```
   IPコード（clientip）　  ：2019002213
   認証キー（ZKEY）：    b6d065489489de0c0518a245f8c926f7a5766d5b
   ```

   - Service ID được sử dụng trong payload API thanh toán (file: `extra-addons/custom_frontend/controllers/payment.py`, dòng 291-300):
   ```python
   payload = {
       "merchant_id": merchant_id,
       "service_id": service_id,  # Không bắt buộc cho tích hợp API cơ bản, có thể bỏ trống hoặc loại bỏ
       "token": token,
       "token_key": token_key,
       "amount": amount,
       "order_id": order_id,
       "signature": signature,
       "currency": "JPY"
   }
   ```

### Giải pháp:

1. **Cập nhật cấu hình Zeus với URL và thông tin merchant chính xác**:
```python
env['vit.zeus_configuration'].sudo().create({
    'name': 'ZEUS Sandbox Configuration',
    'environment': 'test',
    'merchant_id': '2019002213',  # Thông tin chính xác từ Zeus
    'service_id': '',  # Service ID không bắt buộc cho tích hợp API cơ bản của Zeus, có thể bỏ trống
    'secret_key': 'b6d065489489de0c0518a245f8c926f7a5766d5b',  # Thông tin chính xác từ Zeus
    'api_url': 'https://secure2-sandbox.cardservice.co.jp/cgi-bin/secure/api.cgi',  # URL cho 3D Secure
    'token_url': 'https://secure2-sandbox.cardservice.co.jp/api/token/2.0/zeus_token_cvv2.js',  # URL cho token
    'active': True
})
```

2. **Sử dụng URL từ cấu hình thay vì hardcoded**:
```python
# Lấy cấu hình Zeus
zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()
if not zeus_config:
    request.env['vit.zeus_configuration'].sudo().initialize_test_configuration()
    zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()

# Sử dụng URL từ cấu hình
url = zeus_config.api_url
```

## Vấn đề 4: Hardcoded thông tin merchant

### Nguyên nhân:
Thông tin merchant được hardcoded trong nhiều nơi trong code (file: `extra-addons/custom_frontend/controllers/creditcard.py`, dòng 19-22):

```python
clientip.text = "2019002213"
key.text = "b6d065489489de0c0518a245f8c926f7a5766d5b"
```

### Giải pháp:

1. **Lấy thông tin merchant từ cấu hình**:
```python
# Lấy cấu hình Zeus
zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()
if not zeus_config:
    request.env['vit.zeus_configuration'].sudo().initialize_test_configuration()
    zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()

# Sử dụng thông tin merchant từ cấu hình
clientip.text = zeus_config.merchant_id
key.text = zeus_config.secret_key
```

## Vấn đề 5: Thiếu xử lý lỗi và logging

### Nguyên nhân:
Code hiện tại thiếu xử lý lỗi chi tiết và logging, khiến việc debug khó khăn (file: `extra-addons/custom_frontend/controllers/creditcard.py`, dòng 165-203):

```python
try:
    auth_status, auth_response = self.send_zeus_request(xml_auth, url)
    # ...
except Exception as e:
    return {
        'success': False,
        'message': f'Error sending AuthReq/PayReq: {str(e)}',
        'MD': md,
        'PaRes': pares
    }
```

### Giải pháp:

1. **Thêm logging chi tiết**:
```python
def send_zeus_request(self, xml_data, url):
    headers = {'Content-Type': 'application/xml; charset=utf-8'}
    _logger.info(f"Sending request to Zeus: URL={url}, Data={xml_data}")

    try:
        response = requests.post(url, data=xml_data, headers=headers, timeout=30)
        _logger.info(f"Zeus response: Status={response.status_code}, Content={response.text}")

        root = ET.fromstring(response.text)
        status = root.find('.//status').text
        code = root.find('.//code')
        code_text = code.text if code is not None else 'N/A'

        _logger.info(f"Zeus response parsed: Status={status}, Code={code_text}")

        return status, response.text
    except requests.Timeout:
        _logger.error(f"Zeus request timed out: URL={url}")
        return 'error', '<response><result><status>error</status><message>Request timed out</message></result></response>'
    except Exception as e:
        _logger.error(f"Error sending Zeus request: URL={url}, Error={str(e)}")
        return 'error', f'<response><result><status>error</status><message>{str(e)}</message></result></response>'
```

## Tóm tắt giải pháp

1. **Cập nhật phương thức `create_zeus_payreq_xml()`** để thêm thông tin số tiền và order_id
2. **Lưu thông tin thanh toán vào database** sau khi nhận được response thành công từ Zeus
3. **Xử lý lỗi 008** để tiếp tục thanh toán mà không cần 3D Secure hoặc thông báo cho người dùng
4. **Cập nhật cấu hình Zeus** với URL chính xác và thông tin merchant thực tế từ Zeus:
   - Thay thế `TEST_MERCHANT` bằng `2019002213`
   - Thay thế `TEST_SECRET_KEY` bằng `b6d065489489de0c0518a245f8c926f7a5766d5b`
   - Parameter `service_id` không bắt buộc cho tích hợp API cơ bản của Zeus, có thể bỏ trống hoặc loại bỏ
5. **Sử dụng thông tin merchant từ cấu hình** thay vì hardcoded
6. **Thêm logging chi tiết và cải thiện xử lý lỗi** để dễ dàng debug
