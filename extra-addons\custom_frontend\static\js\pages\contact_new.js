import { userInfo } from "../router/router.js";
import { createUsageBreadcrumb } from "../utils/breadcrumbHelper.js";

const Contact_new = {
   'template': `
      <main class="margin-header">
         ${createUsageBreadcrumb('お問合せ')}
   <div class="container-fluid grabient pt-5">
      <div class="row">
         <div class="col-12">
            <form class="new_contact" id="new_contact" action="/contacts/confirm" accept-charset="UTF-8" method="post">
               <input name="utf8" type="hidden" value="✓" autocomplete="off"><input type="hidden" name="authenticity_token" value="bAu1Lb2YZvCf1VlUeKsEAGmkNbrihJNwrSHb18iujE5HvZpQjyC2ZbsUVGQ6bBZt7Ec9l2tfD8IA6tLdExzX0w==" autocomplete="off">
               <div class="card col-sm-12 col-md-10 col-lg-8 mx-auto pt-3">
                  <div class="card-body">

                     <div class="mx-auto mb-5">
                        <div class=""><label class="font-middle mb-3" for="">内容<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label></div>
                        <textarea rows="5" class="form-control" autocomplete="off" id="yourinquiry_field" name="contact[yourinquiry]" v-model="message"></textarea>
                        <div v-if="!check_message" class="text-danger text-left">内容を入力してください</div>
                     </div>
                     <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">会社名</label><input class="form-control" autocomplete="off" id="company_field" type="text" v-model="company" name="contact[company]"></div>

                     <label class="font-middle mb-3">名前<span class="badge-pill badge-danger pink lighten-2 font-small ml-3">必須</span></label>
                     <div class="row">
                        <div class="col-6">
                           <div class="mx-auto mb-5"><label class="custom-grey-text font-middle mb-3 active" for="">姓</label><input class="form-control" autocomplete="off" id="last_name_field" type="text" v-model="lastname" name="contact[last_name]"></div>
                           <div v-if="!check_lastname" class="text-danger text-left" style="margin-top: -48px;">姓を入力してください</div>
                        </div>
                        <div class="col-6">
                           <div class="mx-auto mb-5"><label class="custom-grey-text font-middle mb-3 active" for="">名</label><input class="form-control" autocomplete="off" id="first_name_field" type="text" v-model="firstname" name="contact[first_name]"></div>
                           <div v-if="!check_firstname" class="text-danger text-left" style="margin-top: -48px;">名を入力してください</div>
                        </div>
                     </div>
                     <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">メールアドレス<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="email_field" type="text" v-model="email" name="contact[email]"></div>
                     <div v-if="!check_email" class="text-danger text-left" style="margin-top: -48px;">メールアドレスを入力してください</div>
                     <div class="mx-auto mb-5"><label class="font-middle mb-3" for="">電話番号<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="phone_field" v-model="phone" type="text" name="contact[phone]"></div>
                     <div v-if="!check_phone" class="text-danger text-left" style="margin-top: -48px;">電話番号を入力してください</div>
                     <label class="font-middle mb-3">個人情報取扱い同意書</label>
                     <div class="p-3 scrollspy-example border mb-5">
                        <p>個人情報取り扱い同意書</p>
                        <p>Verticallimit株式会社(以下「当社」)では、お預かりした個人情報について、以下のとおり適正かつ安全に管理・運用することに努めます。</p>
                        <p> １．利用目的</p>
                        <p> 当社は、収集した個人情報について、以下の目的のために利用いたします。</p>
                        <p> ①サービス実施、およびアフターサービスのため <br> ②資料請求に対する発送のため <br> ③相談・お問い合わせへの回答のため <br> ④商品・サービス・イベントの案内のため </p>
                        <p> ２．第三者提供</p>
                        <p> 当社は、以下の場合を除いて、個人データを第三者へ提供することはしません。 </p>
                        <p> ①法令に基づく場合 <br> ②国の機関や地方公共団体、その委託者などによる法令事務の遂行にあたって協力する必要があり、かつ本人の同意を得ることで事務遂行に影響が生じる可能性がある場合</p>
                        <p> ３．業務の委託 <br> 当社はサービスを提供するために業務の一部を外部に委託する場合、業務委託先に対してお客様の個人情報を預けることがあります。この場合、個人情報を適切に取り扱っていると認められる委託先を選定し、契約等において個人情報の適正管理・機密保持などによりお客様の個人情報の漏洩防止に必要な事項を取決め、適切な管理を実施させます。</p>
                        <p> ４．開示請求 <br> 貴社の個人情報について、ご本人には、開示・訂正・削除・利用停止を請求する権利があります。手続きにあたっては、ご本人確認のうえ対応させていただきますが、代理人の場合も可能です。詳細については、以下「個人情報相談窓口」へご連絡ください。 <br> <br> 個人情報相談窓口 <br> メールID：<EMAIL> <br> 責任者：個人情報保護管理者 田畑　雅之</p>
                     </div>
                  </div>
               </div>
               <div class="row justify-content-center mt-4">
                  <div class="col-12 col-md-6">
                     <button
                        name="button"
                        style="margin-bottom:2rem;"
                        type="button"
                        class="btn btn-default btn-block btn-lg font-large submit_change_send_to mt-3 waves-effect waves-light"
                        @click="createContact"
                        :disabled="isLoading">
                        <span v-if="isLoading">
                           <i class="fas fa-spinner fa-spin mr-2"></i>送信中...
                        </span>
                        <span v-else>
                           個人情報取扱いに同意して送信
                        </span>
                     </button>
                  </div>
               </div>
            </form>
         </div>
      </div>
   </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/contact_new.css"/>
<link rel="stylesheet" href="/custom_frontend/static/css/dropdown.css"/>
<link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
      `,
   data() {
      return {
         showNavMenu: false,
         referenceCode: '',
         message: '',
         company: '',
         lastname: '',
         firstname: '',
         email: '',
         phone: '',
         isLoading: false,
         check_message: true,
         check_lastname: true,
         check_firstname: true,
         check_email: true,
         check_phone: true
      }
   },
   mounted() {
      this.user_id = userInfo ? userInfo.user_id : null;
      this.company_id = userInfo ? userInfo.user_company_id : null;
      this.getCompany();
   },
   methods: {
      toggleNavMenu() {
         this.showNavMenu = !this.showNavMenu;
      },

      toggleDropdown() { // New method to toggle dropdown visibility
         this.showDropdown = !this.showDropdown;
      },

      async sentContact() {
         try {
            const response = await fetch('/api/contact', {
               method: 'POST',
               headers: {
                  'Content-Type': 'application/json'
               },
               body: JSON.stringify({
                  reference_code: this.referenceCode,
                  message: this.message,
                  company: this.company,
                  lastname: this.lastname,
                  firstname: this.firstname,
                  email: this.email,
                  phone: this.phone
               })
            });

            const data = await response.json();
            console.log("Send mail: ", data);
            if (data.result.success) {
               window.location.href = '/contact_success';
            } else {
               console(data.message || 'メッセージの送信に失敗しました。');
            }

         } catch (error) {
            console.log('Error:', error);
         }
      },

      async getCompany() {
         try {
            const response = await fetch('/api/get_company_name', {
               method: 'POST',
               headers: {
                  'Content-Type': 'application/json'
               },
               body: JSON.stringify({
                  id: this.company_id
               })
            });

            const data = await response.json();

            console.log("Code: ", data);
            if (data.result.success) {
               this.referenceCode = data.result.referral_code;
            } else {
               console.log(data.result.message || 'Không thể lấy thông tin công ty');
            }
         } catch (error) {
            console.log("Error API: ", error);
         }
      },

      async createContact() {
         if(this.isLoading || !this.validateInput()) return;
         this.isLoading = true;
         try {
            const response = await fetch('/api/create_contact', {
               method: 'POST',
               headers: {
                  'Content-Type': 'application/json'
               },
               body: JSON.stringify({
                  referral_code: this.referenceCode,
                  content: this.message
               })
            });

            const data = await response.json();
            console.log("Create contact: ", data);
            if (data.result.success) {
               await this.sentContact();
            } else {
               console.log(data.result.message || 'Fail to create');
            }
         } catch (error) {
            console.log("Error API: ", error);
         } finally{
            this.isLoading = false;
         }
      },

      validateInput() {
         this.check_message = false;
         this.check_lastname = false;
         this.check_firstname = false;
         this.check_email = false;
         this.check_phone = false;

         if(this.message.trim() !== ''){
            this.check_message = true;
         }

         if(this.lastname.trim() !== ''){
            this.check_lastname = true;
         }

         if(this.firstname.trim() !== ''){
            this.check_firstname = true;
         }

         const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
         if(this.email.trim() !== '' && emailRegex.test(this.email.trim())){
            this.check_email = true;
         }

         const phoneRegex = /^[0-9]{10,11}$/;
         if(this.phone.trim() !== '' && phoneRegex.test(this.phone.trim())){
            this.check_phone = true;
         }

         if(this.check_message && this.check_lastname && this.check_firstname && this.check_email && this.check_phone){
            return true;
         }

         return false;
      }
   },

   watch: {
      message: {
         handler() {
            this.check_message = true;
         }
      },
      lastname: {
         handler() {
            this.check_lastname = true;
         }
      },
      firstname: {
         handler() {
            this.check_firstname = true;
         }
      },
      email: {
         handler() {
            this.check_email = true;
         }
      },
      phone: {
         handler() {
            this.check_phone = true;
         }
      }
   }
}

export default Contact_new;