import { createSingleBreadcrumb } from "../../utils/breadcrumbHelper.js";

const passwordNew = {
    'template': `
<main class="pb-3 margin-header" id="vue-app">
    ${createSingleBreadcrumb('パスワード再設定')}
    <div class="container-fluid grabient pt-2">
        <div class="row">
            <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                <form class="new_user" id="new_user" @submit.prevent="submitForm">
                    <div class="card mb-4">
                        <div class="card-body p-5">
                            <div class="row">
                                <div class="col-12">
                                    <div class="mx-auto mb-0">
                                        <label class="font-middle mb-3" for="email_field">メールアドレス</label>
                                        <input class="form-control" autocomplete="off" id="email_field" type="email" v-model="email">
                                    </div>
                                </div>
                            </div>
                            <div v-if="message" class="mt-3 custom-error-message">
                                {{ message }}
                            </div>
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-12 col-md-4 mt-2">
                            <button type="submit" id="password_reset_submit_btn"
                                    class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light">
                                送信
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="text-right pb-4">
                <a class="pagetop" @click.prevent="scrollToTop" href="#">
                    <i class="material-icons md-blue-grey md-36">arrow_upward</i>
                </a>
            </div>
        </div>
    </div>
</div>
<link rel="stylesheet" href="/custom_frontend/static/css/users/password/password_new.css"/>
<link rel="stylesheet" href="/custom_frontend/static/css/users/password/custom-alert.css"/>
    `,
    data() {
        return {
            email: '',
            message: '',
            success: false
        }
    },
    mounted() {
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
    },
    methods: {
        scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        async submitForm() {
            this.message = '';

            if (!this.email) {
                this.message = 'メールアドレスを入力してください';
                this.success = false;
                return;
            }

            try {
                const response = await fetch('/api/send_email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: this.email }),
                });

                const data = await response.json();

                if (data.result && data.result.success) {
                    // Hiển thị toast message trực tiếp
                    if (window.toastr) {
                        window.toastr.success("パスワード再設定メールを送信しました。", "", {
                            positionClass: 'toast-top-full-width',
                            progressBar: true,
                            closeButton: true
                        });

                        // Đợi 2 giây trước khi chuyển hướng để người dùng có thể thấy thông báo
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                    } else {
                        // Fallback nếu toastr chưa được load
                        sessionStorage.setItem('toastrMessage', "パスワード再設定メールを送信しました。");
                        sessionStorage.setItem('toastrType', "success");
                        window.location.href = '/login';
                    }
                } else {
                    this.message = 'メールアドレスが見つかりません。';
                    this.success = false;
                }
            } catch (error) {
                console.error('Error:', error);
                this.message = 'エラーが発生しました。もう一度お試しください。';
                this.success = false;
            }
        },
    }
}

export default passwordNew;
