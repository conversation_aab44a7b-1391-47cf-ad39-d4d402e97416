<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Plan data for pricing table -->
    <!-- Free Plans -->
    <record id="plan_free_monthly" model="vit.plans">
        <field name="title">無料会員</field>
        <field name="price">0</field>
        <field name="type">free</field>
        <field name="unit">month</field>
        <field name="created_at" eval="datetime.now()"/>
        <field name="created_by">Admin</field>
    </record>

    <record id="plan_free_yearly" model="vit.plans">
        <field name="title">無料会員</field>
        <field name="price">0</field>
        <field name="type">free</field>
        <field name="unit">year</field>
        <field name="created_at" eval="datetime.now()"/>
        <field name="created_by">Admin</field>
    </record>

    <!-- Standard Plans -->
    <record id="plan_standard_monthly" model="vit.plans">
        <field name="title">スタンダード会員</field>
        <field name="price">5000</field>
        <field name="type">standard</field>
        <field name="unit">month</field>
        <field name="created_at" eval="datetime.now()"/>
        <field name="created_by">Admin</field>
    </record>

    <record id="plan_standard_yearly" model="vit.plans">
        <field name="title">スタンダード会員</field>
        <field name="price">55000</field>
        <field name="type">standard</field>
        <field name="unit">year</field>
        <field name="created_at" eval="datetime.now()"/>
        <field name="created_by">Admin</field>
    </record>

    <!-- Premium Plans -->
    <record id="plan_premium_monthly" model="vit.plans">
        <field name="title">プレミアム会員</field>
        <field name="price">80000</field>
        <field name="type">premium</field>
        <field name="unit">month</field>
        <field name="created_at" eval="datetime.now()"/>
        <field name="created_by">Admin</field>
    </record>

    <record id="plan_premium_yearly" model="vit.plans">
        <field name="title">プレミアム会員</field>
        <field name="price">880000</field>
        <field name="type">premium</field>
        <field name="unit">year</field>
        <field name="created_at" eval="datetime.now()"/>
        <field name="created_by">Admin</field>
    </record>
</odoo>
