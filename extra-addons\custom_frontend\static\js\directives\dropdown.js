import { businessFieldMappings,businessFieldList,} from './business_field.js';

const optionsMappings = {
    options: {
        "更新日": "updated_at",
        "登録日": "created_at",
        "応募期限": "expired_at",
        "閲覧数": "view_count",
        "スカウト数": "scout_count"
    },
    work_availability: {
        "未選択": "",
        "即日可": "work_available",
        "今後可": "will_be_available",
        "要相談": "depends_on_opportunities",
        "対応不可": "not_corresponding"
    },
    planOptions: {
        "未選択": "",
        "スタンダード会員": "standard",
        "プレミアム会員": "premium"
    },
    optionPeriodKinds: {
        "登録日": "created_at",
        "応募期限": "expired_at",
        "更新日": "updated_date"
    },
    optionTalentFilter: {
        "全て": "all",
        "日程調整待ち": "awaiting_schedule",
        "日程調整済": "scheduled",
        "面談結果入力待ち": "awaiting_interview_input",
        "面談済": "interview_completed",
        "成約済": "contract_completed"
    },
    optionIssueFilter: {
        "全て": "all",
        "日程調整待ち": "awaiting_schedule",
        "日程調整済": "scheduled",
        "面談結果入力待ち": "awaiting_interview_input",
        "面談済": "interview_completed",
        "成約済(評価待ち)": "contract_completed_pending_review",
        "成約済": "contract_completed"
    },
    gender: {
        "未選択": "",
        "男性": "male",
        "女性": "female"
    },
    nationality_id_field: {
        "未選択": "",
        "日本": "japan",
        "日本以外": "other_country"
    },
    interview_count_id:{
        "未選択": "",
        "1回": "once",
        "2回": "twice",
        "その他": "other"
    },
    options_business_field: businessFieldMappings,
    business_field: businessFieldMappings,
    public_status_field: {
        "登録会社一覧へ掲載する": "public",
        "登録会社一覧へ掲載しない": "private",
    },
    opp_type_id: {
        "未選択": "",
        "エンド": "clnt",
        "元請": "prim",
        "一次請": "subc",
        "二次請以降": "msubc"
    },
    contract_types: {
        "未選択": "",
        "業務委託（準委任）": "quas",
        "業務委託（請負）": "subc",
        "派遣契約": "temp"
    },
    opp_categories: {
        "未選択": "",
        // 設計カテゴリー
        "設計 - PMO": "design_pmo",
        "設計 - PM・PL": "design_pmpl",
        "設計 - DX": "design_DX",
        "設計 - クラウド": "design_cloud",
        "設計 - モダナイゼション": "design_strategy",
        "設計 - セキュリティ": "design_work",
        "設計 - ITインフラ": "design_it",
        "設計 - AI": "design_ai",
        // 開発カテゴリー
        "開発 - PMO": "dev_pmo",
        "開発 - PM・PL": "dev_pmpl",
        "開発 - Webシステム": "dev_web",
        "開発 - IOS": "dev_ios",
        "開発 - Android": "dev_android",
        "開発 - 制御": "dev_control",
        "開発 - 組込": "dev_embedded",
        "開発 - AI・DL・ML": "dev_ai",
        "開発 - テスト": "dev_test",
        "開発 - クラウド": "dev_cloud",
        "開発 - サーバ": "dev_architect",
        "開発 - データベース": "dev_bridge_se",
        "開発 - ネットワーク": "dev_network",
        "開発 - メインフレーム": "dev_mainframe",
        // インフラカテゴリー
        "インフラ - PMO": "infra_pmo",
        "インフラ - PM・PL": "infra_pmpl",
        "インフラ - サーバー": "infra_server",
        "インフラ - ネットワーク": "infra_network",
        "インフラ - データベース": "infra_db",
        "インフラ - クラウド": "infra_cloud",
        "インフラ - 仮想化": "infra_virtualized",
        "インフラ - メインフレーム": "infra_mainframe",
        // 運用・保守カテゴリー
        "運用・保守 - 業務システム": "operation_pmo",
        "運用・保守 - オープン": "operation_pmpl",
        "運用・保守 - クラウド": "operation_DX",
        "運用・保守 - メインフレーム": "operation_mainframe",
        "運用・保守 - ヘルプデスク": "operation_strategy"
    },
    exp_categories: {
        "未選択": "",
        // 設計カテゴリー
        "設計- PMO": "consul_pmo",
        "設計 - PM・PL": "consul_pmpl",
        "設計- DX": "consul_DX",
        "設計 - クラウド": "consul_cloud",
        "設計 - モダナイゼション": "consul_modern",
        "設計 - セキュリティ": "consul_security",
        "設計 - ITインフラ": "consul_it",
        "設計 - AI": "consul_ai",
        // 開発カテゴリー
        "開発 - PMO": "dev_pmo",
        "開発 - PM・PL": "dev_pmpl",
        "開発 - Webシステム": "dev_web",
        "開発 - IOS": "dev_ios",
        "開発 - Android": "dev_android",
        "開発 - 制御": "dev_control",
        "開発 - 組込": "dev_emb",
        "開発 - AI・DL・ML": "dev_ai",
        "開発 - テスト": "dev_test",
        "開発 - クラウド": "dev_cloud",
        "開発 - サーバ": "dev_architect",
        "開発 - データベース": "dev_bridge_se",
        "開発 - ネットワーク": "dev_network",
        "開発 - メインフレーム": "dev_mainframe",
        // インフラカテゴリー
        "インフラ - PMO": "infra_pmo",
        "インフラ - PM・PL": "infra_pmpl",
        "インフラ - サーバー": "infra_server",
        "インフラ - ネットワーク": "infra_network",
        "インフラ - データベース": "infra_db",
        "インフラ - クラウド": "infra_cloud",
        "インフラ - 仮想化": "infra_virtualized",
        "インフラ - メインフレーム": "infra_mainframe",
        // 運用・保守カテゴリー
        "運用・保守 - 業務システム": "design_business",
        "運用・保守 - オープン": "design_open",
        "運用・保守 - クラウド": "design_cloud",
        "運用・保守 - メインフレーム": "design_mainfream",
        "運用・保守 - ヘルプデスク": "design_helpdesk"
    }
};

const optionsList = {
    options: ["更新日", "登録日", "応募期限", "閲覧数", "スカウト数"],
    work_availability: ["未選択", "即日可", "今後可", "要相談", "対応不可"],
    optionPeriodKinds: ["登録日", "応募期限", "更新日"],
    accessible_bookmark_user_groups_field: ["すべてのブックマーク先に公開"],
    accessible_bookmark_resume_groups_field: ["すべてのブックマーク先に公開"],
    planOptions: ["未選択"], // Chỉ giữ lại giá trị mặc định, các giá trị khác sẽ được lấy từ API
    optionYearField: [ 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954,
        1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964,
        1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974,
        1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984,
        1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994,
        1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005 ],
    optionMonthField: [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 ],
    optionDayField: [ 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
        21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31],
    optionIssueFilter: ["全て", "日程調整待ち", "日程調整済", "面談結果入力待ち", "面談済", "成約済(評価待ち)", "成約済"],
    optionInquiryType: ["未選択", "Mi52について", "メディア掲載・取材等のご依頼"],
    optionTalentFilter: ["全て", "日程調整待ち", "日程調整済", "面談結果入力待ち", "面談済", "成約済"],
    interview_count_id: ["未選択" , "1回", "2回", "その他"],
    gender: ["未選択", "男性", "女性"],
    age_min: [ '未選択', 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79 ,80 ],
    age_max: [ '未選択', 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79 ,80 ],
    nationality_id_field: ['未選択', '日本', '日本以外'],
    qualifications: ['未選択', 'ITパスポート', '基本情報処理技術者', '情報検定(J検)', '情報検索応用能力試験', 'MCP(マイクロソフト認定プロフェッショナル)', 'ドットコムマスター', '応用情報技術者', '情報セキュリティスペシャリスト', 'ITサービスマネージャ', 'Linuｘ技術者認定試験(LPIC)', '企業情報管理士認定試験', 'プロジェクトマネージャー', 'PMP(Project Management Professional)試験', '日商PC検定', 'パソコン検定(P検)', 'IC3', 'CompTIAA+', 'パソコンインストラクター資格認定', 'パソコン整備士', 'Excel 表計算処理技能設定試験', 'Access ビジネスデータベース技能認定試験', 'マイクロソフトオフィススペシャリスト(MOS)', 'システムアーキテクト', 'VBAエキスパート', 'C言語プログラミング能力認定試験', 'Javaプログラミング能力認定試験', 'PHP技術者認定試験試験', 'Pythonエンジニア認定試験', 'オラクルJava認定資格(OCJ)', '情報処理安全確保支援士試験', '情報セキュリティマネジメント試験', 'ネットワークスペシャリスト', 'シスコ技術者認定', 'データベーススペシャリスト', 'オラクルマスター', 'システム監査技術者', 'ITストラテジスト', 'ITコーディネータ'],
    optionFilterResume: ['更新日', '登録日', '閲覧数', 'スカウト受領数'],
    options_business_field: businessFieldList,
    business_field: businessFieldList,
    opp_type_id: ["未選択", "エンド", "元請", "一次請", "二次請以降"],
    contract_types: ["未選択", "業務委託（準委任）", "業務委託（請負）", "派遣契約"],
    opp_categories: [
        "未選択",
        // 設計カテゴリー
        "設計 - PMO", "設計 - PM・PL", "設計 - DX", "設計 - クラウド", "設計 - モダナイゼション",
        "設計 - セキュリティ", "設計 - ITインフラ", "設計 - AI",
        // 開発カテゴリー
        "開発 - PMO", "開発 - PM・PL", "開発 - Webシステム", "開発 - IOS", "開発 - Android",
        "開発 - 制御", "開発 - 組込", "開発 - AI・DL・ML", "開発 - テスト", "開発 - クラウド",
        "開発 - サーバ", "開発 - データベース", "開発 - ネットワーク", "開発 - メインフレーム",
        // インフラカテゴリー
        "インフラ - PMO", "インフラ - PM・PL", "インフラ - サーバー", "インフラ - ネットワーク",
        "インフラ - データベース", "インフラ - クラウド", "インフラ - 仮想化", "インフラ - メインフレーム",
        // 運用・保守カテゴリー
        "運用・保守 - 業務システム", "運用・保守 - オープン", "運用・保守 - クラウド",
        "運用・保守 - メインフレーム", "運用・保守 - ヘルプデスク"
    ],
    exp_categories: [
        "未選択",
        // 設計カテゴリー
        "設計 - PMO", "設計 - PM・PL", "設計 - DX", "設計 - クラウド",
        "設計 - モダナイゼション", "設計 - セキュリティ", "設計 - ITインフラ", "設計 - AI",
        // 開発カテゴリー
        "開発 - PMO", "開発 - PM・PL", "開発 - Webシステム", "開発 - IOS", "開発 - Android",
        "開発 - 制御", "開発 - 組込", "開発 - AI・DL・ML", "開発 - テスト", "開発 - クラウド",
        "開発 - サーバ", "開発 - データベース", "開発 - ネットワーク", "開発 - メインフレーム",
        // インフラカテゴリー
        "インフラ - PMO", "インフラ - PM・PL", "インフラ - サーバー", "インフラ - ネットワーク",
        "インフラ - データベース", "インフラ - クラウド", "インフラ - 仮想化", "インフラ - メインフレーム",
        // 運用・保守カテゴリー
        "運用・保守 - 業務システム", "運用・保守 - オープン", "運用・保守 - クラウド",
        "運用・保守 - メインフレーム", "運用・保守 - ヘルプデスク"
    ],
    workplaces: ["未選択","北海道", "青森県", "岩手県", "宮城県", "秋田県", "山形県", "福島県",
        "茨城県", "栃木県", "群馬県", "埼玉県", "千葉県", "東京都", "神奈川県",
        "新潟県", "富山県", "石川県", "福井県", "山梨県", "長野県", "岐阜県",
        "静岡県", "愛知県", "三重県", "滋賀県", "京都府", "大阪府", "兵庫県",
        "奈良県", "和歌山県", "鳥取県", "島根県", "岡山県", "広島県", "山口県",
        "徳島県", "香川県", "愛媛県", "高知県", "福岡県", "佐賀県", "長崎県",
        "熊本県", "大分県", "宮崎県", "鹿児島県", "沖縄県", "海外"],
    public_status_field: ["登録会社一覧へ掲載する", "登録会社一覧へ掲載しない"],
};
window.optionsMappings = optionsMappings;
window.optionsList = optionsList;

// Hàm cập nhật danh sách plan options từ API
window.updatePlanOptions = async function() {
    try {
        //console.log('Fetching plan options from API...');
        // Thêm timestamp để tránh cache
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/get_plan_options?t=${timestamp}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        // Kiểm tra response trước khi parse JSON
        if (!response.ok) {
            console.error('API response not OK:', response.status, response.statusText);
            const text = await response.text();
            console.error('Response text:', text);
            throw new Error(`API response not OK: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success && Array.isArray(data.plan_options) && Array.isArray(data.plan_options_mapping)) {
            optionsList.planOptions = data.plan_options;

            // Tạo mapping label -> id
            const mapping = {};
            for (let i = 0; i < data.plan_options.length; i++) {
                mapping[data.plan_options[i]] = data.plan_options_mapping[i];
            }
            optionsMappings.planOptions = mapping;

            document.dispatchEvent(new CustomEvent('planOptionsUpdated'));
            //console.log('Plan options updated successfully:', data.plan_options);
            //console.log('Plan options mapping updated successfully:', mapping);
        } else {
            console.error('Failed to update plan options:', data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error fetching plan options:', error);
    }
};

const DropdownDirective = {
    mounted(el, binding) {

        const { modelValue , listType: type } = binding.value || {};
        const listType = (typeof type === 'string' && type in optionsList) ? type : 'options';

        let selectedOption = modelValue || optionsList[listType]?.[0] || ""; // Mặc định chọn mục đầu tiên
        let selectedValue = optionsMappings[listType]?.[selectedOption] || selectedOption;

        // Thêm class chuẩn
        el.classList.add("select-wrapper", "mdb-select", "anavi-select");

        // Tạo caret icon
        const caret = document.createElement("span");
        caret.className = "caret material-icons";
        caret.textContent = "keyboard_arrow_down";
        el.appendChild(caret);

        // Tạo input (chỉ đọc)
        const input = document.createElement("input");
        input.type = "text";
        input.className = "select-dropdown select-options form-control";
        input.readOnly = true;
        input.setAttribute("role", "listbox");
        input.setAttribute("aria-multiselectable", "false");
        input.setAttribute("aria-haspopup", "true");
        input.setAttribute("aria-expanded", "false");
        input.value = selectedOption;
        el.appendChild(input);

        // Tạo danh sách dropdown
        const dropdown = document.createElement("ul");
        dropdown.id = "select-options-" + listType + "_field";
        dropdown.className = "dropdown-content select-dropdown select-options w-100";
        dropdown.style.cssText = `
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            max-height: 11.5rem;
        `;
        el.appendChild(dropdown);

        // Hàm để cập nhật danh sách dropdown
        function updateDropdownOptions() {
            // Xóa tất cả các mục hiện tại
            dropdown.innerHTML = '';

            // Thêm lại các mục từ danh sách đã cập nhật
            optionsList[listType].forEach(option => {
                const li = document.createElement("li");
                li.className = option === selectedOption ? "active" : "";
                li.setAttribute("role", "option");

                const span = document.createElement("span");
                span.className = "filtrable";
                span.textContent = option;

                li.appendChild(span);
                li.addEventListener("click", () => {
                    selectedOption = option;
                    selectedValue = optionsMappings[listType]?.[option] || option;
                    input.value = option;
                    updateActiveState();
                    closeDropdown();
                    el.dispatchEvent(new CustomEvent("selected", { detail: selectedValue }));
                });

                dropdown.appendChild(li);
            });
        }

        // Khởi tạo danh sách ban đầu
        updateDropdownOptions();

        // Lắng nghe sự kiện cập nhật danh sách plan options
        if (listType === 'planOptions') {
            document.addEventListener('planOptionsUpdated', () => {
                updateDropdownOptions();
                // Nếu option đã chọn không còn tồn tại trong danh sách mới, chọn mục đầu tiên
                if (!optionsList[listType].includes(selectedOption)) {
                    selectedOption = optionsList[listType][0] || "";
                    selectedValue = optionsMappings[listType]?.[selectedOption] || selectedOption;
                    input.value = selectedOption;
                }
            });
        }

        // Cập nhật trạng thái active
        function updateActiveState() {
            dropdown.querySelectorAll("li").forEach((li) => {
                let liValue = isNaN(li.textContent) ? li.textContent : Number(li.textContent);
                const isActive = liValue === selectedOption;
                li.classList.toggle("active", isActive);

                // Scroll to active item
                if (isActive) {
                    setTimeout(() => {
                        const dropdownHeight = dropdown.offsetHeight;
                        const itemHeight = li.offsetHeight;
                        const scrollTop = li.offsetTop;
                        const scrollAdjust = (dropdownHeight / 2) - (itemHeight / 2);
                        dropdown.scrollTop = scrollTop - scrollAdjust;
                    }, 0);
                }
            });
        }

        // Mở dropdown với opacity
        function openDropdown() {
            dropdown.style.display = "block";

            // Ensure high z-index on mobile
            if (window.innerWidth <= 767) {
                dropdown.style.zIndex = "10000";
                dropdown.style.position = "fixed";

                // Calculate position relative to viewport
                const rect = input.getBoundingClientRect();
                dropdown.style.top = (rect.bottom + 2) + "px";
                dropdown.style.left = rect.left + "px";
                dropdown.style.width = rect.width + "px";
            }

            setTimeout(() => {
                dropdown.style.opacity = "1";
            }, 10);
            updateActiveState();
        }

        // Đóng dropdown với opacity
        function closeDropdown() {
            dropdown.style.opacity = "0";
            setTimeout(() => {
                dropdown.style.display = "none";
            }, 300);
        }

        // Thêm một custom event để đóng các dropdown khác
        const closeOtherDropdownsEvent = new CustomEvent('closeOtherDropdowns', {
            detail: { currentDropdown: el }
        });

        // Toggle dropdown
        input.addEventListener("click", (event) => {
            event.stopPropagation();
            // Emit event để đóng các dropdown khác
            document.dispatchEvent(closeOtherDropdownsEvent);
            dropdown.style.display === "block" ? closeDropdown() : openDropdown();
        });

        // Lắng nghe event đóng từ các dropdown khác
        document.addEventListener("closeOtherDropdowns", (event) => {
            if (event.detail.currentDropdown !== el && dropdown.style.display === "block") {
                closeDropdown();
            }
        });

        // Đóng dropdown khi click ra ngoài
        document.addEventListener("click", (event) => {
            if (!el.contains(event.target)) {
                closeDropdown();
            }
        });

        document.addEventListener("restoreDropdown", (event) => {
            const { key, value } = event.detail;  // Nhận cả key và giá trị từ event

            if (listType === key && optionsList[listType].includes(value)) {
                selectedOption = value;
                selectedValue = optionsMappings[listType]?.[value] || value;
                input.value = value;
                updateActiveState();
            }
        });
    }
};

// Gọi API để lấy danh sách plan options khi file được tải
(async function initPlanOptions() {
    try {
        // Chờ một chút để đảm bảo DOM đã được tải
        await new Promise(resolve => setTimeout(resolve, 100));

        // Gọi API để lấy danh sách plan options
        if (typeof window !== 'undefined' && window.updatePlanOptions) {
            await window.updatePlanOptions();
        }
    } catch (error) {
        console.error('Error initializing plan options:', error);
    }
})();

export default {
    install(app) {
        app.directive("dropdown", DropdownDirective);
    }
};
