import pickadate from '/custom_frontend/static/js/directives/pickadate.js';
import { userInfo } from "../../../router/router.js";
import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const NewResume = {
    'template': `
        <main class="margin-header sp_fluid" id="vue-app" data-v-app="">
            <div class="container-fluid d-none d-md-block">
                ${createBreadcrumb([
                    { text: 'サービスメニュー', link: null },
                    { text: '登録・管理', link: null },
                    { text: '案件・人財登録', link: null },
                    { text: '人財登録', link: null, current: true }
                ], 'container-fluid', '')}
            </div>
            <div class="container-fluid">
                <div style="display: flex; justify-content: flex-start; width: 100%; gap: 2.3%; margin-left: -4px;">
                    <div @click="tabChange(1)" class="tabButton">
                        案件登録
                    </div>
                    <div @click="tabChange(2)" style="margin-left: -1.5%;" class="tabButton btn-default">
                        人財登録
                    </div>
                </div>
            </div>
            <div class="container-fluid grabient pt-5">
                <input id="is_dispatch_license" type="hidden" name="is_dispatch_license" value="false">
                <div class="row">
                    <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                        <form @submit.prevent method="post" class="form_change_send_to">
                            <div class="card px-3 px-md-4 mt-2 pt-5 form-card sp_sides_uniter">
                                <div class="px-1 px-md-3" style="padding: 0 !important;">
                                    <div class="pl-0 pl-sm-2 border-bottom mb-5">
                                        <div class="row px-2 px-md-3">
                                            <div class="col-12" v-if="$route.fullPath.includes('duplicate')">
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-3">スキルシート</label>
                                                <!--v-if--><!--v-if--><!-- subTextIdは人財の「可能な契約形態」にて使用 --><!--v-if-->
                                                <div class="mb-3">
                                                    <a :href="'/resumes/' + res_id + '/file_upload'" @click="savePageInfo('duplicate')">
                                                        <span name="" type="submit"
                                                            class="btn font-default white-text bg-default-green btn-sm btn-outline-default m-0 waves-effect waves-light"
                                                            disabled="false">編集</span>
                                                    </a>
                                                </div>
                                                <div v-if="!cv_name" class="mb-4">登録されていません</div>
                                                <template v-else>
                                                    <div class="d-flex mb-1 align-items-middle">
                                                        <div class="pr-3">{{ cv_name }}</div>
                                                        <div class="ml-2"><a href="javascript:void(0);"
                                                                class="font-middle" @click="fetchCV">ダウンロード</a></div>
                                                    </div>
                                                    <div class="mb-4 grey-text">登録日：{{ cv_date }}</div>
                                                </template>
                                            </div>
                                            <div class="col-12" v-else>
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-3">スキルシート</label>
                                                <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                <div>
                                                    <div class="alert alert-warning text-center" role="alert">
                                                        案件への応募には、スキルシート（履歴書・経歴書）のアップロードが必要です。
                                                    </div>
                                                    <p class="error-text text-center">
                                                        ※登録するスキルシートはweb上で多くの登録企業に閲覧されます。個人情報は本名→イニシャルにするなど、マスキングしてください。
                                                    </p>
                                                </div>
                                                <div class="mb-3">
                                                    <form class="dropzone dz-clickable" id="upload-file" enctype="multipart/form-data" @click="triggerFileInput">
                                                        <div class="dz-default dz-message" id="upload-area" style="cursor: pointer;" v-if="!existingFile">
                                                            <i class="material-icons md-grey md-36">cloud_upload</i>
                                                            <span class="font-weight-bold grey-text mt-3" style="display: block;">
                                                                ここにファイルをドロップしてください。
                                                            </span>
                                                        </div>
                                                        <input type="file" id="file-input" ref="fileInput" style="display: none;" multiple @change="handleFileChange">
                                                        <div v-if="existingFile" class="dz-preview dz-image-preview dz-complete">
                                                            <div class="dz-image"><img :src="getFileIcon(selectedFiles[0].name)"></div>
                                                            <div class="dz-details">
                                                                <div class="dz-size"><span data-dz-size=""><strong>{{ selectedFiles[0].size }}</strong></span></div>
                                                                <div class="dz-filename"><span data-dz-name="">{{ selectedFiles[0].name }}</span></div>
                                                            </div>
                                                            <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress="" style="width: 100%;"></span></div>
                                                            <div class="dz-success-mark"></div>
                                                            <a class="dz-remove" @click="removeFile($event)">削除する</a>
                                                        </div>
                                                    </form>
                                                    <p class="custom-grey-text mt-3 mb-0">対応ファイル形式：PDF、DOC、DOCX、TXT（最大10MBまで）</p>
                                                    <p class="custom-grey-text">掲載可能なファイルは１枚のみです。</p>
                                                </div>
                                                <div v-if="!check_selected_file" class="text-danger">
                                                    スキルシートをアップロードしてください。
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 人財の基本情報 -->
                                <div class="pl-0 pl-sm-2">
                                    <p class="font-extralarge mb-4 pl-sm-3">基本情報</p>
                                    <!-- 名前 -->
                                    <div class="row px-2 px-md-3">
                                        <div class="col-12">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">名前</label>
                                            <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                            <span id="" class="custom-grey-text d-block mb-3">他の会員様には公開されないため、本名をご入力ください。一覧の管理用としてお使いいただけます。</span>
                                        </div>
                                    </div>
                                        <div class="row px-2 px-md-3 mb-2">
                                            <div class="col-6">
                                                <div class="mx-auto mb-5">
                                                    <input id="lastname_field" v-model="UserSurName" class="form-control" autocomplete="off" type="text" name="resume[lastname]">
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="mx-auto mb-5">
                                                    <input id="firstname_field" v-model="UserFirstName" class="form-control" autocomplete="off" type="text" name="resume[firstname]">
                                                </div>
                                            </div>
                                        </div>
                                        <!-- イニシャル -->
                                        <div class="row px-2 px-md-3">
                                            <div class="col-12">
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-2">イニシャル</label>
                                                <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                                <span id="" class="custom-grey-text d-block mb-3">他の会員に公開されます。マスキングの為、人財のイニシャルをご入力ください。</span>
                                            </div>
                                        </div>
                                        <div class="row px-2 px-md-3 mb-2">
                                            <div class="col-6">
                                                <div class="mx-auto mb-5">
                                                    <input id="nickname_lastname_field" v-model="InitalSurName" class="form-control" autocomplete="off" type="text" name="resume[nickname_lastname]">
                                                    <div v-if="!check_lastname" class="text-danger mt-3">
                                                        名前を入力してください
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="mx-auto mb-5">
                                                    <input id="nickname_firstame_field" v-model="InitialFirstName" class="form-control" autocomplete="off" type="text" name="resume[nickname_firstname]">
                                                    <div v-if="!check_firstname" class="text-danger mt-3">
                                                        名前を入力してください
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 性別 -->
                                        <div class="row px-2 px-md-3 mb-4">
                                            <div class="col-12">
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-3">性別</label>
                                                <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                                <div class="mb-0">
                                                    <div class="form-inline mb-3">
                                                        <div class="form-check pl-0 mb-3 mb-sm-0">
                                                            <input id="gender_id_field0" v-model="Gender" class="form-check-input" type="radio" name="resume[gender_id]" value="male">
                                                            <label id="gender_id_field_label_0" class="form-check-label" for="gender_id_field0">男性</label>
                                                        </div>
                                                        <div class="form-check pl-0 mb-3 mb-sm-0">
                                                            <input id="gender_id_field1" v-model="Gender" class="form-check-input" type="radio" name="resume[gender_id]" value="female">
                                                            <label id="gender_id_field_label_1" class="form-check-label" for="gender_id_field1">女性</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="!check_gender" class="text-danger">
                                                    性別を選択してください
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 生年月日 -->
                                        <div class="row px-2 px-md-3">
                                            <div class="col-12">
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-2">生年月日</label>
                                                <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                                <span id="" class="custom-grey-text d-block mb-3">入力した生年月日は公開されません。自動計算により、年齢のみ公開されます。</span>
                                            </div>
                                        </div>
                                        <div class="row px-2 px-md-3 mb-4">
                                            <!-- year -->
                                            <div class="col-12 col-sm-4">
                                                <div data-v-deab947c="" class="mx-auto mb-3">
                                                    <div v-dropdown="{ modelValue: BirthYear, listType: 'optionYearField' }" :key="BirthYear" @selected="BirthYear = $event.detail">
                                                        <input type="hidden" v-model="BirthYear"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- month -->
                                            <div class="col-6 col-sm-4 pl-sm-3">
                                                <div data-v-deab947c="" class="mx-auto mb-3">
                                                    <div v-dropdown="{ modelValue: BirthMonth, listType: 'optionMonthField' }" :key="BirthMonth" @selected="BirthMonth = $event.detail">
                                                        <input type="hidden" v-model="BirthMonth"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- day -->
                                            <div class="col-6 col-sm-4">
                                                <div data-v-deab947c="" class="mx-auto mb-3">
                                                    <div v-dropdown="{ modelValue: BirthDate, listType: 'optionDayField' }" :key="BirthDate" @selected="BirthDate = $event.detail">
                                                        <input type="hidden" v-model="BirthDate"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 国籍 -->
                                        <div class="row px-2 px-md-3 mb-4">
                                            <div class="col-12">
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-2">国籍</label>
                                                <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                                <span id="" class="custom-grey-text d-block mb-3">国籍が「日本以外」の方は、国名をご記入ください。</span>
                                                <div class="mb-0">
                                                    <div class="form-inline mb-3">
                                                        <div class="d-flex">
                                                            <div class="form-check pl-0 mb-3 mb-sm-0">
                                                                <input id="nationality_id_field0" class="form-check-input" v-model="selectedCountry" type="radio" name="resume[nationality_id]" value="japan" @click="handleCountryChange">
                                                                <label id="nationality_id_field_label_0" class="form-check-label" for="nationality_id_field0">日本</label>
                                                            </div>
                                                            <div class="form-check pl-0 mb-3 mb-sm-0">
                                                                <input id="nationality_id_field1" class="form-check-input" v-model="selectedCountry" type="radio" name="resume[nationality_id]" value="foreign" @click="handleCountryChange">
                                                                <label id="nationality_id_field_label_1" class="form-check-label" for="nationality_id_field1">日本以外</label>
                                                            </div>
                                                            <div class="col-12 col-sm-6 px-sm-3 px-0">
                                                                <input id="" class="form-control ml-sm-4 mt-sm-0 w-100" autocomplete="off" type="text" name="resume[country_name]" v-model="Nationality" :disabled="selectedCountry === 'japan'">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-sm-6 px-sm-3 px-0 d-flex mt-1 country">
                                                            <label class="form-check-label">在留資格有無</label>
                                                            <div class="ml-3">
                                                                <input id="reside_field1" class="form-check-input"  type="radio" v-model="isResidential" name="resume[reside]" value="yes" :disabled="selectedCountry === 'japan'">
                                                                <label class="form-check-label" for="reside_field1">有</label>
                                                            </div>
                                                            <div class="ml-3">
                                                                <input id="reside_field2" class="form-check-input"  type="radio" v-model="isResidential" name="resume[reside]" value="no" :disabled="selectedCountry === 'japan'">
                                                                <label class="form-check-label" for="reside_field2">無</label>
                                                            </div>
                                                        </div>
                                                </div>
                                            </div>
                                        </div>
                                <div class="px-2 px-md-3">
                                    <div class="row mb-5">
                                        <div class="col-12">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">得意領域</label>
                                            <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                            <div v-if="!check_categories" class="text-danger">
                                                得意領域を選択してください
                                            </div>
                                            <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                            <span id="" class="custom-grey-text d-block mb-3">2～3個を目安に選択するのがおすすめです。</span>
                                            <div class="consul_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear" style="cursor: pointer" @click="toggleAccordion">
                                                <span class="font-size-middle ex-bold">設計</span>
                                                <i class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">{{ isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                            </div>
                                            <input id="resume_accordion_open_consul" type="hidden" name="resume[accordion_open_consul]" value="yes">
                                            <div class="accordion_contents_consul" style="" v-show="isOpen">
                                                <div class="mx-auto py-3 pl-3">
                                                    <div class="row px-2 px-md-3 mb-2">
                                                        <div class="col-12">
                                                            <div class="selecting-form row px-3">
                                                                <input type="hidden" name="resume[exp_categories][]">
                                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4" v-for="(item, index) in categories" :key="index">
                                                                    <input :id="'consul' + index"
                                                                        id-params="consul"
                                                                        class="custom-control-input"
                                                                        type="checkbox"
                                                                        name="resume[exp_categories][]"
                                                                        v-model="ConsultCastegories"
                                                                        :value="item.value">
                                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                                        :for="'consul' + index">{{ item.label }}</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dev_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear" @click="toggleAccordion1">
                                                <span class="font-size-middle ex-bold">開発</span>
                                                <i class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{ isOpen1 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                                                </i>
                                            </div>
                                            <input id="resume_accordion_open_dev" type="hidden" name="resume[accordion_open_dev]" value="no">
                                            <div class="accordion_contents_dev" style="display: none;" v-show="isOpen1">
                                                <div class="mx-auto py-3 pl-3">
                                                    <div class="row px-2 px-md-3 mb-4">
                                                        <div class="col-12">
                                                            <div class="selecting-form row px-3" >
                                                                <input type="hidden" name="resume[exp_categories][]">
                                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"
                                                                    v-for="(item, index) in categoriesDev" :key="index">
                                                                    <input :id="'dev' + index"
                                                                        id-params="dev"
                                                                        class="custom-control-input"
                                                                        type="checkbox"
                                                                        name="resume[exp_categories][]"
                                                                        v-model="DevCastegories"
                                                                        :value="item.value">
                                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                                        :for="'dev' + index">{{ item.label}}</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="infra_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear" @click="toggleAccordion2">
                                                <span class="font-size-middle ex-bold">インフラ</span>
                                                <i class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{isOpen2 ? 'keyboard_arrow_up' : 'keyboard_arrow_down'}}</i>
                                            </div>
                                            <input id="resume_accordion_open_infra" type="hidden" name="resume[accordion_open_infra]" value="no">
                                            <div class="accordion_contents_infra" style="display: none;" v-show="isOpen2">
                                                <div class="mx-auto py-3 pl-3">
                                                    <div class="row px-2 px-md-3 mb-4">
                                                        <div class="col-12">
                                                            <div class="selecting-form row px-3">
                                                                <input type="hidden" name="resume[exp_categories][]">
                                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"
                                                                    v-for="(item, index) in categoriesInfra" :key="index">
                                                                    <input :id="'infra' + index"
                                                                        id-params="infra"
                                                                        class="custom-control-input"
                                                                        type="checkbox"
                                                                        name="resume[exp_categories][]"
                                                                        v-model="InfraCastegories"
                                                                        :value="item.value">
                                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                                        :for="'infra' + index">
                                                                    {{item.label}}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="design_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear" @click="toggleAccordion3">
                                                <span class="font-size-middle ex-bold">運用・保守</span>
                                                <i class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{ isOpen3 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                            </div>
                                            <input id="resume_accordion_open_design" type="hidden" name="resume[accordion_open_design]" value="no">
                                            <div class="accordion_contents_design" style="display: none;" v-show="isOpen3">
                                                <div class="mx-auto py-3 pl-3">
                                                    <div class="row px-2 px-md-3 mb-4">
                                                        <div class="col-12">
                                                            <div class="selecting-form row px-3">
                                                                <input type="hidden" name="resume[exp_categories][]">
                                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"
                                                                    v-for="(item, index) in categoriesDesign" :key="index">
                                                                    <input id-params="design" class="custom-control-input" type="checkbox" name="resume[exp_categories][]"
                                                                        :id="'design' + index"
                                                                        v-model="DesignCastegories"
                                                                        :value="item.value">
                                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                                        :for="'design' + index">{{item.label}}</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 就業場所 -->
                                <workplace-selector :required-label="true" :mb_5="marginBottom" v-model="specifies_workplaces"></workplace-selector>
                                <div v-if="!check_workplace" class="text-danger">
                                    就業場所を選択してください
                                </div>
                                <div class="row mb-2 px-2 px-md-3">
                                    <div class="col-12">
                                        <div class="mx-auto mb-1">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">駅・地域</label>
                                            <span id="" class="custom-grey-text d-block mb-3">希望する就業場所の駅名/沿線名/地域名などをご入力ください。<br>例）●●駅から通勤1時間圏内を希望/フルリモートを希望/東京23区内および都内近郊なら可 など</span>
                                            <textarea id="station_area_field" v-model="Region" name="resume[station_area]" rows="3" class="form-control" autocomplete="off"></textarea>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-start">
                                            <CharacterCounter targetId="station_area_field" :max-length="1000" />
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-5 mb-2 px-2 px-md-3">
                                    <div class="col-12">
                                        <label id="" class="font-middle pl-1 pl-md-0 mb-2">単価</label><span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                        <span id="" class="custom-grey-text d-block mb-3">稼働率100%（フル稼働）換算での単価を入力してください。</span>
                                        <PriceRange
                                            :min="0"
                                            :max="300"
                                            :step="5"
                                            :startValues="[UnitPriceMin, UnitPriceMax]"
                                            @update:price_min="val => UnitPriceMin = val"
                                            @update:price_max="val => UnitPriceMax = val"
                                        />
                                        <input type="hidden" :value="UnitPriceMin !== null ? UnitPriceMin : ''" name="resume[asking_unit_price_min]">
                                        <input type="hidden" :value="UnitPriceMax !== null ? UnitPriceMax : ''" name="resume[asking_unit_price_max]">
                                        <div v-if="check_unit_price_min" class="text-danger">
                                            {{err_price_min}}
                                        </div>
                                        <div v-if="check_unit_price_max" class="text-danger">
                                            {{err_price_max}}
                                        </div>
                                    </div>
                                </div>
                                <!-- 稼働可能状況 -->
                                <div class="pl-0 mb-4">
                                    <p class="font-extralarge mb-4 pl-sm-3">稼働可能状況</p>
                                    <!-- ステータス -->
                                    <div class="row px-2 px-md-3 mb-4">
                                        <div class="col-12">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">ステータス</label><span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span><!--v-if--><!-- subTextIdは人財の「可能な契約形態」にて使用 --><span id="" class="custom-grey-text d-block mb-3">稼働率が少ない、就業時間が限定されるなど状況により稼働時期を相談したい場合は「要相談」をお選びください。<br>案件探しが終わっている方の情報が滞留しないよう、「即日可」に設定してから20日間経過すると、自動的に「対応不可」に変更されます。引き続き案件を探す場合、稼働可能状況を「即日可」に更新することでスカウトが届く可能性が高まります。</span>
                                            <div class="mb-0">
                                                <div class="form-inline mb-3">
                                                    <div class="form-check pl-0 mb-3 mb-sm-0"><input id="available_status_id_field0" class="form-check-input" type="radio" name="resume[available_status_id]" v-model="ResumeStatus" value="work_available"><label id="available_status_id_field_label_0" class="form-check-label" for="available_status_id_field0">即日可</label></div>
                                                    <div class="form-check pl-0 mb-3 mb-sm-0"><input id="available_status_id_field1" class="form-check-input" type="radio" name="resume[available_status_id]" v-model="ResumeStatus" value="will_be_available"><label id="available_status_id_field_label_1" class="form-check-label" for="available_status_id_field1">今後可</label></div>
                                                    <div class="form-check pl-0 mb-3 mb-sm-0"><input id="available_status_id_field2" class="form-check-input" type="radio" name="resume[available_status_id]" v-model="ResumeStatus" value="depends_on_opportunities"><label id="available_status_id_field_label_2" class="form-check-label" for="available_status_id_field2">要相談</label></div>
                                                    <div class="form-check pl-0 mb-3 mb-sm-0"><input id="available_status_id_field3" class="form-check-input" type="radio" name="resume[available_status_id]" v-model="ResumeStatus" value="not_corresponding"><label id="available_status_id_field_label_3" class="form-check-label" for="available_status_id_field3">対応不可</label></div>
                                                </div>
                                            </div>
                                            <div v-if="!check_resume_status" class="text-danger">
                                                ステータスを選択してください。
                                            </div>
                                            <!--v-if-->
                                        </div>
                                    </div>
                                    <div class="row px-2 px-md-3 mb-4">
                                        <div class="col-12" >
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-3">所属</label><span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span><!--v-if--><!-- subTextIdは人財の「可能な契約形態」にて使用 --><!--v-if--><!--v-if--><!-- モーダル表示ボタン --><button class="mdb-modal-form btn btn-outline-default d-md-block sp-w-100 font-default m-0 mb-3 waves-effect waves-light ex-bold" data-target="#partner_type_id-modal" data-toggle="modal" type="button">選択</button><!--v-if-->
                                            <p id="account-name" class="type-id-selected mt-2">{{ selectedPartnerTypeLabel }}</p>
                                            <div v-if="!check_partner_type" class="text-danger">
                                                所属を選択してください
                                            </div>
                                        </div>
                                        <div id="partner_type_id-modal" class="modal" tabindex="-1" aria-hidden="true" style="display: none;">
                                            <div class="modal-dialog modal-lg" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title w-100">所属を選択</h4>
                                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <!--v-if-->
                                                        <div class="row mb-3">
                                                            <!-- モーダル内にラジオボタンが並ぶ -->
                                                            <div class="row px-5 partner-type-gap">
                                                                <div class="form-check pl-0 mb-3 mb-sm-0" item-class="form-check pl-0 mb-3 mb-sm-0 col-md-4"><input id="partner_type_id_field0" v-model="partner_types" class="form-check-input" type="radio" name="resume[partner_type_id]" value="empl"><label id="partner_type_id_field_label_0" class="form-check-label" for="partner_type_id_field0">自社社員</label></div>
                                                                <div class="form-check pl-0 mb-3 mb-sm-0" item-class="form-check pl-0 mb-3 mb-sm-0 col-md-4"><input id="partner_type_id_field1" v-model="partner_types" class="form-check-input" type="radio" name="resume[partner_type_id]" value="subc"><label id="partner_type_id_field_label_1" class="form-check-label" for="partner_type_id_field1">協力会社社員（一社先）</label></div>
                                                            </div>
                                                            <div class="px-4 affiliation-img opportunity_question"></div>
                                                        </div>
                                                        <div class="text-center"><button aria-label="Close" class="btn btn-blue-grey waves-effect waves-light" data-dismiss="modal">閉じる</button></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 可能な契約形態 -->
                                    <div class="row px-2 px-md-3 mb-4">
                                        <div class="col-12">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">可能な契約形態</label>
                                            <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                            <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                            <div class="selecting-form row px-3">
                                                <!-- チェックボックスが選択されていない場合、nilを送信する。（nilを送信しないと、チェックされたまま返ってきてしまうため。 -->
                                                <input type="hidden" name="resume[contract_types][]">
                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                                                    <input v-model="ContractType" id="contract_types_field0" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="quas">
                                                    <label id="contract_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="contract_types_field0">業務委託（準委任）</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                                                    <input v-model="ContractType" id="contract_types_field1" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="subc">
                                                    <label id="contract_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="contract_types_field1">業務委託（請負）</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                                                    <input v-model="ContractType" id="contract_types_field2" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="temp">
                                                    <label id="contract_types_field_label_2" class="custom-control-label anavi-select-label mb-3" for="contract_types_field2">派遣契約</label>
                                                </div>
                                            </div>
                                            <div v-if="!check_contract_types" class="text-danger">
                                                契約形態を選択してください
                                            </div>
                                        </div>
                                    </div>
                                        <!-- 参画可能時期 -->
                                        <div id="avail_time_at" class="col-12 mb-3" v-show="ResumeStatus === 'will_be_available'">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">参画可能時期</label><span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span><!--v-if--><!-- subTextIdは人財の「可能な契約形態」にて使用 --><span id="" class="custom-grey-text d-block mb-3">参画可能時期が来たら自動的に「今後可」から「即日可」に変更されます。</span>
                                            <div class="row">
                                                <div class="col-11 col-sm-6">
                                                    <div class="mx-auto">
                                                        <input id="available_time_at_field" v-pickadate="{ model: 'DatePeriod' }" v-model="DatePeriod" class="form-control picker__input" autocomplete="off" type="text" name="resume[available_time_at]" readonly="" aria-haspopup="true" aria-expanded="false" aria-readonly="false" aria-owns="available_time_at_field_root">

                                                    </div>
                                                </div>
                                                <div v-if="!check_date_period" class="text-danger">
                                                    参画可能時期を選択してください
                                                </div>
                                                <div class="pl-0 col-1"><i class="material-icons md-grey md-18 inline-unit-label calender-icon">date_range</i></div>
                                            </div>
                                            <!--v-if-->
                                        </div>
                                    <div class="row px-2 px-md-3 mb-4">
                                        <div class="col-12">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">稼働率</label>
                                            <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                            <span id="" class="custom-grey-text d-block mb-3">対応可能な稼働率を選択してください。</span>
                                            <RateSlider
                                                :min="25"
                                                :max="100"
                                                :step="25"
                                                :startValue="UtilizationRate"
                                                @update:rate="val => UtilizationRate = val"
                                                ref="rateSlider"
                                            />
                                            <input type="hidden" :value="UtilizationRate" name="resume[utilization_rate]">
                                            <div v-if="hasAttemptedSubmit && !check_utilization_rate" class="text-danger">
                                                稼働率を選択してください
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 可能な出社頻度 -->
                                    <div class="row px-2 px-md-3 mb-4">
                                        <div class="col-12">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-3">可能な出社頻度</label><span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                            <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                            <FrequencySlider
                                                :min="0"
                                                :max="6"
                                                :step="1"
                                                :startValue="WorkFrenquecy"
                                                @update:frequency="val => WorkFrenquecy = val"
                                            />
                                            <input type="hidden" :value="WorkFrenquecy" name="resume[work_frequencies]">
                                            <div v-if="hasAttemptedSubmit && !check_work_frequencies" class="text-danger">
                                                出社頻度を選択してください
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 経験・資格 -->
                                    <div class="pl-0">
                                        <p class="font-extralarge mb-2 pl-sm-3">経験・資格</p>
                                        <div class="row px-2 px-md-3 mb-2">
                                            <div class="col-12">
                                                <div class="mx-auto mb-1">
                                                    <label id="" class="font-middle pl-1 pl-md-0 mb-2">経験PR</label>
                                                    <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                    <span id="" class="custom-grey-text d-block mb-3">これまでのご経験や強み、人物面などのPRを記載することで、案件元から興味を持ってもらいやすくなります。</span>
                                                    <textarea id="work_exp_pr_field" v-model="ExperiencePR" name="resume[work_exp_pr]" rows="10" class="form-control" autocomplete="off"></textarea>
                                                </div>
                                                <div v-if="!check_experience_pr" class="text-danger">
                                                    経験PRを入力してください
                                                </div>
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <CharacterCounter targetId="work_exp_pr_field" :max-length="2000" />
                                                    <a class="mdb-modal-form work_exp_pr-sample-insertion" data-target="#work_exp_pr-sample-modal" data-toggle="modal" href="">サンプルを使用 </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row px-2 px-md-3 mb-2">
                                            <div class="col-12">
                                                <div class="mx-auto mb-1">
                                                    <label id="" class="font-middle pl-1 pl-md-0 mb-3">資格</label>
                                                    <textarea id="qualification_remark_field_auto" name="resume[qualification_remark]" rows="5" class="form-control" autocomplete="off" v-model="qualificationRemark" readonly></textarea>
                                                    <div style="display: flex;" class="mt-3">
                                                        <div v-dropdown="{ modelValue: '', listType: 'qualifications'}" class="col-3 mr-5" @selected="handleSelection" style="height: fit-content">
                                                        </div>
                                                        <div class="row pl-3 col-4">
                                                            <div class="mx-auto mb-3" style="position: relative;">
                                                                <input class="form-control picker__input" v-model="selectedDate" autocomplete="off" v-pickadate="{ model: 'selectedDate' }" id="certificate_date_field" type="text" name="" readonly="" aria-haspopup="true" aria-expanded="false" aria-readonly="false" aria-owns="">
                                                                <i class="material-icons md-grey md-18 inline-unit-icon calender-icon"  style="position: absolute; top: 50%; transform: translateY(-50%); right: 1rem; height: fit-content; width: fit-content;">date_range</i>
                                                            </div>
                                                        </div>
                                                        <div class="qualification-buttons-container">
                                                            <button type="button" name="btn_qualifications" id="btn_qualifications" class="btn btn-default btn_qualifications" @click="fillTextarea">登録</button>
                                                            <button type="button" name="btn_clear_qualifications" id="btn_clear_qualifications" class="btn btn-red-gradient btn_qualifications" @click="clearQualification">削除</button>
                                                        </div>
                                                    </div>
                                                    <p>選択肢にない資格は下記の記入欄に記載ください。</p>
                                                    <textarea id="qualification_remark_field" v-model="QualiCustom" name="resume[qualification_remark]" rows="5" class="form-control" autocomplete="off"></textarea>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <CharacterCounter targetId="qualification_remark_field" :max-length="2000" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="pl-0">
                                        <p class="font-extralarge mb-4 pl-sm-3">希望条件</p>
                                        <div class="row px-2 px-md-3 mb-2">
                                            <div class="col-12">
                                                <div class="mx-auto mb-1">
                                                    <label id="" class="font-middle pl-1 pl-md-0 mb-2">希望詳細</label>
                                                    <span id="" class="custom-grey-text d-block mb-3">希望する業務内容、就業場所、単価などをできるだけ具体的に記載しましょう。<br>出社頻度 / 稼働率が相談可能な場合はその旨を記載してください。</span>
                                                    <textarea id="condition_text_field" v-model="WorkHopefully" name="resume[condition_text]" rows="10" class="form-control" autocomplete="off"></textarea>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <CharacterCounter targetId="condition_text_field" :max-length="2000" />
                                                    <a class="mdb-modal-form condition_text-sample-insertion" data-target="#condition_text-sample-modal" data-toggle="modal" href="">サンプルを使用 </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 人財の公開設定 -->
                                    <div class="">
                                        <div class="pl-0 mb-4 pt-2">
                                            <div class="row px-2 px-md-3 mb-4">
                                                <div class="col-12">
                                                    <label id="" class="font-middle pl-1 pl-md-0 mb-3">人財の公開設定</label>
                                                    <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                    <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                                    <div class="mb-0">
                                                        <div class="form-inline my-3">
                                                            <div class="form-check ">
                                                                <input class="form-check-input"
                                                                    id="public_status_id_field0"
                                                                    type="radio"
                                                                    value="public"
                                                                    v-model="ResumeVisibility"
                                                                    name="opportunity[public_status_id]">
                                                                <label id="public_status_id_field_label_0" class="form-check-label"
                                                                    for="public_status_id_field0">公開</label>
                                                            </div>
                                                            <div class="form-check ">
                                                                <input class="form-check-input"
                                                                    id="public_status_id_field1"
                                                                    type="radio"
                                                                    value="limited"
                                                                    v-model="ResumeVisibility"
                                                                    name="opportunity[public_status_id]">
                                                                <label id="public_status_id_field_label_1" class="form-check-label"
                                                                    for="public_status_id_field1">ブックマーク先のみに公開</label>
                                                            </div>
                                                            <div class="form-check ">
                                                                <input class="form-check-input"
                                                                    id="public_status_id_field2"
                                                                    type="radio"
                                                                    value="private"
                                                                    v-model="ResumeVisibility"
                                                                    name="opportunity[public_status_id]">
                                                                <label id="public_status_id_field_label_2" class="form-check-label"
                                                                    for="public_status_id_field2">非公開</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="px-2 px-md-3">
                                                <p id="public_status_explanation" v-show="ResumeVisibility === 'public' || ResumeVisibility === ''" class="custom-grey-text mt-n4">すべてのユーザーから検索され、スカウトを受け取ることができます。</p>
                                                <p id="public_status_explanation" v-show="ResumeVisibility === 'limited'" class="custom-grey-text mt-n4">あらかじめブックマークした案件／企業のユーザーにのみ表示され、スカウトを受け取ることができます。</p>
                                                <p id="public_status_explanation" v-show="ResumeVisibility === 'private'" class="custom-grey-text mt-n4">どのユーザーにも検索されず、スカウトは受け取れません。こちらからの応募のみ可能です。</p>
                                                <!-- 限定公開先選択ドロップダウン -->
                                                <div class="row pl-3 accessible-bookmark" v-show="ResumeVisibility === 'limited'">
                                                    <div class="col-12 col-md-6">
                                                        <div class="mx-auto">
                                                            <label class="font-middle mb-3">公開する会社<span
                                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                            <div v-dropdown="{ modelValue: default_user, listType: 'accessible_bookmark_user_groups_field'}" @selected="CompanyPublish = $event.detail"></div>
                                                            <input type="hidden" v-model="CompanyPublish" />
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-md-6">
                                                        <div class="mx-auto">
                                                            <label class="font-middle mb-3">公開する人財<span
                                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                            <div v-dropdown="{ modelValue: default_resume, listType: 'accessible_bookmark_resume_groups_field'}" @selected="HumanPublish = $event.detail"></div>
                                                            <input type="hidden" v-model="HumanPublish" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-if="!check_resume_vibility" class="text-danger">
                                                    人財の公開設定を選択してください。
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 参画可能時期 -->
                                        <div id="avail_time_at" class="col-12 mb-3" v-show="ResumeStatus === 'will_be_available'">
                                            <label id="" class="font-middle pl-1 pl-md-0 mb-2">参画可能時期</label>
                                            <span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                            <span id="" class="custom-grey-text d-block mb-3">参画可能時期が来たら自動的に「今後可」から「即日可」に変更されます。</span>
                                            <div class="row">
                                                <div class="col-11 col-sm-6">
                                                    <div class="mx-auto d-flex align-items-center">
                                                        <input id="available_time_at_field" v-pickadate="{ model: 'DatePeriod' }" v-model="DatePeriod" class="form-control picker__input" autocomplete="off" type="text" name="resume[available_time_at]" readonly="" aria-haspopup="true" aria-expanded="false" aria-readonly="false" aria-owns="available_time_at_field_root">
                                                        <i class="material-icons md-grey md-18 inline-unit-label calender-icon ml-2">date_range</i>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="!check_date_period" class="text-danger mt-2">
                                                参画可能時期を選択してください
                                            </div>
                                        </div>
                                    </div>
                                        <!-- 会社情報の公開設定 -->
                                        <div class="row px-2 px-md-3 mb-4">
                                            <div class="col-12">
                                                <label id="" class="font-middle pl-1 pl-md-0 mb-3">会社情報の公開設定</label><span class="badge-pill font-small ml-2 badge-danger pink lighten-2">必須</span>
                                                <!-- subTextIdは人財の「可能な契約形態」にて使用 -->
                                                <div class="mb-0">
                                                    <div class="form-inline mb-3">
                                                        <div class="form-check pl-0 mb-3 mb-sm-0">
                                                            <input id="publish_company_name_status_id_field0" v-model="CompanySettings" class="form-check-input" type="radio" name="resume[publish_company_name_status_id]" value="public"><label id="publish_company_name_status_id_field_label_0" class="form-check-label" for="publish_company_name_status_id_field0">公開</label></div>
                                                        <div class="form-check pl-0 mb-3 mb-sm-0">
                                                            <input id="publish_company_name_status_id_field1" v-model="CompanySettings" class="form-check-input" type="radio" name="resume[publish_company_name_status_id]" value="private"><label id="publish_company_name_status_id_field_label_1" class="form-check-label" for="publish_company_name_status_id_field1">非公開</label></div>
                                                    </div>
                                                </div>
                                                <div v-if="!check_company_setting" class="text-danger">
                                                    会社情報の公開設定を選択してください
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-md-center mt-5">
                                <div class="col-12 col-md-4 mt-2"><button name="save_button" @click="res_new" class="btn font-extralarge white-text btn-default w-100 btn-lg waves-effect waves-light">登録</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5"></div>
                </div>
                <div aria-hidden="true" aria-labelledby="work_exp_pr-sample-modal" class="modal" id="work_exp_pr-sample-modal" role="dialog" tabindex="-1">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title w-100">サンプルを使用</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                <span aria-hidden="true">
                                <i class="material-icons md-dark mb-36"> clear </i>
                                </span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-5">
                                    <div class="form-check">
                                        <input class="form-check-input" id="exp_pr_sample0"  type="radio" value="consultant" name="experience-pr-sample" v-model="selectedExperience">
                                        <label id="exp_pr_sample_label0" class="form-check-label" for="exp_pr_sample0" >コンサルタント</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" id="exp_pr_sample1"  type="radio" value="system_engineer" name="experience-pr-sample" v-model="selectedExperience">
                                        <label id="exp_pr_sample_label1" class="form-check-label" for="exp_pr_sample1" >システムエンジニア</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" id="exp_pr_sample2"  type="radio" value="infrastructure_engineer" name="experience-pr-sample" v-model="selectedExperience">
                                        <label id="exp_pr_sample_label2" class="form-check-label" for="exp_pr_sample2" >インフラエンジニア</label>
                                    </div>
                                </div>
                                <div class="mb-4">既に入力されている内容は上書きされてしまいますが、よろしいですか？
                                </div>
                            </div>
                            <div class="modal-footer">
                                <div class="col-6">
                                    <button class="btn btn-outline-blue-grey btn-block px-0 waves-effect waves-light" data-dismiss="modal" type="button">キャンセル</button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-default btn-block waves-effect px-0 waves-light insert-work_exp_pr-sample" :disabled="!selectedExperience" @click="updateExperience" type="button">上書き</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="remove_tag_id" id="remove_tag_id" autocomplete="off" value="contract_types_field2">
                <input type="hidden" name="remove_message_id" id="remove_message_id" autocomplete="off" value="dispatch_license_change_message">
                <div aria-hidden="true" aria-labelledby="dispatch_license_update_modal" class="modal" id="dispatch_license_update" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header pb-0">
                                <h4 class="modal-title w-100">会社データ変更</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                <span aria-hidden="true">
                                <i class="material-icons md-dark mb-36">clear</i>
                                </span>
                                </button>
                            </div>
                            <div class="modal-body px-5 confirm-disable-content-modal-body resumes-modal-body">
                                <p class="mb-4"></p>
                                <div class="mb-3">会社情報の労働者派遣事業許可を「有」に変更しますか？</div>
                                労働者派遣事業許可を取得している場合のみ、「有」に設定してください。 <br>「会社データ変更」ページの労働者派遣事業許可の項目でも設定を変更することが可能です。
                                <p></p>
                                <div class="row">
                                    <div class="col-6">
                                        <p class="btn btn-outline-blue-grey btn-block px-0 waves-effect waves-light" data-dismiss="modal">更新しない</p>
                                    </div>
                                    <div class="col-6">
                                        <button name="button" type="submit" class="btn btn-default btn-block px-0 waves-effect waves-light" id="dispatch_license_update_update_btn">更新する</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div aria-hidden="true" aria-labelledby="condition_text-sample-modal" class="modal" id="condition_text-sample-modal" role="dialog" tabindex="-1">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title w-100">サンプルを使用</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                <span aria-hidden="true">
                                <i class="material-icons md-dark mb-36"> clear </i>
                                </span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-5">既に入力されている内容は上書きされてしまいますが、よろしいですか？</div>
                            </div>
                            <div class="modal-footer">
                                <div class="col-6">
                                    <button class="btn btn-outline-blue-grey btn-block px-0 waves-effect waves-light" data-dismiss="modal" type="button">キャンセル</button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-default btn-block waves-effect px-0 waves-light insert-condition_text-sample" @click="sampleTextarea" type="button">上書き</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/resumes/manage/new_resume.css"/>
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css">
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/opportunities/manage/new.css">
    `,
    data() {
        return {
            selectedCountry: "japan",
            selectedStatus: "",
            selectedStatus1: "",
            isChecked: false,
            isOpen: false,
            isOpen1: false,
            isOpen2: false,
            isOpen3: false,
            qualificationRemark: "", // Nội dung textarea
            selectedQualification: "", // Dữ liệu dropdown
            selectedDate: "", // Dữ liệu input date
            categories: [
                { value: "consul_pmo", label: "PMO" },
                { value: "consul_pmpl", label: "PM・PL" },
                { label: "DX", value: "consul_DX" },
                { label: "クラウド", value: "consul_cloud" },
                { label: "モダナイゼション", value: "consul_modern" },
                { label: "セキュリティ", value: "consul_security" },
                { label: "ITインフラ", value: "consul_it" },
                { label: "AI", value: "consul_ai" },
            ],
            categoriesDev: [
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_emb", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
            ],
            categoriesInfra: [
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
            ],
			categoriesDesign: [
				{ value: "design_business", label: "業務システム" },
				{ value: "design_open", label: "オープン" },
				{ label: "クラウド", value: "design_cloud" },
				{ label: "メインフレーム", value: "design_mainfream" },
				{ label: "ヘルプデスク", value: "design_helpdesk" },
			],
            isCheckboxDisabled: true,
            unitPriceMin: "",
            unitPriceMax: "",

            UserSurName: "",
            UserFirstName: "",
            InitalSurName: "",
            InitialFirstName: "",

            //Birthday fields
            BirthYear: "1945",
            BirthMonth: "1",
            BirthDate: "1",

            QualiCustom: "",

            //file upload
            selectedFiles: [],
            existingFile: null,

            //Data fields
            errMsg: "",
            ResumeVisibility: "",
            ResumeStatus: "",
            UserName: "",
            InitialName: "",
            Gender: "",
            Birthday: "",
            Nationality: "",
            isResidential: null,
            ConsultCastegories: [],
            DevCastegories: [],
            InfraCastegories: [],
            DesignCastegories: [],
            ExperiencePR: "",
            Qualification: "",
            Characteristic: [],
            UtilizationRate: 100,
            UnitPriceMin: null,
            UnitPriceMax: null,
            Region: null,
            WorkFrenquecy: "5days",
            specifies_workplaces: [],
            WorkHopefully: null,
            CompanySettings: "",
            ContractType: [],
            CreateAt: "",
            UpdateAt: "",
            CreateBy: "",
            UpdateBy: "",
            selectedExperience: "",
            CompanyPublish: "すべてのブックマーク先に公開",
            HumanPublish: "すべてのブックマーク先に公開",
            userId: "",
            companyId: "",
            isSubmitted: false,
            partner_types: "",
            partner_type_options: [
                { value: 'empl', label: '自社社員' },
                { value: 'subc', label: '協力会社社員（一社先）' },
            ],
            DatePeriod: "",
            default_user: "",
            default_resume: "",
            cv_date: "",
            cv_name: "",
            res_id: "",
            check_selected_file: true,
            check_resume_vibility: true,
            check_resume_status: true,
            check_date_period: true,
            check_lastname: true,
            check_firstname: true,
            check_gender: true,
            check_partner_type: true,
            check_contract_types: true,
            check_categories: true,
            check_experience_pr: true,
            check_utilization_rate: true,
            check_unit_price_min: true,
            check_unit_price_max: true,
            check_work_frequencies: true,
            check_workplace: true,
            check_company_setting: true,
            marginBottom: "",
            err_price_min:"",
            err_price_max:"",
            hasAttemptedSubmit: false,
        }
    },

    props: ["id"],

    async mounted() {
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        this.res_id = this.$route.params.id;
        console.log(this.id);
        if (this.id != null || this.id != "") {
            await this.get_res();
        }
        this.getCV();
        this.userId = userInfo ? userInfo.user_id : null;
        this.companyId = userInfo ? userInfo.user_company_id : null;
        this.loadExternalScript("/custom_frontend/static/js/pages/signup-extra.js");
        this.restoreDropdown();

        // 初期値を設定
        this.$nextTick(() => {
            // 稼働率の初期値を設定 (single value)
            if (!this.UtilizationRate || this.UtilizationRate === 0) {
                this.UtilizationRate = 100; // Default to 100%
                this.check_utilization_rate = true;
            }

            // 出社頻度の初期値を設定 (single value)
            if (!this.WorkFrenquecy || this.WorkFrenquecy === "") {
                this.WorkFrenquecy = "5days"; // Default to 5days
                this.check_work_frequencies = true;
            }
        });
    },
    watch: {
        unitPriceMin: {
            handler: "toggleCheckbox",
            immediate: true
        },
        unitPriceMax: {
            handler: "toggleCheckbox",
            immediate: true
        },
        selectedFiles:{
            handler() {
                this.check_selected_file = true;
            }
        },
        ResumeVisibility: {
            handler() {
                this.check_resume_vibility = true;
            },
        },
        ResumeStatus: {
            handler() {
                this.check_resume_status = true;
            }
        },
        DatePeriod: {
            handler() {
                this.check_date_period = true;
            },
        },
        InitalSurName: {
            handler() {
                this.check_lastname = true;
            },
        },
        InitialFirstName: {
            handler() {
                this.check_firstname = true;
            },
        },
        Gender:{
            handler() {
                this.check_gender = true;
            }
        },
        partner_types: {
            handler() {
                this.check_partner_type = true;
            },
        },
        ContractType: {
            handler() {
                this.check_contract_types = true;
            },
        },
        ConsultCastegories: {
            handler() {
                this.check_categories = true;
            },
        },
        DevCastegories: {
            handler() {
                this.check_categories = true;
            },
        },
        InfraCastegories: {
            handler() {
                this.check_categories = true;
            },
        },
        DesignCastegories: {
            handler() {
                this.check_categories = true;
            },
        },
        ExperiencePR: {
            handler() {
                this.check_experience_pr = true;
            },
        },
        UtilizationRate: {
            handler() {
                this.check_utilization_rate = true;
            },
        },
        UnitPriceMin: {
            handler() {
                // スライダーの場合、nullは有効な値（下限なし）
                this.check_unit_price_min = false;
                this.err_price_min = "";
            },
        },
        UnitPriceMax: {
            handler() {
                // スライダーの場合、nullは有効な値（上限なし）
                this.check_unit_price_max = false;
                this.err_price_max = "";
            },
        },
        WorkFrenquecy: {
            handler() {
                this.check_work_frequencies = true;
            },
        },
        specifies_workplaces: {
            handler() {
                this.check_workplace = true;
            },
            deep: true
        },
        CompanySettings: {
            handler() {
                this.check_company_setting = true;
            },
        }
    },

    methods: {

        tabChange(tabNumber) {
            // Cập nhật trạng thái active tab
    this.activeTab = tabNumber;

    // Chỉ chuyển trang khi tab thay đổi
    if (tabNumber === 1) {
        // Nếu đang ở trang khác trang opportunities/manage/new
        if (location.pathname !== '/opportunities/manage/new') {
            // Dùng Vue Router để chuyển trang không load lại
            window.location.href ='/opportunities/manage/new';
        }
    } else if (tabNumber === 2) {
        // Nếu đang ở trang khác trang resumes/manage/new
        if (location.pathname !== '/resumes/manage/new') {
            // Dùng Vue Router để chuyển trang không load lại
            window.location.href ='/resumes/manage/new';
        }
    }
        },


        validateInput(){
            console.log("validate");
            this.check_selected_file = true;
            this.check_resume_vibility = true;
            this.check_resume_status = true;
            this.check_date_period = true;
            this.check_lastname = false;
            this.check_firstname = false;
            this.check_gender = true;

            this.check_partner_type = false;
            this.check_contract_types = false;
            this.check_categories = false;
            this.check_experience_pr = false;
            this.check_utilization_rate = false;
            this.check_unit_price_min = false;
            this.check_unit_price_max = false;
            this.check_work_frequencies = false;
            this.check_workplace = false;
            this.check_company_setting = true;
            if(this.selectedFiles.length == 0){
                this.check_selected_file = false;
            }
            if(this.ResumeVisibility == ""){
                this.check_resume_vibility = false;
            }
            if(this.ResumeStatus == ""){
                this.check_resume_status = false;
            }
            if(this.ResumeStatus === "will_be_available"){
                if(this.DatePeriod !== "" && this.DatePeriod !== null){
                    this.check_date_period = true;
                } else {
                    this.check_date_period = false;
                }
            }
            if(this.InitalSurName !== ""){
                this.check_lastname = true  ;
            }
            if(this.InitialFirstName !== ""){
                this.check_firstname = true;
            }
            if(this.partner_types.length > 0){
                this.check_partner_type = true;
            }
            if(this.ContractType.length > 0){
                this.check_contract_types = true;
            }
            if(this.ConsultCastegories.length > 0 || this.DevCastegories.length > 0 || this.InfraCastegories.length > 0 || this.DesignCastegories.length > 0){
                this.check_categories = true;
            }
            if(this.Gender == ""){
                this.check_gender = false;
            }
            if(this.ExperiencePR !== ""){
                this.check_experience_pr = true;
            }
            // Check utilization_rate (single value)
            if (this.UtilizationRate && this.UtilizationRate >= 25 && this.UtilizationRate <= 100) {
                this.check_utilization_rate = true;
            } else {
                this.check_utilization_rate = false;
            }
            // 単価下限のチェック
            if (this.UnitPriceMin === null || this.UnitPriceMin === "") {
                // スライダーの場合、nullは有効な値（下限なし）
                this.check_unit_price_min = false;
                this.err_price_min = "";
            } else if (isNaN(this.UnitPriceMin)) {
                this.check_unit_price_min = true;
                this.err_price_min = "単価下限は数値で入力してください。";
            } else {
                this.check_unit_price_min = false;
                this.err_price_min = "";
            }

            // 単価上限のチェック
            if (this.UnitPriceMax === null || this.UnitPriceMax === "") {
                // スライダーの場合、nullは有効な値（上限なし）
                this.check_unit_price_max = false;
                this.err_price_max = "";
            } else if (Number.isNaN(Number(this.UnitPriceMax))) {
                this.check_unit_price_max = true;
                this.err_price_max = "単価上限は数値で入力してください。";
            } else if (!this.check_unit_price_min && this.UnitPriceMin !== null && this.UnitPriceMax !== null && Number(this.UnitPriceMax) < Number(this.UnitPriceMin)) {
                this.check_unit_price_max = true;
                this.err_price_max = "単価上限は下限以上で入力してください。";
            } else {
                this.check_unit_price_max = false;
                this.err_price_max = "";
            }
            // Check work_frequencies (single value)
            if (this.WorkFrenquecy && this.WorkFrenquecy.length > 0) {
                this.check_work_frequencies = true;
            } else {
                this.check_work_frequencies = false;
            }
            if(this.specifies_workplaces.length > 0){
                this.check_workplace = true;
            }
            if(this.CompanySettings == ""){
                this.check_company_setting = false;
            }
            if(this.check_resume_vibility && this.check_resume_status && this.check_lastname && this.check_firstname && this.check_gender && this.check_partner_type && this.check_contract_types && this.check_categories && this.check_experience_pr && this.check_utilization_rate && !this.check_unit_price_min && !this.check_unit_price_max && this.check_work_frequencies && this.check_workplace && this.check_date_period && this.check_company_setting){
                return true;
            }
            return false;
        },
        //upload file
        triggerFileInput() {
            this.$refs.fileInput.click();
        },

        async handleFileChange(event) {
            const files = event.target.files;
            if (files.length > 0) {
                for (let file of files) {
                    // Check file size (10MB = 10 * 1024 * 1024 bytes)
                    if (file.size > 10485760) {
                        window.toastr.error(`ファイル ${file.name} のサイズが10MBを超えています。`);
                        event.target.value = ''; // Reset input file
                        return;
                    }
                    // Check file type
                    const allowedTypes = ['.pdf', '.doc', '.docx', '.txt'];
                    const fileName = file.name.toLowerCase();
                    const isValidType = allowedTypes.some(type => fileName.endsWith(type));
                    if (!isValidType) {
                        window.toastr.error(`ファイル ${file.name} の形式が対応していません。PDF、DOC、DOCX、TXTファイルのみアップロード可能です。`);
                        event.target.value = ''; // Reset input file
                        return;
                    }
                }
                if (this.existingFile) {
                    window.toastr.error("アップロードできるファイルは 1 つだけです。新しいファイルをアップロードする前に、現在のファイルを削除してください。");
                    event.target.value = ''; // Reset input file
                    return;
                }
                this.selectedFiles = Array.from(files);
                console.log('file', this.selectedFiles[0]);
                this.existingFile = true;
                window.toastr.success("ファイルがアップロードされました。");
                event.target.value = ''; // Reset input file
            }
        },

        removeFile(event) {
            event.stopPropagation();
            this.selectedFiles = [];
            this.existingFile = null;
            window.toastr.success("ファイルが削除されました。");
        },

        getFileIcon(filename) {
            if (filename == null) {
                return;
            }
            const ext = filename.split('.').pop().toLowerCase();
            if (ext === 'pdf') return 'https://img.icons8.com/color/48/000000/pdf.png';
            if (['doc', 'docx'].includes(ext)) return 'https://img.icons8.com/color/48/000000/microsoft-word-2019.png';
            return 'https://img.icons8.com/color/48/000000/file.png';
        },

        //front end script
        handleCountryChange(event) {
            this.selectedCountry = event.target.value;

            //"日本 (Japan)" -> Uncheck radio reside
            if (this.selectedCountry === "japan") {
                this.Nationality = ""; // Xóa nội dung ô nhập quốc gia

                // Bỏ chọn tất cả radio button trong nhóm "reside"
                document.querySelectorAll('input[name="resume[reside]"]').forEach(radio => {
                    radio.checked = false;
                });
            }
        },

        savePageInfo(pageType) {
            // Lưu thông tin trang hiện tại vào localStorage
            localStorage.setItem('lastPage', pageType);
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },

        toggleAccordion() {
            this.isOpen = !this.isOpen; // Đảo trạng thái mở/đóng
        },
        toggleAccordion1() {
            this.isOpen1 = !this.isOpen1; // Đảo trạng thái mở/đóng
        },
        toggleAccordion2() {
            this.isOpen2 = !this.isOpen2; // Đảo trạng thái mở/đóng
        },
        toggleAccordion3() {
            this.isOpen3 = !this.isOpen3; // Đảo trạng thái mở/đóng
        },
        fillTextarea() {
            // Validate: kiểm tra đã chọn bằng cấp và ngày chưa
            if (!this.selectedQualification || this.selectedQualification === '未選択') {
                window.toastr.error('資格を選択してください。');
                return;
            }

            if (!this.selectedDate) {
                window.toastr.error('取得日を選択してください。');
                return;
            }

            // Cập nhật giá trị textarea từ dropdown và date picker
            const newQualification = `${this.selectedQualification} ${this.selectedDate}取得`;

            // Nếu đã có bằng cấp, thêm bằng cấp mới vào danh sách
            if (this.qualificationRemark && this.qualificationRemark.trim() !== '') {
                this.qualificationRemark = this.qualificationRemark + '\n' + newQualification;
            } else {
                this.qualificationRemark = newQualification;
            }

            // Hiển thị thông báo thành công
            window.toastr.success('資格情報が追加されました。');
        },

        clearQualification() {
            // Xóa tất cả bằng cấp
            this.qualificationRemark = '';
            this.selectedQualification = '';
            this.selectedDate = '';

            // Reset dropdown về "未選択"
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "qualifications", value: "未選択" } }));

            // Hiển thị thông báo
            window.toastr.success('資格情報が削除されました。');
        },
        handleSelection(event) {
            this.selectedQualification = event.detail; // Nhận giá trị từ dropdown
        },
        restoreDropdown(){
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "optionYearField", value: Number(this.BirthYear) } }));
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "optionMonthField", value: Number(this.BirthMonth) } }));
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "optionDayField", value: Number(this.BirthDate) } }));
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "qualifications", value: this.selectedQualification } }));
        },
        toggleSelection(prefecture) {
            const index = this.selectedPrefectures.indexOf(prefecture);
            if (index === -1) {
                this.selectedPrefectures.push(prefecture);
            } else {
                this.selectedPrefectures.splice(index, 1);
            }

            this.$nextTick(() => {
                const buttons = document.querySelectorAll(".pref");
                buttons.forEach((btn) => {
                    btn.style.pointerEvents = "none";
                });

                setTimeout(() => {
                    buttons.forEach((btn) => {
                        btn.style.pointerEvents = "auto";
                    });
                }, 50);
            });
        },
        toggleCheckbox() {
            this.isCheckboxDisabled = !(this.unitPriceMin.trim() !== "" && this.unitPriceMax.trim() !== "");
        },




        //call api
        async res_new() {
            this.hasAttemptedSubmit = true;
            if(!this.validateInput()){
                window.toastr.error("すべての項目を正しく入力してください。");
                return;
            }

            try {
                this.UserName = this.UserSurName + " " + this.UserFirstName;
                this.InitialName = this.InitalSurName + " " + this.InitialFirstName;
                this.Birthday = `${this.BirthYear}-${String(this.BirthMonth).padStart(2, '0')}-${String(this.BirthDate).padStart(2, '0')}`;
                let formattedText = this.specifies_workplaces.join(',');

                const sendData = {
                    'resume_visibility': this.ResumeVisibility,
                    'status': this.ResumeStatus,
                    'company_publish': this.CompanyPublish,
                    'human_publish': this.HumanPublish,
                    'date_period': $("#available_time_at_field").val() ? this.convertJapaneseDate($("#available_time_at_field").val()) : null,
                    'user_name': this.UserName,
                    'initial_name': this.InitialName,
                    'gender': this.Gender,
                    'birthday': this.Birthday,
                    'nationality': this.selectedCountry === 'japan' ? 'Japan' : this.Nationality,
                    'resident': this.isResidential,
                    'contract_types': this.ContractType.join(','),
                    'categories_consultation': this.ConsultCastegories.join(','),
                    'categories_development': this.DevCastegories.join(','),
                    'categories_infrastructure': this.InfraCastegories.join(','),
                    'categories_design': Array.isArray(this.DesignCastegories) ? this.DesignCastegories.join(',') : '',
                    'experience_pr': this.ExperiencePR,
                    'qualification': this.qualificationRemark && this.QualiCustom ?
                        this.qualificationRemark + "," + this.QualiCustom :
                        this.qualificationRemark || this.QualiCustom,
                    'characteristic': Array.isArray(this.Characteristic) ? this.Characteristic.join(',') : '',
                    'utilization_rate': String(this.UtilizationRate),
                    'unit_price_min': this.UnitPriceMin,
                    'unit_price_max': this.UnitPriceMax,
                    'region': this.Region,
                    'working_frequency': String(this.WorkFrenquecy),
                    'working_location': formattedText,
                    'working_hope': this.WorkHopefully,
                    'company_settings': this.CompanySettings,
                    'created_at': new Date().toISOString().slice(0, 19).replace('T', ' '),
                    'updated_at': new Date().toISOString().slice(0, 19).replace('T', ' '),
                    'created_by': this.userId,
                    'company_id': this.companyId,
                    'partner_types': this.partner_types
                }

                const formData = new FormData();
                formData.append("file", this.selectedFiles[0]);
                formData.append("content", JSON.stringify(sendData));

                const response = await fetch('/api/resume_new', {
                    method: 'POST',
                    headers: {
                        "X-Requested-With": "XMLHttpRequest"
                    },
                    body: formData,
                });
                const data = await response.json();

                if (data.success) {
                    this.errMsg = data.message;
                    window.toastr.success(data.message);
                    window.location.href = "/resumes/manage/index";
                } else {
                    console.log("Resumes Create failed " + data.message);
                    window.toastr.error(data.message);
                    this.errMsg = data.message;
                }
            }
            catch (err) {
                console.log("Error API response: " + err.message);
                window.toastr.error('エラーが発生しました。');
            }

        },

        convertJapaneseDate(jpDate) {
            // Tách các thành phần từ chuỗi ngày tiếng Nhật
            let parts = jpDate.match(/(\d{4})年(\d{2})月(\d{2})日/);
            if (!parts) return null; // Nếu không khớp định dạng, trả về null

            let year = parts[1];
            let month = parts[2];
            let day = parts[3];

            // Trả về định dạng YYYY-MM-DD
            return `${year}-${month}-${day}`;
        },

        async get_res() {
            try {
                const res = await fetch(`/api/res_edit?id=${this.id}`, {
                    method: "GET", // Hoặc "GET" nhưng phải đổi Controller
                    headers: { "Content-Type": "application/json" },
                });

                if (!res.ok) {
                    console.error("Error fetching opportunity:", res.statusText);
                    return;
                }

                const result = await res.json();
                if (result.success) {
                    console.log("get data successfully");
                    console.log(result.data);
                    this.ResumeVisibility = result.data.resume_visibility;
                    this.ResumeStatus = result.data.status;
                    this.CompanyPublish = result.data.company_publish;
                    this.HumanPublish = result.data.human_publish;
                    this.UserName = result.data.user_name;
                    this.DatePeriod = result.data.date_period;
                    this.InitialName = result.data.initial_name;
                    this.Gender = result.data.gender;
                    this.Birthday = result.data.birthday;
                    this.Nationality = result.data.nationality;
                    this.isResidential = result.data.resident;
                    this.ConsultCastegories = result.data.categories_consultation || "";
                    this.DevCastegories = result.data.categories_development || "";
                    this.InfraCastegories = result.data.categories_infrastructure || "";
                    this.DesignCastegories = result.data.categories_design || "";
                    this.ExperiencePR = result.data.experience_pr || "";
                    // Không cần thiết lập selectedQualification và selectedDate từ dữ liệu DB
                    // vì chúng sẽ được thiết lập khi người dùng chọn từ dropdown
                    this.Qualification = result.data.qualification || "";
                    this.Characteristic = result.data.characteristic || "";
                    this.UtilizationRate = result.data.utilization_rate || "";
                    this.UnitPriceMin = result.data.unit_price_min;
                    this.UnitPriceMax = result.data.unit_price_max;
                    this.Region = result.data.region || "";
                    this.WorkFrenquecy = result.data.working_frequency;
                    this.specifies_workplaces = result.data.working_location;
                    this.WorkHopefully = result.data.working_hope || "";
                    this.CompanySettings = result.data.company_settings;
                    this.ContractType = result.data.contract_types ? result.data.contract_types.split(',') : [];
                    this.partner_types = result.data.partner_types;
                    this.dataSep();
                    try {
                        const res = await fetch(`/api/check_cv_exist?id=${this.id}`, {
                            method: "GET",
                            headers: { "Content-Type": "application/json" },
                        });

                        if (!res.ok) {
                            console.error("Error fetching opportunity:", res.statusText);
                            return;
                        }

                        const result = await res.json();
                        console.log("CV: ", result);
                        if (result.success) {
                            this.cv_name = result.data.name;
                            this.cv_date = this.convertDateToJapanese(result.data.write_date);
                        }
                    } catch (error) {
                        console.log(error);
                    }
                    this.$nextTick(() => {
                        pickadate.updatePickadate('available_time_at_field', this.DatePeriod);
                        pickadate.updatePickadate('certificate_date_field', this.selectedDate);
                    });
                }
            } catch (error) {
                console.log(error);
            }
        },

        dataSep() {
            this.BirthYear = this.Birthday.split('-')[0];
            this.BirthMonth = this.Birthday.split('-')[1];
            this.BirthDate = this.Birthday.split('-')[2].split(' ')[0];

            [this.UserSurName, ...this.UserFirstName] = this.UserName.split(' ');
            this.UserFirstName = this.UserFirstName.join(' ');

            this.InitalSurName = this.InitialName.split(' ')[0];
            this.InitialFirstName = this.InitialName.split(' ')[1];

            this.DatePeriod = this.convertDateToJapanese(this.DatePeriod);

            if (this.Nationality != "Japan") {
                this.selectedCountry = "foreign";
            } else {
                this.selectedCountry = "japan";
            }

            // Xử lý trường hợp có nhiều bằng cấp và bằng cấp tùy chỉnh
            const parts = this.Qualification.split(',');
            if (parts.length > 1) {
                this.qualificationRemark = parts[0];
                this.QualiCustom = parts.slice(1).join(',');
            } else {
                this.qualificationRemark = this.Qualification;
                this.QualiCustom = '';
            }

            this.ConsultCastegories = this.ConsultCastegories ? this.ConsultCastegories.split(',') : [];
            this.DevCastegories = this.DevCastegories ? this.DevCastegories.split(',') : [];
            this.InfraCastegories = this.InfraCastegories ? this.InfraCastegories.split(',') : [];
            this.DesignCastegories = this.DesignCastegories ? this.DesignCastegories.split(',') : [];
            this.Characteristic = this.Characteristic ? this.Characteristic.split(',') : [];
            // Handle utilization_rate - convert from array to single value
            const utilizationRateArray = this.UtilizationRate ? this.UtilizationRate.split(',').filter(item => item.trim() !== "") : [];
            this.UtilizationRate = utilizationRateArray.length > 0 ? parseInt(utilizationRateArray[0]) : 100;

            // Handle work_frequencies - convert from array to single value
            const workFrequenciesArray = this.WorkFrenquecy ? this.WorkFrenquecy.split(',').filter(item => item.trim() !== "") : [];
            this.WorkFrenquecy = workFrequenciesArray.length > 0 ? workFrequenciesArray[0] : "5days";
            this.specifies_workplaces = this.specifies_workplaces ? this.specifies_workplaces.split(',') : [];
        },

        convertDateToJapanese(date) {
            if (!date) return null;
            const year = date.split('-')[0];
            const month = date.split('-')[1];  // Đảm bảo tháng luôn 2 chữ số
            const day = date.split('-')[2].split(' ')[0];  // Đảm bảo ngày luôn 2 chữ số
            return `${year}年${month}月${day}日`;
        },

        convertJapaneseDate(jpDate) {
            // Tách các thành phần từ chuỗi ngày tiếng Nhật
            let parts = jpDate.match(/(\d{4})年(\d{2})月(\d{2})日/);
            if (!parts) return null; // Nếu không khớp định dạng, trả về null

            let year = parts[1];
            let month = parts[2];
            let day = parts[3];

            // Trả về định dạng YYYY-MM-DD
            return `${year}-${month}-${day}`;
        },

        updateExperience() {
            if (this.selectedExperience === "consultant") {
                this.ExperiencePR = "【経歴サマリ】\n・外資系コンサルティング会社で約5年のコンサル経験をベースに多くの業界、業種、大小様々なプロジェクトを経験 \n・特にグローバルプロジェクトへの参画経験が豊富なため、複雑なコミュニケーションやプロジェクトマネジメントが可能 \n・直近では、某マザーズ上場ベンチャー企業での新規事業立ち上げ支援、コンサルティングに従事 \n\n【得意とする経験・分野・スキル】 \n◎グローバル展開支援 \n・S/4HANAロールアウト支援（日本・アメリカ・イギリス） \n・保険会社向けCRM導入支援（日本・アメリカ） \n◎プロジェクトマネジメント \n・システム開発計画の策定 \n・プロジェクトチームの編成 \n・ベンダーコントロール \n・プロジェクト推進管理 \n・QCDレビュー\n◎新規事業立ち上げ支援 \n・新規事業の戦略立案 \n・参入マーケットの調査、事業計画書の作成支援";
            } else if (this.selectedExperience === "system_engineer") {
                this.ExperiencePR = "に取ることが出来ます。【経験スキル】 \n開発言語：PHP、Java、C#、Python、VB.NET、Ruby、VBA \nフレームワーク：Laravel、.NET Framework \nOS：Linux、Windows \nDB：Oracle、PostgreSQL、MySQL\n\n【経験フェーズ】 \n基本設計、詳細設計、製造、単体テスト、結合テスト\n\n【PR】 \n・PHPでのWebサービス開発に3年ほど携わっており基本設計から結合テストまでを経験しています。 \n・少規模プロジェクトではチームリーダーの経験もあります。 \n・クライアント側の要望を聞き発言の意図を理解した上で、自分の意見を伝えることが出来るなど、コミュニケーションも良好";
            } else if (this.selectedExperience === "infrastructure_engineer") {
                this.ExperiencePR = "【経験スキル】 \n・Linux、CentOS \n・Windows Server 2008/2012/2016 \n・AWS\n\n【経験フェーズ】 \n詳細設計、構築、運用設計、運用保守\n\n【PR】 \n・Linux,Windows Serverの設計構築の経験が長く自信があります。 \n・少規模プロジェクトではチームリーダーの経験もあります。 \n・クライアント側の要望を聞き発言の意図を理解した上で、自分の意見を伝えることが出来るなど、コミュニケーションも良好に取ることが出来ます。";
            }

            $("#work_exp_pr-sample-modal").modal("hide");
        },

        sampleTextarea() {
            this.WorkHopefully = "【業務内容】\n・PHPでのシステム開発に、基本設計から関われるような案件を希望します。\n・フロントエンドにも関われるような案件だと尚可です。\n\n【単価】\n基本的に70万円～ですが、フロントエンドにも関われるようであれば70万円以下でも検討させていただきます。\n※70万円は稼働率100％時の単価です。\n\n【その他】\n出社可能日数：週4日\n稼働率：80～100％\n人財ID:000011112222の人財と、チーム提案を希望しています。";
            $("#condition_text-sample-modal").modal("hide");
        },
        async fetchCV() {
            try {
                const response = await fetch(`/api/resume/get_cv?resume_id=${this.id}`);
                const data = await response.json();

                if (data.success && data.file_url) {
                    console.log(data.file_name);
                    this.downloadFile(data.file_url, data.file_name);
                } else {
                    alert(data.message || "Không tìm thấy CV.");
                }
            } catch (error) {
                console.error("Lỗi khi lấy CV:", error);
                alert("Đã xảy ra lỗi khi tải CV.");
            }
        },
        async getCV(){
            try {
                if(this.id){
                    const response = await fetch(`/api/resume/get_cv?resume_id=${this.id}`);
                    const data = await response.json();

                    if (data.success && data.file_url) {
                        console.log(data.file_name);
                        const blob = new Blob([new Uint8Array(data.file_content)]);  // Tạo Blob từ dữ liệu file
                        const file = new File([blob], data.file_name, { type: 'application/octet-stream' }); // Tạo file từ Blob
                        this.selectedFiles = [file];  // Gán file vào selectedFiles
                        console.log('File đã được tải và gán:', this.selectedFiles);
                    }
                }
                else{
                    return;
                }
            } catch (error) {

            }
        },
    },
    computed: {
        selectedPartnerTypeLabel() {
            const selected = this.partner_type_options.find(option => option.value === this.partner_types);
            return selected ? selected.label : '選択されていません';
        }
    }
}

export default NewResume
