import web from './web.js'

const routes = [...web]
const router = VueRouter.createRouter({
    history: VueRouter.createWebHistory(),
    routes
})

// Danh sách các route không cần kiểm tra session
const publicPaths = ['/login', '/signup', '/our_services/userguide', '/our_services/agreement', '/our_services/law', '/account/wait_active', '/account/verify_active','/users/password/new','/users/password/edit', '/home', '/gratitude', '/payment/result', '/plan', '/guideline', '/contact_new' ];

const pathLoad = ['/our_services/userguide', '/our_services/agreement', '/our_services/law', '/home', '/gratitude', '/plan', '/guideline', '/contact_new' ];

// Danh sách các route cần redirect khi đã đăng nhập
const authRedirectPaths = ['/login', '/signup', '/users/password/new', '/users/password/edit'];

const path = ['/login', '/signup'];

// Hàm kiểm tra session
async function checkSession() {
    const token = localStorage.getItem("authToken");
    //if (!token) return false;

    try {
        const response = await fetch("/api/check_session", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ token }),
        });
        const data = await response.json();
        return data.loginEmail ? true : false;
    } catch (error) {
        return false;
    }
}

export let userInfo = null;
export async function getUserInfo(isPublicRoute = false) {
    const token = localStorage.getItem("token_login");
    if (!token) {
        // Only redirect to login if this is not a public route
        if (!isPublicRoute) {
            localStorage.clear();
            window.location.href = "/login";
        }
        return null;
    }

    if(path.includes(window.location.pathname)){
        return null;
    }

    try {
        const response = await fetch("/api/get_user_info", {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "Authorization": token,
            },
        });
        const data = await response.json();

        if(response.status === 200){
            userInfo = {
                user_id: data.user_id,
                user_email: data.user_email,
                user_name: data.user_name,
                user_active: data.active,
                user_company_id: data.company_id,
            }
        }
    } catch (error) {
        console.error('Error fetching user info:', error);
        // Only redirect to login if this is not a public route
        if (!isPublicRoute) {
            localStorage.clear();
            window.location.href = "/login";
        }
    }
    return null;
}

// Middleware kiểm tra session trước khi chuyển route
router.beforeEach(async (to, from, next) => {
    // Nếu là route public thì bỏ qua getUserInfo
    if (publicPaths.includes(to.path) || to.path.startsWith('/odoo')) {
        if(pathLoad.includes(to.path)){
            await getUserInfo(true); // Pass true to indicate this is a public route
        }
        return next();
    }

    // GỌI getUserInfo() ở đây để luôn cập nhật userInfo mỗi lần chuyển trang
    await getUserInfo();

    const isloggedin = localStorage.getItem("isloggedin") != null;

    // Kiểm tra nếu đã đăng nhập và cố truy cập vào login/signup
    if (isloggedin && authRedirectPaths.includes(to.path)) {
        return next('/home');  // Chuyển hướng về mypage
    }

    const isAuthenticated = await checkSession();
    if (!isAuthenticated) {
        localStorage.removeItem("authToken");
        return next('/login');
    }

    next();

    let lastPath = ''; // Lưu lại đường dẫn trước đó

    router.afterEach((to, from) => {
        // Kiểm tra khi người dùng nhấn back hoặc forward
        if (lastPath === to.path) {
            // Khi đường dẫn không thay đổi (Back hoặc Forward), tải lại trang
            console.log('Detected Back/Forward action, reloading page...');
            window.location.href = to.fullPath; // Tải lại trang hoàn toàn
        } else {
            // Lưu lại đường dẫn của trang hiện tại khi có thay đổi
            lastPath = to.path;
        }
    });
});

export default router