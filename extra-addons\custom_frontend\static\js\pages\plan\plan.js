
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const plan = {
	'template': `
        <main class="pb-3 margin-header" id="vue-app" data-v-app="">
            ${createBreadcrumb([
                { text: '利用方法', link: null },
                { text: '料金プラン', link: null, current: true }
            ])}
            <div class="mt-5 mx-5">
                <h3 class="mb-3">料金プラン説明</h3>
                <div class="mb-5">
                    <p class="mb-0">業界最安値水準（スタンダードプランの場合）</p>
                    <p class="mb-0">会社単位の契約になります。</p>
                </div>
                <table class="custom-plan-table mb-4">
                    <thead class="thead-dark">
                        <tr>
                            <th colspan="2">プラン一覧</th>
                            <th>無料会員</th>
                            <th>スタンダード会員</th>
                            <th>プレミアム会員</th>
                        </tr>
                    </thead>
                    <tbody v-if="plans.length >= 2">
                        <tr>
                            <td rowspan="2">費用</td>
                            <td>月払</td>
                            <td v-html="formatMultiline(plans[0].free_plan)"></td>
                            <td v-html="formatMultiline(plans[0].standard_plan)"></td>
                            <td v-html="formatMultiline(plans[0].premium_plan)"></td>
                        </tr>
                        <tr>
                            <td>年払</td>
                            <td v-html="formatMultiline(plans[1].free_plan)"></td>
                            <td v-html="formatMultiline(plans[1].standard_plan)"></td>
                            <td v-html="formatMultiline(plans[1].premium_plan)"></td>
                        </tr>

                        <template v-for="(plan, index) in plans.slice(2)" :key="index">
                            <tr>
                                <td colspan="2">{{ plan.title }}</td>
                                <td v-html="formatMultiline(plan.free_plan)"></td>
                                <td v-html="formatMultiline(plan.standard_plan)"></td>
                                <td v-html="formatMultiline(plan.premium_plan)"></td>
                            </tr>
                        </template>
                    </tbody>
                </table>
                <div class="plan-notes mb-4">
                    <p class="mb-2">*1 ベストエフォートでの紹介となります。</p>
                    <p class="mb-0">*2 交流会(2回/年　実施予定)での会社紹介枠(3社限定/回)の提供となります。尚、希望会社が多数の場合、抽選となります。また交流会の参加費用は、別途必要となります。</p>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/plan/plan.css" />
	`,
    data(){
        return {
            plans: []
        }
    },

    mounted() {
        this.getPlan();
        const style = document.createElement('style');
        style.textContent = `
            .custom-plan-table {
                border-collapse: collapse;
                width: 100%;
                text-align: center;
            }

            .custom-plan-table th,
            .custom-plan-table td {
                border: 2px solid #ffffff;
                padding: 12px;
            }

            .custom-plan-table thead th {
                background-color: #4472C4;
                color: white;
            }

            /* Đặt độ rộng cố định cho bảng */
            .custom-plan-table {
                table-layout: fixed;
            }

            /* Đặt độ rộng cho 2 cột đầu */
            .custom-plan-table thead th:nth-child(1) {
                width: 12%;
            }
            .custom-plan-table thead th:nth-child(2) {
                width: 20%;
            }

            /* Đặt độ rộng bằng nhau cho 3 cột plan */
            .custom-plan-table thead th:nth-child(3),
            .custom-plan-table thead th:nth-child(4),
            .custom-plan-table thead th:nth-child(5) {
                width: 20%;
            }

            /* Áp dụng cho cả tbody */
            .custom-plan-table tbody td:nth-child(1) {
                width: 12%;
            }
            .custom-plan-table tbody td:nth-child(2) {
                width: 15%;
            }
            .custom-plan-table tbody td:nth-child(3),
            .custom-plan-table tbody td:nth-child(4),
            .custom-plan-table tbody td:nth-child(5) {
                width: 20%;
            }

            /* Record 1: sẫm, Record 2: nhạt */
            .custom-plan-table tbody tr:nth-child(odd) {
                background-color: #CFD5EA; /* màu xanh dương nhạt */
            }

            .custom-plan-table tbody tr:nth-child(even) {
                background-color: #E9EBF5;
            }

            /* Các ô rowspan thuộc dòng lẻ (màu sẫm) giữ màu */
            .custom-plan-table tbody tr:nth-child(odd) td[rowspan] {
                background-color: #CFD5EA;
            }

            .custom-plan-table td[rowspan] {
                vertical-align: middle;
            }

            .custom-plan-table td[colspan] {
                text-align: left;
            }
        `;
        document.head.appendChild(style);
    },

    methods: {
        async getPlan() {
            try {
                const response = await fetch('/api/get_plan', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                });
                const data = await response.json();
                if (data.success) {
                    console.log("Plans fetched successfully: ", data.plans);
                    console.log(data.message)
                    this.plans = data.plans;
                } else {
                    console.error("Failed to fetch plans", data.message);
                }
            } catch (error) {
                console.error('Failed to fetch api: ', error.message)
            }
        },

        formatMultiline(text) {
            return text.toString().replace(/\n/g, '<br>');
        }
    }
}

export default plan