import { userInfo, getUserInfo } from "../router/router.js";

const Home = {
  'template': `
    <main class="margin-header sp_fluid" id="vue-app">
      <div class="banner-wrapper">
        <div class="text-top">
          <div class="waves-effect waves-light it-text">
            <img src="custom_frontend/static/img/it-text.png">
          </div>
          <div id="div_img_1" class="waves-effect waves-light img-1">
            <img id="home1" src="custom_frontend/static/img/img_home_1.png">
          </div>
        </div>
        <div class="text-bottom align-items-end">
          <div class="waves-effect waves-light img-2">
            <img src="custom_frontend/static/img/img_home_2.png">
            <img id="home2" src="custom_frontend/static/img/img_home_2.1.png">
          </div>
          <div id="home3" class="waves-effect waves-light img-3">
            <img src="custom_frontend/static/img/img_home_3.png">
          </div>
        </div>
      </div>
    </main>
    <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/home.css">
    `,
  data() {
    return {
      showNavMenu: false,
      isloggedin: "",
    }
  },
  async mounted() {
    const el = document.getElementById('app');
    this.isloggedin = userInfo ? userInfo.user_id : null;
    if (el && this.isloggedin) {
      el.style.setProperty('margin-top', '80px');
      console.log("isloggedin: ", this.isloggedin);
    }
  },
  methods: {
    toggleNavMenu() {
      this.showNavMenu = !this.showNavMenu;
    }
  },

}

// Add styles
const style = document.createElement('style');
style.textContent = `
    .nav-menu {
        position: absolute;
        bottom: 70px;
        right: 0;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 8px 0;
        min-width: 200px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .nav-item {
        display: block;
        padding: 12px 20px;
        color: #333;
        text-decoration: none;
        transition: all 0.3s;
        font-size: 14px;
        white-space: nowrap;
    }

    .nav-item:hover {
        color: #1072e9;
    }

    #home1 {
        scale: 1.02;
    }

    #home3 {
        scale: 0.8;
        margin-bottom: -1.2%;
    }
    
    #home2 {
        display: block;
    }

    #div_img_1{
        margin-right: 27%;
        margin-left: 1.5%;
    }
`;
document.head.appendChild(style);

export default Home;