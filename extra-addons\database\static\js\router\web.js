const web = [
    {
        path: "/",
        component: () => import("../layouts/layout.js").then(m => m.default),
        children: [
            {
                path: "home",
                name: "home",
                meta:{
                    jp: "HomePage"
                },
                component: () => import("../pages/home.js").then(m => m.default)
            },
            {
                path: "login",
                name: "login",
                meta:{
                    jp: "ログイン"
                },
                component: () => import("../pages/login.js").then(m => m.default)
            },
            {
                path: "signup",
                name: "signup",
                meta:{
                    jp: "新規作成"
                },
                component: () => import("../pages/signup.js").then(m => m.default)
            },
            {
                path: "preview_project",
                name: "preview_project",
                meta:{
                    jp:"案件情報閲覧"
                },
                component: () => import("../pages/preview_project.js").then(m => m.default)
            },
            {
                path: "edit_companyprofile",
                name: "edit_companyprofile",
                meta:{
                    jp:"基本情報編集"
                },
                component: () => import("../pages/edit_companyprofile.js").then(m => m.default)
            },
            {
                path: "edit_project",
                name: "edit_project",
                meta:{
                    jp:"案件情報修正"
                },
                component: () => import("../pages/edit_project.js").then(m => m.default)
            },
            {
                path: "select_plan",
                name: "select_plan",
                meta:{
                    jp:"プラン一覧"
                },
                component: () => import("../pages/select_plan.js").then(m => m.default)
            },
            {
                path: "opportunities/manage/index",
                name: "opportunities_manage_index",
                meta:{
                    jp:"案件情報一覧"
                },
                component: () => import("../pages/opportunities/manage/Index.js").then(m => m.default)
            },
            {
                path: "opportunities/manage/delete",
                name: "opportunities_manage_delete",
                meta:{
                    jp:"案件情報削除"
                },
                component: () => import("../pages/opportunities/manage/Delete.js").then(m => m.default)
            },
            {
                path: "opportunities/manage/new",
                name: "opportunities_manage_new",
                meta:{
                    jp:"案件情報作成"
                },
                component: () => import("../pages/opportunities/manage/New.js").then(m => m.default)
            },
            {
                path: "/users/manage/users",
                name: "users_manage_users",
                meta:{
                    jp:"企業管理者一覧"
                },
                component: () => import("../pages/users/manage/Users.js").then(m => m.default)
            },
            {
                path: "PMO",
                name: "PMO",
                meta:{
                    jp:"人気の案件を探す"
                },
                component: () => import("../pages/PMO.js").then(m => m.default)
            },
            {
                path: "ERP",
                name: "ERP",
                meta:{
                    jp:"パッケージ導入コンサルタント"
                },
                component: () => import("../pages/ERP.js").then(m => m.default)
            },
            {
                path: "IT",
                name: "IT",
                meta:{
                    jp:"ITコンサルタント"
                },
                component: () => import("../pages/IT.js").then(m => m.default)
            },
            {
                path: "PMO_talent",
                name: "人気の人財を探す_PMO",
                meta:{
                    jp:"人気の人財を探す_PMO"
                },
                component: () => import("../pages/PMO_talent.js").then(m => m.default)
            },
            {
                path: "business_system",
                name: "人気の人財を探す_業務システム開発",
                meta:{
                    jp:"人気の人財を探す_業務システム開発"
                },
                component: () => import("../pages/business_system.js").then(m => m.default)
            },
            {
                path: "ERP_package",
                name: "ERP・パッケージ導入コンサルタント",
                meta:{
                    jp:"ERP・パッケージ導入コンサルタント"
                },
                component: () => import("../pages/ERP_package.js").then(m => m.default)
            },
            {
                path: "design_server",
                name: "サーバー設計・構築",
                meta:{
                    jp:"サーバー設計・構築"
                },
                component: () => import("../pages/design_server.js").then(m => m.default)
            },
            {
                path: "PM_PL",
                name: "PM_PL",
                meta:{
                    jp:"PM_PL"
                },
                component: () => import("../pages/PM_PL.js").then(m => m.default)
            }

        ]
    }
]

export default web