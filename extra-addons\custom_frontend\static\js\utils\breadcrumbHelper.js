/**
 * Breadcrumb Helper Utilities
 * Tạo breadcrumb HTML strings để sử dụng trong templates
 * Approach đơn giản hơn Vue component, tránh import issues
 */

/**
 * Tạo breadcrumb HTML từ array of items
 * @param {Array} items - Array of breadcrumb items
 * @param {string} containerClass - CSS class cho container (default: 'container-fluid')
 * @param {string} containerStyle - Inline style cho container (default: '')
 * @returns {string} HTML string
 */
export function createBreadcrumb(items, containerClass = 'container-fluid', containerStyle = '') {
    if (!items || items.length === 0) {
        return '';
    }
    
    let breadcrumbHTML = '';
    
    items.forEach((item, index) => {
        // Add breadcrumb item
        if (item.link && !item.current) {
            breadcrumbHTML += `<a href="${item.link}">${item.text}</a>`;
        } else {
            const currentClass = item.current ? ' class="current"' : '';
            breadcrumbHTML += `<span${currentClass}>${item.text}</span>`;
        }
        
        // Add arrow separator (không hiển thị cho item cuối)
        if (index < items.length - 1) {
            breadcrumbHTML += '<span class="breadcrumb-arrow"><span></span></span>';
        }
    });
    
    const styleAttr = containerStyle ? ` style="${containerStyle}"` : '';
    
    return `
        <div class="${containerClass}"${styleAttr}>
            <div class="breadcrumbs">${breadcrumbHTML}</div>
        </div>
    `;
}

/**
 * Common breadcrumb patterns
 */
export const BreadcrumbPatterns = {
    // Single page breadcrumb
    single: (pageName) => [
        { text: pageName, link: null, current: true }
    ],
    
    // Service menu pattern
    serviceMenu: (currentPage) => [
        { text: 'サービスメニュー', link: null },
        { text: currentPage, link: null, current: true }
    ],
    
    // Management path pattern
    management: (currentPage) => [
        { text: 'サービスメニュー', link: null },
        { text: '登録・管理', link: null },
        { text: '登録データ管理', link: null },
        { text: currentPage, link: null, current: true }
    ],
    
    // Search path pattern
    search: (currentPage) => [
        { text: 'サービスメニュー', link: null },
        { text: '探す', link: null },
        { text: currentPage, link: null, current: true }
    ],
    
    // Usage guide pattern
    usage: (currentPage) => [
        { text: '利用方法', link: null },
        { text: currentPage, link: null, current: true }
    ]
};

/**
 * Quick helper functions cho common patterns
 */
export function createSingleBreadcrumb(pageName, containerClass = 'container-fluid', containerStyle = '') {
    return createBreadcrumb(BreadcrumbPatterns.single(pageName), containerClass, containerStyle);
}

export function createServiceMenuBreadcrumb(currentPage, containerClass = 'container-fluid', containerStyle = '') {
    return createBreadcrumb(BreadcrumbPatterns.serviceMenu(currentPage), containerClass, containerStyle);
}

export function createManagementBreadcrumb(currentPage, containerClass = 'container-fluid', containerStyle = '') {
    return createBreadcrumb(BreadcrumbPatterns.management(currentPage), containerClass, containerStyle);
}

export function createSearchBreadcrumb(currentPage, containerClass = 'container-fluid', containerStyle = '') {
    return createBreadcrumb(BreadcrumbPatterns.search(currentPage), containerClass, containerStyle);
}

export function createUsageBreadcrumb(currentPage, containerClass = 'container-fluid', containerStyle = '') {
    return createBreadcrumb(BreadcrumbPatterns.usage(currentPage), containerClass, containerStyle);
}

/**
 * Examples:
 * 
 * // Basic usage
 * const breadcrumbHTML = createBreadcrumb([
 *     { text: 'サービスメニュー', link: null },
 *     { text: '案件を探す', link: null, current: true }
 * ]);
 * 
 * // Using patterns
 * const managementHTML = createManagementBreadcrumb('人財管理一覧');
 * const serviceHTML = createServiceMenuBreadcrumb('ご利用ガイドライン');
 * 
 * // Custom container
 * const customHTML = createSingleBreadcrumb('お問合せ', 'container-fluid', 'padding: 0;');
 */
