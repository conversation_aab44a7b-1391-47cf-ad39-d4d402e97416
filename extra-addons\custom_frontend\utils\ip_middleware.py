from odoo.http import request, Response
import json
import logging
from .ip_security import ip_security

_logger = logging.getLogger(__name__)

class IPSecurityMiddleware:
    """Middleware để kiểm tra IP security cho các routes"""
    
    @staticmethod
    def check_ip_access(require_admin=False, endpoint_name="unknown"):
        """
        Decorator để check IP access
        Usage:
        @IPSecurityMiddleware.check_ip_access(require_admin=True, endpoint_name="admin_panel")
        def admin_function(self):
            ...
        """
        def decorator(func):
            def wrapper(*args, **kwargs):
                # Validate IP
                is_allowed, reason, country = ip_security.validate_ip_access(
                    request, require_admin=require_admin
                )
                
                # Log access attempt
                ip_security.log_ip_access(
                    request, endpoint_name, success=is_allowed, reason=reason
                )
                
                if not is_allowed:
                    # Return error response
                    error_response = {
                        "success": False,
                        "error": "アクセスが拒否されました。",  # "Access denied"
                        "code": "IP_ACCESS_DENIED"
                    }
                    
                    # Log security violation
                    from .security_logger import security_logger
                    security_logger.log_security_violation(
                        'ip_access_denied',
                        f'IP access denied: {reason}',
                        request.session.get('loginId')
                    )
                    
                    return Response(
                        json.dumps(error_response),
                        content_type='application/json',
                        status=403
                    )
                
                # Proceed with original function
                return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    @staticmethod
    def validate_payment_ip():
        """Đặc biệt validate IP cho payment endpoints"""
        client_ip = ip_security.get_client_ip(request)
        
        # Kiểm tra blacklist
        if ip_security.is_ip_blacklisted(client_ip):
            return False, "IP is blacklisted"
        
        # Kiểm tra country cho payment (strict hơn)
        country = ip_security.get_country_from_ip(client_ip)
        if country:
            # Chỉ cho phép một số nước an toàn cho payment
            safe_countries = {'JP', 'US', 'GB', 'DE', 'FR', 'AU', 'CA', 'SG'}
            if country not in safe_countries:
                return False, f"Payment not allowed from country: {country}"
        
        return True, "Payment IP validated"
    
    @staticmethod
    def track_failed_login(email=None):
        """Track failed login attempts by IP"""
        client_ip = ip_security.get_client_ip(request)
        
        # Add failed attempt
        auto_blacklisted = ip_security.add_failed_login_attempt(client_ip)
        
        # Log failed login
        from .security_logger import security_logger
        security_logger.log_security_violation(
            'failed_login',
            f'Failed login from IP {client_ip}, email: {email}',
            None
        )
        
        if auto_blacklisted:
            security_logger.log_security_violation(
                'ip_auto_blacklisted',
                f'IP {client_ip} auto-blacklisted due to multiple failed logins',
                None
            )
        
        return auto_blacklisted
    
    @staticmethod
    def validate_session_ip():
        """Validate session IP binding (chống session hijacking)"""
        if 'session_ip' not in request.session:
            # First time - bind IP to session
            request.session['session_ip'] = ip_security.get_client_ip(request)
            return True
        
        # Check if IP matches
        session_ip = request.session['session_ip']
        current_ip = ip_security.get_client_ip(request)
        
        if session_ip != current_ip:
            # IP changed - potential session hijacking
            from .security_logger import security_logger
            security_logger.log_security_violation(
                'session_ip_mismatch',
                f'Session IP changed from {session_ip} to {current_ip}',
                request.session.get('loginId')
            )
            
            # Clear session
            request.session.clear()
            return False
        
        return True
    
    @staticmethod
    def get_security_headers():
        """Generate security headers based on IP"""
        client_ip = ip_security.get_client_ip(request)
        country = ip_security.get_country_from_ip(client_ip)
        
        headers = {}
        
        # Add country info for frontend
        if country:
            headers['X-Client-Country'] = country
        
        # Add security warnings for high-risk IPs
        if ip_security.is_ip_blacklisted(client_ip):
            headers['X-Security-Warning'] = 'IP_BLACKLISTED'
        
        return headers

# Helper functions for easy use in controllers
def require_admin_ip(endpoint_name="admin"):
    """Decorator shortcut for admin IP validation"""
    return IPSecurityMiddleware.check_ip_access(require_admin=True, endpoint_name=endpoint_name)

def require_safe_ip(endpoint_name="general"):
    """Decorator shortcut for general IP validation"""
    return IPSecurityMiddleware.check_ip_access(require_admin=False, endpoint_name=endpoint_name)

def validate_payment_ip():
    """Function shortcut for payment IP validation"""
    return IPSecurityMiddleware.validate_payment_ip()
