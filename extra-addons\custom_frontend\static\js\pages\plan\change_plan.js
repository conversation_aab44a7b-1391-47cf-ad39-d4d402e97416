import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const changePlan = {
    'template': `
    <main class="pb-3 margin-header" id="vue-app">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '会社データ管理', link: '/companies/manage/edit' },
            { text: 'プラン変更', link: null, current: true }
        ])}
        <div class="container-fluid">
            <div class="d-flex justify-content-end align-items-center">
                <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a href="/companies/manage/edit">会社データ</a></li>
                        <li><a href="/users/edit">プロフィール</a></li>
                        <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a href="/users/profile/edit_password">パスワード</a></li>
                        <li><a href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a class="active" href="/mypage/plan">プラン</a></li>
                        <li><a href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="container-fluid grabient pt-5 position-relative">
            <div class="row mb-4 mb-md-0">
                <div class="d-none d-md-block col-md-4 col-lg-3 side-menu-contents">
                    <div class="card px-3 pb-3 side-card collapsible">
                        <ul class="collapsible mb-0">
                            <div class="d-none d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                            <li class="my-md-1" hidden><a class="d-block py-1 px-3 active" aria-current="page" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                    <div class="card mb-5 px-md-3">
                        <div class="card-body py-5">
                            <div class="mt-3 mx-3">
                                <h3 class="mb-3">料金プラン説明</h3>
                                <div class="mb-5">
                                    <p class="mb-0">業界最安値水準（スタンダードプランの場合）</p>
                                    <p class="mb-0">会社単位の契約になります。</p>
                                </div>
                                <table class="custom-plan-table mb-4">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th colspan="2">プラン一覧</th>
                                            <th>スタンダード会員</th>
                                            <th>プレミアム会員</th>
                                        </tr>
                                    </thead>
                                    <tbody v-if="plans.length >= 2">
                                        <tr>
                                            <td rowspan="2">費用</td>
                                            <td>月額</td>
                                            <td v-html="formatMultiline(plans[0].standard_plan)"></td>
                                            <td v-html="formatMultiline(plans[0].premium_plan)"></td>
                                        </tr>
                                        <tr>
                                            <td>年額</td>
                                            <td v-html="formatMultiline(plans[1].standard_plan)"></td>
                                            <td v-html="formatMultiline(plans[1].premium_plan)"></td>
                                        </tr>
                                        <template v-for="(plan, index) in plans.slice(2)" :key="index">
                                            <tr>
                                                <td colspan="2">{{ plan.title }}</td>
                                                <td v-html="formatMultiline(plan.standard_plan)"></td>
                                                <td v-html="formatMultiline(plan.premium_plan)"></td>
                                            </tr>
                                        </template>
                                    </tbody>
                                </table>
                            </div>
                            <div class="row justify-content-center mt-5">
                                <div class="col-12 col-md-6">
                                    <button class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light" @click="changeToPremium">プレミアムプランに変更</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css"/>
    <link rel="stylesheet" href="/custom_frontend/static/css/plan/plan.css" />
    <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css">
    <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            plans: [],
            isMobileMenuOpen: false
        }
    },
    mounted() {
        this.getPlan();
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });

        // Add custom CSS for plan table
        const style = document.createElement('style');
        style.textContent = `
            .custom-plan-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1rem;
            }

            .custom-plan-table th, .custom-plan-table td {
                padding: 0.75rem;
                border: 1px solid #dee2e6;
            }

            .custom-plan-table thead th {
                background-color: #343a40;
                color: white;
                border-color: #454d55;
                text-align: center;
            }

            .custom-plan-table td {
                text-align: center;
            }

            .custom-plan-table td[colspan] {
                text-align: left;
            }
        `;
        document.head.appendChild(style);
    },
    methods: {
        async getPlan() {
            try {
                const response = await fetch('/api/get_plan', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                });
                const data = await response.json();
                if (data.success) {
                    console.log("Plans fetched successfully: ", data.plans);
                    this.plans = data.plans;
                } else {
                    console.error("Failed to fetch plans", data.message);
                }
            } catch (error) {
                console.error('Failed to fetch api: ', error.message)
            }
        },
        formatMultiline(text) {
            return text.toString().replace(/\n/g, '<br>');
        },
        changeToPremium() {
            // Implement plan change logic here
            window.toastr.info('プラン変更のリクエストを送信しました。');
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function() {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function() {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function() {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function() {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        }
    }
}

export default changePlan
