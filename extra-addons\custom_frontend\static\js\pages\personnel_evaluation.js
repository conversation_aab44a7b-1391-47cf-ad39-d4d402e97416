import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const RatingPersonal = {
    template: `
<main class="margin-header sp_fluid" id="vue-app">
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: '探す', link: null },
        { text: 'マッチング状況', link: '/mypage' },
        { text: '要員評価', link: null, current: true }
    ], 'container-fluid', '')}

    <div class="evaluation-container grabient pt-5 position-relative">
        <!-- Personnel Information Section -->
        <div class="personnel-info">
            <div class="info-row">
                <div class="info-label">案件名:</div>
                <a class="info-value">{{ subject }}</a>
            </div>
            <div class="info-row">
                <div class="info-label">人財名:</div>
                <a class="info-value">{{ user_name }}さん</a>
            </div>
            <div class="info-row">
                <div class="info-label">状況:</div>
                <div class="info-value">{{ mapping[status] || status }}</div>
            </div>
            <div class="info-row">
                <div class="info-label">入力期限:</div>
                <div class="info-value">{{ expire_date }}</div>
            </div>
        </div>

        <div class="evaluation-form">
            <div class="form-header">
                <div class="header-item">項目</div>
                <div class="header-stars">評価</div>
                <div class="header-reason">理由</div>
            </div>

            <div v-for="category in categories" :key="category.id" class="evaluation-item">
                <div class="category-name">
                    <span>{{ category.name }}</span>
                </div>

                <div class="rating">
                <span
                        v-for="n in 5"
                        :key="n"
                        @click="rate(category, n)"
                        class="star"
                        :class="{ filled: n <= category.rating }"
                >★</span>
                </div>

                <div class="reason">
                    <input
                            v-model="category.reason"
                            type="text"
                            class="reason-input"
                    />
                </div>
            </div>
        </div>
        <div class="actions-row">
            <button @click="createWorkflowEvaluation" class="button submit-button">登録</button>
        </div>
    </div>


</main>
<link rel="stylesheet" href="/custom_frontend/static/css/personnel_evaluation.css"/>
<link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            categories: [],
            subject: "",
            user_name: "",
            status: "",
            expire_date: "",
            isSubmitted: false,
        }
    },
    mounted() {
        this.userId = userInfo ? userInfo.user_id : null;
        this.getCriteria();
        this.getWorkflow();
    },
    computed: {
        mapping() {
            return {
                0: "日程調整待ち"
            }
        }
    },
    methods: {
        rate(category, rating) {
            category.rating = rating
        },

        async getCriteria() {
            try {
                let response = await fetch('/api/get_criteria', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                let data = await response.json();
                this.categories = data.result.map(item => ({
                    id: item.id,
                    name: item.name,
                    rating: 0,
                    reason: ""
                }));

                console.log("categories", this.categories);
            } catch (error) {
                console.error("Error fetching criteria:", error);
            }
        },

        async getWorkflow() {
            try {
                const id = this.$route.params.id;
                let response = await fetch(`/api/get_workflowbyid?id=${id}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                let data = await response.json();
                if (data.success) {
                    this.workflow_id = data.id,
                    this.subject = data.opportunities_name;
                    this.user_name = data.resumes_name;
                    this.status = data.status;
                    let [year, month, day] = data.expire_date.split("T")[0].split("-");
                    this.expire_date = `${month}/${day}`;
                } else {
                    console.error("Error fetching workflow1:", data.message);
                }
            } catch (error) {
                console.error("Error fetching workflow2:", error);
            }
        },

        async createWorkflowEvaluation() {
            if (this.isSubmitted) return
            this.isSubmitted = true;
            try {
                const id = this.$route.params.id;
                let payload = {
                    workflow_id: id,
                    created_by: this.userId,
                    evaluations: this.categories.map(category => ({
                        criteria_id: category.id,
                        score: category.rating,
                        reason: category.reason || ""
                    }))
                };

                let response = await fetch('/api/create_workflow_evaluation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                let data = await response.json();
                console.log("Evaluation response:", data);

                if (data.result.success) {
                    window.location.href = "/mypage";
                } else {
                    console.error("Lỗi khi tạo đánh giá:", data.message);
                }

            } catch (error) {
                console.error("Lỗi khi gửi request:", error);
            }
        }
    },
}

export default RatingPersonal;