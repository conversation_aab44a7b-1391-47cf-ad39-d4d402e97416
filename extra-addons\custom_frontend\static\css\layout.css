/*CSS Header*/
/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */

/* CSS Variables for responsive header heights */
:root {
  --header-height-desktop: 90px;
  --header-height-tablet: 80px;
  --header-height-mobile: 60px;
}

/* Common Breadcrumb Component CSS - Consolidated từ tất cả các file CSS khác nhau */

/* Base breadcrumb styles */
.breadcrumbs {
    overflow-x: scroll;
    overflow-y: hidden;
    -ms-overflow-style: none;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    line-height: 1;
    font-size: 0.75rem;
    padding-top: 10px !important;
    padding-bottom: 15px !important;
}

/* Hide scrollbar */
.breadcrumbs::-webkit-scrollbar {
    display: none;
}

/* Breadcrumb items */
.breadcrumbs a,
.breadcrumbs span {
    display: inline-block;
    padding: 8px 3px 7px;
}

/* Links styling */
.breadcrumbs a {
    color: #1072e9;
    text-decoration: none;
    cursor: pointer;
    transition: all .2s ease-in-out;
}

.breadcrumbs a:hover {
    color: #0056b3;
    text-decoration: none;
    transition: all .2s ease-in-out;
}

/* Current page styling */
.breadcrumbs span.current {
    color: #333;
    font-weight: normal;
}

/* Arrow separator */
.breadcrumbs span.breadcrumb-arrow {
    position: relative;
    width: 1rem;
}

.breadcrumbs span.breadcrumb-arrow span {
    border-bottom: 14px solid rgba(0, 0, 0, 0);
    border-left: 12px solid #c7c7c7;
    border-right: 0 dotted;
    border-top: 14px solid rgba(0, 0, 0, 0);
    height: 0 !important;
    margin: -1px 10px -1px 10px;
    width: 0;
    position: absolute;
    top: -2px;
    left: -10px;
    padding: 0;
}

.breadcrumbs span.breadcrumb-arrow span:after {
    border-bottom: 14px solid rgba(0, 0, 0, 0);
    border-left: 12px solid #eee;
    border-right: 0 dotted;
    border-top: 14px solid rgba(0, 0, 0, 0);
    content: " ";
    display: block;
    height: 0 !important;
    margin-left: -13px;
    position: absolute;
    top: -14px;
    width: 0;
}

@media screen and (min-width: 1024px) and (max-width: 1279px) {
  #app {
    margin-top: 11vh !important;
  }

  body header .header-search-menu{
    height: 3vh !important;
  }
}

@media screen and (min-width: 1280px) {
  #app {
    margin-top: 17vh !important;
  }
}

@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  header,
  nav {
    display: block;
  }

  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  ul {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  ul ul {
    margin-bottom: 0;
  }

  a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
  }

  a:hover {
    color: #0056b3;
  }

  img {
    vertical-align: middle;
    border-style: none;
  }

  label {
    display: inline-block;
    margin-bottom: .5rem;
  }

  .btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .btn {
      transition: none;
    }
  }

  .btn:hover {
    color: #212529;
    text-decoration: none;
  }

  .btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .btn:disabled {
    opacity: .65;
  }

  .btn:not(:disabled):not(.disabled) {
    cursor: pointer;
  }

  .btn-sm {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem;
  }

  .dropdown {
    position: relative;
  }

  .navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: .5rem 1rem;
  }

  .navbar-brand {
    display: inline-block;
    padding-top: .3125rem;
    padding-bottom: .3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap;
  }

  .navbar-brand:focus,
  .navbar-brand:hover {
    text-decoration: none;
  }

  .navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
  }

  @media (min-width: 768px) {
    .navbar-expand-md {
      flex-flow: row nowrap;
      justify-content: flex-start;
    }

    .navbar-expand-md .navbar-nav {
      flex-direction: row;
    }
  }

  .progress {
    display: flex;
    height: 1rem;
    overflow: hidden;
    line-height: 0;
    font-size: .75rem;
    background-color: #e9ecef;
    border-radius: .25rem;
  }

  .border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
  }

  .d-nonehome {
    display: flex !important;
  }

  .d-inline-block {
    display: inline-block !important;
  }

  .d-block {
    display: block !important;
  }

    @media (min-width: 1200px) {
      .d-xl-none {
        display: none !important;
      }

      .d-xl-inline-block {
        display: inline-block !important;
      }

      .d-xl-block {
        display: block !important;
      }
    }

  .align-items-center {
    align-items: center !important;
  }

  .float-right {
    float: right !important;
  }

  .fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
  }

  .shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  }

  .w-100 {
    width: 100% !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .my-0 {
    margin-top: 0 !important;
  }

  .mb-0,
  .my-0 {
    margin-bottom: 0 !important;
  }

  .mr-1 {
    margin-right: 0.25rem !important;
  }

  .mb-1 {
    margin-bottom: 0.25rem !important;
  }

  .ml-1 {
    margin-left: 0.25rem !important;
  }

  .mt-3 {
    margin-top: 1rem !important;
  }

  .mr-3 {
    margin-right: 1rem !important;
  }

  .mr-4 {
    margin-right: 1.5rem !important;
  }

  .p-0 {
    padding: 0 !important;
  }

  .pb-0 {
    padding-bottom: 0 !important;
  }

  .pt-1 {
    padding-top: 0.25rem !important;
  }

  .p-2 {
    padding: 0.5rem !important;
  }

  .pb-2 {
    padding-bottom: 0.5rem !important;
  }

  .pl-2 {
    padding-left: 0.5rem !important;
  }

  .pr-3,
  .px-3 {
    padding-right: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .p-4 {
    padding: 1.5rem !important;
  }

  .mr-auto,
  .mx-auto {
    margin-right: auto !important;
  }

  .ml-auto,
  .mx-auto {
    margin-left: auto !important;
  }

  .text-right {
    text-align: right !important;
  }

  .font-weight-bold {
    font-weight: 700 !important;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }

    a:not(.btn) {
      text-decoration: underline;
    }

    img {
      page-break-inside: avoid;
    }

    p {
      orphans: 3;
      widows: 3;
    }

    .navbar {
      display: none;
    }
  }

  .black-text {
    color: #000 !important;
  }

  .yellow {
    background-color: #FFF2CC !important;
    padding-bottom: 0 !important;
  }

  .white-text {
    color: #fff !important;
  }

  .primary-color-dark {
    background-color: #0d47a1 !important;
  }

  :disabled {
    pointer-events: none !important;
  }

  a {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    transition: all .2s ease-in-out;
  }

  a:hover {
    color: #0056b3;
    text-decoration: underline;
    transition: all .2s ease-in-out;
  }

  a:disabled:hover {
    color: #007bff;
  }

  .font-small {
    font-size: .9rem;
  }

  .waves-effect {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  a.waves-effect,
  a.waves-light {
    display: inline-block;
  }

  .btn {
    margin: .375rem;
    color: inherit;
    text-transform: uppercase;
    word-wrap: break-word;
    white-space: normal;
    cursor: pointer;
    border: 0;
    border-radius: .25rem;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    padding: .84rem 2.14rem;
    font-size: .81rem;
  }

  .btn:hover,
  .btn:focus,
  .btn:active {
    outline: 0;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn.btn-sm {
    padding: .5rem 1.6rem;
    font-size: .64rem;
  }

  .btn:disabled:hover,
  .btn:disabled:focus,
  .btn:disabled:active {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .btn[class*=btn-outline-] {
    padding-top: .7rem;
    padding-bottom: .7rem;
  }

  .btn.btn-sm[class*=btn-outline-] {
    padding-top: .38rem;
    padding-bottom: .38rem;
  }

  .btn-default {
      color: #fff !important;
      background: linear-gradient(to right, #61b8f7, #1072e9) !important;
  }

  .btn-default:hover {
    color: #fff;
    background-color: #61b8f7;
  }

  .btn-default:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-default:focus,
  .btn-default:active {
    background-color: #005650;
  }

  .btn-default:not([disabled]):not(.disabled):active {
    background-color: #005650 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-default:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-default {
    color: #1072e9 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border: 2px solid #1072e9 !important;
  }

  .btn-outline-default:hover,
  .btn-outline-default:focus,
  .btn-outline-default:active,
  .btn-outline-default:active:focus {
    color: #1072e9 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #1072e9 !important;
  }

  .btn-outline-default:not([disabled]):not(.disabled):active {
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #1072e9 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-default:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .navbar {
    font-weight: 300;
  }

  .menu_right_item{
    background-color: #1076d6;
    color: white;
    width: 100%;
    justify-self: flex-start;
    border-radius: 0;
  }

  .menu_right_item a {
    text-align: left !important;
    padding-left: 15px !important;
  }

  @media (min-width: 600px) {
    .navbar.scrolling-navbar {
      padding-top: 12px;
      padding-bottom: 12px;
      transition: background .5s ease-in-out, padding .5s ease-in-out;
    }

    .navbar.scrolling-navbar .navbar-nav>li {
      transition-duration: 1s;
    }
  }

  .md-progress {
    position: relative;
    display: block;
    width: 100%;
    height: .25rem;
    margin-bottom: 1rem;
    overflow: hidden;
    background-color: #eee;
    box-shadow: none;
  }

  .md-progress .indeterminate {
    background-color: #90caf9;
  }

  .md-progress .indeterminate:before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    content: "";
    background-color: inherit;
    -webkit-animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
    animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
    will-change: left, right;
  }

  .md-progress .indeterminate:after {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    content: "";
    background-color: inherit;
    -webkit-animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
    animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
    -webkit-animation-delay: 1.15s;
    animation-delay: 1.15s;
    will-change: left, right;
  }

  .bold {
    font-weight: 500;
  }

  .ex-bold {
    font-weight: 700 !important;
  }

  .font-default {
    font-size: .875rem !important;
  }

  .font-middle {
    font-size: 1rem !important;
  }

  .font-small {
    font-size: .75rem !important;
  }

  .white-text {
    color: #fff;
  }

  .custom-grey-6-text {
    color: #455965;
  }

  .default-main-color {
    color: #1072e9;
  }

  .p-0 {
    padding: 0 !important;
  }

  .pb-0 {
    padding-bottom: 0 !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .my-0 {
    margin-top: 0 !important;
  }

  .mb-0,
  .my-0 {
    margin-bottom: 0 !important;
  }

  .pt-1 {
    padding-top: .25rem !important;
  }

  .mr-1 {
    margin-right: .25rem !important;
  }

  .mb-1 {
    margin-bottom: .25rem !important;
  }

  .ml-1 {
    margin-left: .25rem !important;
  }

  .p-2 {
    padding: .5rem !important;
  }

  .pb-2 {
    padding-bottom: .5rem !important;
  }

  .pl-2 {
    padding-left: .5rem !important;
  }

  .pr-3,
  .px-3 {
    padding-right: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .mt-3 {
    margin-top: 1rem !important;
  }

  .mr-3 {
    margin-right: 1rem !important;
  }

  .p-4 {
    padding: 1.5rem !important;
  }

  .mr-4 {
    margin-right: 1.5rem !important;
  }

  .mt-6 {
    margin-top: 2.5rem !important;
  }

  body a {
    color: #1072e9;
  }

  body a:hover {
    color: #1072e9;
  }

  body a:hover img {
    opacity: .7;
  }

  .material-icons {
    vertical-align: bottom;
    cursor: pointer;
  }

  .vertical-middle {
    vertical-align: middle !important;
  }

  .vertical-text-bottom {
    vertical-align: text-bottom;
  }

  .btn {
    line-height: 1;
    text-transform: none;
  }

  .btn:hover,
  .btn:active,
  .btn:focus {
    opacity: .7;
  }

  .btn[class*=btn-outline-]:hover,
  .btn[class*=btn-outline-]:active,
  .btn[class*=btn-outline-]:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    outline: 0;
    opacity: 1;
  }

  .btn.btn-sm {
    padding: .5rem 1rem;
    font-size: .875rem;
  }

  body header a {
    color: rgba(0, 0, 0, 0.87);
    overflow: visible;
  }

  body header a:hover,
  body header a:hover .material-icons {
    opacity: .7;
  }

  body header .navbar.scrolling-navbar {
    padding: 28px 32px;
  }

  @media (min-width: 768px) {
    body header .navbar.scrolling-navbar {
      height: 110px;
    }
  }

  body header .navbar-brand img {
    width: 113px;
    vertical-align: baseline;
  }

  @media (min-width: 768px) {
    body header .navbar-brand img {
      width: 12rem;
      margin-right: 1.5rem;
      margin-bottom: 0;
      vertical-align: sub;
    }
  }

  body header .navbar-left>ul {
    margin: 0;
  }

  body header .navbar-left>ul li {
    display: inline-block;
    position: relative;
    line-height: 5vh;
  }

  body header .navbar-left>ul li span {
    cursor: pointer;
  }

  body header .header-search-menu i {
    color: #fff !important;
  }

  body header .navbar-left>ul li ul {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background-color: #FFF2CC;
    display: none;
    position: absolute;
    right: 0;
    left: 0;
  }

  body header .navbar-left>ul li ul.big_ul {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background-color: #499ae6;
    color: #fff;
    display: none;
    position: absolute;
    z-index: 1000;
    margin-top: -0.5%;
  }


  body header .navbar-left>ul li ul.big_ul .menu-item-parent {
    position: relative;
    background-color: #1076d6;
    border-radius: 0;
  }

  body header .navbar-left>ul li ul.big_ul .menu-item-parent > span {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    color: white;
    font-weight: 500;
    padding: 0 10px;
  }

  body header .navbar-left>ul li ul.big_ul .menu-item-parent:hover {
    background-color: #00B0F0;
  }

  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu {
    display: none;
    position: absolute;
    left: 100.1%;
    background-color: #3b7bc3;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 0;
    min-width: 103.5%;
    z-index: 1001;
    background-color: #5B9BD5;
    margin-top: -15.5%;
  }

  /* Desktop hover behavior */
  @media screen and (min-width: 768px) {
    body header .navbar-left>ul li ul.big_ul .menu-item-parent:hover .submenu {
      display: block;
    }

    #div-surround-1:hover #submenu1{
      display: block;
    }

    #div-surround-2:hover #submenu2{
      display: block;
    }
  }

  /* Mobile click behavior - submenu hidden by default, shown via JavaScript */
  @media screen and (max-width: 767px) {
    body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu {
      display: none !important;
    }

    /* Add cursor pointer for mobile menu items */
    body header .navbar-left>ul li ul.big_ul .menu-item-parent {
      cursor: pointer;
    }
  }

  body header .navbar-left>ul li ul.big_ul .submenu li {
    background-color: #1076d6;
    color: white;
    width: 100%;
    justify-self: center;
    border-radius: 0;
  }

  #concierge-service{
    background-color: #e9ba01;
  }

  #concierge-service:hover {
    background-color: #f8d751 !important;
  }

  #concierge-service a:hover {
    background-color: #f8d751 !important;
    color: #fff !important;
  }

  /* Override any other hover styles for this specific element */
  body header .navbar-left>ul li ul.big_ul .submenu li#concierge-service:hover {
    background-color: #f8d751 !important;
  }

  body header .navbar-left>ul li:hover ul a:hover#concierge-service {
    background-color: #f8d751 !important;
  }

  body header .navbar-left>ul li ul.big_ul .submenu li a {
    color: white;
    display: block;
    text-align: left;
    padding-left: 15px;
  }

  body header .navbar-left>ul li ul.big_ul .submenu li a:hover {
    text-decoration: underline;
  }

  body header .navbar-left>ul li ul.big_ul .submenu li:hover {
    background-color: #00B0F0;
    border-radius: 0;
  }

  body header .navbar-left>ul li ul.big_ul .logout-item {
    display: block;
    color: white;
    background-color: #ff9800;
    text-align: left;
    padding-left: 10px;
  }

  body header .navbar-left>ul li ul.big_ul .logout-item:hover {
    background-color: #fcae3a;
    text-decoration: underline;
  }

  body #app .title {
    background: linear-gradient(135deg, #00B0F0, #5B9BD5, #4472C4, #002060) !important;
    h1 {
      color: #fff !important;
    }
  }


  /* Breadcrumb styles moved to components/breadcrumb.css */

  .btn-deep-orange {
    color: #fff;
    background-color: #1072e9 !important;
}

  body header .navbar-left>ul li ul li {
    display: block;
    margin: 0;
  }

  body header .navbar-left>ul li ul li a {
    display: block;
    text-decoration: none;
    white-space: nowrap;
    color: #fff !important;
  }

  body header .navbar-left>ul li:hover ul.big_ul {
    display: block;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  body header .navbar-left>ul li:hover ul a:hover {
    opacity: 1;
    background-color: #00B0F0;
    border-radius: 0;
    color: #fff !important;
    text-decoration: underline;
  }

  /* Special style for concierge service */
  body header .navbar-left>ul li:hover ul li#concierge-service a:hover {
    background-color: #f8d751 !important;
  }

  body header .navbar-left>ul li ul.big_ul .submenu li#concierge-service a:hover {
    background-color: #f8d751 !important;
  }

  .slogan {
    width: 700px;
    height: 50px;
    margin-left: 20px;
  }

  body header #login_link,
  body header #registration_link {
    color: #000 !important;
    height: 32px;
    font-weight: 700;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
  }

  body header #login_link {
    line-height: .8;
    width: 95px;
    background-color: rgb(100,166,226) !important;
  }

  body header #registration_link {
    line-height: 1;
    width: 140px;
    background-color: rgb(5,188,254) !important;
  }

  body header .header-search-menu {
    font-weight: 500;
    width: 270px;
    margin-right: 10px;
    /* border-bottom: 30px solid #499ae6; */
    height: 5vh;
    text-align: left;
    padding-left: 10px;
    cursor: pointer;
    transition: border-bottom-color 0.3s;
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
  }

  body header .header-search-menu a {
    color: #2a3942;
  }

  body header .header-search-menu.active {
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
  }

  body header .header-search-menu span:hover {
    opacity: .7;
  }

  /* Flex layout cho nội dung menu */
  body header .header-search-menu .menu-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100%;
    height: 100%;
    padding-right: 10px;
  }

  body header .navbar-nav {
    flex-direction: row;
  }

  body header .icon-wrapper {
    position: relative;
    float: left;
  }

  body header .icon-wrapper i {
    width: 28px;
    height: 28px;
    font-size: 28px;
    text-align: center;
    vertical-align: middle;
    color: #455965;
    margin-right: 20px;
  }

  @media (min-width: 768px) {
    body header .icon-wrapper i {
      width: 32px;
      height: 32px;
      font-size: 32px;
    }
  }

  body header .link_area {
    font-size: 13px;
    line-height: 1;
  }

  body header .phone-area label {
    font-size: 18px;
  }

  body header .phone-icon {
    width: 17px;
    height: 17px;
    display: inline-block;
    margin: 0 4px 3px 0;
  }

  body header .phone-reception-time {
    font-weight: 500;
    line-height: normal;
  }

  body header .phone-outside-reception-hours {
    font-family: "YakuHanJP";
    font-feature-settings: "palt" 1;
  }

  body header .sp-phone-area {
    display: block;
    text-align: center;
    background-color: #eaf8f7;
  }

  body header .sp-phone-invitation-message {
    font-weight: 700;
    margin-bottom: 0;
    white-space: nowrap;
  }

  body header .sp-phone-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    margin: 0 4px 6px 0;
    vertical-align: bottom;
  }

  body header .sp-phone-number {
    margin-bottom: 0;
    font-size: 1.5rem;
    line-height: normal;
    white-space: nowrap;
  }

  body header .sp-phone-reception-time {
    color: #2a3942;
    font-weight: 500;
    line-height: normal;
  }

  body header .sp-phone-outside-reception-hours {
    color: #2a3942;
    font-family: "YakuHanJP";
    font-feature-settings: "palt" 1;
  }

  body header .sp-phone-area .tel-btn {
    font-size: 1.5rem;
    line-height: normal;
  }

  body header .line-height-normal {
    line-height: 1.6;
  }

  body header .line-height-mini {
    line-height: .7;
  }

  body header .roboto {
    font-family: "Roboto";
  }

  body header .header-color {
    color: white;
  }

  .material-icons{
    line-height: 5vh !important;
  }

  @media screen and (min-width: 481px) {
    body header .tel-btn {
      display: none;
    }
  }

  @media screen and (max-width: 767px) {
    body header .navbar.scrolling-navbar {
      padding: 12px 16px;
      height: 110px;
      display: flex !important;
      flex-direction: column !important;
      align-items: stretch !important;
      justify-content: flex-start !important;
    }

    /* Dòng 1: Logo + Nút đăng nhập (chỉ hiển thị trên mobile khi chưa đăng nhập) */
    body header .navbar.scrolling-navbar .navbar-top-row {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      width: 100% !important;
    }

    /* Logo trong dòng 1 */
    body header .navbar.scrolling-navbar .navbar-top-row .navbar-brand {
      flex: 0 0 auto !important;
      margin-bottom: 0 !important;
    }

    /* Container cho nút đăng nhập trong dòng 1 */
    body header .navbar.scrolling-navbar .navbar-top-row .ml-auto {
      flex: 0 0 auto !important;
      margin-left: auto !important;
    }

    /* Style cho nút đăng nhập trên mobile - xếp ngang */
    body header .navbar-top-row .navbar-nav.header-notification {
      display: flex !important;
      flex-direction: row !important;
      align-items: center !important;
      gap: 6px !important;
      margin: 0 !important;
    }

    body header .navbar-top-row .navbar-nav.header-notification li {
      margin: 0 !important;
      padding: 0 !important;
    }

    /* Giảm kích thước nút trên mobile */
    body header .navbar-top-row #login_link,
    body header .navbar-top-row #registration_link {
      font-size: 10px !important;
      padding: 4px 8px !important;
      height: auto !important;
      line-height: 1.2 !important;
      width: auto !important;
      min-width: 60px !important;
    }

    body header .navbar-top-row #registration_link {
      min-width: 75px !important;
    }

    /* Dòng 2: Slogan xuống dưới và full width */
    body header .navbar.scrolling-navbar .d-flex.flex-column {
      width: 100% !important;
      margin-left: 0 !important;
      align-self: flex-start !important;
    }

    /* Slogan full width */
    body header .slogan {
      width: 100% !important;
      max-width: 100% !important;
      height: auto !important;
      margin-left: -24px !important;
    }

    /* Ẩn logo và nút desktop trên mobile */
    body header .navbar.scrolling-navbar .d-none.d-md-block {
      display: none !important;
    }

    /* Khi đã đăng nhập, layout khác */
    body header .navbar.scrolling-navbar:has(.navbar-brand:not(.d-none)) {
      flex-direction: row !important;
      align-items: center !important;
      justify-content: flex-start !important;
    }

    body header .navbar.scrolling-navbar:has(.navbar-brand:not(.d-none)) .d-flex.flex-column {
      margin-left: 20px !important;
      width: auto !important;
    }
  }

  @media (max-width: 480px) {
    body header .text-phone-number {
      display: none;
    }

    /* Điều chỉnh thêm cho màn hình nhỏ hơn */
    body header .navbar.scrolling-navbar {
      height: 90px !important;
      padding: 8px 12px !important;
    }

    /* Nút nhỏ hơn trên màn hình rất nhỏ */
    body header .navbar-top-row #login_link,
    body header .navbar-top-row #registration_link {
      font-size: 9px !important;
      padding: 3px 6px !important;
      min-width: 50px !important;
    }

    body header .navbar-top-row #registration_link {
      min-width: 65px !important;
    }

    /* Gap nhỏ hơn giữa các nút */
    body header .navbar-top-row .navbar-nav.header-notification {
      gap: 4px !important;
    }

    /* Logo nhỏ hơn trên màn hình rất nhỏ */
    body header .navbar-brand .logo {
      max-width: 70px !important;
      height: auto !important;
    }

    /* Slogan nhỏ hơn */
    body header .slogan {
      max-height: 30px !important;
    }
  }

  /* Footer - Cột 利用方法 trên mobile */
  @media screen and (max-width: 767px) {
    /* Target cụ thể thẻ dl có margin-left: 35px */
    footer.page-footer .home-directive dl[style*="margin-left: 35px"] {
      margin-left: 0px !important;
    }

    /* Backup selector nếu cần */
    footer.page-footer .home-directive .col-12.col-md-6.col-lg-3:nth-child(2) dl {
      margin-left: 0px !important;
    }

    /* Selector mạnh nhất */
    .page-footer .home-directive div[style*="flex: 0 0 20%"]:nth-child(2) dl {
      margin-left: 0px !important;
    }
  }

  :focus {
    outline: 0;
  }

  ul {
    list-style: none;
    padding: 0;
  }

  .account-side-scrollbar {
    overflow-y: scroll;
    height: 100vh;
    border-left: 2px solid #9da9b2;
  }

  .custom-side-nav {
    color: #2a3942;
    display: none;
    position: fixed;
    right: 0;
    width: 318px !important;
    z-index: 10;
    height: 100%;
  }

  .custom-side-nav .sign_in_area {
    text-align: center;
    margin-top: 26px;
    margin-bottom: 32px;
  }

  .custom-side-nav .sign_in_area a {
    width: 138px;
    height: 48px;
    line-height: 2.75rem;
  }

  .custom-side-nav ul li .border-bottom {
    margin-bottom: 12px;
  }

  .custom-side-nav ul li .side-nav-title {
    padding-bottom: 12px;
  }

  .custom-side-nav ul li .side-nav-title,
  .custom-side-nav ul li .side-nav-contents {
    cursor: pointer;
    transform: scaleY(1);
  }

  .custom-side-nav ul li .side-nav-title a,
  .custom-side-nav ul li .side-nav-contents a {
    color: #2a3942;
    padding: 0 0 12px 24px;
  }

  .custom-side-nav ul li .side-nav-title a,
  .custom-side-nav ul li .side-nav-contents div {
    color: #2a3942;
    padding: 0 0 12px 24px;
  }

  .custom-side-nav ul li .side-nav-title a:hover span,
  .custom-side-nav ul li .side-nav-contents a:hover span {
    opacity: .7;
  }

  .custom-side-nav ul li .side-nav-title a span,
  .custom-side-nav ul li .side-nav-contents a span {
    font-size: 1rem;
    line-height: 32px;
  }

  .accordion_close {
    cursor: pointer;
  }

  .progress-zindex {
    z-index: 1050;
  }

  .progress {
    border-radius: 1.25rem;
  }

  @media (max-width: 768px) {
    .progress {
      width: 263px;
      margin: 0 auto;
    }
  }

  .border-bottom {
    border-bottom: 1px solid #e6eaec !important;
  }

  @media (max-width: 767px) {
    .btn-outline-default:hover {
      border-color: #1072e9 !important;
      background-color: inherit !important;
      color: #1072e9 !important;
    }
  }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
  }
}

/*! CSS Used keyframes */
@-webkit-keyframes indeterminate {
  0% {
    right: 100%;
    left: -35%;
  }

  60% {
    right: -90%;
    left: 100%;
  }

  100% {
    right: -90%;
    left: 100%;
  }
}

@keyframes indeterminate {
  0% {
    right: 100%;
    left: -35%;
  }

  60% {
    right: -90%;
    left: 100%;
  }

  100% {
    right: -90%;
    left: 100%;
  }
}

/*! CSS Used fontfaces */
@font-face {
  font-family: "Noto Sans Japanese";
  font-style: normal;
  font-weight: 300;
  font-display: auto;
  src: local("Noto Sans CJK JP Regular"), local("NotoSansCJKjp-Regular"), local("NotoSansJP-Regular"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff) format("woff");
}

@font-face {
  font-family: "Noto Sans Japanese";
  font-style: normal;
  font-weight: 700;
  src: local("Noto Sans CJK JP Bold"), local("NotoSansCJKjp-Bold"), local("NotoSansJP-Bold"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff) format("woff");
}

@font-face {
  font-family: MySansSerif;
  font-weight: normal;
  font-display: auto;
  src: local("HelveticaNeue"), local("Helvetica Neue"), local("Helvetica"), local("Arial");
}

@font-face {
  font-family: MySansSerif;
  font-weight: 700;
  font-display: auto;
  src: local("HelveticaNeueBold"), local("HelveticaNeue-Bold"), local("Helvetica Neue Bold"), local("HelveticaBold"), local("Helvetica-Bold"), local("Helvetica Bold"), local("Arial Bold");
}

@font-face {
  font-family: MySansSerif;
  font-weight: 900;
  font-display: auto;
  src: local("HelveticaNeueBlack"), local("HelveticaNeue-Black"), local("Helvetica Neue Black"), local("HelveticaBlack"), local("Helvetica-Black"), local("Helvetica Black"), local("Arial Black");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 100;
  font-display: auto;
  src: local("HiraginoSans-W1"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 200;
  font-display: auto;
  src: local("HiraginoSans-W2"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 300;
  font-display: auto;
  src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 400;
  font-display: auto;
  src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 500;
  font-display: auto;
  src: local("HiraginoSans-W5"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 600;
  src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 700;
  font-display: auto;
  src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 800;
  font-display: auto;
  src: local("HiraginoSans-W7"), local("Hiragino Sans");
}

@font-face {
  font-family: "HiraginoCustom";
  font-weight: 900;
  font-display: auto;
  src: local("HiraginoSans-W8"), local("Hiragino Sans");
}

@font-face {
  font-family: MyYugo;
  font-weight: normal;
  font-display: auto;
  src: local("YuGothic-Medium"), local("Yu Gothic Medium"), local("YuGothic-Regular");
}

@font-face {
  font-family: MyYugo;
  font-weight: bold;
  font-display: auto;
  src: local("YuGothic-Bold"), local("Yu Gothic");
}

@font-face {
  font-family: "YakuHanJP";
  font-style: normal;
  font-weight: 500;
  src: url(https://assign-navi.jp/assets/font/YakuHanJP-Medium.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 300;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}


/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }
  }

  :disabled {
    pointer-events: none !important;
  }

  #sidenav-overlay {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 997;
    height: 120vh;
    background-color: rgba(50, 50, 50, 0.5);
    will-change: opacity;
  }

  #sidenav-overlay {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 997;
    height: 120vh;
    background-color: rgba(50, 50, 50, 0.5);
    will-change: opacity;
  }

  :focus {
    outline: 0;
  }
}

/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  ul {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  ul ul {
    margin-bottom: 0;
  }

  a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
  }

  a:hover {
    color: #0056b3;
    text-decoration: underline;
  }

  .d-block {
    display: block !important;
  }

  .d-flex {
    display: flex !important;
  }

  .justify-content-between {
    justify-content: space-between !important;
  }

  .align-items-center {
    align-items: center !important;
  }

  .w-50 {
    width: 50% !important;
  }

  .mx-3 {
    margin-right: 1rem !important;
  }

  .mx-3 {
    margin-left: 1rem !important;
  }

  .pt-3,
  .py-3 {
    padding-top: 1rem !important;
  }

  .px-3 {
    padding-right: 1rem !important;
  }

  .py-3 {
    padding-bottom: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .text-center {
    text-align: center !important;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }

    a:not(.btn) {
      text-decoration: underline;
    }
  }

  :disabled {
    pointer-events: none !important;
  }

  a {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    transition: all .2s ease-in-out;
  }

  a:hover {
    color: #0056b3;
    text-decoration: none;
    transition: all .2s ease-in-out;
  }

  a:disabled:hover {
    color: #007bff;
  }

  .waves-effect {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  a.waves-effect,
  a.waves-light {
    display: inline-block;
  }

  .ex-bold {
    font-weight: 700 !important;
  }

  .font-middle {
    font-size: 1rem !important;
  }

  .custom-grey-3-text {
    color: #d5d9db;
  }

  .pt-3,
  .py-3 {
    padding-top: 1rem !important;
  }

  .px-3 {
    padding-right: 1rem !important;
  }

  .py-3 {
    padding-bottom: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .mx-3 {
    margin-right: 1rem !important;
  }

  .mx-3 {
    margin-left: 1rem !important;
  }

  body a {
    color: #1072e9;
  }

  body a:hover {
    color: #1072e9;
  }

  body header a {
    color: rgba(0, 0, 0, 0.87);
    overflow: visible;
  }

  body header a:hover {
    opacity: .7;
  }

  :focus {
    outline: 0;
  }

  ul {
    list-style: none;
    padding: 0;
  }
}

/*! CSS Used from: Embedded */
ul[data-v-6d56596f] {
  border-bottom: 1px solid #e6eaec;
}

ul li[data-v-6d56596f] {
  padding-bottom: 1rem;
  transition: opacity .2s ease-in-out;
}

#app {
  margin-top: 17vh;
}

/* Common margin-header class for consistent spacing from header */
.margin-header {
  margin-top: calc(var(--header-height-desktop) + 20px); /* Desktop header height + padding */
  padding-top: 0;
}

/* Breadcrumb spacing moved to components/breadcrumb.css */

ul li[data-v-6d56596f]:hover {
  cursor: pointer;
  opacity: .6;
  color: #1072e9;
  transition: opacity, color .2s ease-in-out;
}

.tab-active[data-v-6d56596f] {
  padding-bottom: calc(1rem - 2px);
  border-bottom: 2px solid #1072e9;
  color: #1072e9;
}

/*! CSS Used from: Embedded */
.separater[data-v-31c30a8b] {
  border-bottom: 1px solid #e6eaec;
}

.link-to-index[data-v-31c30a8b] {
  color: #1072e9;
}

.link-to-index[data-v-31c30a8b]:hover {
  color: #1072e9;
  background-color: #eaf8f7;
  opacity: .6;
}

.header-tab-menu-wrapper[data-v-31c30a8b] {
  position: absolute;
  background-color: #fff;
  width: 432px;
  right: 0;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .12), 0px 2px 5px 0px rgba(0, 0, 0, .16);
  border-radius: 4px;
  z-index: 100;
}

@media screen and (max-width: 768px) {
  .header-tab-menu-wrapper[data-v-31c30a8b] {
    position: fixed;
    max-width: 364px;
    right: 4px;
  }
}

/* CSS Footer*/
/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  footer {
    display: block;
  }

  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  dl {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  dt {
    font-weight: 700;
  }

  dd {
    margin-bottom: .5rem;
    margin-left: 0;
  }

  a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
  }

  a:hover {
    color: #0056b3;
    text-decoration: underline;
  }

  img {
    vertical-align: middle;
    border-style: none;
  }

  .small {
    font-size: 80%;
    font-weight: 400;
  }

  .container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .col-12,
  .col-lg-3,
  .col-md-6 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
  }

  .col-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  @media (min-width: 768px) {
    .col-md-6 {
      flex: 0 0 50%;
      max-width: 50%;
    }
  }

  @media (min-width: 992px) {
    .col-lg-3 {
      flex: 0 0 25%;
      max-width: 25%;
    }
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  .mt-2 {
    margin-top: 0.5rem !important;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  .mb-5 {
    margin-bottom: 3rem !important;
  }

  .py-4 {
    padding-top: 1.5rem !important;
  }

  .py-4 {
    padding-bottom: 1.5rem !important;
  }

  .text-center {
    text-align: center !important;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }

    a:not(.btn) {
      text-decoration: underline;
    }

    img {
      page-break-inside: avoid;
    }

    p {
      orphans: 3;
      widows: 3;
    }
  }

  .white-text {
    color: #fff !important;
  }

  :disabled {
    pointer-events: none !important;
  }

  a {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    transition: all .2s ease-in-out;
  }

  a:hover {
    color: #0056b3;
    text-decoration: none;
    transition: all .2s ease-in-out;
  }

  a:disabled:hover {
    color: #007bff;
  }

  footer.page-footer {
    bottom: 0;
    color: #fff;
    background: linear-gradient(135deg, #00B0F0, #5B9BD5, #4472C4, #002060);
  }

  footer.page-footer .container-fluid {
    width: auto;
  }

  /* Quy tắc này được thay thế bởi quy tắc mới ở dưới */



  .fancy-button {
    background-color: rgb(5,188,254);
    color: black !important;
    font-weight: bold;
    padding: 20px 50px;
    border: none;
    border-radius: 0px !important;
    box-shadow:
      inset -5px -5px 15px rgba(255, 255, 255, 0.7),
      inset 5px 5px 15px rgba(0, 0, 0, 0.4),
      0 0 0 3px #0077aa,
      0 0 15px #0077aa;
    cursor: pointer;
  }
  .fancy-button:hover {
    background: linear-gradient(to bottom, #00bfff, #007acc);
  }

  .font-middle {
    font-size: 1rem !important;
  }

  .white-text {
    color: #fff;
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  .mt-2 {
    margin-top: .5rem !important;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  .py-4 {
    padding-top: 1.5rem !important;
  }

  .py-4 {
    padding-bottom: 1.5rem !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  body a {
    color: #1072e9 !important;
  }

  body a:hover {
    color: #1072e9;
  }

  /* Loại trừ thẻ a trong footer khỏi quy tắc body a */
  footer.page-footer a {
    color: #fff !important;
  }

  footer.page-footer a:hover {
    color: #1072e9 !important;
    text-decoration: none;
  }

  footer {
    width: 100%;
    height: auto;
    background-color: #78909c;
  }

  :focus {
    outline: 0;
  }

  dl {
    list-style: none;
    padding: 0;
  }
}

/* Additional Toastr CSS */
.toast {
  flex-basis: 350px;
  max-width: 350px;
  font-size: .875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  opacity: 0;
  border-radius: .25rem
}

.toast:not(:last-child) {
  margin-bottom: .75rem
}

.toast.showing {
  opacity: 1
}

.toast.show {
  display: block;
  opacity: 1
}

.toast.hide {
  display: none
}

.toast-header {
  display: flex;
  align-items: center;
  padding: .25rem .75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(.25rem - 1px);
  border-top-right-radius: calc(.25rem - 1px)
}

.toast-body {
  padding: .75rem
}

.md-toast-close-button:hover,
.md-toast-close-button:focus {
  color: #000 !important
}

.md-toast-title {
  font-weight: 400
}

.md-toast-message {
  word-wrap: break-word
}

.md-toast-message a:hover {
  color: #ccc;
  text-decoration: none
}

.md-toast-close-button {
  position: relative;
  top: -0.3em;
  right: -0.3em;
  float: right;
  font-size: 1.25rem;
  font-weight: 400;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=80);
  opacity: .8
}

.md-toast-close-button:hover,
.md-toast-close-button:focus {
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=40);
  opacity: .4
}

button.md-toast-close-button {
  padding: 0;
  cursor: pointer;
  background: rgba(0, 0, 0, 0);
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

.md-toast-top-center {
  top: 0;
  right: 0;
  width: 100%
}

.md-toast-bottom-center {
  right: 0;
  bottom: 0;
  width: 100%
}

.md-toast-top-full-width {
  top: 0;
  right: 0;
  width: 100%
}

.md-toast-bottom-full-width {
  right: 0;
  bottom: 0;
  width: 100%
}

.md-toast-top-left {
  top: 12px;
  left: 12px
}

.md-toast-top-right {
  top: 12px;
  right: 12px
}

.md-toast-bottom-right {
  right: 12px;
  bottom: 12px
}

.md-toast-bottom-left {
  bottom: 12px;
  left: 12px
}

#toast-container {
  position: fixed;
  z-index: 999999
}

#toast-container * {
  box-sizing: border-box
}

#toast-container>div {
  position: relative;
  width: 18.75rem;
  padding: 15px 15px 15px 50px;
  margin: 0 0 6px;
  overflow: hidden;
  filter: alpha(opacity=95);
  background-repeat: no-repeat;
  background-position: 15px center;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  opacity: .95
}

#toast-container>:hover {
  cursor: pointer;
  filter: alpha(opacity=100);
  box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  opacity: 1;
  transition: .45s
}

#toast-container.md-toast-top-center>div,
#toast-container.md-toast-bottom-center>div {
  width: 18.75rem;
  margin: auto
}

#toast-container.md-toast-top-full-width>div,
#toast-container.md-toast-bottom-full-width>div {
  width: 96%;
  margin: auto
}

.md-toast {
  background-color: #030303
}

.md-toast-success {
  background-color: #00c851;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==") !important
}

.md-toast-error {
  background-color: #ff285b;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=") !important
}

.md-toast-info {
  background-color: #33b5e5;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=") !important
}

.md-toast-warning {
  background-color: #fb3;
  background-image: url(/assets/img/common/outline_warning_white_24dp-d6716e15b9bcd2a6cabda273f44e820f4afee186d282e647a1b7305fab1e70ce.png) !important
}

.md-toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background-color: #000;
  filter: alpha(opacity=40);
  opacity: .4
}

.navbar-text {
  padding: 0 !important;
  font-size: 1rem;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  line-height: 1.2;
  max-height: 2.4rem;
  max-width: 18rem;
  white-space: normal;
  word-break: break-word;
}

#toast-container.toast-top-full-width {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  top: 80px;
  width: 100%
}

#toast-container.toast-top-full-width>div {
  width: 100%;
  opacity: 1;
  margin-bottom: 0;
  border-radius: 0
}

#toast-container.toast-top-full-width .toast-info {
  background-color: #51a351
}

#toast-container.toast-top-full-width .toast-warning {
  background-color: #bd362f
}

.toast-title {
  font-weight: bold
}

.toast-message {
  word-wrap: break-word
}

.toast-message a,
.toast-message label {
  color: #FFF
}

.toast-message a:hover {
  color: #CCC;
  text-decoration: none
}

.toast-top-left {
  top: 12px;
  left: 12px
}

.toast-bottom-right {
  right: 12px;
  bottom: 12px
}

.toast-bottom-left {
  left: 12px;
  bottom: 12px
}

#toast-container {
  position: fixed;
  z-index: 9999
}

#toast-container>div {
  background-position: 15px center;
  background-repeat: no-repeat;
  border-radius: 3px 3px 3px 3px;
  -o-box-shadow: 0 0 12px #999999;
  box-shadow: 0 0 12px #999999;
  color: #FFFFFF;
  margin: 0 0 6px;
  filter: alpha(opacity=80);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  opacity: 0.8;
  padding: 15px 15px 15px 50px;
  width: 300px
}

.toast {
  background-color: #030303
}

.toast-success {
  background-color: #51A351
}

.toast-error {
  background-color: #BD362F
}

.toast-info {
  background-color: #2F96B4
}

.toast-warning {
  background-color: #F89406
}

.toast-top-right {
  top: 12px;
  right: 12px
}

#toast-container>:hover {
  -o-box-shadow: 0 0 12px #000000;
  box-shadow: 0 0 12px #000000;
  filter: alpha(opacity=100);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  opacity: 1;
  cursor: pointer
}

#toast-container>.toast-info {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=") !important
}

#toast-container>.toast-error {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=") !important
}

#toast-container>.toast-success {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==") !important
}

#toast-container>.toast-warning {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=") !important
}

.picker__box .picker__header .picker__date-display,
.picker__box .picker__table .picker__day--outfocus,
.picker__box .picker__table .picker__day--selected,
.picker__box .picker__table .picker__day--selected:hover,
.picker__box .picker__table .picker--focused,
.clockpicker_container.clockpicker .picker__box .clockpicker-am-pm-block .active,
.picker__list-item--selected,
.picker__list-item--selected:hover,
.picker--focused .picker__list-item--selected,
.picker--time .picker__button--clear:hover,
.picker--time .picker__button--clear:focus,
.picker--time .picker__button--clear:hover::before,
.picker--time .picker__button--clear:focus::before,
.picker__date-display,
.picker__date-display .clockpicker-display .clockpicker-display-column .clockpicker-span-hours.text-primary,
.picker__date-display .clockpicker-display .clockpicker-display-column .clockpicker-span-minutes.text-primary,
.picker__date-display .clockpicker-display .clockpicker-display-column #click-am.text-primary,
.picker__date-display .clockpicker-display .clockpicker-display-column #click-pm.text-primary,
.clockpicker-display .clockpicker-display-column .clockpicker-span-hours.text-primary,
.clockpicker-display .clockpicker-display-column .clockpicker-span-minutes.text-primary,
.clockpicker-display .clockpicker-display-column #click-am.text-primary,
.clockpicker-display .clockpicker-display-column #click-pm.text-primary,
.darktheme .picker__box .picker__date-display .clockpicker-display,
.darktheme .picker__box .picker__date-display .clockpicker-display .clockpicker-span-am-pm,
.darktheme .picker__box .picker__calendar-container .clockpicker-plate .clockpicker-tick,
.darktheme .picker__box .picker__footer button,
.md-toast-message a,
.md-toast-message label,
.md-toast-close-button,
#toast-container>div,
.side-nav2,
.side-nav2 .search-form .form-control,
.side-nav,
.side-nav .search-form .form-control,
.dropdown .dropdown-menu .dropdown-item:hover,
.dropdown .dropdown-menu .dropdown-item:active,
.dropup .dropdown-menu .dropdown-item:hover,
.dropup .dropdown-menu .dropdown-item:active,
.dropleft .dropdown-menu .dropdown-item:hover,
.dropleft .dropdown-menu .dropdown-item:active,
.dropright .dropdown-menu .dropdown-item:hover,
.dropright .dropdown-menu .dropdown-item:active {
  color: #fff !important
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #dc3545
}

input:focus,
textarea:focus,
select:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: #ccc !important;
  /* Có thể đổi màu theo ý muốn */
}

.noUi-connect {
  background: #1072e9 !important;
}


footer.page-footer {
  background-image: url('/custom_frontend/static/img/bg_footer.png') !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  padding-bottom: 13vw !important;
}

.home-footer {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  .home-logo {
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
          width: 10rem;
      }
  }
  .home-directive {
      max-width: 100% !important;
  }
}


.header-bg {
  background: linear-gradient(135deg, #00B0F0, #5B9BD5, #4472C4, #002060) !important;
  background-position: center;
  background-size: cover;
}

@media screen and (max-width: 767px) {

  #app {
      margin-top: 68px;
  }

  /* Mobile responsive for margin-header */
  .margin-header {
    margin-top: calc(var(--header-height-mobile) + 8px) !important;
    padding-top: 0 !important;
  }

  /* Mobile responsive breadcrumb spacing */
  .breadcrumbs {
    padding-top: 5px !important;
    padding-bottom: 10px !important;
    font-size: 0.7rem !important;
  }

  .breadcrumbs a,
  .breadcrumbs span {
    padding: 6px 2px 5px !important;
  }

  .navbar.navbar-toggleable-md.navbar-expand-md.navbar_boxshadow.pb-0 {
    padding-left: 1.8rem !important;
  }

  /* Expand menu tab width for mobile to display text in single column */
  body header .header-search-menu{
    max-width: none !important;
    width: 48% !important;
    min-width: 173px !important;
  }

  body header .header-search-menu .find {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  body header .navbar-left>ul li ul.big_ul {
    max-width: none !important;
    width: 48% !important;
    min-width: 173px !important;
  }

  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu {
    left: 100%;  /* Submenu xổ sang ngang bên phải */
    top: 0;      /* Căn chỉnh với top của parent menu item */
    max-width: none !important;
    width: 200px !important;  /* Độ rộng cố định cho submenu */
    min-width: 200px !important;
    border-radius: 0 15px 15px 0;  /* Bo góc phải */
  }

  /* Mobile submenu positioning adjustments */
  body header .navbar-left>ul li ul.big_ul .menu-item-parent {
    position: relative;
  }

  /* Adjust submenu position if it would overflow screen */
  body header .navbar-left>ul li ul.big_ul .menu-item-parent:last-child .submenu,
  body header .navbar-left>ul li ul.big_ul #div-surround-2 .submenu {
    left: -100%;  /* Submenu xổ sang trái cho item cuối và "登録・管理" */
    border-radius: 15px 0 0 15px;  /* Bo góc trái */
  }

  /* Ensure submenu doesn't overflow on very small screens */
  @media screen and (max-width: 480px) {
    body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu {
      width: 183px !important;
      min-width: 180px !important;
    }

    /* For very small screens, show submenu below if needed */
    body header .navbar-left>ul li ul.big_ul .menu-item-parent:last-child .submenu,
    body header .navbar-left>ul li ul.big_ul #div-surround-2 .submenu {
      left: 100%;
      top: 60%;  /* Adjust for smaller screens */
    }
  }

  /* Ensure submenu is clickable on mobile - override desktop hover */
  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu {
    display: none !important;  /* Hidden by default, shown via JavaScript */
  }

  /* Mobile nav styling */
  @media (max-width: 767px) {
    nav.navbar.navbar-toggleable-md.navbar-expand-md.navbar-boxshadow {
      text-align: center !important;
      justify-content: center !important;
      align-items: center !important;
    }

    body header .navbar-left > ul {
      text-align: center !important;
      justify-content: center !important;
      align-items: center !important;
    }

    body header .navbar-left > ul li {
      text-align: center !important;
    }

    body header .navbar-left > ul li ul.big_ul {
      text-align: center !important;
      justify-content: center !important;
      align-items: center !important;
    }
  }

  /* Show submenu when explicitly shown via JavaScript */
  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu.mobile-show {
    display: block !important;
  }

  /* Expand logo area to full screen width for mobile */
  .slogan {
    width: calc(100vw - 250px) !important;
    max-width: calc(100vw - 250px) !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  #app {
      margin-top: 0;
  }

  /* Tablet responsive for margin-header */
  .margin-header {
    margin-top: calc(var(--header-height-tablet) + 15px) !important;
    padding-top: 0 !important;
  }

  /* Tablet responsive breadcrumb spacing */
  .breadcrumbs {
    padding-top: 8px !important;
    padding-bottom: 12px !important;
  }

  /* Expand menu tab width for tablet to display text in single column */
  body header .header-search-menu{
    width: 35% !important;
    min-width: 300px !important;
    max-width: none !important;
  }

  body header .header-search-menu .find {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  body header .navbar-left>ul li ul.big_ul {
    width: 35% !important;
    min-width: 300px !important;
    max-width: none !important;
  }

  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu {
    min-width: 220px !important;
  }

  /* Expand logo area to full screen width for tablet */
  .slogan {
    width: calc(100vw - 500px) !important;
    max-width: calc(100vw - 500px) !important;
  }
}

/* Desktop large screens */
@media screen and (min-width: 1024px) {
  /* Desktop breadcrumb spacing moved to components/breadcrumb.css */
}

.d-flexft {
  display: flex ;
}
@media screen and (max-width: 700px) {
    .body header .navbar-brand img {
      width: 115px;
    }
    .slogan {
      width: calc(150vw - 230px) !important;
      max-width: calc(160vw - 230px) !important;
      margin-left: -4px!important;
      height: 35px!important;
    }
    .d-flexft {
      display: block;
    }
    .col-6ft {
      margin-bottom: 39vw;
  }

}
@media (min-width: 768px) and (max-width: 1023px) {
  .body header .navbar-brand img {
    width: 115px;
  }
  .slogan {
    width: calc(136vw - 480px) !important;
    max-width: calc(128vw - 470px) !important;
    margin-left: -7px!important;
  }
}

/* Ẩn breadcrumb trên mobile cho tất cả các trang */
@media (max-width: 767px) {
  .breadcrumbs {
    display: none !important;
  }
}

@media screen and (min-width: 1280px) {
  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu{
    min-width: 103% !important;
  }
}

@media screen and (min-width: 1024px) and (max-width: 1279px) {
  body header .navbar-left>ul li ul.big_ul .menu-item-parent .submenu{
    min-width: 105.5% !important;
  }
}
