import re
import requests
import urllib.parse
from odoo import http
from odoo.http import request, Response
import json
from . import common as global_common

class NotificationController(http.Controller):
    @http.route('/api/send_notification', type='json', auth='public', methods=['POST'], csrf=False)
    def send_notification(self, **kwargs):
        data = request.httprequest.get_json(silent=True)

        user_id = data.get('user_id')
        message = data.get('message')
        resume_id = data.get('resume_id')
        created_by = data.get('created_by')
        is_read = data.get('is_read')
        created_at = data.get('created_at')

        required_fields = [
            user_id, message, resume_id, created_by, is_read, created_at
        ]

        if any(field in [None, ""] for field in required_fields):
                return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE,}

        db_notif = request.env['vit.notification'].sudo().create({
            'user_id': user_id,
            'message': message,
            'resume_id': resume_id,
            'created_by': created_by,
            'is_read': is_read,
            'created_at': created_at,
        })
        
        if db_notif:
            return {
                'success': True,
                'message': 'Send notification successfully',
            }
            
    @http.route('/api/get_number_msg', type='http', auth='public', methods=['GET'], csrf=False)
    def get_number_msg(self, **kwargs):
        company_id = kwargs.get("id")

        if not company_id:
            return Response(json.dumps({"success": False, "message": "Missing company_id"}), content_type="application/json", status=400)

        try:
            query_resume = "SELECT id FROM vit_partner WHERE company_id = %s"
            request.env.cr.execute(query_resume, (int(company_id),))
            resume_ids = [row[0] for row in request.env.cr.fetchall()]

            if not resume_ids:
                return Response(json.dumps({"success": True, "total": 0, "unread_count": 0, "notifications": []}), content_type="application/json", status=200)

            resume_ids_tuple = tuple(resume_ids) if len(resume_ids) > 1 else (resume_ids[0],) if resume_ids else (-1,)

            query_total = "SELECT COUNT(*) FROM vit_notification WHERE resume_id IN %s"
            request.env.cr.execute(query_total, (resume_ids_tuple,))
            total_count = request.env.cr.fetchone()[0]

            query_noti = "SELECT id, message, created_at, resume_id FROM vit_notification WHERE resume_id IN %s AND is_read = FALSE"
            request.env.cr.execute(query_noti, (resume_ids_tuple,))
            notifications = [{"id": row[0], "message": row[1], "created_at": row[2], "resume_id": row[3]} for row in request.env.cr.fetchall()]

            return Response(json.dumps({"success": True, "total": total_count, "unread_count": len(notifications), "notifications": notifications}), content_type="application/json", status=200)

        except Exception as e:
            return Response(json.dumps({"success": False, "message": str(e)}), content_type="application/json", status=500)

