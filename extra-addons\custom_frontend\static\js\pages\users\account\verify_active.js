const verify_active = {
    'template': `
    <main class="pb-3 margin-header" id="vue-app">
        <div class="container-fluid"></div>
        <div class="container-fluid grabient pt-2">
            <div class="row">
                <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                    <div class="card px-0 px-md-4 py-3 mb-4 wow fadeIn animated" data-wow-delay="0.3s" style="visibility: visible; animation-name: fadeIn; animation-iteration-count: 1; animation-delay: 0.3s;">
                        <div class="card-body">
                            <div class="mx-auto py-4 px-md-4">
                                <div class="col-12 col-md-10 mx-auto">
                                    <ul>
                                        <li>登録完了しました。</li>
                                        <li>ご登録いただいたメールアドレスとパスワードでログインしてください。</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-12 px-0">
                                <div class="col-12 col-md-6 mx-auto text-center px-0"><button onclick="location.href='/login'" class="btn btn-default btn-block btn-lg font-middle px-0 waves-effect waves-light">ログイン画面へ</button></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/users/account/active.css"/>
    `,
    data(){
        return {
            token: '',
        };
    },
    methods:{
        async validateToken() {
            const urlParams = new URLSearchParams(window.location.search);
            this.token = urlParams.get('token');
            console.log(this.token);
            if (!this.token) {
                window.location.href = "/404";
            }
            try {
                const response = await fetch("/api/active_account", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ token: this.token }),
                });

                const data = await response.json();
                if (data.result.success) {
                    console.log('success');
                } else {
                    window.location.href = "/404";
                }
            } catch (error) {

            }
        }
    },
    mounted(){
        this.validateToken();
    }
}
export default verify_active;