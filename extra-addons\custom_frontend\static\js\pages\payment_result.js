import { createSingleBreadcrumb } from "../utils/breadcrumbHelper.js";

const PaymentResult = {
    'template': `
        <main class="pb-3 margin-header" id="vue-app">
            ${createSingleBreadcrumb('決済結果')}
            <div class="container-fluid grabient pt-5 position-relative">
                <div class="row mb-4 mb-md-0 justify-content-center">
                    <div class="col-12 col-md-8 col-lg-6 mb-4 mb-md-0">
                        <div class="card px-4 py-5 text-center" v-if="success">
                            <div class="success-icon mb-4">
                                <i class="material-icons" style="font-size: 64px; color: #4CAF50;">check_circle</i>
                            </div>
                            <h2 class="mb-4">決済が完了しました</h2>
                            <div v-if="isNyukai">
                                <p class="mb-4">ご入会ありがとうございます。プランの登録が完了しました。</p>
                                <p class="mb-4">これからMi52のサービスをご利用いただけます。</p>
                            </div>
                            <div v-else>
                                <p class="mb-4">ご選択いただいたプランへの変更が完了しました。</p>
                                <p class="mb-4">新しいプランは即時に適用されます。</p>
                            </div>
                            <div class="plan-info my-4 py-3 px-4" style="background-color: #f8f9fa; border-radius: 8px;">
                                <div class="row align-items-center">
                                    <div class="col-md-6 text-md-right">
                                        <span style="font-weight: bold; font-size: 18px;">ご契約プラン:</span>
                                    </div>
                                    <div class="col-md-6 text-md-left">
                                        <span style="font-weight: bold; font-size: 20px; color: #1976D2;">{{ plan }}</span>
                                    </div>
                                </div>
                                <div class="row align-items-center mt-2">
                                    <div class="col-md-6 text-md-right">
                                        <span style="font-weight: bold; font-size: 18px;">決済日時:</span>
                                    </div>
                                    <div class="col-md-6 text-md-left">
                                        <span>{{ currentDate }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center mt-5">
                                <div class="col-6 col-md-4 mt-2">
                                    <a href="/mypage" class="btn btn-block btn-primary waves-effect waves-light" style="color: white">マイページへ</a>
                                </div>
                            </div>
                        </div>
                        <div class="card px-4 py-5 text-center" v-else>
                            <div class="error-icon mb-4">
                                <i class="material-icons" style="font-size: 64px; color: #F44336;">error</i>
                            </div>
                            <h2 class="mb-4">決済に失敗しました</h2>
                            <p class="mb-4">申し訳ありませんが、決済処理中にエラーが発生しました。</p>

                            <div class="error-details my-4 py-3 px-4" style="background-color: #fff8f8; border-radius: 8px; border: 1px solid #ffcdd2;">
                                <div class="row">
                                    <div class="col-12 text-left">
                                        <p class="mb-2"><strong>エラーコード:</strong> {{ errorCode }}</p>
                                        <p class="mb-0"><strong>考えられる原因:</strong></p>
                                        <ul class="text-left mt-2">
                                            <li v-if="errorCode === 'card_error'">カード情報が正しくありません。カード番号、有効期限、セキュリティコードをご確認ください。</li>
                                            <li v-else-if="errorCode === 'expired_card'">カードの有効期限が切れています。</li>
                                            <li v-else-if="errorCode === 'insufficient_funds'">カードの残高が不足しています。</li>
                                            <li v-else-if="errorCode === 'processing_error'">決済処理中にエラーが発生しました。しばらく経ってからもう一度お試しください。</li>
                                            <li v-else>決済処理中に問題が発生しました。カード情報をご確認いただくか、別のカードでお試しください。</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <p class="mt-4 mb-2">以下のいずれかの方法でもう一度お試しください：</p>
                            <ul class="text-left">
                                <li>カード情報を再確認する</li>
                                <li>別のクレジットカードを使用する</li>
                                <li>しばらく時間をおいてから再度試す</li>
                            </ul>

                            <div class="row justify-content-center mt-5">
                                <div class="col-6 col-md-4 mt-2">
                                    <a :href="retryUrl" class="btn btn-block btn-primary waves-effect waves-light" style="color: white">再試行する</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/new_payment.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            success: false,
            plan: '',
            errorCode: '',
            isNyukai: false,
            currentDate: '',
            paymentType: '',
            transactionId: '',
            retryUrl: '/mypage/plan'
        }
    },
    mounted() {
        // URLからクエリパラメータを取得
        const urlParams = new URLSearchParams(window.location.search);

        // 現在の日時をフォーマット
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        this.currentDate = `${year}年${month}月${day}日 ${hours}:${minutes}`;

        if (urlParams.has('success') && urlParams.get('success') === 'true') {
            this.success = true;
            this.plan = urlParams.get('plan') || 'プレミアム会員';
            this.isNyukai = urlParams.has('type') && urlParams.get('type') === 'nyukai';
            this.paymentType = this.isNyukai ? '新規入会' : 'プラン変更';
            this.transactionId = urlParams.get('transaction_id') || '';
        } else {
            this.success = false;
            this.errorCode = urlParams.get('error') || 'unknown';

            // 再試行URLを設定
            if (urlParams.has('type') && urlParams.get('type') === 'nyukai') {
                this.retryUrl = '/nyukai/plan';
                this.isNyukai = true;
            } else {
                this.retryUrl = '/mypage/plan';
                this.isNyukai = false;
            }
        }
    }
}

export default PaymentResult;
