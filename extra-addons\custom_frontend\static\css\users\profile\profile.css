/*! CSS Used from: https://assign-navi.jp/assets/application-5c62c0bf10dc71ffa28617d13da03c81eeb71d47f2b1b1f657b8b6641e347cff.css ; media=screen */
@media screen{
*,::after,::before{box-sizing:border-box;}
main{display:block;}
[tabindex="-1"]:focus:not(:focus-visible){outline:0!important;}
h1,h4{margin-top:0;margin-bottom:.5rem;}
p{margin-top:0;margin-bottom:1rem;}
ul{margin-top:0;margin-bottom:1rem;}
ul ul{margin-bottom:0;}
a{color:#007bff;text-decoration:none;background-color:transparent;}
a:hover{color:#0056b3;text-decoration:underline;}
label{display:inline-block;margin-bottom:.5rem;}
button{border-radius:0;}
button:focus:not(:focus-visible){outline:0;}
button,input,select,textarea{margin:0;font-family:inherit;font-size:inherit;line-height:inherit;}
button,input{overflow:visible;}
button,select{text-transform:none;}
select{word-wrap:normal;}
[type=button],[type=submit],button{-webkit-appearance:button;}
[type=button]:not(:disabled),[type=submit]:not(:disabled),button:not(:disabled){cursor:pointer;}
input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0;}
textarea{overflow:auto;resize:vertical;}
h1,h4{margin-bottom:.5rem;font-weight:500;line-height:1.2;}
h1{font-size:2.5rem;}
h4{font-size:1.5rem;}
.container-fluid{width:100%;padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto;}
.row{display:flex;flex-wrap:wrap;margin-right:-15px;margin-left:-15px;}
.col-12,.col-4,.col-lg-3,.col-lg-9,.col-md-4,.col-md-6,.col-md-8,.col-sm-2,.col-sm-4{position:relative;width:100%;padding-right:15px;padding-left:15px;}
.col-4{flex:0 0 33.333333%;max-width:33.333333%;}
.col-12{flex:0 0 100%;max-width:100%;}
@media (min-width: 576px){
.col-sm-2{flex:0 0 16.666667%;max-width:16.666667%;}
.col-sm-4{flex:0 0 33.333333%;max-width:33.333333%;}
}
@media (min-width: 768px){
.col-md-4{flex:0 0 33.333333%;max-width:33.333333%;}
.col-md-6{flex:0 0 50%;max-width:50%;}
.col-md-8{flex:0 0 66.666667%;max-width:66.666667%;}
}
@media (min-width: 992px){
.col-lg-3{flex:0 0 25%;max-width:25%;}
.col-lg-9{flex:0 0 75%;max-width:75%;}
}
.form-control{display:block;width:100%;height:calc(1.5em + .75rem + 2px);padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:.25rem;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
@media (prefers-reduced-motion: reduce){
.form-control{transition:none;}
}
.form-control:focus{color:#495057;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 0.2rem rgba(0,123,255,0.25);}
.form-control::placeholder{color:#6c757d;opacity:1;}
.form-control:disabled,.form-control[readonly]{background-color:#e9ecef;opacity:1;}
textarea.form-control{height:auto;}
.form-check{position:relative;display:block;padding-left:1.25rem;}
.form-check-input{position:absolute;margin-top:.3rem;margin-left:-1.25rem;}
.form-check-input:disabled~.form-check-label{color:#6c757d;}
.form-check-label{margin-bottom:0;}
.form-inline{display:flex;flex-flow:row wrap;align-items:center;}
.form-inline .form-check{width:100%;}
@media (min-width: 576px){
.form-inline label{display:flex;align-items:center;justify-content:center;margin-bottom:0;}
.form-inline .form-check{display:flex;align-items:center;justify-content:center;width:auto;padding-left:0;}
.form-inline .form-check-input{position:relative;flex-shrink:0;margin-top:0;margin-right:.25rem;margin-left:0;}
}
.btn{display:inline-block;font-weight:400;color:#fff !important;text-align:center;vertical-align:middle;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
@media (prefers-reduced-motion: reduce){
.btn{transition:none;}
}
.btn:hover{color:#fff !important;text-decoration:none;}
.btn:focus{outline:0;box-shadow:0 0 0 0.2rem rgba(0,123,255,0.25);}
.btn:disabled{opacity:.65;}
.btn:not(:disabled):not(.disabled){cursor:pointer;}
.btn-lg{padding:.5rem 1rem;font-size:1.25rem;line-height:1.5;border-radius:.3rem;}
.btn-sm{padding:.25rem .5rem;font-size:.875rem;line-height:1.5;border-radius:.2rem;}
.btn-block{display:block;width:100%;}
.custom-control{position:relative;z-index:1;display:block;min-height:1.5rem;padding-left:1.5rem;-webkit-print-color-adjust:exact;color-adjust:exact;}
.custom-control-input{position:absolute;left:0;z-index:-1;width:1rem;height:1.25rem;opacity:0;}
.custom-control-input:checked~.custom-control-label::before{color:#fff;border-color:#007bff;background-color:#007bff;}
.custom-control-input:focus~.custom-control-label::before{box-shadow:0 0 0 0.2rem rgba(0,123,255,0.25);}
.custom-control-input:focus:not(:checked)~.custom-control-label::before{border-color:#80bdff;}
.custom-control-input:not(:disabled):active~.custom-control-label::before{color:#fff;background-color:#b3d7ff;border-color:#b3d7ff;}
.custom-control-input:disabled~.custom-control-label{color:#6c757d;}
.custom-control-input:disabled~.custom-control-label::before{background-color:#e9ecef;}
.custom-control-label{position:relative;margin-bottom:0;vertical-align:top;}
.custom-control-label::before{position:absolute;top:.25rem;left:-1.5rem;display:block;width:1rem;height:1rem;pointer-events:none;content:"";background-color:#fff;border:#adb5bd solid 1px;}
.custom-control-label::after{position:absolute;top:.25rem;left:-1.5rem;display:block;width:1rem;height:1rem;content:"";background:50%/50% 50% no-repeat;}
.custom-checkbox .custom-control-label::before{border-radius:.25rem;}
.custom-checkbox .custom-control-input:checked~.custom-control-label::after{background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");}
.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before{background-color:rgba(0,123,255,0.5);}
.custom-control-label::before{transition:background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
@media (prefers-reduced-motion: reduce){
.custom-control-label::before{transition:none;}
}
.card{position:relative;display:flex;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:1px solid rgba(0,0,0,0.125);border-radius:.25rem;}
.card-body{flex:1 1 auto;min-height:1px;padding:1.25rem;}
.badge-pill{padding-right:.6em;padding-left:.6em;border-radius:10rem;}
.badge-danger{color:#fff;background-color:#dc3545;}
.close{float:right;font-size:1.5rem;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;opacity:.5;}
.close:hover{color:#000;text-decoration:none;}
.close:not(:disabled):not(.disabled):focus,.close:not(:disabled):not(.disabled):hover{opacity:.75;}
button.close{padding:0;background-color:transparent;border:0;}
.modal{position:fixed;top:0;left:0;z-index:1050;display:none;width:100%;height:100%;overflow:hidden;outline:0;}
.modal-dialog{position:relative;width:auto;margin:.5rem;pointer-events:none;}
.modal-content{position:relative;display:flex;flex-direction:column;width:100%;pointer-events:auto;background-color:#fff;background-clip:padding-box;border:1px solid rgba(0,0,0,0.2);border-radius:.3rem;outline:0;}
.modal-header{display:flex;align-items:flex-start;justify-content:space-between;padding:1rem 1rem;border-bottom:none !important;border-top-left-radius:calc(.3rem - 1px);border-top-right-radius:calc(.3rem - 1px);}
.modal-header .close{padding:1rem 1rem;margin:-1rem -1rem -1rem auto;}
.modal-title{margin-bottom:0;line-height:1.5;}
.modal-body{position:relative;flex:1 1 auto;padding:1rem;}
@media (min-width: 576px){
.modal-dialog{max-width:500px;margin:1.75rem auto;}
}
.border-bottom{border-bottom:1px solid #dee2e6!important;}
.d-none{display:none!important;}
.d-inline-block{display:inline-block!important;}
.d-block{display:block!important;}
@media (min-width: 768px){
.d-md-block{display:block!important;}
}
.justify-content-center{justify-content:center!important;}
.position-relative{position:relative!important;}
.w-100{width:100%!important;}
.m-0{margin:0!important;}
.mt-0{margin-top:0!important;}
.mr-0{margin-right:0!important;}
.mb-0{margin-bottom:0!important;}
.ml-0{margin-left:0!important;}
.mb-1{margin-bottom:0.25rem!important;}
.mt-2{margin-top:0.5rem!important;}
.mb-2{margin-bottom:0.5rem!important;}
.ml-2{margin-left:0.5rem!important;}
.mt-3,.my-3{margin-top:1rem!important;}
.mb-3,.my-3{margin-bottom:1rem!important;}
.ml-3{margin-left:1rem!important;}
.mb-4{margin-bottom:1.5rem!important;}
.mb-5{margin-bottom:3rem!important;}
.p-0{padding:0!important;}
.px-0{padding-right:0!important;}
.px-0{padding-left:0!important;}
.p-1{padding:0.25rem!important;}
.py-1{padding-top:0.25rem!important;}
.py-1{padding-bottom:0.25rem!important;}
.pl-1{padding-left:0.25rem!important;}
.py-2{padding-top:0.5rem!important;}
.px-2{padding-right:0.5rem!important;}
.py-2{padding-bottom:0.5rem!important;}
.px-2{padding-left:0.5rem!important;}
.py-3{padding-top:1rem!important;}
.px-3{padding-right:1rem!important;}
.pb-3,.py-3{padding-bottom:1rem!important;}
.pl-3,.px-3{padding-left:1rem!important;}
.p-4{padding:1.5rem!important;}
.pl-4{padding-left:1.5rem!important;}
.p-5{padding:3rem!important;}
.pt-5{padding-top:3rem!important;}
.mx-auto{margin-right:auto!important;}
.mx-auto{margin-left:auto!important;}
@media (min-width: 768px){
.mb-md-0{margin-bottom:0!important;}
.my-md-1{margin-top:0.25rem!important;}
.my-md-1{margin-bottom:0.25rem!important;}
.pr-md-0{padding-right:0!important;}
.pl-md-0{padding-left:0!important;}
.px-md-3{padding-right:1rem!important;}
.px-md-3{padding-left:1rem!important;}
.py-md-4{padding-top:1.5rem!important;}
.py-md-4{padding-bottom:1.5rem!important;}
}
.text-center{text-align:center!important;}
@media print{
*,::after,::before{text-shadow:none!important;box-shadow:none!important;}
a:not(.btn){text-decoration:underline;}
p{orphans:3;widows:3;}
}
.pink.lighten-2{background-color:#ff6388!important;}
.pink{background-color:#ff285b!important;}
:disabled{pointer-events:none!important;}
a{color:#007bff;text-decoration:none;cursor:pointer;transition:all .2s ease-in-out;}
a:hover{color:#0056b3;text-decoration:none;transition:all .2s ease-in-out;}
a:disabled:hover{color:#007bff;}
a:not([href]):not([tabindex]),a:not([href]):not([tabindex]):focus,a:not([href]):not([tabindex]):hover{color:inherit;text-decoration:none;}
.collapsible-body{display:none;}
h1,h4{font-weight:300;}
.font-small{font-size:.9rem;}
.waves-effect{position:relative;overflow:hidden;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a.waves-effect,a.waves-light{display:inline-block;}
.btn{margin:.375rem;color:inherit;text-transform:uppercase;word-wrap:break-word;white-space:normal;cursor:pointer;border:0;border-radius:.25rem;box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;padding:.84rem 2.14rem;font-size:.81rem;}
.btn:hover,.btn:focus,.btn:active{outline:0;box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn.btn-block{margin:inherit;}
.btn.btn-lg{padding:1rem 2.4rem;font-size:.94rem;}
.btn.btn-sm{padding:.5rem 1.6rem;font-size:.64rem;}
.btn:disabled:hover,.btn:disabled:focus,.btn:disabled:active{box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);}
.btn[class*=btn-outline-]{padding-top:.7rem;padding-bottom:.7rem;}
.btn-outline-default{color:#00B0F0!important;background-color:rgba(0,0,0,0)!important;border:2px solid #00B0F0!important;}
.btn-outline-default:hover,.btn-outline-default:focus,.btn-outline-default:active,.btn-outline-default:active:focus{color:#00B0F0!important;background-color:rgba(0,0,0,0)!important;border-color:#00B0F0!important;}
.btn-outline-default:not([disabled]):not(.disabled):active{background-color:rgba(0,0,0,0)!important;border-color:#00B0F0!important;box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn-outline-default:not([disabled]):not(.disabled):active:focus{box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.selected-btn {
    background-color: #1072e9 !important;
    color: white !important;
}

.selected-btn:hover {
    background-color: #1072e9 !important;
    /* Giữ nguyên màu khi hover */
    color: #fff !important;
    opacity: 0.7 !important;
}
.custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background: url(https://assign-navi.jp/assets/img/common/check_white.png) !important;
    background-size: contain !important;
    top: 3px;
}
.btn-blue-grey{color:#fff;background-color:#78909c!important;}
.btn-blue-grey:hover{color:#fff;background-color:#879ca7;}
.btn-blue-grey:focus{box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn-blue-grey:focus,.btn-blue-grey:active{background-color:#4a5b64;}
.btn-blue-grey:not([disabled]):not(.disabled):active{background-color:#4a5b64!important;box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn-blue-grey:not([disabled]):not(.disabled):active:focus{box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.card{font-weight:400;border:0;box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);}
.card .card-body h4{font-weight:400;}
.badge-pill{padding-right:.5rem;padding-left:.5rem;border-radius:10rem;}
.badge-danger{color:#fff!important;background-color:#ff285b!important;}
.modal-dialog .modal-content{border:0;border-radius:.25rem;box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.modal-dialog .modal-content .modal-header{border-top-left-radius:.25rem;border-top-right-radius:.25rem;}
.modal{padding-right:0!important;}
button,html [type=button],[type=submit]{-webkit-appearance:none;-moz-appearance:none;appearance:none;}
a.btn:not([href]):not([tabindex]),a.btn:not([href]):not([tabindex]):focus,a.btn:not([href]):not([tabindex]):hover{color:#fff;}
.form-check-input:not(:checked),.form-check-input:checked{position:absolute;pointer-events:none;opacity:0;}
.form-check-input[type=radio]:not(:checked)+label,.form-check-input[type=radio]:checked+label{position:relative;display:inline-block;height:1.5625rem;padding-left:28px;line-height:1.5625rem;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;transition:.28s ease;}
.form-check-input[type=radio]+label:before,.form-check-input[type=radio]+label:after{position:absolute;top:0;left:0;z-index:0;width:18px;height:18px;margin:4px;content:"";transition:.28s ease;}
.form-check-input[type=radio]:not(:checked)+label:before,.form-check-input[type=radio]:not(:checked)+label:after,.form-check-input[type=radio]:checked+label:before,.form-check-input[type=radio]:checked+label:after{border-radius:50%;}
.form-check-input[type=radio]:not(:checked)+label:before,.form-check-input[type=radio]:not(:checked)+label:after{border:2px solid #d5d9db;}
.form-check-input[type=radio]:not(:checked)+label:after{transform:scale(0);}
.form-check-input[type=radio]:checked+label:before{border:2px solid rgba(0,0,0,0);}
.form-check-input[type=radio]:checked+label:after{border:2px solid #00B0F0;}
.form-check-input[type=radio]:checked+label:after{background-color:#00B0F0;}
.form-check-input[type=radio]:checked+label:after{transform:scale(1.02);}
.form-check-input[type=radio]:disabled:not(:checked)+label:before,.form-check-input[type=radio]:disabled:checked+label:before{background-color:rgba(0,0,0,0);border-color:rgba(0,0,0,0.46);}
.form-check-input[type=radio]:focus+label:before{border-color:#80bdff;box-shadow:0 0 0 0.2rem rgba(0,123,255,0.25);}
[type=checkbox]:not(:checked),[type=checkbox]:checked{position:absolute;pointer-events:none;opacity:0;}
.select-wrapper .select-dropdown{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}
.select-wrapper{position:relative;}
.select-wrapper:not(.md-outline) .select-dropdown:focus{border-bottom:1px solid #4285f4;box-shadow:0 1px 0 0 #4285f4;}
.select-wrapper input.select-dropdown{position:relative;z-index:2;display:block;width:100%;height:40px;padding:0;margin:0 0 .94rem 0;font-size:1rem;line-height:2.9rem;text-overflow:ellipsis;cursor:pointer;background-color:rgba(0,0,0,0);border:none;border-bottom:1px solid #ced4da;outline:none;}
.select-wrapper input.select-dropdown:disabled{color:rgba(0,0,0,0.3);cursor:default;border-bottom-color:rgba(0,0,0,0.2);}
.select-wrapper span.caret{position:absolute;top:.8rem;right:0;font-size:.63rem;color:#495057;}
.select-wrapper ul{padding-left:0;list-style-type:none;}
select{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;}
select.mdb-select.initialized{display:none!important;}
select:disabled{color:rgba(0,0,0,0.3);}
.dropdown-content{position:absolute;z-index:1021;display:none;min-width:6.25rem;max-height:40.625rem;margin:0;overflow-y:auto;background-color:#fff;box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);opacity:0;will-change:width,height;}
.dropdown-content li{width:100%;clear:both;line-height:1.3rem;color:#000;text-align:left;text-transform:none;cursor:pointer;}
.dropdown-content li:hover,.dropdown-content li.active{background-color:#eee;}
.dropdown-content li>span{display:block;padding:.5rem;font-size:.9rem;color:#4285f4;}
button:focus{outline:0!important;}
.font-middle{font-size:1rem!important;}
.font-large{font-size:1.125rem!important;}
.font-extralarge{font-size:1.25rem!important;}
.font-small{font-size:.75rem!important;}
.custom-grey-text{color:rgba(84,110,122,0.87);}
.custom-grey-5-text{color:#738a97;}
.p-0{padding:0!important;}
.px-0{padding-right:0!important;}
.px-0{padding-left:0!important;}
.m-0{margin:0!important;}
.mt-0{margin-top:0!important;}
.mr-0{margin-right:0!important;}
.mb-0{margin-bottom:0!important;}
.ml-0{margin-left:0!important;}
.p-1{padding:.25rem!important;}
.py-1{padding-top:.25rem!important;}
.py-1{padding-bottom:.25rem!important;}
.pl-1{padding-left:.25rem!important;}
.mb-1{margin-bottom:.25rem!important;}
.py-2{padding-top:.5rem!important;}
.px-2{padding-right:.5rem!important;}
.py-2{padding-bottom:.5rem!important;}
.px-2{padding-left:.5rem!important;}
.mt-2{margin-top:.5rem!important;}
.mb-2{margin-bottom:.5rem!important;}
.ml-2{margin-left:.5rem!important;}
.py-3{padding-top:1rem!important;}
.px-3{padding-right:1rem!important;}
.pb-3,.py-3{padding-bottom:1rem!important;}
.pl-3,.px-3{padding-left:1rem!important;}
.mt-3,.my-3{margin-top:1rem!important;}
.mb-3,.my-3{margin-bottom:1rem!important;}
.ml-3{margin-left:1rem!important;}
.p-4{padding:1.5rem!important;}
.pl-4{padding-left:1.5rem!important;}
.mb-4{margin-bottom:1.5rem!important;}
.p-5{padding:2rem!important;}
.pt-5{padding-top:2rem!important;}
.mb-5{margin-bottom:2rem!important;}
.pl-7{padding-left:3rem!important;}
@media (min-width: 576px){
.mr-sm-8{margin-right:3.5rem!important;}
}
@media (min-width: 768px){
.pr-md-0{padding-right:0!important;}
.pl-md-0{padding-left:0!important;}
.mb-md-0{margin-bottom:0!important;}
.my-md-1{margin-top:.25rem!important;}
.my-md-1{margin-bottom:.25rem!important;}
.px-md-3{padding-right:1rem!important;}
.px-md-3{padding-left:1rem!important;}
.py-md-4{padding-top:1.5rem!important;}
.py-md-4{padding-bottom:1.5rem!important;}
}
body a:not([href]):not([tabindex]):focus,body a:not([href]):not([tabindex]):hover{color:#00B0F0;}
.material-icons{vertical-align:bottom;cursor:pointer;}
.material-icons.md-dark{color:rgba(0,0,0,0.54);}
.material-icons.md-grey{color:rgba(0,0,0,0.38);}
.material-icons.md-36{font-size:36px;}
.btn{line-height:1;text-transform:none;}
.btn:hover,.btn:active,.btn:focus{opacity:.7;}
.btn[class*=btn-outline-]:hover,.btn[class*=btn-outline-]:active,.btn[class*=btn-outline-]:focus{box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);outline:0;opacity:1;}
.btn.btn-sm{padding:.5rem 1rem;font-size:.875rem;}
.badge-pill{padding:.1rem .5rem;color:#fff;}
.dropdown-content li>span{color:#595959!important;}
.dropdown-content li.active,.dropdown-content li:hover{background-color:#00B0F0;}
.dropdown-content li.active>span,.dropdown-content li:hover>span{color:#fff;}
.title{background-image:url(https://assign-navi.jp/assets/img/common/bg-title-1afb32d6753617ed3284b711315764d1894c70aeff8a945eab5bd541d5c5ff2a.png);background-size:cover;}
.title h1{color:#fff;}
@media (max-width: 767px){
.title h1{font-size:1.8rem;}
}
:focus{outline:0;}
.form-control:focus{box-shadow:none;border-color:#00B0F0;}
input.form-control{border:none;border-bottom:1px solid #00B0F0;border-radius:0;background-color:#eee;color:rgba(0,0,0,0.87);height:inherit;padding:.75rem .75rem;cursor:text;}
textarea.form-control{border-color:#00B0F0;}
.custom-checkbox label[class*=custom-control-label]{cursor:pointer;}
.custom-control-label::before{width:1.125rem;height:1.125rem;background-color:rgba(0,0,0,0);border:2px solid #d5d9db;top:3px;box-shadow:none!important;}
.custom-checkbox .custom-control-input:checked~.custom-control-label::before{background-color:#00B0F0;border-color:#00B0F0;}
.custom-checkbox .custom-control-input:checked~.custom-control-label::after{background:url(https://assign-navi.jp/assets/img/common/check_white.png);background-size:contain;top:3px;}
.custom-control-input:focus:not(:checked)~.custom-control-label::before{border-color:#d5d9db;}
.select-wrapper.anavi-select{background-color:#f4f4f4;border-radius:3px 3px 0 0;}
.select-wrapper.anavi-select input.select-dropdown{padding-right:0;height:3rem;border-color:#00B0F0;background-color:rgba(0,0,0,0);font-size:1rem;text-indent:1rem;}
.select-wrapper.anavi-select span.caret{top:50%;transform:translateY(-50%);right:1rem;}
.select-wrapper.anavi-select span.caret.material-icons{font-size:16px;}
.modal-header{padding:2rem;border-bottom:none;}
.modal-body{padding:0 2rem 2rem;}
@media (max-width: 767px){
.modal-header{padding:2rem 1rem;border-bottom:none;}
.modal-body{padding:0 1rem 2rem;}
}
ul{list-style:none;padding:0;}
.side-menu-contents ul.collapsible li a{color:rgba(0,0,0,0.87);position:relative;}
.side-menu-contents ul.collapsible li a.active{background:#00B0F0;color:#fff !important;}
.side-menu-contents ul.collapsible li a.active i{color:#fff !important;}
.side-menu-contents ul.collapsible li a:hover{background:#00B0F0;color:#fff !important;}
.side-menu-contents ul.collapsible li a:hover i{color:#fff !important;}
.side-menu-contents ul.collapsible li a .rotate-icon{position:absolute;right:0;top:.5rem;}
.side-menu-contents ul.collapsible li>div{cursor:pointer;}
.side-menu-contents ul.collapsible li>div:hover{background:#00B0F0;color:#fff !important;}
.side-menu-contents ul.collapsible li>div:hover i,
.side-menu-contents ul.collapsible li>div:hover span{color:#fff !important;}
@media (max-width: 768px){
.side-menu-contents ul li a{border-top:1px solid #ccc;}
.side-menu-contents ul li .collapsible-body li a{border:none;}
}
.border-bottom{border-bottom:1px solid #e6eaec!important;}
@media (max-width: 767px){
.btn-outline-default:hover{border-color:#00B0F0!important;background-color:inherit!important;color:#00B0F0!important;}
}
.form-check-input[type=radio]:focus+label:before{border-color:custom-(#d5d9db);box-shadow:none;}
.form-check-input[type=radio]:not(:checked)+label,.form-check-input[type=radio]:checked+label{height:initial;}
}
/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen{
.material-icons{font-family:'Material Icons';font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-feature-settings:'liga';-webkit-font-smoothing:antialiased;}
}
/*! CSS Used from: Embedded */
.dropzone-wrapper[data-v-02ef13da]{border:1px solid #b4bcc2;border-radius:.25rem;}
.dropzone-wrapper .dropzone-content[data-v-02ef13da]{cursor:pointer;}
/*! CSS Used fontfaces */
@font-face{font-family:'Material Icons';font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2')!important;background-color:inherit!important;color:#00B0F0!important;}

.form-check-input[type=radio]:focus+label:before{border-color:custom-(#d5d9db);box-shadow:none;}
.form-check-input[type=radio]:not(:checked)+label,.form-check-input[type=radio]:checked+label{height:initial;}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen{
.material-icons{font-family:'Material Icons';font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-feature-settings:'liga';-webkit-font-smoothing:antialiased;}
}
/*! CSS Used from: Embedded */
.dropzone-wrapper[data-v-02ef13da]{border:1px solid #b4bcc2;border-radius:.25rem;}
.dropzone-wrapper .dropzone-content[data-v-02ef13da]{cursor:pointer;}
/*! CSS Used fontfaces */
@font-face{font-family:'Material Icons';font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');}
/* Mobile (< 768px) */
@media screen and (max-width: 767px) {
    main {
        margin-top: 0;
    }

    .container-fluid {
        padding-right: 15px;
        padding-left: 15px;
    }

    h1 {
        font-size: 1.8rem !important;  /* Tăng size chữ cho dễ đọc */
    }
    .title_bookmark {
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: bold;
    }

    .title {
        padding: 1.5rem 0;
    }

    .grabient {
        padding-top: 2rem !important;
    }

    /* Tăng khoảng cách giữa các phần tử để dễ bấm hơn */
    .mb-5 {
        margin-bottom: 2.5rem !important;
    }

    .mt-3 {
        margin-top: 1.5rem !important;
    }
    .container-fluid {
        padding-right: 20px;
        padding-left: 20px;
    }

}

/* Tablet (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
    .container-fluid {
        padding-right: 20px;
        padding-left: 20px;
    }

    h1 {
        font-size: 2.6rem !important;  /* Size chữ lớn cho dễ đọc */
    }
}

/* Mobile Menu */
.container-fluid.title {
    position: relative;
}

.mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: -300px;
    width: 300px;
    background: white;
    z-index: 9999;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.mobile-menu.active {
    right: 0;
    display: block;
}

.mobile-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    margin-right: 15px;
}

.mobile-menu-btn span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: #fff;
    margin: 5px 0;
    transition: 0.3s;
}

.mobile-menu-content {
    padding: 20px;
    background: white;
    position: relative;
}

.mobile-menu-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-menu-content li {
    margin-bottom: 10px;
}

.mobile-menu-content a {
    display: block;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.mobile-menu-content a:hover {
    background: #00B0F0;
    color: #fff !important;
}

.mobile-menu-content a:hover i,
.mobile-menu-content a:hover span {
    color: #fff !important;
}

.mobile-menu-content a.active {
    background: #00B0F0;
    color: #fff !important;
}

.mobile-menu-content a.active i,
.mobile-menu-content a.active span {
    color: #fff !important;
}

.mobile-menu-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    z-index: 9999;
}

.mobile-menu-close span {
    display: block;
    width: 20px;
    height: 20px;
    position: relative;
}

.mobile-menu-close span::before,
.mobile-menu-close span::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #333;
    top: 50%;
    left: 0;
}

.mobile-menu-close span::before {
    transform: rotate(45deg);
}

.mobile-menu-close span::after {
    transform: rotate(-45deg);
}

.mobile-menu-close:hover span::before,
.mobile-menu-close:hover span::after {
    background-color: #00B0F0;
}

@media screen and (max-width: 767px) {
    .mobile-menu {
        position: fixed;
        top: 0;
        height: 100vh;
        right: -300px;
    }

    .mobile-menu.active {
        right: 0;
    }

    .mobile-menu-content {
        padding-top: 60px;
    }
}

@media screen and (min-width: 375px) and (max-width: 390px) {
    .position-relative{
        margin-top: 10rem !important;
    }
}
