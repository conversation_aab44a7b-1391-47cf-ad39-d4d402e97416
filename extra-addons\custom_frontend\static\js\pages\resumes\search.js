import pickadate from '/custom_frontend/static/js/directives/pickadate.js';
import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const resumes_search = {
	'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
	${createBreadcrumb([
		{ text: 'マイページ', link: '/mypage' },
		{ text: '人財を探す', link: null, current: true }
	])}
	<div class="container-fluid title py-2 py-md-4">
		<h1 class="mb-0">人財を探す</h1>
	</div>
	<div class="container-fluid grabient pt-5">
		<div class="row">
			<div class="d-none d-md-block col-12 col-md-4 col-lg-3" id="side-search">
				<div class="card py-4 side-card">
					<i class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
					<div class="mb-3" v-if="isSaveQuery"><div class="reset_link_area-pc mx-3 d-none d-md-block"><a class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light" :href="query"><span>保存済み条件で検索</span></a></div></div>
					<form class="new_resume_search_condition" id="resume_search_condition_form" novalidate="" @submit.prevent="resumes_search" accept-charset="UTF-8" method="get" @input="handleInputChange">
						<div class="container">
							<div class="row">
								<div class="col-12 px-0 px-md-3">
									<label class="font-middle mb-3 ex-bold">フリーワード</label>
									<div class="mb-4">
										<div class="vertical-top d-inline-block w-100" data-html="true" data-toggle="tooltip" title="" data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
											<div class="mb-1"><input class="form-control" autocomplete="off" id="free_keyword_field" v-model="free_keyword" type="text" name="resume_search_condition[free_keyword]"></div>
										</div>
										<div class="mb-3"><span class="font-middle">を含む</span></div>
										<div class="mb-1"><input class="form-control" autocomplete="off" id="negative_keyword_field" v-model="negative_keyword" type="text" name="resume_search_condition[negative_keyword]"></div>
										<div class="mb-3"><span class="font-middle">を除く</span></div>
									</div>
									<div class="mb-5">
										<label class="font-middle mb-3 ex-bold vertical-line-label">得意領域</label>
										<div class="consul_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion">
                                            <span class="font-size-middle ex-bold">コンサル</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_consul" v-show="isOpen">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categories" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'consul' + index"
                                                            id_params="consul"
                                                            type="checkbox"
															v-model="exp_categories"
                                                            name="resume_search_condition[exp_categories][]"
                                                            :value="category.value"
                                                            />
                                                        <label
                                                            :id="'exp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'consul' + index"
                                                            >
                                                        {{ category.label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="dev_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion1"><span
                                            class="font-size-middle ex-bold">開発</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen1 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_dev" v-show="isOpen1">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categoriesDev" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'dev' + index"
                                                            id_params="dev"
                                                            type="checkbox"
															v-model="exp_categories"
                                                            name="resume_search_condition[exp_categories][]"
                                                            :value="category.value"
                                                            />
                                                        <label
                                                            :id="'exp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'dev' + index"
                                                            >
                                                        {{ category.label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="infra_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion2">
                                            <span class="font-size-middle ex-bold">インフラ</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen2 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_infra" v-show="isOpen2">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categoriesInfra" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'infra' + index"
                                                            id_params="infra"
                                                            type="checkbox"
															v-model="exp_categories"
                                                            name="resume_search_condition[exp_categories][]"
                                                            :value="category.value"
                                                            />
                                                        <label
                                                            :id="'exp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'infra' + index"
                                                            >
                                                        {{ category.label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="design_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion3">
                                            <span class="font-size-middle ex-bold">運用・保守</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen3 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_design" v-show="isOpen3">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categories3" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'category' + index"
                                                            id_params="category"
                                                            type="checkbox"
															v-model="exp_categories"
                                                            name="resume_search_condition[exp_categories][]"
                                                            :value="category.value"
                                                            />
                                                        <label
                                                            :id="'exp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'category' + index"
                                                            >
                                                        {{ category.label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
									</div>
									<div class="mb-3">
										<label class="font-middle mb-3 ex-bold vertical-line-label" for="">稼働可能状況</label>
										<div class="selecting-form row px-3 with-title">
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="status_field_resume_search_condition_0" v-model="status" type="checkbox" name="resume_search_condition[status][]" value="work_available"><label id="status_field_label_0" class="custom-control-label anavi-select-label mb-3" for="status_field_resume_search_condition_0">即日可</label></div>
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="status_field_resume_search_condition_1" v-model="status" type="checkbox" name="resume_search_condition[status][]" value="will_be_available"><label id="status_field_label_1" class="custom-control-label anavi-select-label mb-3" for="status_field_resume_search_condition_1">今後可</label></div>
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="status_field_resume_search_condition_2" v-model="status" type="checkbox" name="resume_search_condition[status][]" value="depends_on_opportunities"><label id="status_field_label_2" class="custom-control-label anavi-select-label mb-3" for="status_field_resume_search_condition_2">要相談</label></div>
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="status_field_resume_search_condition_3" v-model="status" type="checkbox" name="resume_search_condition[status][]" value="not_corresponding"><label id="status_field_label_3" class="custom-control-label anavi-select-label mb-3" for="status_field_resume_search_condition_3">対応不可</label></div>
										</div>
									</div>
									<div class="mx-auto mt-0 mb-3">
										<label class="font-middle mb-3 ex-bold vertical-line-label" for="">所属</label>
										<div class="selecting-form row px-3 with-title">
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="partner_types_field_resume_search_condition_0" v-model="partner_types" type="checkbox" name="resume_search_condition[partner_types][]" value="empl"><label id="partner_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="partner_types_field_resume_search_condition_0">自社社員</label></div>
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="partner_types_field_resume_search_condition_1" v-model="partner_types" type="checkbox" name="resume_search_condition[partner_types][]" value="subc"><label id="partner_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="partner_types_field_resume_search_condition_1">協力会社社員</label></div>
										</div>
									</div>
									<div class="mx-auto mt-0 mb-3">
										<label class="font-middle mb-3 ex-bold vertical-line-label" for="">可能な契約形態</label>
										<div class="selecting-form row px-3 with-title">
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="contract_types_field_resume_search_condition_0" v-model="contract_types" type="checkbox" name="resume_search_condition[contract_types][]" value="quas"><label id="contract_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="contract_types_field_resume_search_condition_0">業務委託（準委任）</label></div>
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="contract_types_field_resume_search_condition_1" v-model="contract_types" type="checkbox" name="resume_search_condition[contract_types][]" value="subc"><label id="contract_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="contract_types_field_resume_search_condition_1">業務委託（請負）</label></div>
											<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="contract_types_field_resume_search_condition_2" v-model="contract_types" type="checkbox" name="resume_search_condition[contract_types][]" value="temp"><label id="contract_types_field_label_2" class="custom-control-label anavi-select-label mb-3" for="contract_types_field_resume_search_condition_2">派遣契約</label></div>
										</div>
									</div>
									<label class="font-middle mb-3 ex-bold vertical-line-label">人財の特徴</label>
									<div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
																		for="">国籍</label>
										<div class="selecting-form row px-3 with-title">
											<div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
												<input class="form-check-input"
																						id="nationality0"
																						type="radio"
																						v-model="nationality"
																						value="japan"
																						name="resume_search_condition[nationality]"><label
												class="form-check-label" for="nationality0">日本人のみ</label>
											</div>
											<div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
												<input class="form-check-input"
																						id="nationality1"
																						type="radio"
																						v-model="nationality"
																						value="all"
																						name="resume_search_condition[nationality]"><label
												class="form-check-label" for="nationality1">国籍問わず</label>
											</div>
										</div>
									</div>
									<div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
																		for="">在留資格有無</label>
										<div class="selecting-form row px-3 with-title">
											<div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
												<input class="form-check-input"
																						id="resident0"
																						type="radio"
																						v-model="resident"
																						value="yes"
																						name="resume_search_condition[resident]"><label
												class="form-check-label" for="resident0">有</label>
											</div>
											<div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
												<input class="form-check-input"
																						id="resident1"
																						type="radio"
																						v-model="resident"
																						value="no"
																						name="resume_search_condition[resident]"><label
												class="form-check-label" for="resident1">無し</label>
											</div>
											<div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
												<input class="form-check-input"
																						id="resident2"
																						type="radio"
																						v-model="resident"
																						value="other"
																						name="resume_search_condition[resident]"><label
												class="form-check-label" for="resident2">必須資格</label>
											</div>
										</div>
									</div>
									<div class="accordion_details py-3 px-2 clear border-top accordion_close" @click="toggleAccordion4"><span class="float-left pr-7">詳細検索条件</span><i class="material-icons md-dark float-right d-block">{{ isOpen4 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i></div>
									<div class="accordion_contents_details mb-4 pt-3" v-show="isOpen4">
										<div class="mb-5">
											<label class="font-middle mb-3 ex-bold vertical-line-label">参画可能時期</label>
											<div class="row pl-3">
												<div class="col-9">
													<div class="mx-auto mb-3">
														<input class="form-control picker__input" autocomplete="off" v-pickadate="{ model: 'date_period' }" v-model="date_period" id="available_time_at_earliest_field" type="text" name="resume_search_condition[available_time_at_earliest]" readonly="" aria-haspopup="true" aria-expanded="false" aria-readonly="false" aria-owns="available_time_at_earliest_field_root">

													</div>
												</div>
												<div class="col-1 p-0"><i class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i></div>
											</div>
										</div>
										<div class="mx-auto mb-5">
											<label class="mb-3 font-middle ex-bold vertical-line-label">性別</label>
											<div v-dropdown="{modelValue: '', listType: 'gender'}" @selected="select_gender"><input type="hidden" v-model="gender" name="resume_search_condition[gender]"></div>
										</div>
										<label class="font-middle mb-3 ex-bold vertical-line-label">年齢</label>
										<div class="row">
											<div class="col-7">
												<div class="mx-auto mb-5">
													<div v-dropdown="{modelValue: '', listType: 'age_min'}" @selected="select_age_min"><input type="hidden" v-model="age_min" name="resume_search_condition[age_min]"></div>
												</div>
											</div>
											<div class="col-4 pl-0"><label class="inline-unit-label">歳以上</label></div>
										</div>
										<div class="row">
											<div class="col-7">
												<div class="mx-auto mb-5">
													<div v-dropdown="{modelValue: '', listType: 'age_max'}" @selected="select_age_max"><input type="hidden" v-model="age_max" name="resume_search_condition[age_max]"></div>
												</div>
											</div>
											<div class="col-4 pl-0"><label class="inline-unit-label">歳以下</label></div>
										</div>
										<label class="font-middle mb-3 ex-bold">単価</label>
										<PriceRange
											:min="0"
											:max="300"
											:step="5"
											:startValues="[priceMin, priceMax]"
											@update:price_min="updatePriceMin"
											@update:price_max="updatePriceMax"
										/>
										<input type="hidden" :value="priceMin !== null ? priceMin : ''" name="resume_search_condition[asking_unit_price_min]">
										<input type="hidden" :value="priceMax !== null ? priceMax : ''" name="resume_search_condition[asking_unit_price_max]">
										<div class="mx-auto mt-0 mb-3">
											<label class="font-middle mb-3 ex-bold" for="">稼働率</label>
											<div class="selecting-form row px-3 with-title">
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="utilization_rate_field_resume_search_condition_0" v-model="utilization_rate" type="checkbox" name="resume_search_condition[utilization_rate][]" value="100"><label id="utilization_rate_field_label_0" class="custom-control-label anavi-select-label mb-3" for="utilization_rate_field_resume_search_condition_0">100%（フル稼働）</label></div>
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="utilization_rate_field_resume_search_condition_1" v-model="utilization_rate" type="checkbox" name="resume_search_condition[utilization_rate][]" value="75"><label id="utilization_rate_field_label_1" class="custom-control-label anavi-select-label mb-3" for="utilization_rate_field_resume_search_condition_1">75%</label></div>
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="utilization_rate_field_resume_search_condition_2" v-model="utilization_rate" type="checkbox" name="resume_search_condition[utilization_rate][]" value="50"><label id="utilization_rate_field_label_2" class="custom-control-label anavi-select-label mb-3" for="utilization_rate_field_resume_search_condition_2">50%</label></div>
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="utilization_rate_field_resume_search_condition_3" v-model="utilization_rate" type="checkbox" name="resume_search_condition[utilization_rate][]" value="25"><label id="utilization_rate_field_label_3" class="custom-control-label anavi-select-label mb-3" for="utilization_rate_field_resume_search_condition_3">25%</label></div>
											</div>
										</div>
										<div class="mx-auto mt-0 mb-3">
											<label class="font-middle mb-3 ex-bold" for="">出社頻度</label>
											<div class="selecting-form row px-3 with-title">
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="working_frequency_field_resume_search_condition_0" v-model="working_frequency" type="checkbox" name="resume_search_condition[working_frequency][]" value="5days"><label id="working_frequency_field_label_0" class="custom-control-label anavi-select-label mb-3" for="working_frequency_field_resume_search_condition_0">週5日出社</label></div>
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="working_frequency_field_resume_search_condition_1" v-model="working_frequency" type="checkbox" name="resume_search_condition[working_frequency][]" value="2to4days"><label id="working_frequency_field_label_1" class="custom-control-label anavi-select-label mb-3" for="working_frequency_field_resume_search_condition_1">週4 〜 2日出社</label></div>
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="working_frequency_field_resume_search_condition_2" v-model="working_frequency" type="checkbox" name="resume_search_condition[working_frequency][]" value="less_than_1day"><label id="working_frequency_field_label_2" class="custom-control-label anavi-select-label mb-3" for="working_frequency_field_resume_search_condition_2">週1日以下出社</label></div>
												<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="working_frequency_field_resume_search_condition_3" v-model="working_frequency" type="checkbox" name="resume_search_condition[working_frequency][]" value="full_remote"><label id="working_frequency_field_label_3" class="custom-control-label anavi-select-label mb-3" for="working_frequency_field_resume_search_condition_3">フルリモート</label></div>
											</div>
										</div>
										<workplace-selector :required-label="false" :inputName="'resume_search_condition[workplaces][]'" v-model="workplaces"></workplace-selector>
										<div class="accordion-close-area text-center" @click="toggleAccordion4">閉じる<i class="material-icons md-dark md-18">close</i></div>
										<input autocomplete="off" type="hidden" name="resume_search_condition[switch_type]" id="resume_search_condition_switch_type" value="yes">
									</div>
									<div class="accordion_premium py-3 px-2 clear border-top accordion_close" @click="toggleAccordion5"><span class="float-left pr-7">評価中の人財</span><i class="material-icons md-dark float-right d-block">{{ isOpen5? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i></div>
									<div class="accordion_contents_premium mb-4 pt-3" v-show="isOpen5">
										<div class="mb-5">
											<label class="font-middle mb-3 ex-bold">社内コメント</label>
											<div class="row pl-3">
												<div class="col-9">
													<div class="mx-auto mb-3">
														<div class="selecting-form custom-control custom-checkbox z-2"><input id="with_internal_comment_field" class="custom-control-input" v-model="with_internal_comment" type="checkbox" name="resume_search_condition[with_internal_comment]" value="社内コメントあり"><label id="with_internal_comment_field_label" class="custom-control-label" for="with_internal_comment_field">社内コメントあり</label></div>
													</div>
												</div>
											</div>
										</div>
										<div class="mb-0">
											<div class="row">
												<div class="col-9">
													<div class="mt-0 mb-5">
														<label class="font-middle mb-3 ex-bold" for="">評価フラグ</label>
														<div class="selecting-form row px-3 with-title">
															<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="rating_flag_field_resume_search_condition_0" v-model="rating_flag" type="checkbox" name="resume_search_condition[rating_flag][]" value="evaluation_flag_A"><label id="rating_flag_field_label_0" class="custom-control-label anavi-select-label mb-3" for="rating_flag_field_resume_search_condition_0">A評価</label></div>
															<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="rating_flag_field_resume_search_condition_1" v-model="rating_flag" type="checkbox" name="resume_search_condition[rating_flag][]" value="evaluation_flag_B"><label id="rating_flag_field_label_1" class="custom-control-label anavi-select-label mb-3" for="rating_flag_field_resume_search_condition_1">B評価</label></div>
															<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="rating_flag_field_resume_search_condition_2" v-model="rating_flag" type="checkbox" name="resume_search_condition[rating_flag][]" value="evaluation_flag_C"><label id="rating_flag_field_label_2" class="custom-control-label anavi-select-label mb-3" for="rating_flag_field_resume_search_condition_2">C評価</label></div>
															<div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input class="custom-control-input" id="rating_flag_field_resume_search_condition_3" v-model="rating_flag" type="checkbox" name="resume_search_condition[rating_flag][]" value="evaluation_flag_yet"><label id="rating_flag_field_label_3" class="custom-control-label anavi-select-label mb-3" for="rating_flag_field_resume_search_condition_3">未評価</label></div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="text-center d-none d-md-block search-area py-2">
										<button name="button" id="resume-search-button" class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light" data-disable-with="検索中" @click="resumes_search">
											<div id="btn-text" v-if="isFinding" class="">検索中</div>
											<div class="py-2" id="loader" v-else-if="isLoading">
												<div class="loader"></div>
											</div>
											<div id="btn-text" v-else class=""><span class="font-extralarge" id="search-count">{{count_resumes}}</span> 件<br>この条件で検索</div>
										</button>
										<div class="py-2"><a href="/resumes/active?search_reset=true"><span>条件をリセット</span></a></div>
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
			<div class="col-12 col-md-8 col-lg-9">
				<div class="row">
					<div class="col-12 d-none d-md-block mb-4">
						<div class="d-flex">
							<span class="mr-3 nowrap">よく検索されるキーワード</span>
							<div>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('Java')"><span>Java</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('SAP')"><span>SAP</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('PHP')"><span>PHP</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('COBOL')"><span>COBOL</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('C#')"><span>C#</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('PMO')"><span>PMO</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('VB.NET')"><span>VB.NET</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('PL/SQL')"><span>PL/SQL</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('Linux')"><span>Linux</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="resumes_keyword('Android')"><span>Android</span></a>
							</div>
						</div>
					</div>
				</div>
				<div class="row mr-0 ml-0">
					<div class="col-12 mt-3 border p-3 px-md-4 bg-grey-2 rounded-sm" id="border-light">
						<div class="position-relative">
							<div class="d-sm-flex search-condition-area line-height-180">
								<div class="text-nowrap mb-2 mb-sm-n2 ml-3 ml-sm-0">検索条件</div>
								<div class="pl-3">
									<div v-if="this.categorizedExps && Object.keys(this.categorizedExps || {}).length > 0" class="d-sm-inline border-left">
										<span class="mr-3 custom-grey-5-text">得意領域</span>
										<div v-for="(values, category) in this.categorizedExps" :key="category" class="d-sm-inline mb-2 mb-sm-0">
										<span class="border-grey-3 bg-white mr-2 px-2">{{ category }}</span>
										<div class="d-sm-inline">
											<span v-for="value in values" :key="value" class="mr-3">{{ value }}</span>
										</div>
										</div>
									</div>

									<div v-if="validConditions.length">
										<div v-for="condition in validConditions" :key="condition.key" class="d-sm-inline border-left">
										<span class="mr-3 custom-grey-5-text">{{ condition.label }}</span>
										<div class="d-sm-inline">
											<span class="mr-3">{{ condition.value }}</span>
										</div>
										</div>
									</div>
								</div>
							</div>
							<div class="accordion_search_condition_btn"><a class="accordion_search_condition accordion_close text-right btn-block pr-3 bg-grey-2 pl-5" href="javascript:void(0);" style="display: none;"><span class="vertical-middle font-middle">すべて表示</span><i class="material-icons">keyboard_arrow_down</i></a></div>
						</div>
						<div class="text-center">
							<a v-if="this.isloggedin.length > 0" class="btn btn-outline-default px-5 btn-white mt-3 mb-0 font-default table-responsive-sm save_search_condition_btn waves-effect waves-light" href="javascript:void(0);" @click="saveQuery"> この条件を保存 </a>
							<a v-if="isSaveQuery" class="btn btn-outline-blue-grey px-5 btn-white mt-3 mb-0 font-default table-responsive-sm delete_search_condition_btn waves-effect waves-light" rel="nofollow" href="/resumes/active" @click="unSaveQuery"> 保存条件を破棄 </a>
						</div>
					</div>
				</div>
				<hr class="mt-0 mb-3">
				<div class="d-flex justify-content-between align-items-center mb-3">
					<div class="font-middle"><span class="font-ll ex-bold" id="total-count">{{this.totalRecord}}</span> 件中 {{totalRecord.length ? 1 : 0 }}〜{{ displayedRecords }}件</div>
					<div class="position-relative pl-3" ref="dropdown">
                        <!-- Khu vực hiển thị và bấm vào -->
                        <div class="d-flex justify-content-end sort-display-area p-2" @click="isOpen6 = !isOpen6">
                            <label class="pr-2 mb-0">{{ selectedOption }}</label>
                            <i class="material-icons custom-grey-6-text pl-1">
                            {{ isOpen6 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                            </i>
                        </div>

                        <!-- Danh sách hiển thị khi bấm vào -->
                        <ul class="bg-white sort-options-area text-right" v-show="isOpen6">
                            <li v-for="option in options" :key="option"
                                :class="{ 'sort-active': option === selectedOption }"
                                @click="selectOption(option)">
                            {{ option }}
                            <i v-if="option === selectedOption" class="material-icons custom-grey-6-text">done</i>
                            </li>
                        </ul>
                    </div>
				</div>
				<div class="row">
					<div class="col-12">
						<div v-for="resume in resumes" :key="resume.id">
							<a class="card mb-4 w-100 hoverable d-block" :href="'/resumes/' + resume.id + '/detail?prev_next_display=display'" @click="add_viewer(resume.id, this.isloggedin)">
								<div class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start">
									<div v-if="viewedStatuses[resume.id] === true" class="viewed-flag-pc"><span class="font-small">閲覧済</span></div>
									<div v-else class="new-flag-pc"><span class="font-small">NEW!</span></div>
									<h5 class="mb-0 ml-5">
										<div class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
										<div class="default-main-color">{{ resume.initial_name }} {{getGenderLabel(resume.gender)}} {{ calculateAge(resume.birthday) }}歳</div>
										</div>
										<div class="d-md-inline-block mt-1 font-small custom-grey-6-text">
										<span>最終ログイン</span><span class="px-1">:</span><span>{{ resume.lastLogin }}</span>
										</div>
										<div class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
										<span>(更新</span><span class="px-1">:</span><span>{{ resume.updated_at }})</span>
										</div>
									</h5>
								</div>
								<div class="pt-3 pb-4 px-3 px-md-4">
									<div class="mb-2">
										<span class="ex-bold">稼働 : {{ resume.status }}</span>
									</div>
									<p class="mt-1">【経験スキル】 {{ resume.skills }}</p>
									<div class="row mb-2">
										<div class="col-12 col-lg-6 d-flex align-items-start mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">所属</label>
										<div><span>{{ (resume?.partner_types || '').split(',').map(rate => mapping_partner_types[resume.partner_types] || '').join('') || '' }}</span></div>
										</div>
										<div class="col-12 col-lg-6 mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
										<span>{{ resume.company_settings === 'public' ? resume.company_name : '非公開' }}</span>
										</div>
										<div class="col-12 col-lg-6 mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label>
										<span>{{resume.unit_price_min}}万円 〜 {{resume.unit_price_max}}万円 / 月</span>
										</div>
										<div class="col-12 col-lg-6 d-flex mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label>
										<span>{{ (resume?.utilization_rate || '').split(',').map(rate => mapping_utilization[rate] || '').join('／') || '' }}</span>
										</div>
										<div class="col-12 col-lg-6 d-flex mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label>
										<span>{{ (resume?.working_frequency || '').split(',').map(fr => mapping_working_frequency[fr] || '').join('／') || '' }}</span>
										</div>
										<div class="col-12 col-lg-6 d-flex mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label>
										<span>{{ resume.working_location }}</span>
										</div>
										<div class="col-12 col-lg-6 d-flex mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span>{{ resume.contract_type }}</span>
										</div>
										<div class="col-12 col-lg-6 d-flex align-items-start mb-2">
										<label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">国籍</label><span>{{ resume.nationality }}</span>
										</div>
									</div>
									<div class="text-right">
										<!-- ブロックモーダルのアイコン -->
										<div class="d-inline-block">
											<div class="d-inline-block" data-toggle="tooltip" data-original-title="テキストで表示">
												<a class="mdb-modal-form custom-grey-6-text z-2 p-2" :data-target="'#display_text_format_' + resume.id" data-toggle="modal" href="">
												<i class="material-icons ml-3 mr-1 align-text-bottom">file_copy</i>
												<span>テキスト表示</span></a>
											</div>
										</div>
									</div>
								</div>
							</a>
							<div aria-labelledby="display_text_format_modal" class="modal" :id="'display_text_format_' + resume.id" tabindex="-1" aria-modal="true" role="dialog">
								<div class="modal-dialog modal-lg" role="document">
									<div class="modal-content">
										<div class="modal-header">
											<h4 class="modal-title w-100">人財詳細</h4>
											<button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span></button>
										</div>
										<div class="modal-body">
											<div class="mb-4">**********************************************************<br>◆人財ID: {{resume.id}}<br>◆氏名: {{
												resume.initial_name }} {{getGenderLabel(resume.gender)}} {{ calculateAge(resume.birthday) }}歳<br>◆参画可能時期:
												{{resume.status}}<br>◆単価: {{resume.unit_price_min}}万円 〜 {{resume.unit_price_max}}万円 / 月 <br>◆稼働率: {{
												(resume?.utilization_rate || '').split(',').map(rate => mapping_utilization[rate] || '').join('／') || '' }}<br>◆就業場所:
												{{ (resume.working_location ?? "").split(',').join('／') }}<br>◆所属: 自社社員<br>◆経験PR:<br>◆人物サマリー<br>--------------------------------------------<br>名前：{{
												resume.initial_name }} （{{getGenderLabel(resume.gender)}} ／{{ calculateAge(resume.birthday) }}）<br>所属：弊社社員<br>開始日：2025年3月17日～
												2025年4月1日開始<br>金額　：110万(稼働率週5日100％時　応相談)　<br>稼働率：80％～100％<br>業界　：金融（生保・損保・銀行・官公庁・製造業）<br><br>◆スキルサマリー<br>{{resume.qualification}}<br><br>◆備考<br>・{{resume.experience_pr}}<br><br>-------------------------------------------------<br><br><br>◆希望詳細:<br>・{{resume.working_hope}}<br><br>**********************************************************
											</div>
											<div class="text-center"><a aria-label="Close" class="btn btn-blue-grey waves-effect waves-light" data-dismiss="modal">閉じる</a></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<nav role="navigation" class="pagination">
					<ul class="pagination">
						<li class="page-item" :class="{ disabled: currentPage === 1 }">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)"></a>
						</li>
						<li v-for="page in visiblePages" :key="page" class="page-item" :class="{ active: page === currentPage }">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
						</li>
						<li class="page-item disabled" v-if="totalPages > 5 && currentPage < totalPages - 2">
							<a class="page-link waves-effect" href="#">…</a>
						</li>
						<li class="page-item" v-if="totalPages > 5">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(totalPages)">{{ totalPages }}</a>
						</li>
						<li class="page-item" :class="{ disabled: currentPage === totalPages }">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)"></a>
						</li>
					</ul>
				</nav>
			</div>
		</div>
	</div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/resumes/active.css"/>
    `,
	data() {
		return {
			isOpen: false,
			isOpen1: false,
			isOpen2: false,
			isOpen3: false,
			isOpen4: false,
			isOpen5: false,
			isOpen6: false,
			isLoading: false,
			priceMin: null,
			priceMax: null,
			params_search: {},
			categorizedExps: {},
			viewedStatuses: {},
			isSaveQuery: localStorage.getItem("query") !== null,
			query: localStorage.getItem('query') || '',
			isloggedin: userInfo ? userInfo.user_id : null,
			currentPage: 1,
			totalPages: 1,
			totalRecord: 0,
			count_resumes: 0,
			sortType: '',
			resumes: [],
			free_keyword: '',
			negative_keyword: '',
			nationality: '',
			resident: '',
			gender: '',
			age_min: '',
			age_max: '',
			with_internal_comment: null,
			status: [],
			exp_categories: [],
			partner_types: [],
			contract_types: [],
			utilization_rate: [],
			working_frequency: [],
			workplaces: [],
			rating_flag: [],
			date_period : '',
			searchConditions: [
				{ key: 'resume_search_condition[free_keyword]', label: 'フリーキーワード' },
				{ key: 'resume_search_condition[negative_keyword]', label: '除外キーワード' },
				{ key: 'resume_search_condition[status][]', label: '稼働可能状況' },
				{ key: 'resume_search_condition[partner_types][]', label: '所属' },
				{ key: 'resume_search_condition[contract_types][]', label: '可能な契約形態' },
				{ key: 'resume_search_condition[nationality]', label: '国籍' },
				{ key: 'resume_search_condition[resident]', label: '在留資格有無' },
				{ key: 'resume_search_condition[gender]', label: '性別' },
				{ key: 'resume_search_condition[age_min]', label: '年齢' },
				{ key: 'resume_search_condition[age_max]', label: '年齢' },
				{ key: 'resume_search_condition[asking_unit_price_min]', label: '単価' },
				{ key: 'resume_search_condition[asking_unit_price_max]', label: '単価' },
				{ key: 'resume_search_condition[utilization_rate][]', label: '稼働率' },
				{ key: 'resume_search_condition[working_frequency][]', label: '出社頻度' },
				{ key: 'resume_search_condition[workplaces][]', label: '就業場所' },
				...(this.with_internal_comment ? [{ key: 'resume_search_condition[with_internal_comment]', label: '社内コメント' }] : []),
				{ key: 'resume_search_condition[rating_flag][]', label: '評価フラグ' },
				{ key: 'resume_search_condition[available_time_at_earliest]', label: '参画可能時期' },
			],
			categoryMapping: {
				コンサル: 'consul_',
				開発: 'dev_',
				インフラ: 'infra_',
				運用・保守: 'design_'
			},
			selectedOption: "新着（降順）", // Mặc định
			options: ["新着（降順）", "更新（降順）", "単価（降順）"],
			sortMap: {
				"新着（降順）": "created_at",
				"更新（降順）": "updated_at",
				"単価（降順）": "unit_price_max"
			},
			mapping_partner_types: {
				'subc': '協力会社社員（一社先）',
				'empl': '自社社員',
			},
			mapping_working_frequency: {
				'5days': '週5日出社',
				'4days': '週4日出社',
				'3days': '週3日出社',
				'2days': '週2日出社',
				'2to4days': '週4 〜 2日出社',
				'less_than_1day': '週1日未満出社',
				'full_remote': 'フルリモート',
				'1day': '週1日出社'
			},
			mapping_utilization: {
				'100': '100%（フル稼働）',
				'75': '75%',
				'50': '50%',
				'25': '25%'
			},
			mapping_resume_visibility: {
				'public': 'public',
				'private': '非公開',
				'limited': 'ログイン後に表示',
			},
			categories: [
				{ label: "PMO", value: "consul_pmo" },
				{ label: "PM・PL", value: "consul_pmpl" },
				{ label: "DX", value: "consul_DX" },
				{ label: "クラウド", value: "consul_cloud" },
				{ label: "モダナイゼション", value: "consul_strategy" },
				{ label: "セキュリティ", value: "consul_work" },
				{ label: "ITインフラ", value: "consul_it" },
				{ label: "AI", value: "consul_ai" },
			],
			categoriesDev: [
				{ label: "PMO", value: "dev_pmo" },
				{ label: "PM・PL", value: "dev_pmpl" },
				{ label: "クラウド", value: "dev_cloud" },
				{ label: "サーバー", value: "dev_server" },
				{ label: "データベース", value: "dev_db" },
				{ label: "ネットワーク", value: "dev_network" },
				{ label: "メインフレーム", value: "dev_mainfream" },
			],
			categoriesInfra: [
				{ label: "PMO", value: "infra_pmo" },
				{ label: "PM・PL", value: "infra_pmpl" },
				{ label: "サーバー", value: "infra_server" },
				{ label: "ネットワーク", value: "infra_network" },
				{ label: "データベース", value: "infra_db" },
				{ label: "クラウド", value: "infra_cloud" },
				{ label: "仮想化", value: "infra_virtualized" },
				{ label: "メインフレーム", value: "infra_mainframe" },
				{ label: "運用・保守", value: "infra_operation" },
			],
			categories3: [
				{ value: "design_business", label: "業務システム" },
				{ value: "design_open", label: "オープン" },
				{ label: "クラウド", value: "design_cloud" },
				{ label: "メインフレーム", value: "design_mainfream" },
				{ label: "ヘルプデスク", value: "design_helpdesk" },
			],
			isFinding: false,
		}
	},
	methods: {
		updatePriceMin(value) {
			if (this.priceMin !== value) {
				this.priceMin = value;
			}
		},
		updatePriceMax(value) {
			if (this.priceMax !== value) {
				this.priceMax = value;
			}
		},
		toggleAccordion() {
			this.isOpen = !this.isOpen; // Đảo trạng thái mở/đóng
		},
		toggleAccordion1() {
			this.isOpen1 = !this.isOpen1; // Đảo trạng thái mở/đóng
		},
		toggleAccordion2() {
			this.isOpen2 = !this.isOpen2; // Đảo trạng thái mở/đóng
		},
		toggleAccordion3() {
			this.isOpen3 = !this.isOpen3; // Đảo trạng thái mở/đóng
		},
		toggleAccordion4() {
			this.isOpen4 = !this.isOpen4; // Đảo trạng thái mở/đóng
		},
		toggleAccordion5() {
			this.isOpen5 = !this.isOpen5; // Đảo trạng thái mở/đóng
		},
		selectOption(option) {
			this.selectedOption = option; // Cập nhật nội dung hiển thị
			this.isOpen6 = false; // Đóng dropdown
			const sort_type = this.sortMap[option]; // Lấy giá trị sort_type từ map
			this.updateURLAndReload(1, sort_type); // Cập nhật URL & load lại trang
		},
		updateURLAndReload(page, sort_type) {
			const url = new URL(window.location.href);
			url.searchParams.set('page', page);
			url.searchParams.set('sort_type', sort_type);
			window.location.href = url.toString(); // Cập nhật URL và reload trang
		},
		closeDropdown(e) {
			if (!this.$refs.dropdown.contains(e.target)) this.isOpen6 = false;
		},
		select_gender(event) {
			this.gender = event.detail; // Nhận giá trị từ dropdown
		},
		select_age_min(event) {
			this.age_min = event.detail;
		},
		select_age_max(event) {
			this.age_max = event.detail
		},
		async fetch_search(page = 1, sort_type = 'created_at') {
			try {
				let params = new URLSearchParams();
				params.append("resume_search_condition[free_keyword]", this.free_keyword || '');
				params.append("resume_search_condition[negative_keyword]", this.negative_keyword || '');
				params.append("resume_search_condition[nationality]", this.nationality || '');
				params.append("resume_search_condition[resident]", this.resident || '');
				params.append("resume_search_condition[gender]", this.gender === "未選択" ? '' : this.gender);
				params.append("resume_search_condition[age_min]", this.age_min === "未選択" ? '' : this.age_min);
				params.append("resume_search_condition[age_max]", this.age_max === "未選択" ? '' : this.age_max);
				params.append("resume_search_condition[with_internal_comment]", this.with_internal_comment || false);
				params.append("resume_search_condition[asking_unit_price_min]", this.priceMin || '');
				params.append("resume_search_condition[asking_unit_price_max]", this.priceMax || '');
				params.append("resume_search_condition[available_time_at_earliest]", this.date_period || '');

				// Xử lý mảng status[]
				if (this.status.length > 0) {
					this.status.forEach(value => {
						params.append("resume_search_condition[status][]", value);
					});
				}

				// Xử lý mảng exp_categories[]
				if (this.exp_categories.length > 0) {
					this.exp_categories.forEach(value => {
						params.append("resume_search_condition[exp_categories][]", value);
					});
				}

				if (this.partner_types.length > 0) {
					this.partner_types.forEach(value => {
						params.append("resume_search_condition[partner_types][]", value);
					});
				}

				if (this.contract_types.length > 0) {
					this.contract_types.forEach(value => {
						params.append("resume_search_condition[contract_types][]", value);
					});
				}

				if (this.utilization_rate.length > 0) {
					this.utilization_rate.forEach(value => {
						params.append("resume_search_condition[utilization_rate][]", value);
					});
				}

				if (this.working_frequency.length > 0) {
					this.working_frequency.forEach(value => {
						params.append("resume_search_condition[working_frequency][]", value);
					});
				}

				if (this.workplaces.length > 0) {
					this.workplaces.forEach(value => {
						params.append("resume_search_condition[workplaces][]", value);
					});
				}

				if (this.rating_flag.length > 0) {
					this.rating_flag.forEach(value => {
						params.append("resume_search_condition[rating_flag][]", value);
					});
				}
				// Gọi API lấy dữ liệu
				const response = await fetch(`/api/resume_search_condition/search?${params.toString()}&page=${page}&sort_type=${sort_type}`, {
					method: 'GET',
					headers: { 'Content-Type': 'application/json' },
				});

				const data = await response.json();
				console.log('data',data);
				if (data.success === true) {
					this.resumes = data.data;
					this.currentPage = data.current_page;
					this.totalPages = data.total_pages;
					this.totalRecord = data.total_records;
				} else {
					console.warn("API data is not in the correct format:", data);
				}
				this.$nextTick(() => {
					pickadate.updatePickadate('available_time_at_earliest_field', this.date_period);
				});
			} catch (error) {
				console.error('Error fetching API:', error.message);
				this.errorMessage = `Error fetching opportunities: ${error.message}`;
			}
		},
		async resumes_search() {
			this.isFinding = true;
			let params = new URLSearchParams();
			params.append("resume_search_condition[free_keyword]", this.free_keyword || '');
			params.append("resume_search_condition[negative_keyword]", this.negative_keyword || '');
			params.append("resume_search_condition[nationality]", this.nationality || '');
			params.append("resume_search_condition[resident]", this.resident || '');
			params.append("resume_search_condition[gender]", this.gender === "未選択" ? '' : this.gender);
			params.append("resume_search_condition[age_min]", this.age_min === "未選択" ? '' : this.age_min);
			params.append("resume_search_condition[age_max]", this.age_max === "未選択" ? '' : this.age_max);
			params.append("resume_search_condition[with_internal_comment]", this.with_internal_comment || false);
			params.append("resume_search_condition[asking_unit_price_min]", this.priceMin || '');
			params.append("resume_search_condition[asking_unit_price_max]", this.priceMax || '');
			params.append("resume_search_condition[available_time_at_earliest]", this.date_period || '');
			params.append("resume_search_condition[accordion_open_consul]", this.isOpen ? "yes" : "no");
			params.append("resume_search_condition[accordion_open_dev]", this.isOpen1 ? "yes" : "no");
			params.append("resume_search_condition[accordion_open_infra]", this.isOpen2 ? "yes" : "no");
			params.append("resume_search_condition[accordion_open_design]", this.isOpen3 ? "yes" : "no");
			params.append("resume_search_condition[accordion_open_details]", this.isOpen4 ? "yes" : "no");
			params.append("resume_search_condition[accordion_open_premium]", this.isOpen5 ? "yes" : "no");


			// Xử lý mảng status[]
			if (this.status.length > 0) {
				this.status.forEach(value => {
					params.append("resume_search_condition[status][]", value);
				});
			}

			// Xử lý mảng exp_categories[]
			if (this.exp_categories.length > 0) {
				this.exp_categories.forEach(value => {
					params.append("resume_search_condition[exp_categories][]", value);
				});
			}

			if (this.partner_types.length > 0) {
				this.partner_types.forEach(value => {
					params.append("resume_search_condition[partner_types][]", value);
				});
			}

			if (this.contract_types.length > 0) {
				this.contract_types.forEach(value => {
					params.append("resume_search_condition[contract_types][]", value);
				});
			}

			if (this.utilization_rate.length > 0) {
				this.utilization_rate.forEach(value => {
					params.append("resume_search_condition[utilization_rate][]", value);
				});
			}

			if (this.working_frequency.length > 0) {
				this.working_frequency.forEach(value => {
					params.append("resume_search_condition[working_frequency][]", value);
				});
			}

			if (this.workplaces.length > 0) {
				this.workplaces.forEach(value => {
					params.append("resume_search_condition[workplaces][]", value);
				});
			}

			if (this.rating_flag.length > 0) {
				this.rating_flag.forEach(value => {
					params.append("resume_search_condition[rating_flag][]", value);
				});
			}
			window.location.href = `/resume_search_condition/search?${params.toString()}`;
		},
		restoreSelection() {
			const urlParams = new URLSearchParams(window.location.search);
			const sort_type = urlParams.get('sort_type') || 'created_at';
			this.selectedOption = Object.keys(this.sortMap).find(key => this.sortMap[key] === sort_type) || "新着（降順）";
		},
		restoreSelections() {
			const urlParams = new URLSearchParams(window.location.search);
			const genderEN = urlParams.get("resume_search_condition[gender]");
			const ageMin = urlParams.get("resume_search_condition[age_min]");
			const ageMax = urlParams.get("resume_search_condition[age_max]");

			// Chuyển đổi giá trị từ EN → JP
			this.gender = Object.keys(optionsMappings.gender).find(
				key => optionsMappings.gender[key] === genderEN
			) || "未選択";

			// Kiểm tra và gán giá trị age_min, age_max
			this.age_min = optionsList.age_min.includes(Number(ageMin)) ? Number(ageMin) : "未選択";
			this.age_max = optionsList.age_max.includes(Number(ageMax)) ? Number(ageMax) : "未選択";

			// Gửi sự kiện để directive cập nhật dropdown
			document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "gender", value: this.gender } }));
			document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "age_min", value: this.age_min } }));
			document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "age_max", value: this.age_max } }));
		},
		changePage(page) {
			if (page >= 1 && page <= this.totalPages) {
				window.location.href = `?page=${page}`; // Cập nhật URL và reload trang
			}
		},
		calculateAge(birthday) {
			if (!birthday) return "不明"; // Nếu không có ngày sinh, trả về "不明" (không rõ)

			const birthDate = new Date(birthday); // Chuyển đổi chuỗi ngày sinh thành Date
			const today = new Date(); // Lấy ngày hiện tại
			let age = today.getFullYear() - birthDate.getFullYear();

			return age; // Trả về tuổi kèm theo "歳" (tuổi)
		},
		getGenderLabel(gender) {
			if (gender === "male") return "男性";
			if (gender === "female") return "女性";
		},
		checkNew(updated_at) {
			if (!updated_at) return false; // Tránh lỗi nếu dữ liệu không có

			const updatedDate = new Date(updated_at);

			// Lấy ngày hiện tại (không có giờ, phút, giây)
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			// Lấy ngày cách đây 3 ngày
			const threeDaysAgo = new Date(today);
			threeDaysAgo.setDate(today.getDate() - 3);

			// Kiểm tra nếu `updatedDate` nằm trong khoảng từ 3 ngày trước đến hôm nay
			return updatedDate >= threeDaysAgo && updatedDate <= today;
		},
		async add_viewer(partner_id, user_id) {
			try {
				// Tạo URL với user_id nếu có
				let url = `/api/view_partner?partner_id=${encodeURIComponent(partner_id)}`;
				if (user_id) {
					url += `&user_id=${encodeURIComponent(user_id)}`;
				}

				// Gửi yêu cầu GET đến API
				const response = await fetch(url, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
				});

				// Kiểm tra nếu có lỗi từ phía server
				if (!response.ok) {
					throw new Error('Error: ' + response.statusText);
				}

				// Xử lý kết quả
				const data = await response.json();
				console.log('Response:', data);

			} catch (error) {
				console.error('Fetch error:', error);
				this.errorMessage = error.message;
			}
		},
		loadQueryParams() {
			const urlParams = new URLSearchParams(window.location.search);

			this.free_keyword = urlParams.get("resume_search_condition[free_keyword]") || "";
			this.negative_keyword = urlParams.get("resume_search_condition[negative_keyword]") || "";
			this.nationality = urlParams.get("resume_search_condition[nationality]") || "";
			this.resident = urlParams.get("resume_search_condition[resident]") || "";
			this.gender = urlParams.get("resume_search_condition[gender]") || "";
			this.age_min = urlParams.get("resume_search_condition[age_min]") || "";
			this.age_max = urlParams.get("resume_search_condition[age_max]") || "";
			this.with_internal_comment = urlParams.get("resume_search_condition[with_internal_comment]") || false;
			this.priceMin = urlParams.get("resume_search_condition[asking_unit_price_min]" || "");
			this.priceMax = urlParams.get("resume_search_condition[asking_unit_price_max]")?.trim() || null;
			this.date_period = urlParams.get("resume_search_condition[available_time_at_earliest]" || "");
			this.isOpen = urlParams.get("resume_search_condition[accordion_open_consul]") === "yes";
			this.isOpen1 = urlParams.get("resume_search_condition[accordion_open_dev]") === "yes";
			this.isOpen2 = urlParams.get("resume_search_condition[accordion_open_infra]") === "yes";
			this.isOpen3 = urlParams.get("resume_search_condition[accordion_open_design]") === "yes";
			this.isOpen4 = urlParams.get("resume_search_condition[accordion_open_details]") === "yes";
			this.isOpen5 = urlParams.get("resume_search_condition[accordion_open_premium]") === "yes";


			// Xử lý các tham số dạng mảng
			this.status = urlParams.getAll("resume_search_condition[status][]");
			this.exp_categories = urlParams.getAll("resume_search_condition[exp_categories][]");
			this.partner_types = urlParams.getAll("resume_search_condition[partner_types][]");
			this.contract_types = urlParams.getAll("resume_search_condition[contract_types][]");
			this.utilization_rate = urlParams.getAll("resume_search_condition[utilization_rate][]");
			this.working_frequency = urlParams.getAll("resume_search_condition[working_frequency][]");
			this.workplaces = urlParams.getAll("resume_search_condition[workplaces][]");
			this.rating_flag = urlParams.getAll("resume_search_condition[rating_flag][]");
		},
		saveQuery() {
			this.isSaveQuery = true
			localStorage.setItem('query', window.location.href)
		},
		unSaveQuery() {
			this.isSaveQuery = false
			localStorage.removeItem('query')
		},
		formatCondition(key) {
			if (!this.params_search[key] || this.params_search[key].length === 0) return null;

			// Xử lý tuổi (age_min, age_max)
			if (key === 'resume_search_condition[age_min]' || key === 'resume_search_condition[age_max]') {
				const minAge = this.params_search['resume_search_condition[age_min]']?.[0];
				const maxAge = this.params_search['resume_search_condition[age_max]']?.[0];

				if (minAge && maxAge) return `${minAge}歳 ~ ${maxAge}歳`; // Cả min & max
				if (minAge) return `${minAge}歳以上`; // Chỉ có min
				if (maxAge) return `${maxAge}歳以下`; // Chỉ có max
				return null; // Không có giá trị hợp lệ
			}

			// Xử lý giá (asking_unit_price_min, asking_unit_price_max)
			if (key === 'resume_search_condition[asking_unit_price_min]' || key === 'resume_search_condition[asking_unit_price_max]') {
				const minPrice = this.params_search['resume_search_condition[asking_unit_price_min]']?.[0];
				const maxPrice = this.params_search['resume_search_condition[asking_unit_price_max]']?.[0];

				if (minPrice && maxPrice) return `${minPrice}万円 ~ ${maxPrice}万円`; // Cả min & max
				if (minPrice) return `${minPrice}万円以上`; // Chỉ có min
				if (maxPrice) return `${maxPrice}万円以下`; // Chỉ có max
				return null; // Không có giá trị hợp lệ
			}

			// Trả về các giá trị khác (nếu có)
			const value = this.params_search[key].join(' / ');
			return value ? value : null;
		},
		params() {
			const urlParams = new URLSearchParams(window.location.search);
			const paramObj = {};

			urlParams.forEach((value, key) => {
				if (!paramObj[key]) {
					paramObj[key] = [];
				}
				paramObj[key].push(value);
			});
			this.params_search = paramObj;
			console.log('params_search', this.params_search);
			this.categorizedExp();
		},

		// Nhóm các exp_categories vào category chính
		categorizedExp() {
			if (!this.params_search || !this.params_search["resume_search_condition[exp_categories][]"]) {
				this.categorizedExps = {};
				return;
			}
			const expCategories = this.params_search['resume_search_condition[exp_categories][]'] || [];
			const grouped = {};

			for (const category in this.categoryMapping) {
				const prefix = this.categoryMapping[category];
				const matchedValues = expCategories.filter(value => value.startsWith(prefix));

				if (matchedValues.length) {
					grouped[category] = matchedValues.map(val => val.replace(prefix, ''));
				}
			}
			this.categorizedExps = grouped;
			console.log('categories', this.categorizedExps);
		},
		async resumes_count() {
			this.isLoading = true;
			try {
				let params = new URLSearchParams();
				params.append("resume_search_condition[free_keyword]", this.free_keyword || '');
				params.append("resume_search_condition[negative_keyword]", this.negative_keyword || '');
				params.append("resume_search_condition[nationality]", this.nationality || '');
				params.append("resume_search_condition[resident]", this.resident || '');
				params.append("resume_search_condition[gender]", this.gender === "未選択" ? '' : this.gender);
				params.append("resume_search_condition[age_min]", this.age_min === "未選択" ? '' : this.age_min);
				params.append("resume_search_condition[age_max]", this.age_max === "未選択" ? '' : this.age_max);
				params.append("resume_search_condition[with_internal_comment]", this.with_internal_comment || false);
				params.append("resume_search_condition[asking_unit_price_min]", this.priceMin || '');
				params.append("resume_search_condition[asking_unit_price_max]", this.priceMax || '');
				params.append("resume_search_condition[available_time_at_earliest]", this.date_period || '');

				// Xử lý mảng status[]
				if (this.status.length > 0) {
					this.status.forEach(value => {
						params.append("resume_search_condition[status][]", value);
					});
				}

				// Xử lý mảng exp_categories[]
				if (this.exp_categories.length > 0) {
					this.exp_categories.forEach(value => {
						params.append("resume_search_condition[exp_categories][]", value);
					});
				}

				if (this.partner_types.length > 0) {
					this.partner_types.forEach(value => {
						params.append("resume_search_condition[partner_types][]", value);
					});
				}

				if (this.contract_types.length > 0) {
					this.contract_types.forEach(value => {
						params.append("resume_search_condition[contract_types][]", value);
					});
				}

				if (this.utilization_rate.length > 0) {
					this.utilization_rate.forEach(value => {
						params.append("resume_search_condition[utilization_rate][]", value);
					});
				}

				if (this.working_frequency.length > 0) {
					this.working_frequency.forEach(value => {
						params.append("resume_search_condition[working_frequency][]", value);
					});
				}

				if (this.workplaces.length > 0) {
					this.workplaces.forEach(value => {
						params.append("resume_search_condition[workplaces][]", value);
					});
				}

				if (this.rating_flag.length > 0) {
					this.rating_flag.forEach(value => {
						params.append("resume_search_condition[rating_flag][]", value);
					});
				}
				const user_id = userInfo ? userInfo.user_id : null;
				const company_id = userInfo ? userInfo.user_company_id : null;
				// Gọi API lấy dữ liệu
				const response = await fetch(`/api/resume_count?${params.toString()}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'X-User-ID': user_id,
						'X-Company-ID': company_id
					},
				});

				const data = await response.json();
				if (data.success === true) {
					this.count_resumes = data.total_records;
				} else {
					console.warn("API data is not in the correct format:", data);
				}
			} catch (error) {
				console.error('Error fetching API:', error.message);
				this.errorMessage = `Error fetching opportunities: ${error.message}`;
			} finally {
				this.isLoading = false; // Dừng loading khi API trả về kết quả
			}
		},
		// Hàm gọi khi có sự thay đổi của form
		handleInputChange() {
			// Nếu đã có một timeout trước đó, xóa nó đi để tránh gọi API quá nhiều
			if (this.searchTimeout) {
				clearTimeout(this.searchTimeout);
			}

			// Thiết lập timeout mới để trì hoãn việc gọi API
			this.searchTimeout = setTimeout(() => {
				this.resumes_count(); // Gọi API sau khi người dùng ngừng thay đổi trong 500ms
			}, 500); // 500ms là khoảng thời gian trì hoãn
		},
		async resumes_keyword(keyword) {
			let params = new URLSearchParams();
			params.append("resume_search_condition[free_keyword]", keyword);
			window.location.href = `/resume_search_condition/search?${params.toString()}`;
		},
		async isViewed(partnerId) {
			if (this.viewedStatuses[partnerId] === undefined) {
				await this.fetchViewedStatus(partnerId); // Chỉ gọi API nếu chưa có trong viewedStatuses
			}
			return this.viewedStatuses[partnerId] || false; // Trả về trạng thái đã xem nếu có, false nếu chưa xem
		},

		// Lấy trạng thái đã xem của partner từ API
		async fetchViewedStatus(partnerId) {
			try {
				const response = await fetch(`/api/check_view?user_id=${this.isloggedin}&partner_id=${partnerId}`);
				const data = await response.json();
				this.viewedStatuses[partnerId] = data.success ? data.viewed : false; // Lưu kết quả vào viewedStatuses
			} catch (error) {
				console.error('Error fetching viewed status:', error);
				this.viewedStatuses[partnerId] = false; // Mặc định là chưa xem nếu có lỗi
			}
		},

		// Kiểm tra tất cả các partner đã được xem hay chưa (chỉ gọi API 1 lần cho mỗi partner)
		async checkAllViewStatuses() {
			const partnerIds = this.resumes.map(resume => resume.id);
			await Promise.all(partnerIds.map(id => this.isViewed(id))); // Kiểm tra tất cả partnerId trong danh sách resumes
		},
	},
	async mounted() {
		$(function () {
			$('[data-toggle="tooltip"]').tooltip()
		});
		this.loadQueryParams();
		document.addEventListener("click", this.closeDropdown);
		const params = new URLSearchParams(window.location.search);
        this.sortType = params.get('sort_type') || 'created_at';
        const page = parseInt(params.get('page')) || 1;
		await this.fetch_search(page, this.sortType);
		this.params();
		this.resumes_count();
		if (this.resumes.length > 0) {
			this.checkAllViewStatuses();
		} else {
			console.log('No resumes data available.');
		}
		this.restoreSelection();
		this.restoreSelections();
	},
	computed: {
		visiblePages() {
			let pages = [];
			if (this.totalPages <= 5) {
				pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
			} else if (this.currentPage <= 3) {
				pages = [1, 2, 3, 4, 5];
			} else if (this.currentPage >= this.totalPages - 2) {
				pages = [this.totalPages - 4, this.totalPages - 3, this.totalPages - 2, this.totalPages - 1, this.totalPages];
			} else {
				pages = [this.currentPage - 2, this.currentPage - 1, this.currentPage, this.currentPage + 1, this.currentPage + 2];
			}
			return pages;
		},
		validConditions() {
			if (!this.params_search) return [];

			const uniqueConditions = new Map(); // Dùng Map để tránh trùng lặp key-value

			this.searchConditions.forEach(condition => {
				const value = this.formatCondition(condition.key);
				if (value) {
					uniqueConditions.set(condition.label, { ...condition, value });
				}
			});

			return Array.from(uniqueConditions.values()); // Trả về danh sách đã loại bỏ trùng lặp
		},
		displayedRecords() {
			return this.totalRecord < 24 ? this.totalRecord : 24;
		}
	}
}
export default resumes_search;