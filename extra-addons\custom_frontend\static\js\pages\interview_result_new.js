import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const InterviewResult = {
    template: `
        <main class="margin-header sp_fluid" id="vue-app" data-v-app>
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: 'マッチング状況', link: '/mypage' },
                { text: '面談結果（入力）', link: null, current: true }
            ], 'container-fluid', '')}


            <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                <div class="card px-3 px-md-4 mt-2 pt-5 form-card sp_sides_uniter">
                    <div class="d-flex checkbox-container justify-content-start">
                        <div v-if="userAccess !== 'hiring'" class="custom-control custom-checkbox pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field0" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="scout"
                                   v-model="contractTypes" disabled>
                            <label id="contract_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="contract_types_field0">人財スカウト</label>
                        </div>
                        <div v-if="userAccess !== 'scout'" class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field1" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="case"
                                   v-model="contractTypes" disabled>
                            <label id="contract_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="contract_types_field1">案件ヒアリング</label>
                        </div>
                    </div>

                    <div class="info-container">
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">案件名:</span>
                                <a class="info-value" :href="'/opportunities/' + opp_id + '/manage/edit'">{{ opportunityName }}</a>
                            </div>
                            <div class="info-item">
                                <span class="info-label">人財名:</span>
                                <a class="info-value"  :href="'/resumes/' + resume_id + '/manage/edit'">{{ partnerName }}</a>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex mb-5">
                        <div class="form-check">
                            <input class="form-check-input" id="result_interview0" required="required" type="radio" value="false" name="result" checked @click="ResultInterviewChange">
                            <label id="result_interview_label0" class="form-check-label" for="result_interview0" required="required" >OK</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" id="result_interview1" required="required" type="radio" value="true" name="result" @click="ResultInterviewChange">
                            <label id="result_interview_label1" class="form-check-label" for="result_interview1" required="required">NG</label>
                        </div>
                    </div>

                    <div class="mb-5">
                        <label class="form-label">メッセージ</label>
                        <div class="textarea-container" id="textareaContainer">
                            <textarea id="message" rows="5" class="form-control" :placeholder="isRejected ? '面談結果がNGの理由をご記入ください' : '面談結果に関するメッセージをご記入ください'"></textarea>
                            <div class="d-flex justify-content-between">
                                <CharacterCounter targetId="message" :max-length="2000" />
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-center">
                        <button class="btn mb-5 font-middle white-text btn-default btn-lg width-200 sp-width-150 px-0 submit_change_send_to waves-effect waves-light" @click="submitData">登録する</button>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/interview_result.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            block_date: '',
            contract_types: [],
            isRejected: false,
            opportunity_id: '',
            resume_id: '',
            opportunityName: 'xxxxx',
            partnerName: 'xxxxx',
            isSubmitted: false,
            userAccess: null,
            user_id: userInfo ? userInfo.user_id : null,
            contractTypes: [],
            opp_id: null,
            resume_id: null,
        }
    },
    methods: {
        ResultInterviewChange(event) {
            this.isRejected = event.target.value === 'true';
        },
        async fetchData() {
            const urlParams = new URLSearchParams(window.location.search);
            const opp_id = urlParams.get('opp_id');
            const resume_id = urlParams.get('resume_id');
            this.opp_id = opp_id;
            this.resume_id = resume_id;

            if (opp_id && resume_id) {
                try {
                    const response = await fetch('/api/get_opportunity_and_partner', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ opp_id, resume_id })
                    });
                    const data = await response.json();
                    if (data.result.success) {
                        this.opportunityName = data.result.opportunity_name;
                        this.partnerName = data.result.partner_name;
                    }
                } catch (error) {
                    console.error('Error fetching data:', error);
                }
            }
        },
        async submitData() {
            if (this.isSubmitted) return;
            this.isSubmitted = true;

            const urlParams = new URLSearchParams(window.location.search);
            const opp_id = urlParams.get('opp_id');
            const resume_id = urlParams.get('resume_id');
            const flow_id = urlParams.get('flow_id');
            const message = document.getElementById('message').value;

            try {
                const response = await fetch('/api/interview_result_create', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        opp_id,
                        resume_id,
                        flow_id,
                        contract_types: this.contract_types.join(','),
                        result: this.isRejected ? 'Fail' : 'Pass',
                        message
                    })
                });
                const data = await response.json();
                if (data.result.success) {
                    console.log('Interview result create new successful:', data.result.message);
                    window.location.href = '/mypage';
                } else {
                    console.log('Interview result create new fail:', data.result.message);
                    window.toastr.warning(data.result.message);
                }
            } catch (error) {
                console.error('Error submitting data:', error);
            }
        },

        async checkScout() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const opp_id = urlParams.get('opp_id');
                const resume_id = urlParams.get('resume_id');
                const res = await fetch('/api/check_scout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        resume_id: resume_id,
                        opportunity_id: opp_id,
                        user_id: this.user_id
                    })
                });

                const result = await res.json();

                if (result.result && result.result.success) {
                    if (result.result.type === 'scout') {
                        this.userAccess = 'scout';
                        this.contractTypes = ['scout'];
                    } else if (result.result.type === 'hiring') {
                        this.userAccess = 'hiring';
                        if (!this.contractTypes.includes('case')) {
                            this.contractTypes.push('case');
                        }
                    } else {
                        this.userAccess = 'none';
                        this.contractTypes = [];
                    }
                    return true;
                } else {
                    if(result.result && result.result.redirect) {
                        //window.location.href = result.result.redirect;
                    } else {
                        //window.location.href = '/notfound';
                    }
                    return false;
                }
            } catch(error) {
                console.error("Error checking scout:", error);
                //window.location.href = '/notfound';
                return false;
            }
        }
    },
    async mounted() {
        await this.fetchData();
        await this.checkScout();
    }
}

export default InterviewResult;
