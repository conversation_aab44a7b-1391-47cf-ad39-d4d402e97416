const CharacterCounter = {
    props: {
        targetId: String, // ID của input hoặc textarea cần theo dõi
        maxLength: {
            type: Number,
            default: 70, // <PERSON>iới hạn ký tự mặc định
        }
    },
    data() {
        return {
            currentLength: 0, // Số ký tự hiện tại
        };
    },
    computed: {
        remainingCharacters() {
            return this.maxLength - this.currentLength;
        },
        isOverLimit() {
            return this.remainingCharacters < 0;
        },
        inputStyle() {
            return this.isOverLimit ? "background-color: #ffcac7;" : "";
        },
        textColorStyle() {
            return this.isOverLimit ? "color: red;" : "";
        }
    },
    methods: {
        updateCharacterCount(event) {
            const target = event.target;
            if (target) {
                this.currentLength = target.value.length;

                // Nếu vượt quá giới hạn, đổi màu nền input
                target.style = this.inputStyle;
            }
        }
    },
    mounted() {
        const target = document.getElementById(this.targetId);
        if (target) {
            this.currentLength = target.value.length; // Cập nhật lần đầu
            target.addEventListener('input', this.updateCharacterCount); // Lắng nghe sự kiện input
            target.style = this.inputStyle; // Áp dụng màu ban đầu nếu đã có text
        }
    },
    beforeUnmount() {
        const target = document.getElementById(this.targetId);
        if (target) {
            target.removeEventListener('input', this.updateCharacterCount);
        }
    },
    template: `
        <div class="row px-3 show-count-wrapper">
            <div class="show-count custom-grey-text mb-5" :style="textColorStyle">
                あと{{ remainingCharacters }}文字
            </div>
            <p class="custom-grey-text mb-5">（最大{{ maxLength }}文字まで）</p>
        </div>
    `
};

export default CharacterCounter;
