import { createSingleBreadcrumb } from "../../utils/breadcrumbHelper.js";

const passwordEdit = {
    template: `
<main class="pb-3 margin-header" id="vue-app" v-if="isTokenValid">
    <div style="margin-top: -4vh;" class="container-fluid">
    ${createSingleBreadcrumb('パスワード', 'container-fluid', '')}
        <div class="container-fluid title py-2 py-md-4"><h1 class="mb-0">パスワード</h1></div>
        <div class="container-fluid grabient pt-5">
            <div class="row">
                <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                    <input type="hidden" name="user[reset_password_token]" id="user_reset_password_token" :value="token">

                    <div class="card mb-4">
                        <div class="card-body p-5">
                            <div class="row">
                                <div class="col-12">
                                    <div class="mx-auto mb-5">
                                        <label class="mb-3 font-middle active" for="password_field">パスワード
                                            <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                        </label>
                                        <span class="custom-grey-6-text d-block mb-3">パスワードは8文字以上30文字以内で、小文字・大文字・数字・記号(!@#$%^&*)を含む必要があります。</span>
                                        <input class="form-control" autocomplete="off"
                                               id="password_field" type="password" v-model="password">
                                        <small v-if="passwordError" class="text-danger">{{ passwordError }}</small>
                                    </div>
                                    <div class="mx-auto mb-0">
                                        <label class="mb-3 font-middle" for="password_confirmation_field">パスワードの確認
                                            <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                        </label>
                                        <input class="form-control" autocomplete="off" id="password_confirmation_field"
                                               type="password" v-model="password_confirmation">
                                        <small v-if="passwordConfirmationError" class="text-danger">{{ passwordConfirmationError }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-12 col-md-4 mt-2">
                            <button type="button" @click="submitForm" class="btn btn-default btn-block btn-lg font-middle px-0 waves-effect waves-light">
                                パスワードを変更
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/users/password/password_edit.css"/>
    `,
    data() {
        return {
            token: '',
            password: '',
            password_confirmation: '',
            isTokenValid: false,
            passwordError: '',
            passwordConfirmationError: ''
        };
    },
    methods: {
        async validateToken() {
            const urlParams = new URLSearchParams(window.location.search);
            this.token = urlParams.get('token');
            console.log(this.token);
            if (!this.token) {
                window.location.href = "/login";
                return;
            }

            try {
                const response = await fetch("/api/reset_password_validate", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ token: this.token }),
                });

                const data = await response.json();
                if (data.result.success) {
                    this.isTokenValid = true;
                } else {
                    window.location.href = "/404";
                }
            } catch (error) {

            }
        },

        async submitForm() {
            this.passwordError = '';
            this.passwordConfirmationError = '';

            // Validate password format
            const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[a-zA-Z\d!@#$%^&*]{8,30}$/;

            if (!this.password) {
                this.passwordError = "パスワードを入力してください。";
                return;
            }

            if (!passwordRegex.test(this.password)) {
                this.passwordError = "パスワードは8文字以上30文字以内で、小文字・大文字・数字・記号(!@#$%^&*)を含む必要があります。";
                return;
            }

            if (!this.password_confirmation) {
                this.passwordConfirmationError = "パスワードの確認を入力してください。";
                return;
            }

            if (this.password && this.password_confirmation && this.password !== this.password_confirmation) {
                this.passwordConfirmationError = "パスワードが一致しません。";
                return;
            }

            try {
                const response = await fetch("/api/reset_password", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        token: this.token,
                        new_password: this.password
                    }),
                });

                const data = await response.json();
                if (data.result.success) {
                    window.toastr.success("パスワードが正常に変更されました。");
                    window.location.href = "/login";
                } else {
                    alert(result.message);
                }
            } catch (error) {
                alert("Error connecting to the server.");
            }
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
    },
    mounted() {
        this.validateToken();
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
    }
};

export default passwordEdit;
