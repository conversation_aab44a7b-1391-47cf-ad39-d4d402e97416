from odoo import http
from odoo.http import request
from datetime import datetime
import json

class CompaniesController(http.Controller):
    @http.route('/api/get_comments', type='http', auth='public', methods=['GET'], csrf=False)
    def getComments(self, **kwargs):
        opp_id = kwargs.get('id')

        comments = request.env['vit.comments'].sudo().search([('opportunities_id', '=', int(opp_id))])

        response_data = []
        for comment in comments:
            response_data.append({
                'id': comment.id,
                'resume_id': comment.resume_id.id,
                'opportunities_id': comment.opportunities_id.id,
                'content': comment.content,
                'status': comment.status,
                'created_by': comment.created_by,
                'created_at': comment.created_at.isoformat() if comment.created_at else None,
                'updated_at': comment.updated_at.isoformat() if comment.updated_at else None,
                'updated_by': comment.updated_by
            })

        id = request.session.get('loginId')
        
        return request.make_response(
            json.dumps({'success': True, 'data': response_data, 'id': id}),
            headers={'Content-Type': 'application/json'}
        )
        
    @http.route('/api/add_comment', type='json', auth='public', methods=['POST'], csrf=False)
    def addComment(self):
        data = request.httprequest.get_json(silent=True)
        
        opp_id = data.get('opportunities_id')
        resume_id = data.get('resume_id')
        content = data.get('content')
        status = data.get('status')
        created_by = data.get('created_by')

        if not opp_id or not resume_id or not content:
            return {'success': False, 'message': 'opportunities_id, resume_id, and content are required'}

        new_comment = request.env['vit.comments'].sudo().create({
            'opportunities_id': opp_id,
            'resume_id': resume_id,
            'content': content,
            'status': status,
            'created_by': created_by,
            'created_at': datetime.now(),
        })

        if new_comment:
            return {
            'success': True,
            'message': 'Comment created successfully'
        }
            
        return {'success': False, 'message': 'Can not add comment'}
    
    @http.route('/api/delete_comment', type='json', auth='public', methods=['POST'], csrf=False)
    def delete_comment(self):
        data = request.httprequest.get_json(silent=True)
        comment_id = data.get('id')

        if not comment_id:
            return {'success': False, 'message': 'comment_id is required'}

        comment = request.env['vit.comments'].sudo().search([('id', '=', comment_id)], limit=1)

        if not comment:
            return {'success': False, 'message': 'Comment not found'}

        comment.sudo().write({'status': 0})

        return {'success': True, 'message': 'Comment status updated to 0'}
