from odoo import http
from odoo.http import request
import json

class DashboardController(http.Controller):

    @http.route('/api/dashboard_resumes', type='http', auth="public", methods=['GET'], website=True)
    def getData(self, **kwargs):
        try:
            company_id = kwargs.get("id")
            if not company_id:
                return request.make_response(json.dumps({"success": False, "message": "Missing company_id parameter"}),
                                         headers=[('Content-Type', 'application/json')])
            
            company_id = int(company_id)
            query = """
                WITH all_months AS (
                    SELECT DISTINCT TO_CHAR(create_date, 'YYYY-MM') AS month FROM vit_partner WHERE create_date IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_workflow WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_interviewresult WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_viewresumes WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_scout WHERE created_at IS NOT NULL
                )
                SELECT 
                    am.month,
                    %s AS company_id,
                    COALESCE(total_resumes, 0) AS total_resumes,
                    COALESCE(total_workflows, 0) AS total_workflows,
                    COALESCE(total_interviews, 0) AS total_interviews,
                    COALESCE(total_passed_interviews, 0) AS total_passed_interviews,
                    COALESCE(total_views, 0) AS total_views,
                    COALESCE(total_scouts, 0) AS total_scouts
                FROM all_months am
                LEFT JOIN (
                    SELECT TO_CHAR(p.create_date, 'YYYY-MM') AS month, COUNT(DISTINCT p.id) AS total_resumes
                    FROM vit_partner p
                    WHERE p.company_id = %s AND p.create_date IS NOT NULL
                    GROUP BY month
                ) p ON am.month = p.month
                LEFT JOIN (
                    SELECT TO_CHAR(w.created_at, 'YYYY-MM') AS month, COUNT(DISTINCT w.id) AS total_workflows
                    FROM vit_workflow w
                    JOIN vit_partner p ON w.resumes_id = p.id
                    WHERE p.company_id = %s AND w.created_at IS NOT NULL
                    GROUP BY month
                ) w ON am.month = w.month
                LEFT JOIN (
                    SELECT 
                        TO_CHAR(i.created_at, 'YYYY-MM') AS month, 
                        COUNT(DISTINCT i.id) AS total_interviews,
                        COUNT(DISTINCT i.id) FILTER (WHERE i.result = 'Pass') AS total_passed_interviews
                    FROM vit_interviewresult i
                    JOIN vit_partner p ON i.resume_id = p.id
                    WHERE p.company_id = %s AND i.created_at IS NOT NULL
                    GROUP BY month
                ) i ON am.month = i.month
                LEFT JOIN (
                    SELECT 
                        TO_CHAR(v.created_at, 'YYYY-MM') AS month, 
                        COUNT(DISTINCT v.id) AS total_views
                    FROM vit_viewresumes v
                    JOIN vit_partner p ON v.resumes_id = p.id
                    WHERE p.company_id = %s AND v.created_at IS NOT NULL
                    GROUP BY month
                ) v ON am.month = v.month
                LEFT JOIN (
                    SELECT 
                        TO_CHAR(s.created_at, 'YYYY-MM') AS month, 
                        COUNT(DISTINCT s.id) AS total_scouts
                    FROM vit_scout s
                    JOIN vit_partner p ON s.resumes_id = p.id
                    WHERE p.company_id = %s AND s.created_at IS NOT NULL
                    GROUP BY month
                ) s ON am.month = s.month
                WHERE am.month IS NOT NULL
                ORDER BY month DESC
            """
            
            params = [company_id, company_id, company_id, company_id, company_id, company_id]
            
            request.env.cr.execute(query, params)
            data = request.env.cr.fetchall()

            result = [{
                "month": row[0], 
                "company_id": row[1], 
                "total_resumes": row[2], 
                "total_workflows": row[3], 
                "total_interviews": row[4], 
                "total_passed_interviews": row[5], 
                "total_views": row[6], 
                "total_scouts": row[7]
            } for row in data]

            return request.make_response(json.dumps({"success": True, "data": result}),
                                         headers=[('Content-Type', 'application/json')])

        except Exception as e:
            return request.make_response(json.dumps({"success": False, "message": str(e)}),
                                         headers=[('Content-Type', 'application/json')])
            
    @http.route('/api/dashboard_opp', type='http', auth="public", methods=['GET'], website=True)
    def getData_Opp(self, **kwargs):
        try:
            company_id = kwargs.get("id")
            if not company_id:
                return request.make_response(json.dumps({"success": False, "message": "Missing company_id parameter"}),
                                         headers=[('Content-Type', 'application/json')])
            
            company_id = int(company_id)
            query = """
                WITH all_months AS (
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') AS month FROM vit_opportunities WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_viewopportunities WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_scout WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_workflow WHERE created_at IS NOT NULL
                    UNION
                    SELECT DISTINCT TO_CHAR(created_at, 'YYYY-MM') FROM vit_interviewresult WHERE created_at IS NOT NULL
                )
                SELECT 
                    am.month,
                    %s AS company_id,
                    COALESCE(total_opportunities, 0) AS total_opportunities,
                    COALESCE(total_views, 0) AS total_views,
                    COALESCE(total_scouts, 0) AS total_scouts,
                    COALESCE(total_workflows, 0) AS total_workflows,
                    COALESCE(total_interviews, 0) AS total_interviews,
                    COALESCE(total_passed_interviews, 0) AS total_passed_interviews
                FROM all_months am
                LEFT JOIN (
                    SELECT TO_CHAR(o.created_at, 'YYYY-MM') AS month, COUNT(DISTINCT o.id) AS total_opportunities
                    FROM vit_opportunities o
                    WHERE o.company_id = %s AND o.created_at IS NOT NULL
                    GROUP BY month
                ) o ON am.month = o.month
                LEFT JOIN (
                    SELECT TO_CHAR(v.created_at, 'YYYY-MM') AS month, COUNT(DISTINCT v.id) AS total_views
                    FROM vit_viewopportunities v
                    JOIN vit_opportunities o ON v.opportunities_id = o.id
                    WHERE o.company_id = %s AND v.created_at IS NOT NULL
                    GROUP BY month
                ) v ON am.month = v.month
                LEFT JOIN (
                    SELECT TO_CHAR(s.created_at, 'YYYY-MM') AS month, COUNT(DISTINCT s.id) AS total_scouts
                    FROM vit_scout s
                    JOIN vit_opportunities o ON s.opportunities_id = o.id
                    WHERE o.company_id = %s AND s.created_at IS NOT NULL
                    GROUP BY month
                ) s ON am.month = s.month
                LEFT JOIN (
                    SELECT TO_CHAR(w.created_at, 'YYYY-MM') AS month, COUNT(DISTINCT w.id) AS total_workflows
                    FROM vit_workflow w
                    JOIN vit_opportunities o ON w.opportunities_id = o.id
                    WHERE o.company_id = %s AND w.created_at IS NOT NULL
                    GROUP BY month
                ) w ON am.month = w.month
                LEFT JOIN (
                    SELECT 
                        TO_CHAR(i.created_at, 'YYYY-MM') AS month, 
                        COUNT(DISTINCT i.id) AS total_interviews,
                        COUNT(DISTINCT i.id) FILTER (WHERE i.result = 'Pass') AS total_passed_interviews
                    FROM vit_interviewresult i
                    JOIN vit_opportunities o ON i.opp_id = o.id
                    WHERE o.company_id = %s AND i.created_at IS NOT NULL
                    GROUP BY month
                ) i ON am.month = i.month
                WHERE am.month IS NOT NULL
                ORDER BY month DESC
            """
            
            params = [company_id, company_id, company_id, company_id, company_id, company_id]
            
            request.env.cr.execute(query, params)
            data = request.env.cr.fetchall()

            result = [{"month": row[0], "company_id": row[1], "total_opportunities": row[2], "total_views": row[3], "total_scouts": row[4], "total_workflows": row[5], "total_interviews": row[6], "total_passed_interviews": row[7]} for row in data]

            return request.make_response(json.dumps({"success": True, "data": result}),
                                         headers=[('Content-Type', 'application/json')])

        except Exception as e:
            return request.make_response(json.dumps({"success": False, "message": str(e)}),
                                         headers=[('Content-Type', 'application/json')])

    @http.route('/api/news', type='http', auth="public", methods=['GET'], website=True)
    def get_news(self, **kwargs):
        try:
            # Đọc các tham số GET
            page = int(kwargs.get('page', 1))
            limit = int(kwargs.get('limit', 2))
            offset = (page - 1) * limit

            domain = []  # Có thể thêm lọc theo điều kiện ở đây

            # Lấy tổng số bản ghi
            total = request.env['vit.news'].search_count(domain)

            # Lấy dữ liệu theo phân trang
            news = request.env['vit.news'].search(domain, order='updated_at DESC, id DESC', limit=limit, offset=offset)

            news_list = []
            for n in news:
                news_list.append({
                    'id': n.id,
                    'title': n.title,
                    'content': n.content,
                    'created_at': n.created_at.strftime('%Y-%m-%d %H:%M:%S') if n.created_at else None,
                    'created_by': n.created_by,
                    'updated_at': n.updated_at.strftime('%Y-%m-%d %H:%M:%S') if n.updated_at else None,
                    'updated_by': n.updated_by
                })

            return request.make_response(
                json.dumps({
                    "success": True,
                    "data": news_list,
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit  # làm tròn lên
                }),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            return request.make_response(
                json.dumps({"success": False, "message": str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/api/referral_list', type='http', auth="public", methods=['GET'], website=True)
    def get_referral_list(self, **kwargs):
        try:
            company_id = kwargs.get("id")
            referral_list = request.env['vit.referral_list'].sudo().search([('company_id', '=', int(company_id))], order='created_at DESC')
            referral_list_data = []
            for r in referral_list:
                referral_list_data.append({
                    'id': r.id,
                    'referral_company_id': r.referral_company_id.id,
                    'referral_company_name': r.referral_company_id.name,
                    'referral_company_employee_count': r.referral_company_id.employee_count,
                    'referral_company_established_year': r.referral_company_id.established_year,
                    'referral_company_site_url': r.referral_company_id.company_site_url,
                    'created_at': r.created_at.strftime('%Y-%m-%d %H:%M:%S') if r.created_at else None,
                    'created_by': r.created_by
                })
            return request.make_response(json.dumps({"success": True, "data": referral_list_data}), headers=[('Content-Type', 'application/json')])

        except Exception as e:
            return request.make_response(json.dumps({"success": False, "message": str(e)}), headers=[('Content-Type', 'application/json')])

