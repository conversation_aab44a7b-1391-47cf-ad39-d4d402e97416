const List = {
    'template': `
        <div class="modal fade" id="modelId" tabindex="-1" style="z-index: 100000000; top:200px; opacity:1;" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">List route</h5>
                            <button type="button" class="close py-2" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                    </div>
            <div class="modal-body">
                <div class="container-fluid">
                    <div class="list-router" v-if="routes.length > 0">
                        <ul>
                            <li v-for="route in routes">
                                <router-link :to="{name: route.name}" @click="closeModal()">{{ route.meta.jp }}</router-link>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    `,
    data() {
        return {
            router: VueRouter.useRouter(),
            routes: []
        }
    },
    mounted() {
        this.getList()
    },
    methods: {
     getList() {
       return this.routes = this.router.getRoutes() 
    },
    closeModal() {
        setTimeout(() => {
            $('#modelId').modal('hide')
        }, 100)
    }
}
    
}
export default List