import { createSingleBreadcrumb } from "../../utils/breadcrumbHelper.js";
const Law = {
	'template': `
    <div>
        <!-- Load Law page specific CSS -->
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/law.css">

        <main class="pb-3 margin-header law-page" id="vue-app" data-v-app="">
            ${createSingleBreadcrumb('特定商取引に基づく表記', 'container-fluid','')}
        <section class="guide-section pt-3 userguide-text-black">
          <div class="row">
            <div class="col-12 col-md-8 mx-auto">
              <div class="card sp_sides_uniter" style="margin: 20px;">
                <div class="card-body">
                  <table class="table table-borderless">
                    <tbody>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">販売業者</td>
                        <td>Verticallimit株式会社</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">代表責任者</td>
                        <td>小林　尚</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">所在地</td>
                        <td>
                          <table class="table table-borderless m-0">
                            <tr>
                              <td style="width: 25%;">本社所在地</td>
                              <td>〒534-0025<br>大阪府大阪市都島区片町2-2-40　京橋大発ビル</td>
                            </tr>
                            <tr>
                              <td>東京拠点</td>
                              <td>〒160-0023<br>東京都新宿区西新宿3-3-13　西新宿水間ビル</td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">ホームページ</td>
                        <td><a href="https://verticallimit.co.jp/">https://verticallimit.co.jp/</a></td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">メールアドレス</td>
                        <td><a href="mailto:<EMAIL>"><EMAIL></a></td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">電話番号</td>
                        <td>050-3152-1945<br>(受付時間 月曜日～金曜日 10:00~17:00<br>※土日、祝日はお休み)</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">販売価格</td>
                        <td>クレジットカード決済：ご注文時にお支払いが確定します。</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">商品以外の必要料金</td>
                        <td>なし。<br>（サービス利用に必要な、インターネット接続、回線等の費用は別途お客様にてご用意いただく必要があります。）</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">支払方法と期限</td>
                        <td>クレジットカード決済：ご注文時にお支払いが確定します。</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">キャンセルについて</td>
                        <td>キャンセルを希望される場合は、お申込みから1週間以内に<br>問い合わせフォームよりお問い合わせをお願いいたします。<br>※サービス提供後のキャンセル・返金には応じかねます。</td>
                      </tr>
                      <tr>
                        <td class="font-weight-bold" style="color: rgba(84, 110, 122, 0.87);">中途解約について</td>
                        <td>中途解約の際は、ログイン後のプラン変更画面より次回更新日の10日前までに無料プランへの変更手続きを行ってください。<br>なお、中途解約された場合でも、既にお支払いいただいた料金の返金はいたしかねますので、あらかじめご了承ください。</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </section>
	</main>
    </div>
	`,
    data(){
        return {}
    }
}

export default Law