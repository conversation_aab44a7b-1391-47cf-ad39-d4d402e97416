const RateSlider = {
    props: {
        min: { type: Number, default: 25 },
        max: { type: Number, default: 100 },
        step: { type: Number, default: 25 },
        startValue: {
            type: [Array, Number],
            default: () => 100
        },
    },
    data() {
        return {
            selectedValue: 100,
            slider: null,
        };
    },
    watch: {
        startValue: {
            handler(newValue) {
                // Handle both array (legacy) and single value
                let value = 100; // default
                if (Array.isArray(newValue)) {
                    // If array, take the first value or default to 100
                    value = newValue.length > 0 ? parseInt(newValue[0]) : 100;
                } else if (typeof newValue === 'number') {
                    value = newValue;
                }

                if (value !== this.selectedValue) {
                    this.selectedValue = value;
                    if (this.slider) {
                        this.slider.set(value);
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) {
                // Initialize single-handle slider
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedValue,
                    connect: [true, false], // Connect from start to handle
                    range: { min: this.min, max: this.max },
                    step: this.step,
                });

                // Handle slider update events
                this.slider.on("update", (values) => {
                    const newValue = Math.round(parseFloat(values[0]));
                    this.selectedValue = newValue;
                    // Emit single value instead of array
                    this.$emit("update:rate", newValue);
                });
            }
        });
    },
    computed: {
        displayValue() {
            return this.selectedValue + "%";
        },
    },
    template: `
      <div class="mb-5">
        <p class="d-flex justify-content-center" id="rate-range">
          {{ displayValue }}
        </p>
        <div class="slider-styled mx-4" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>25%</span><span>100%</span>
        </div>
      </div>
    `,
};
export default RateSlider;
