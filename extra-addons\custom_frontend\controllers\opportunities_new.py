from odoo import http
from odoo.http import request
import json
from datetime import datetime
from . import common as global_common
from datetime import datetime
import logging
_logger = logging.getLogger(__name__)
from collections import defaultdict

import logging
_logger = logging.getLogger(__name__)

def convert_date(date_str):
    try:
        return datetime.strptime(date_str, "%Y年%m月%d日").strftime("%Y-%m-%d")
    except ValueError:
        return None  # Nếu lỗi, trả về None

class OpportunitiesNewController(http.Controller):

    @http.route('/api/opp_new', type='json', auth='public', methods=['POST'])
    def signup(self):
        data = request.httprequest.get_json(silent=True)

        company_id = data.get('company_id')
        subject = data.get('subject')
        categories_consultation = data.get('categories_design')
        categories_development = data.get('categories_development')
        categories_infrastructure = data.get('categories_infrastructure')
        categories_design = data.get('categories_operation_maintenance')
        utilization_rate = data.get('utilization_rate')
        unit_price_min = data.get('unit_price_min')
        unit_price_max = data.get('unit_price_max')
        skill_matching_flg = data.get('skill_matching_flg')
        work_frequencies = data.get('work_frequencies')
        specifies_workplaces = data.get('specifies_workplaces')
        contract_startdate_at = data.get('contract_startdate_at')
        contract_enddate_at = data.get('contract_enddate_at')
        possible_continue_flg = data.get('possible_continue_flg')
        requirements = data.get('requirements')
        skill_requirements = data.get('skill_requirements')
        order_accuracy_ids = data.get('order_accuracy_ids')
        involvements = data.get('involvements')
        opp_type_id = data.get('opp_type_id')
        contract_types = data.get('contract_types')
        expired_at = data.get('expired_at')
        participants = data.get('participants')
        interview_count_id = data.get('interview_count_id')
        trading_restriction = data.get('trading_restriction')
        opp_qualities = data.get('opp_qualities')
        public_status_id = data.get('public_status_id')
        publish_company_name_status_id = data.get('publish_company_name_status_id')
        business_field = data.get('business_field')
        status = data.get('status')
        nationality = data.get('nationality')
        resident = data.get('resident')
        interview_start_time = data.get('interview_start_time')
        interview_end_time = data.get('interview_end_time')
        available_monday = data.get('available_monday', False)
        available_tuesday = data.get('available_tuesday', False)
        available_wednesday = data.get('available_wednesday', False)
        available_thursday = data.get('available_thursday', False)
        available_friday = data.get('available_friday', False)
        available_saturday = data.get('available_saturday', False)

        required_fields = [
            subject,
            utilization_rate, unit_price_min, unit_price_max, work_frequencies, specifies_workplaces, contract_startdate_at,
            contract_enddate_at, requirements, skill_requirements, order_accuracy_ids, involvements,
            contract_types, expired_at, participants, interview_count_id, trading_restriction,
            public_status_id, publish_company_name_status_id, business_field, nationality
            # BỎ interview_start_time, interview_end_time khỏi required_fields để cho phép không nhập
        ]
        if involvements == 'enter_sales_channels':
            opp_type_id = ''
        else:
            required_fields.append(opp_type_id)
        if any(field in [None, ""] for field in required_fields):
            error_field = next(field for field in required_fields if field in [None, ""])
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE + str(error_field)}
        if not any(map(bool, [categories_consultation, categories_development, categories_infrastructure, categories_design])):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}
        if nationality != 'Japan' and resident == '':
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}

        # create new opportunity
        db_opp = request.env['vit.opportunities'].sudo().create({
            'company_id': company_id,
            'subject': subject,
            'categories_design': categories_consultation,
            'categories_development': categories_development,
            'categories_infrastructure': categories_infrastructure,
            'categories_operation_maintenance': categories_design,
            'utilization_rate': utilization_rate,
            'unit_price_min': unit_price_min,
            'unit_price_max': unit_price_max,
            'skill_matching_flg': skill_matching_flg,
            'work_frequencies': work_frequencies,
            'specifies_workplaces': specifies_workplaces,
            'contract_startdate_at': contract_startdate_at,
            'contract_enddate_at': contract_enddate_at,
            'possible_continue_flg': possible_continue_flg,
            'requirements': requirements,
            'skill_requirements': skill_requirements,
            'order_accuracy_ids': order_accuracy_ids,
            'involvements': involvements,
            'opp_type_id': opp_type_id,
            'contract_types': contract_types,
            'expired_at': expired_at,
            'participants': participants,
            'interview_count_id': interview_count_id,
            'trading_restriction': trading_restriction,
            'opp_qualities': opp_qualities,
            'public_status_id': public_status_id,
            'publish_company_name_status_id': publish_company_name_status_id,
            'business_field': business_field,
            'status': status,
            'nationality': nationality,
            'resident': resident,
            'created_at': datetime.now(),
            'created_by': request.session.get('loginId'),
            'interview_start_time': interview_start_time,
            'interview_end_time': interview_end_time,
            'available_monday': available_monday,
            'available_tuesday': available_tuesday,
            'available_wednesday': available_wednesday,
            'available_thursday': available_thursday,
            'available_friday': available_friday,
            'available_saturday': available_saturday
        })

        if db_opp:
            return {
                'success': True,
                'message': 'Opportunity created successfully',
            }

    @http.route('/api/opp/charge_accounts', type='http', auth='none', csrf=False)
    def check_charge_accounts(self, **kwargs):
        user_id = kwargs.get('user_id')
        loginEmail = request.session.get('loginEmail')
        company_id = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', loginEmail)], limit=1).company_id.id
        charge_accounts = request.env['vit.users'].with_context(active_test=False).sudo().search([('company_id', '=', company_id)]).mapped(lambda u: {'id': u.id, 'username': u.username, 'email': u.email, 'company_id': u.company_id.id})

        return request.make_response(json.dumps({'charge_accounts': charge_accounts}))

    @http.route('/api/getOpp', type='http', auth='public', methods=['GET'])
    def get_all_opportunities(self, **kwargs):
        user_id = request.httprequest.headers.get('X-User-ID')
        company_id = kwargs.get('company_id')
        page = int(request.httprequest.args.get('page', 1))
        limit = 20
        sort_type = request.httprequest.args.get('sort_type', 'created_at')

        offset = (page - 1) * limit

        valid_sort_fields = ["created_at", "updated_at", "unit_price_max"]
        sort_column = sort_type if sort_type in valid_sort_fields else "created_at"

        # Tạo domain để filter opportunities chưa hết hạn ứng tuyển và chưa đóng
        today = datetime.now().date()

        base_domain = [
            ('expired_at', '>=', today),  # Filter opportunities that haven't expired yet
            ('status', '!=', '0'),  # Filter opportunities that are not closed
            ('public_status_id', '!=', 'private')  # Filter opportunities that are not private
        ]

        if not company_id:
            opportunities = request.env['vit.opportunities'].sudo().search(base_domain, limit=limit, offset=offset, order=f"{sort_column} desc")
            total_opportunities = request.env['vit.opportunities'].sudo().search_count(base_domain)
            total_pages = (total_opportunities + limit - 1) // limit
        else:
            domain = [('company_id', '=', int(company_id))] + base_domain
            opportunities = request.env['vit.opportunities'].sudo().search(domain, limit=limit, offset=offset, order=f"{sort_column} desc")
            total_opportunities = request.env['vit.opportunities'].sudo().search_count(domain)
            total_pages = (total_opportunities + limit - 1) // limit

        opp_ids = opportunities.ids
        reaction_data = {}

        if opp_ids:
            # Đếm số lượng reaction trên mỗi opp
            reaction_counts = request.env['vit.reaction'].sudo().read_group(
                [('opportunities_id', 'in', opp_ids), ('status', '=', 1)],
                ['opportunities_id', 'id:count'],
                ['opportunities_id']
            )
            _logger.info(f"reaction_counts: {reaction_counts}")
            reaction_data = {
                rec['opportunities_id'][0]: rec['opportunities_id_count']
                for rec in reaction_counts
            }

            _logger.info(f"userid: {user_id}")
            # Nếu có user_id, kiểm tra user có react không
            user_react_map = {}
            if user_id:
                user_id = int(user_id) if user_id.isdigit() else None
                if user_id:
                    user_reacts = request.env['vit.reaction'].sudo().search([
                        ('user_id', '=', user_id),
                        ('opportunities_id', 'in', opp_ids),
                        ('status', '=', 1)
                    ])
                    user_react_map = {react.opportunities_id.id: True for react in user_reacts}

        data = []
        for opportunity in opportunities:
            opp_id = opportunity.id
            data.append({
                'id': opp_id,
                'company_id': opportunity.company_id.id if opportunity.company_id else None,
                'company_name': opportunity.company_id.name if opportunity.company_id else '',
                'subject': opportunity.subject,
                'categories_design': opportunity.categories_design,
                'categories_development': opportunity.categories_development,
                'categories_infrastructure': opportunity.categories_infrastructure,
                'categories_operation_maintenance': opportunity.categories_operation_maintenance,
                'utilization_rate': opportunity.utilization_rate,
                'unit_price_min': opportunity.unit_price_min,
                'unit_price_max': opportunity.unit_price_max,
                'skill_matching_flg': opportunity.skill_matching_flg,
                'work_frequencies': opportunity.work_frequencies,
                'specifies_workplaces': opportunity.specifies_workplaces,
                'contract_startdate_at': str(opportunity.contract_startdate_at) if opportunity.contract_startdate_at else '',
                'contract_enddate_at': str(opportunity.contract_enddate_at) if opportunity.contract_enddate_at else '',
                'possible_continue_flg': opportunity.possible_continue_flg,
                'requirements': opportunity.requirements,
                'skill_requirements': opportunity.skill_requirements,
                'order_accuracy_ids': opportunity.order_accuracy_ids,
                'involvements': opportunity.involvements,
                'opp_type_id': opportunity.opp_type_id,
                'contract_types': opportunity.contract_types,
                'expired_at': str(opportunity.expired_at) if opportunity.expired_at else '',
                'participants': opportunity.participants,
                'interview_count_id': opportunity.interview_count_id,
                'trading_restriction': opportunity.trading_restriction,
                'opp_qualities': opportunity.opp_qualities,
                'public_status_id': opportunity.public_status_id,
                'publish_company_name_status_id': opportunity.publish_company_name_status_id,
                'business_field': opportunity.business_field,
                'status': opportunity.status,
                'created_at': str(opportunity.created_at) if opportunity.created_at else '',
                'updated_at': str(opportunity.updated_at) if opportunity.updated_at else '',
                'react_number': reaction_data.get(opp_id, 0),  # Thêm số lượng react
                'is_react': user_react_map.get(opp_id, False),  # Thêm trạng thái react của user hiện tại
                'nationality': opportunity.nationality,
                'resident': opportunity.resident,
                'interview_start_time': opportunity.interview_start_time,
                'interview_end_time': opportunity.interview_end_time,
                'available_monday': opportunity.available_monday,
                'available_tuesday': opportunity.available_tuesday,
                'available_wednesday': opportunity.available_wednesday,
                'available_thursday': opportunity.available_thursday,
                'available_friday': opportunity.available_friday,
                'available_saturday': opportunity.available_saturday
            })

        return request.make_response(
            json.dumps({
                'success': True,
                'data': data,
                'current_page': page,
                'total_pages': total_pages,
                'total_records': total_opportunities
            }),
            headers={'Content-Type': 'application/json'}
        )
    # def is_valid_email(self, email):
    #         pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    #         return re.match(pattern, email) is not None

    @http.route('/api/opp_index', type='http', auth='public', methods=['GET'], csrf=False)
    def get_opp_index(self, **kwargs):
        user_id = kwargs.pop('user_id')
        sort_type = kwargs.get('sort_type', 'updated_at')  # Mặc định sắp xếp theo ngày cập nhật
        order_by = f"{sort_type} desc"  # Mặc định sắp xếp theo ngày tạo mới nhất

        if not user_id:
            opps = request.env['vit.opportunities'].sudo().search([], order=order_by)
        else:
            opps = request.env['vit.opportunities'].sudo().search([('created_by', '=', int(user_id))], order=order_by)

        # Lấy số lượng scout cho mỗi opportunity
        scout_counts = {}
        scout_data = request.env.cr.execute("""
            SELECT opportunities_id, COUNT(*) as scout_number
            FROM vit_scout
            WHERE opportunities_id IN %s
            GROUP BY opportunities_id
        """, (tuple(opps.ids),))
        scout_data = request.env.cr.fetchall()
        for opp_id, count in scout_data:
            scout_counts[opp_id] = count

        # Lấy số lượng workflow cho mỗi opportunity
        workflow_counts = {}
        workflow_data = request.env.cr.execute("""
            SELECT opportunities_id, COUNT(*) as workflow_number
            FROM vit_workflow
            WHERE opportunities_id IN %s
            GROUP BY opportunities_id
        """, (tuple(opps.ids),))
        workflow_data = request.env.cr.fetchall()
        for opp_id, count in workflow_data:
            workflow_counts[opp_id] = count

        company_id = None
        Users = request.env['vit.users'].sudo()
        Company = Users.search([('id', '=', user_id)], limit=1)
        if Company:
            company_id = Company.company_id.id
            _logger.info(f"company_id: {company_id}")

        View = request.env['vit.viewopportunities'].sudo()

        data = []
        for opp in opps:
            view_count = View.search_count([('opportunities_id', '=', opp.id)])
            oppViewUserId = View.search([('opportunities_id', '=', opp.id)])
            for view in oppViewUserId:
                _logger.info(f"oppViewUserId: {view.user_id}")
                if not view.user_id:
                    continue  # bỏ qua nếu không có user
                CompanySub = Users.search([('id', '=', view.user_id.id)], limit=1)
                _logger.info(f"CompanySub: {CompanySub.company_id.id}")
                if CompanySub and CompanySub.company_id.id == company_id:
                    view_count -= 1

            scout_number = scout_counts.get(opp.id, 0)
            workflow_number = workflow_counts.get(opp.id, 0)
            apply_number = workflow_number - scout_number

            # Lấy memo từ comments (status = 1)
            memo_comment = request.env['vit.comments'].sudo().search([
                ('opportunities_id', '=', opp.id),
                ('status', '=', 1)
            ], limit=1)
            memo_content = memo_comment.content if memo_comment else ''

            data.append({
                "id": opp.id,
                "company_id": opp.company_id.id if opp.company_id else "",
                "subject": opp.subject,
                "categories_design": opp.categories_design,
                "categories_development": opp.categories_development,
                "categories_infrastructure": opp.categories_infrastructure,
                "categories_operation_maintenance": opp.categories_operation_maintenance,
                "utilization_rate": opp.utilization_rate,
                "unit_price_min": opp.unit_price_min,
                "unit_price_max": opp.unit_price_max,
                "skill_matching_flg": opp.skill_matching_flg,
                "work_frequencies": opp.work_frequencies,
                "specifies_workplaces": opp.specifies_workplaces,
                "contract_startdate_at": opp.contract_startdate_at.strftime('%Y/%m/%d') if opp.contract_startdate_at else None,
                "contract_enddate_at": opp.contract_enddate_at.strftime('%Y/%m/%d') if opp.contract_enddate_at else None,
                "possible_continue_flg": opp.possible_continue_flg,
                "requirements": opp.requirements,
                "skill_requirements": opp.skill_requirements,
                "order_accuracy_ids": opp.order_accuracy_ids,
                "involvements": opp.involvements,
                "opp_type_id": opp.opp_type_id,
                "contract_types": opp.contract_types,
                "expired_at": opp.expired_at.strftime('%Y/%m/%d') if opp.expired_at else None,
                "participants": opp.participants,
                "interview_count_id": opp.interview_count_id,
                "trading_restriction": opp.trading_restriction,
                "opp_qualities": opp.opp_qualities,
                "public_status_id": opp.public_status_id,
                "publish_company_name_status_id": opp.publish_company_name_status_id,
                "business_field": opp.business_field,
                "status": opp.status,
                "created_at": opp.created_at.strftime('%Y/%m/%d') if opp.created_at else None,
                "created_by": opp.created_by.id if opp.created_by else None,
                "updated_at": opp.updated_at.strftime('%Y/%m/%d') if opp.updated_at else None,
                "updated_by": opp.updated_by.id if opp.updated_by else None,
                "view_count": view_count,
                "scout_number": scout_number,
                "apply_number": apply_number,
                "memo": memo_content,  # Thêm memo content
            })

        return request.make_response(
            json.dumps({
                'success': True,
                'data': data
            }),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/opportunity_manage_condition/search', type='http', auth='public', methods=['GET'], csrf=False)
    def search_opportunities(self, **kwargs):
        domain = []
        sort_type = kwargs.get('sort_type', 'updated_at')  # Mặc định sắp xếp theo ngày cập nhật
        order_by = f"{sort_type} desc"  # Mặc định sắp xếp theo ngày tạo mới nhất

        # Lấy user_id từ kwargs hoặc header (tương tự như opp_index API)
        user_id = kwargs.get('user_id')
        if user_id and str(user_id).isdigit():
            domain.append(('created_by', '=', int(user_id)))
        param_mappings = {
            'opportunity_manage_condition[free_keyword]': ('subject', 'ilike', str),
        }

        # Kiểm tra period_kinds
        period_kinds = kwargs.get('opportunity_manage_condition[period_kinds]')
        date_start = kwargs.get('opportunity_manage_condition[date_start]', '')
        date_end = kwargs.get('opportunity_manage_condition[date_end]', '')

        # Chuyển đổi định dạng
        date_start = convert_date(date_start) if date_start else None
        date_end = convert_date(date_end) if date_end else None

        if period_kinds and date_start and date_end:
            domain.append((period_kinds, '>=', date_start))
            domain.append((period_kinds, '<=', date_end))

        # Xử lý status với logic giống hiển thị
        # Logic này khớp với cách hiển thị status ở frontend:
        # - 募集中 (wanted): status != '0' AND expired_at >= today
        # - 応募期間終了 (end): status != '0' AND expired_at < today
        # - 案件終了 (close): status = '0'
        status = request.httprequest.args.getlist('opportunity_manage_condition[status][]')
        if status:
            from datetime import datetime
            today = datetime.now().date()

            status_conditions = []

            for s in status:
                if s == 'wanted':  # 募集中: status != '0' AND expired_at >= today
                    status_conditions.append([
                        '&',
                        ('status', '!=', '0'),
                        ('expired_at', '>=', today)
                    ])
                elif s == 'end':  # 応募期間終了: status != '0' AND expired_at < today
                    status_conditions.append([
                        '&',
                        ('status', '!=', '0'),
                        ('expired_at', '<', today)
                    ])
                elif s == 'close':  # 案件終了: status = '0'
                    status_conditions.append([('status', '=', '0')])

            # Nếu có nhiều status được chọn, dùng OR để kết hợp
            if len(status_conditions) > 1:
                # Tạo OR domain cho các điều kiện status
                or_domain = []
                for i in range(len(status_conditions) - 1):
                    or_domain.append('|')
                for condition in status_conditions:
                    or_domain.extend(condition)
                domain.extend(or_domain)
            elif len(status_conditions) == 1:
                domain.extend(status_conditions[0])

        # Xử lý public_status_id
        public_status_id = request.httprequest.args.getlist('opportunity_manage_condition[public_status_id][]')
        if public_status_id:
            domain.append(('public_status_id', 'in', public_status_id))

        # Xử lý match_status (選考中) - filter những opportunity có workflow
        match_status = kwargs.get('opportunity_manage_condition[match_status]')
        if match_status:
            # Lấy danh sách opportunity_id có workflow (đang trong quá trình tuyển chọn)
            workflow_opp_ids = request.env['vit.workflow'].sudo().search([]).mapped('opportunities_id.id')
            if workflow_opp_ids:
                domain.append(('id', 'in', workflow_opp_ids))
            else:
                # Nếu không có workflow nào, trả về empty result
                domain.append(('id', '=', False))

        # Xử lý các tham số khác theo param_mappings
        for param, (field, operator, cast_type) in param_mappings.items():
            if param in kwargs and kwargs[param]:
                try:
                    domain.append((field, operator, cast_type(kwargs[param])))
                except ValueError:
                    pass

        # Debug: Log domain và parameters
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"Search domain: {domain}")
        _logger.info(f"Search kwargs: {kwargs}")

        # Truy vấn dữ liệu từ Odoo
        opportunities = request.env['vit.opportunities'].sudo().search(domain, order=order_by)
        _logger.info(f"Found {len(opportunities)} opportunities")

        # Chuẩn bị dữ liệu trả về
        results = []
        for opp in opportunities:
            # Lấy memo từ comments (status = 1)
            memo_comment = request.env['vit.comments'].sudo().search([
                ('opportunities_id', '=', opp.id),
                ('status', '=', 1)
            ], limit=1)
            memo_content = memo_comment.content if memo_comment else ''

            # Tính toán số lượng workflow và scout
            workflow_count = request.env['vit.workflow'].sudo().search_count([('opportunities_id', '=', opp.id)])
            scout_count = request.env['vit.scout'].sudo().search_count([('opportunities_id', '=', opp.id)])
            view_count = request.env['vit.viewopportunities'].sudo().search_count([('opportunities_id', '=', opp.id)])

            results.append({
                "id": opp.id,
                "company_id": opp.company_id.id if opp.company_id else "",
                "subject": opp.subject,
                "categories_design": opp.categories_design,
                "categories_development": opp.categories_development,
                "categories_infrastructure": opp.categories_infrastructure,
                "categories_operation_maintenance": opp.categories_operation_maintenance,
                "utilization_rate": opp.utilization_rate,
                "unit_price_min": opp.unit_price_min,
                "unit_price_max": opp.unit_price_max,
                "skill_matching_flg": opp.skill_matching_flg,
                "work_frequencies": opp.work_frequencies,
                "specifies_workplaces": opp.specifies_workplaces,
                "contract_startdate_at": opp.contract_startdate_at.strftime('%Y/%m/%d') if opp.contract_startdate_at else None,
                "contract_enddate_at": opp.contract_enddate_at.strftime('%Y/%m/%d') if opp.contract_enddate_at else None,
                "possible_continue_flg": opp.possible_continue_flg,
                "requirements": opp.requirements,
                "skill_requirements": opp.skill_requirements,
                "order_accuracy_ids": opp.order_accuracy_ids,
                "involvements": opp.involvements,
                "opp_type_id": opp.opp_type_id,
                "contract_types": opp.contract_types,
                "expired_at": opp.expired_at.strftime('%Y/%m/%d') if opp.expired_at else None,
                "participants": opp.participants,
                "interview_count_id": opp.interview_count_id,
                "trading_restriction": opp.trading_restriction,
                "opp_qualities": opp.opp_qualities,
                "public_status_id": opp.public_status_id,
                "publish_company_name_status_id": opp.publish_company_name_status_id,
                "business_field": opp.business_field,
                "status": opp.status if opp.status else None,
                "created_at": opp.created_at.strftime('%Y/%m/%d') if opp.created_at else None,
                "created_by": opp.created_by.id if opp.created_by else None,
                "updated_at": opp.updated_at.strftime('%Y/%m/%d') if opp.updated_at else None,
                "updated_by": opp.updated_by.id if opp.updated_by else None,
                "view_count": view_count,
                "scout_number": scout_count,
                "apply_number": workflow_count - scout_count,
                "memo": memo_content,  # Thêm memo content
            })

        return request.make_response(json.dumps({'results': results}), headers={'Content-Type': 'application/json'})

    @http.route('/api/opportunity_search_condition/search', type='http', auth='public', methods=['GET'], csrf=False)
    def opportunity_search_condition(self):
        today = datetime.now().date()

        args = [
            ('expired_at', '>=', today),
            ('status', '!=', '0'),  # Filter opportunities that are not closed
            ('public_status_id', '!=', 'private')  # Filter opportunities that are not private
        ]

        page = int(request.httprequest.args.get('page', 1))
        limit = 20
        offset = (page - 1) * limit
        sort_type = request.httprequest.args.get('sort_type', 'created_at')
        valid_sort_fields = ["created_at", "updated_at", "unit_price_max"]
        sort_column = sort_type if sort_type in valid_sort_fields else "created_at"

        # Filter by unit price
        unit_price_min = request.httprequest.args.get('opportunity_search_condition[unit_price_min]')
        unit_price_max = request.httprequest.args.get('opportunity_search_condition[unit_price_max]')
        if unit_price_min:
            args.append(('unit_price_min', '>=', int(unit_price_min)))
        if unit_price_max:
            args.append(('unit_price_max', '<=', int(unit_price_max)))

        # Keyword search
        free_keyword = request.httprequest.args.get('opportunity_search_condition[free_keyword]')
        if free_keyword:
            args.append('|')
            args.append(('subject', 'ilike', free_keyword))
            args.append(('skill_requirements', 'ilike', free_keyword))

        # Negative keyword search
        negative_keyword = request.httprequest.args.get('opportunity_search_condition[negative_keyword]')
        if negative_keyword:
            args.append(('subject', 'not ilike', negative_keyword))
            args.append(('skill_requirements', 'not ilike', negative_keyword))

        contract_startdate_at = request.httprequest.args.get('opportunity_search_condition[contract_startdate_at]')
        if contract_startdate_at:
            try:
                contract_startdate_at = datetime.strptime(contract_startdate_at, "%Y年%m月%d日").strftime("%Y-%m-%d")
                args.append(('contract_startdate_at', '>=', contract_startdate_at))
            except ValueError:
                pass
        # Filter by categories
        prefix_mapping = {
            "operation": "categories_operation_maintenance",
            "dev": "categories_development",
            "infra": "categories_infrastructure",
            "design": "categories_design"
        }

        # Các tham số có nhiều giá trị cần xử lý với "ilike"
        multi_value_params = [
            'trading_restriction'
        ]
        single_value_params = ['involvements', 'participants', 'interview_count_id', ]

        # Xử lý opp_categories với prefix_mapping
        selected_category = request.httprequest.args.get('opportunity_search_condition[opp_categories]')
        if selected_category:
            prefix = selected_category.split("_")[0]
            if prefix in prefix_mapping:
                field_name = prefix_mapping[prefix]
                args.append((field_name, 'ilike', f'%{selected_category}%'))

        # Xử lý business_field
        business_field = request.httprequest.args.get('opportunity_search_condition[business_field]')
        if business_field:
            args.append(('business_field', 'ilike', f'%{business_field}%'))

        #Filter by participants
        participant_min = request.httprequest.args.get('opportunity_search_condition[participants_min]')
        participant_max = request.httprequest.args.get('opportunity_search_condition[participants_max]')
        if participant_min:
            args.append(('participants', '>=', int(participant_min)))
        if participant_max:
            args.append(('participants', '<=', int(participant_max)))

        #Filter by nationality
        nationality = request.httprequest.args.get('opportunity_search_condition[nationality]')
        if nationality:
            if nationality == 'japanese_only':
                args.append(('nationality', 'ilike', 'Japan'))
            else:
                resident = request.httprequest.args.get('opportunity_search_condition[status_resident]')
                args.append(('nationality', 'not ilike', 'Japan'))
                if resident:
                    args.append(('resident', 'ilike', resident))

        #Filter by order_accuracy_id
        order_accuracy_ids = request.httprequest.args.getlist('opportunity_search_condition[order_accuracy_id][]')
        if order_accuracy_ids:
            domain = []
            if len(order_accuracy_ids) > 1:
                domain.extend(['|'] * (len(order_accuracy_ids) - 1))
            domain.extend([('order_accuracy_ids', 'ilike', f'%{id}%') for id in order_accuracy_ids])
            args.extend(domain)

        #Filter by opp_type_id
        opp_type_id = request.httprequest.args.get('opportunity_search_condition[opp_type_id]')
        if opp_type_id:
            args.append(('opp_type_id', 'ilike', f'%{opp_type_id}%'))

        #Filter by contract_types
        contract_types = request.httprequest.args.get('opportunity_search_condition[contract_types]')
        if contract_types:
            args.append(('contract_types', 'ilike', f'%{contract_types}%'))

        # Xử lý utilization_rate range
        utilization_rate_min = request.httprequest.args.get('opportunity_search_condition[utilization_rate_min]')
        utilization_rate_max = request.httprequest.args.get('opportunity_search_condition[utilization_rate_max]')
        if utilization_rate_min or utilization_rate_max:
            # Range search for utilization rate
            try:
                if utilization_rate_min and utilization_rate_max:
                    # Both min and max specified
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
                elif utilization_rate_min:
                    # Only min specified
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                elif utilization_rate_max:
                    # Only max specified
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
            except (ValueError, TypeError):
                # If conversion fails, skip utilization rate filtering
                pass
        else:
            # Fallback to old array-based search for backward compatibility
            utilization_rates = request.httprequest.args.getlist('opportunity_search_condition[utilization_rate][]')
            if utilization_rates:
                domain = []
                if len(utilization_rates) > 1:
                    domain.extend(['|'] * (len(utilization_rates) - 1))
                domain.extend([('utilization_rate', 'ilike', f'%{rate}%') for rate in utilization_rates])
                args.extend(domain)

        # Xử lý work_frequencies range
        work_frequencies_min = request.httprequest.args.get('opportunity_search_condition[work_frequencies_min]')
        work_frequencies_max = request.httprequest.args.get('opportunity_search_condition[work_frequencies_max]')
        if work_frequencies_min or work_frequencies_max:
            # Range search for work frequencies - need to map string values to numeric for comparison
            freq_order = {
                'full_remote': 0,
                'less_than_1day': 1,
                '1day': 2,
                '2days': 3,
                '3days': 4,
                '4days': 5,
                '5days': 6,
                '2to4days': 3.5  # Legacy value, treat as middle of range
            }

            # Convert frequency keys to numeric values for range comparison
            min_val = freq_order.get(work_frequencies_min, 0) if work_frequencies_min else 0
            max_val = freq_order.get(work_frequencies_max, 6) if work_frequencies_max else 6

            # Build OR condition for all frequencies in the range
            freq_domain = []
            matching_freqs = [key for key, val in freq_order.items() if min_val <= val <= max_val]

            if matching_freqs:
                if len(matching_freqs) > 1:
                    freq_domain.extend(['|'] * (len(matching_freqs) - 1))
                freq_domain.extend([('work_frequencies', 'ilike', f'%{freq}%') for freq in matching_freqs])
                args.extend(freq_domain)
        else:
            # Fallback to old array-based search for backward compatibility
            work_frequencies = request.httprequest.args.getlist('opportunity_search_condition[work_frequencies][]')
            if work_frequencies:
                domain = []
                if len(work_frequencies) > 1:
                    domain.extend(['|'] * (len(work_frequencies) - 1))
                domain.extend([('work_frequencies', 'ilike', f'%{freq}%') for freq in work_frequencies])
                args.extend(domain)

        # Xử lý các tham số khác
        for param in multi_value_params:
            if param:  # Skip đã xử lý ở trên
                values = request.httprequest.args.getlist(f'opportunity_search_condition[{param}][]')
                if values:
                    domain = []
                    if len(values) > 1:
                        domain.extend(['|'] * (len(values) - 1))
                    domain.extend([(param, 'ilike', f'%{v}%') for v in values])
                    args.extend(domain)

        # **Xử lý specifies_workplaces với OR**
        specifies_workplaces = request.httprequest.args.getlist('opportunity_search_condition[workplaces][]')
        if specifies_workplaces:
            domain = []
            if len(specifies_workplaces) > 1:
                domain.extend(['|'] * (len(specifies_workplaces) - 1))
            domain.extend([('specifies_workplaces', 'ilike', f'%{workplace.strip()}%') for workplace in specifies_workplaces])
            args.extend(domain)

        # **Xử lý single_value_params với AND**
        for param in single_value_params:
            if (value := request.httprequest.args.get(f'opportunity_search_condition[{param}]')):
                args.append((param, '=', value))

        # **Xóa '&' đầu tiên nếu có**
        if args and args[0] == '&':
            args.pop(0)  # Tránh lỗi cú pháp khi '&' đứng đầu

        _logger.info("🔍 ORM Query Args: %s", args)
        opps = request.env['vit.opportunities'].sudo().search_read(args, limit=limit, offset=offset, order=f"{sort_column} desc")
        total_count = request.env['vit.opportunities'].sudo().search_count(args)
        total_pages = (total_count + limit - 1) // limit

        opp_ids = [opp['id'] for opp in opps]
        reaction_data = {}

        if opp_ids:
            # Đếm số lượng reaction trên mỗi opp
            reaction_counts = request.env['vit.reaction'].sudo().read_group(
                [('opportunities_id', 'in', opp_ids), ('status', '=', 1)],
                ['opportunities_id', 'id:count'],
                ['opportunities_id']
            )
            _logger.info(f"reaction_counts: {reaction_counts}")
            reaction_data = {
                rec['opportunities_id'][0]: rec['opportunities_id_count']
                for rec in reaction_counts
            }

            user_id = request.httprequest.headers.get('X-User-ID')
            _logger.info(f"userid: {user_id}")
            # Nếu có user_id, kiểm tra user có react không
            user_react_map = {}
            if user_id:
                user_id = int(user_id) if user_id.isdigit() else None
                if user_id:
                    user_reacts = request.env['vit.reaction'].sudo().search([
                        ('user_id', '=', user_id),
                        ('opportunities_id', 'in', opp_ids),
                        ('status', '=', 1)
                    ])
                    user_react_map = {react.opportunities_id.id: True for react in user_reacts}

        # Prepare response
        data = [{
            'id': opp['id'],
            'company_id': opp['company_id'] if opp['company_id'] else "",
            'subject': opp['subject'],
            'categories_design': opp['categories_design'],
            'categories_development': opp['categories_development'],
            'categories_infrastructure': opp['categories_infrastructure'],
            'categories_operation_maintenance': opp['categories_operation_maintenance'],
            'utilization_rate': opp['utilization_rate'],
            'unit_price_min': opp['unit_price_min'],
            'unit_price_max': opp['unit_price_max'],
            'skill_matching_flg': opp['skill_matching_flg'],
            'work_frequencies': opp['work_frequencies'],
            'specifies_workplaces': opp['specifies_workplaces'],
            'contract_startdate_at': opp['contract_startdate_at'].strftime('%Y/%m/%d') if opp['contract_startdate_at'] else None,
            'contract_enddate_at': opp['contract_enddate_at'].strftime('%Y/%m/%d') if opp['contract_enddate_at'] else None,
            'possible_continue_flg': opp['possible_continue_flg'],
            'requirements': opp['requirements'],
            'skill_requirements': opp['skill_requirements'],
            'order_accuracy_ids': opp['order_accuracy_ids'],
            'involvements': opp['involvements'],
            'opp_type_id': opp['opp_type_id'],
            'contract_types': opp['contract_types'],
            'expired_at': opp['expired_at'].strftime('%Y/%m/%d') if opp['expired_at'] else None,
            'participants': opp['participants'],
            'interview_count_id': opp['interview_count_id'],
            'trading_restriction': opp['trading_restriction'],
            'opp_qualities': opp['opp_qualities'],
            'public_status_id': opp['public_status_id'],
            'publish_company_name_status_id': opp['publish_company_name_status_id'],
            'status': opp['status'] if opp['status'] else None,
            'created_at': opp['created_at'].strftime('%Y/%m/%d') if opp['created_at'] else None,
            'created_by': opp['created_by'] if opp['created_by'] else None,
            'updated_at': opp['updated_at'].strftime('%Y/%m/%d') if opp['updated_at'] else None,
            'updated_by': opp['updated_by'] if opp['updated_by'] else None,
            'view_count': opp['view_count'],
            'scout_count': opp['scout_count'],
            'application_receipt': opp['application_receipt'],
            'react_number': reaction_data.get(opp['id'], 0),
            'is_react': user_react_map.get(opp['id'], False),
        } for opp in opps]

        return request.make_response(
            json.dumps({
                'success': True,
                'data': data,
                'current_page': page,
                'total_pages': total_pages,
                'total_records': total_count
            }),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/opportunity_count', type='http', auth='public', methods=['GET'], csrf=False)
    def count_opp(self):
        today = datetime.now().date()

        args = [
            ('expired_at', '>=', today),
            ('status', '!=', '0'),  # Filter opportunities that are not closed
            ('public_status_id', '!=', 'private')  # Filter opportunities that are not private
        ]

        # Filter by unit price
        unit_price_min = request.httprequest.args.get('opportunity_search_condition[unit_price_min]')
        unit_price_max = request.httprequest.args.get('opportunity_search_condition[unit_price_max]')
        if unit_price_min:
            args.append(('unit_price_min', '>=', int(unit_price_min)))
        if unit_price_max:
            args.append(('unit_price_max', '<=', int(unit_price_max)))

        # Keyword search
        free_keyword = request.httprequest.args.get('opportunity_search_condition[free_keyword]')
        if free_keyword:
            args.append('|')
            args.append(('subject', 'ilike', free_keyword))
            args.append(('skill_requirements', 'ilike', free_keyword))

        # Negative keyword search
        negative_keyword = request.httprequest.args.get('opportunity_search_condition[negative_keyword]')
        if negative_keyword:
            args.append(('subject', 'not ilike', negative_keyword))
            args.append(('skill_requirements', 'not ilike', negative_keyword))

        contract_startdate_at = request.httprequest.args.get('opportunity_search_condition[contract_startdate_at]')
        if contract_startdate_at:
            try:
                contract_startdate_at = datetime.strptime(contract_startdate_at, "%Y年%m月%d日").strftime("%Y-%m-%d")
                args.append(('contract_startdate_at', '>=', contract_startdate_at))
            except ValueError:
                pass
        # Filter by categories
        prefix_mapping = {
            "operation": "categories_operation_maintenance",
            "dev": "categories_development",
            "infra": "categories_infrastructure",
            "design": "categories_design"
        }

        # Các tham số có nhiều giá trị cần xử lý với "ilike"
        multi_value_params = [
            'trading_restriction'
        ]
        single_value_params = ['involvements', 'participants', 'interview_count_id', ]

        # Xử lý opp_categories với prefix_mapping
        selected_category = request.httprequest.args.get('opportunity_search_condition[opp_categories]')
        if selected_category:
            prefix = selected_category.split("_")[0]
            if prefix in prefix_mapping:
                field_name = prefix_mapping[prefix]
                args.append((field_name, 'ilike', f'%{selected_category}%'))

        # Xử lý business_field
        business_field = request.httprequest.args.get('opportunity_search_condition[business_field]')
        if business_field:
            args.append(('business_field', 'ilike', f'%{business_field}%'))

        #Filter by participants
        participant_min = request.httprequest.args.get('opportunity_search_condition[participants_min]')
        participant_max = request.httprequest.args.get('opportunity_search_condition[participants_max]')
        if participant_min:
            args.append(('participants', '>=', int(participant_min)))
        if participant_max:
            args.append(('participants', '<=', int(participant_max)))

        #Filter by nationality
        nationality = request.httprequest.args.get('opportunity_search_condition[nationality]')
        if nationality:
            if nationality == 'japanese_only':
                args.append(('nationality', 'ilike', 'Japan'))
            else:
                resident = request.httprequest.args.get('opportunity_search_condition[status_resident]')
                args.append(('nationality', 'not ilike', 'Japan'))
                if resident:
                    args.append(('resident', 'ilike', resident))

        #Filter by order_accuracy_id
        order_accuracy_ids = request.httprequest.args.getlist('opportunity_search_condition[order_accuracy_id][]')
        if order_accuracy_ids:
            domain = []
            if len(order_accuracy_ids) > 1:
                domain.extend(['|'] * (len(order_accuracy_ids) - 1))
            domain.extend([('order_accuracy_ids', 'ilike', f'%{id}%') for id in order_accuracy_ids])
            args.extend(domain)

        #Filter by opp_type_id
        opp_type_id = request.httprequest.args.get('opportunity_search_condition[opp_type_id]')
        if opp_type_id:
            args.append(('opp_type_id', 'ilike', f'%{opp_type_id}%'))

        #Filter by contract_types
        contract_types = request.httprequest.args.get('opportunity_search_condition[contract_types]')
        if contract_types:
            args.append(('contract_types', 'ilike', f'%{contract_types}%'))

        # Xử lý utilization_rate range
        utilization_rate_min = request.httprequest.args.get('opportunity_search_condition[utilization_rate_min]')
        utilization_rate_max = request.httprequest.args.get('opportunity_search_condition[utilization_rate_max]')
        if utilization_rate_min or utilization_rate_max:
            # Range search for utilization rate
            try:
                if utilization_rate_min and utilization_rate_max:
                    # Both min and max specified
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
                elif utilization_rate_min:
                    # Only min specified
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                elif utilization_rate_max:
                    # Only max specified
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
            except (ValueError, TypeError):
                # If conversion fails, skip utilization rate filtering
                pass
        else:
            # Fallback to old array-based search for backward compatibility
            utilization_rates = request.httprequest.args.getlist('opportunity_search_condition[utilization_rate][]')
            if utilization_rates:
                domain = []
                if len(utilization_rates) > 1:
                    domain.extend(['|'] * (len(utilization_rates) - 1))
                domain.extend([('utilization_rate', 'ilike', f'%{rate}%') for rate in utilization_rates])
                args.extend(domain)

        # Xử lý work_frequencies range
        work_frequencies_min = request.httprequest.args.get('opportunity_search_condition[work_frequencies_min]')
        work_frequencies_max = request.httprequest.args.get('opportunity_search_condition[work_frequencies_max]')
        if work_frequencies_min or work_frequencies_max:
            # Range search for work frequencies - need to map string values to numeric for comparison
            freq_order = {
                'full_remote': 0,
                'less_than_1day': 1,
                '1day': 2,
                '2days': 3,
                '3days': 4,
                '4days': 5,
                '5days': 6,
                '2to4days': 3.5  # Legacy value, treat as middle of range
            }

            # Convert frequency keys to numeric values for range comparison
            min_val = freq_order.get(work_frequencies_min, 0) if work_frequencies_min else 0
            max_val = freq_order.get(work_frequencies_max, 6) if work_frequencies_max else 6

            # Build OR condition for all frequencies in the range
            freq_domain = []
            matching_freqs = [key for key, val in freq_order.items() if min_val <= val <= max_val]

            if matching_freqs:
                if len(matching_freqs) > 1:
                    freq_domain.extend(['|'] * (len(matching_freqs) - 1))
                freq_domain.extend([('work_frequencies', 'ilike', f'%{freq}%') for freq in matching_freqs])
                args.extend(freq_domain)
        else:
            # Fallback to old array-based search for backward compatibility
            work_frequencies = request.httprequest.args.getlist('opportunity_search_condition[work_frequencies][]')
            if work_frequencies:
                domain = []
                if len(work_frequencies) > 1:
                    domain.extend(['|'] * (len(work_frequencies) - 1))
                domain.extend([('work_frequencies', 'ilike', f'%{freq}%') for freq in work_frequencies])
                args.extend(domain)

        # Xử lý các tham số khác
        for param in multi_value_params:
            if param:  # Skip đã xử lý ở trên
                values = request.httprequest.args.getlist(f'opportunity_search_condition[{param}][]')
                if values:
                    domain = []
                    if len(values) > 1:
                        domain.extend(['|'] * (len(values) - 1))
                    domain.extend([(param, 'ilike', f'%{v}%') for v in values])
                    args.extend(domain)

        # **Xử lý specifies_workplaces với OR**
        specifies_workplaces = request.httprequest.args.getlist('opportunity_search_condition[workplaces][]')
        if specifies_workplaces:
            domain = []
            if len(specifies_workplaces) > 1:
                domain.extend(['|'] * (len(specifies_workplaces) - 1))
            domain.extend([('specifies_workplaces', 'ilike', f'%{workplace.strip()}%') for workplace in specifies_workplaces])
            args.extend(domain)

        # **Xử lý single_value_params với AND**
        for param in single_value_params:
            if (value := request.httprequest.args.get(f'opportunity_search_condition[{param}]')):
                args.append((param, '=', value))

        # **Xóa '&' đầu tiên nếu có**
        if args and args[0] == '&':
            args.pop(0)  # Tránh lỗi cú pháp khi '&' đứng đầu

        total_count = request.env['vit.opportunities'].sudo().search_count(args)
        return json.dumps({ 'success': True, 'total_records': total_count})

    @http.route('/api/view_opp', type='http', auth='public', methods=['POST'], csrf=False)
    def add_viewer_to_opp(self, **kwargs):
        user_id = kwargs.get('user_id')
        opp_id = kwargs.get('opp_id')
        _logger.info("👀 User %s viewed opp %s", user_id, opp_id)
        # Nếu user_id hợp lệ, thêm bản ghi vào vit.viewopportunities
        if user_id:
            request.env['vit.viewopportunities'].sudo().create({
                'user_id': int(user_id),
                'opportunities_id': int(opp_id),
                'created_at': datetime.now(),
            })

        return json.dumps({
            'success': True,
            'message': f'User (ID: {user_id if user_id else "Anonymous"}) viewed partner {opp_id}',
            'opp_id': opp_id,
        })

    @http.route('/api/check_view_opp', type='http', auth='public', methods=['GET'], csrf=False)
    def check_view_opp(self, **kwargs):
        user_id = kwargs.get('user_id')
        opp_id = kwargs.get('opp_id')

        if not user_id or not opp_id:
            return json.dumps({'success': False, 'message': 'Missing user_id or opp_id'})

        # Ép kiểu dữ liệu để tránh lỗi so sánh chuỗi và số nguyên
        try:
            user_id = int(user_id)
            opp_id = int(opp_id)
        except ValueError:
            return json.dumps({'success': False, 'message': 'Invalid user_id or opp_id'})

        # Truy vấn lấy danh sách kết quả thay vì chỉ đếm số lượng
        viewed_records = request.env['vit.viewopportunities'].sudo().search([
            ('user_id', '=', user_id),
            ('opportunities_id', '=', opp_id)
        ])

        # Ghi log danh sách bản ghi tìm thấy
        _logger.info("🔍 Checking views for user %s on partner %s: Found %s records -> %s",
                     user_id, opp_id, len(viewed_records), viewed_records)

        return json.dumps({
            'success': True,
            'viewed': bool(viewed_records)  # Trả về True nếu có ít nhất một bản ghi
        })
