from odoo import http
from odoo.http import request, Response
import json

class CurriculumVitaeController(http.Controller):
    @http.route('/api/check_cv_exist', type='http', auth='public', methods=['GET'], csrf=False)
    def check_cv_exist(self, **kwargs):
        resume_id = kwargs.get('id')
        if not resume_id:
            return Response(json.dumps({"success": False, "error": "Required fields or id are missing."}),
                            content_type="application/json", status=400)
        
        try:
            resume_id = int(resume_id)
            existing_cv = request.env["vit.curriculum_vitae"].sudo().search([("resume_id", "=", resume_id)], limit=1)
            
            if existing_cv:
                cv_data = {
                    "id": existing_cv.id,
                    "name": existing_cv.name,
                    #"file": existing_cv.file,  
                    "create_date": existing_cv.create_date.strftime("%Y-%m-%d %H:%M:%S"),
                    "write_date": existing_cv.write_date.strftime("%Y-%m-%d %H:%M:%S") if existing_cv.write_date else None
                }
                return Response(json.dumps({"success": True, "data": cv_data}),
                                content_type="application/json", status=200)
            else:
                return Response(json.dumps({"success": False, "message": "CV is not found"}),
                                content_type="application/json", status=200)
        
        except Exception as e:
            return Response(json.dumps({"success": False, "message": str(e)}),
                            content_type="application/json", status=500)

