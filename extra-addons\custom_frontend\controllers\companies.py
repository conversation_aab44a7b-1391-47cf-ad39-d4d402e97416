from odoo import http
from odoo.http import request
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class CompaniesController(http.Controller):
    @http.route('/api/get_company', type='json', auth='public', methods=['POST'], csrf=False)
    def getCompany(self):
        data = request.httprequest.get_json(silent=True)

        com_id = data.get('id')

        today = datetime.now().date()
        company = request.env['vit.companies'].sudo().browse(int(com_id))
        res_of_company = request.env['vit.partner'].sudo().search([('company_id', '=', int(com_id))])
        opp_of_company = request.env['vit.opportunities'].sudo().search([('company_id', '=', int(com_id)), ('expired_at', '>=', today)])

        if not company.exists():
            return {'success': False, 'message': 'company not found'}

        print(f"res_of_company: {res_of_company}")  # Debug: In danh sách lấy được
        print(f"opp_of_company: {opp_of_company}")  # Debug: In danh sách lấy được

        response_data = {
            'id': company.id,
            'name': company.name,
            'referral_code': company.referral_code,
            'addresses': company.addresses,
            'phone_number': company.phone_number,
            'email': company.email,
            'representative': company.representative,
            'charter_capital': company.charter_capital,
            'employee_count': company.employee_count,
            'industry': company.industry,
            'specialty_industry': company.specialty_industry,
            'public_status': company.public_status,
            'description': company.description,
            'options_bussiness': company.options_bussiness,
            'head_office_location': company.head_office_location,
            'brand_location': company.brand_location,
            'established_year': company.established_year,
            'has_dispatch_license': company.has_dispatch_license,
            'response_rate': company.response_rate,
            'interview_count': company.interview_count,
            'successful_contracts': company.successful_contracts,
            'high_ratings_count': company.high_ratings_count,
            'company_site_url': company.company_site_url,
            'created_at': company.created_at.isoformat() if company.created_at else None,
            'created_by': company.created_by,
            'updated_at': company.updated_at.isoformat() if company.updated_at else None,
            'updated_by': company.updated_by,
            'res': [],
            'opp': []
        }

        for res in res_of_company:
            response_data['res'].append({
                'id': res.id,
                'company_id': res.company_id

            })

        for opp in opp_of_company:
            response_data['opp'].append({
                'id': opp.id,
                'company_id': opp.company_id
            })

        return {'success': True, 'data': response_data}


    @http.route('/api/get_company_name', type='json', auth='public', methods=['POST'], csrf=False)
    def getCompanyName(self):
        data = request.httprequest.get_json(silent=True)

        com_id = data.get('id')

        if not com_id:
            return {'success': False, 'message': 'company id is required'}
        else:
            company = request.env['vit.companies'].sudo().browse(int(com_id))


        if not company.exists():
            return {'success': False, 'message': 'company not found'}

        return {'success': True, 'company_name': company.name, 'referral_code': company.referral_code}

    @http.route('/api/company_edit', type='json', auth='public', methods=['POST'])
    def company_edit(self):
        data = request.httprequest.get_json(silent=True)

        # Log toàn bộ dữ liệu nhận được
        _logger.info(f"Received data for company_edit: {data}")

        com_id = data.get('id')
        user_id = data.get('user_id')
        name = data.get('name')
        addresses = data.get('addresses')
        phone_number = data.get('phone_number')
        charter_capital = data.get('charter_capital')
        employee_count = data.get('employee_count')
        industry = data.get('specialty')
        specialty_industry = data.get('specialty_industry')
        public_status = data.get('public_status')
        description = data.get('description')
        options_bussiness = data.get('options_bussiness')
        head_office_location = data.get('head_office_location')
        brand_location = data.get('brand_location')
        established_year = data.get('established_year')
        has_dispatch_license = data.get('has_dispatch_license')
        company_site_url = data.get('company_site_url')
        updated_at = data.get('updated_at')

        company = request.env['vit.companies'].sudo().search([('id', '=', com_id)], limit=1)

        if not company:
            return {'success': False, 'message': 'company not found'}

        company.sudo().write({
            'name': name,
            'addresses': addresses,
            'phone_number': phone_number,
            'charter_capital': charter_capital,
            'employee_count': employee_count,
            'industry': industry,
            'specialty_industry': specialty_industry,
            'public_status': public_status,
            'description': description,
            'options_bussiness': options_bussiness,
            'head_office_location': head_office_location,
            'brand_location': brand_location,
            'established_year': established_year,
            'has_dispatch_license': has_dispatch_license,
            'company_site_url': company_site_url,
            'updated_at': updated_at,
        })

        # Cập nhật số điện thoại cho người dùng
        if user_id:
            try:
                _logger.info(f"Searching for user with ID: {user_id}")
                user = request.env['vit.users'].sudo().search([('id', '=', int(user_id))], limit=1)
                _logger.info(f"Found user: {user}")

                if user:
                    # Log thông tin trước khi cập nhật
                    _logger.info(f"Updating user phone: user_id={user_id}, phone_number={phone_number}")
                    user.sudo().write({
                        'phone': phone_number,
                    })
                    # Đọc lại thông tin người dùng sau khi cập nhật
                    user = request.env['vit.users'].sudo().search([('id', '=', int(user_id))], limit=1)
                    # Log thông tin sau khi cập nhật
                    _logger.info(f"Updated user phone: user_id={user_id}, new_phone={user.phone}")
                else:
                    _logger.error(f"User not found with ID: {user_id}")
            except Exception as e:
                _logger.error(f"Error updating user phone: {str(e)}")
        return {'success': True, 'message': '会社が更新されました。'}

    @http.route('/api/get_average_scores', type='json', auth='public', methods=['POST'], csrf=False)
    def get_average_scores(self):
        data = request.httprequest.get_json(silent=True)
        com_id = data.get('company_id')

        if not com_id:
            return {'success': False, 'message': 'company id is required'}

        try:
            # Step 1: Get all resume_ids from partner table
            partners = request.env['vit.partner'].sudo().search([('company_id', '=', com_id)])
            resume_ids = partners.mapped('id')

            # Step 2: Get workflows with resume_ids from step 1
            workflows = request.env['vit.workflow'].sudo().search([
                ('resumes_id', 'in', resume_ids)
            ])

            # Step 3: Calculate average score for each workflow
            all_evaluations = []
            for workflow in workflows:
                # Lấy tất cả đánh giá cho workflow này
                evaluations = request.env['vit.workflow_evaluation'].sudo().search([
                    ('workflow_id', '=', workflow.id)
                ])
                all_evaluations.extend(evaluations)

            # Tính điểm trung bình cho toàn bộ công ty
            if all_evaluations:
                total_score = sum(eval.score for eval in all_evaluations)
                avg_score = total_score / len(all_evaluations)
            else:
                avg_score = 0

            return { 'success': True, 'data': round(avg_score, 2) }

        except Exception as e:
            return {'success': False, 'message': 'An error occurred while processing the request', 'error': str(e)}