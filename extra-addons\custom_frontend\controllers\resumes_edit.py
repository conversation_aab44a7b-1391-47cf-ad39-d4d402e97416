from odoo import http
from odoo.http import request
import json
from . import common as global_common

class ResumesEditController(http.Controller):

    @http.route('/api/res_edit', type='http', auth='public', methods=['GET'], csrf=False)
    def get_res(self, **kwargs):
        res_id = request.params.get('id')

        if not res_id or not str(res_id).isdigit():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Invalid or missing resume ID'}),
                headers={'Content-Type': 'application/json'}
            )

        resume = request.env['vit.partner'].sudo().browse(int(res_id))

        if not resume.exists():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Resume not found'}),
                headers={'Content-Type': 'application/json'}
            )

        def serialize_value(value):
            if isinstance(value, bytes):  # Nếu là bytes, ch<PERSON><PERSON><PERSON> sang string
                return value.decode('utf-8', errors='ignore')
            elif isinstance(value, (int, float, str, bool, type(None))):
                return value  # Nếu là kiểu dữ liệu JSON hợp lệ
            elif hasattr(value, 'strftime'):  # Nếu là datetime, chuyển thành chuỗi
                return value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, list):  # Nếu là list, xử lý từng phần tử
                return [serialize_value(v) for v in value]
            elif isinstance(value, dict):  # Nếu là dict, xử lý từng phần tử
                return {k: serialize_value(v) for k, v in value.items()}
            else:
                return str(value)  # Chuyển tất cả kiểu dữ liệu khác thành chuỗi

        # Chuyển đổi tất cả giá trị thành JSON-compatible
        data = {
            'resume_visibility': serialize_value(resume.resume_visibility),
            'status': serialize_value(resume.status),
            'company_publish': serialize_value(resume.company_publish),
            'human_publish': serialize_value(resume.human_publish), 
            'date_period' : serialize_value(resume.date_period),
            'user_id': serialize_value(resume.user_id),
            'cv_id': serialize_value(resume.cv_id),
            'opp_id': serialize_value(resume.opp_id),
            'user_name': serialize_value(resume.user_name),
            'initial_name': serialize_value(resume.initial_name),
            'gender': serialize_value(resume.gender),
            'birthday': serialize_value(resume.birthday),
            'nationality': serialize_value(resume.nationality),
            'resident': serialize_value(resume.resident),
            'categories_consultation': serialize_value(resume.categories_consultation),
            'categories_development': serialize_value(resume.categories_development),
            'categories_infrastructure': serialize_value(resume.categories_infrastructure),
            'categories_design': serialize_value(resume.categories_design),
            'experience_pr': serialize_value(resume.experience_pr),
            'qualification': serialize_value(resume.qualification),
            'characteristic': serialize_value(resume.characteristic),
            'utilization_rate': serialize_value(resume.utilization_rate),
            'unit_price_min': serialize_value(resume.unit_price_min),
            'unit_price_max': serialize_value(resume.unit_price_max),
            'region': serialize_value(resume.region),
            'working_frequency': serialize_value(resume.working_frequency),
            'working_location': serialize_value(resume.working_location),
            'working_hope': serialize_value(resume.working_hope),
            'company_settings': serialize_value(resume.company_settings),
            'created_at': serialize_value(resume.created_at),
            'created_by': serialize_value(resume.created_by),
            'updated_at': serialize_value(resume.updated_at),
            'updated_by': serialize_value(resume.updated_by),
            'del_flg': serialize_value(resume.del_flg),
            'contract_types': serialize_value(resume.contract_types),
            'partner_types': serialize_value(resume.partner_types)
        }

        return request.make_response(
            json.dumps({'success': True, 'data': data}),
            headers={'Content-Type': 'application/json'}
        )
        
    @http.route('/api/res_edit', type='json', auth='public', methods=['POST'], csrf=False)
    def edit_res(self):
        data = request.httprequest.get_json(silent=True)

        res_id = data.get('id')
        if not res_id:
            return {'success': False, 'message': 'ID is required'}
        
        db_res = request.env['vit.partner'].sudo().search([('id', '=', res_id)], limit=1)
        if not db_res:
            return {'success': False, 'message': 'Resume not found'}

        resume_visibility = data.get('resume_visibility')
        status = data.get('status')
        company_publish = data.get('company_publish')
        human_publish = data.get('human_publish')
        date_period = data.get('date_period')
        user_id = data.get('user_id')
        cv_id = data.get('cv_id')
        opp_id = data.get('opp_id')
        user_name = data.get('user_name')
        initial_name = data.get('initial_name')
        gender = data.get('gender')
        birthday = data.get('birthday')
        nationality = data.get('nationality')
        resident = data.get('resident')
        categories_consultation = data.get('categories_consultation')
        categories_development = data.get('categories_development')
        categories_infrastructure = data.get('categories_infrastructure')
        categories_design = data.get('categories_design')
        experience_pr = data.get('experience_pr')
        qualification = data.get('qualification')
        characteristic = data.get('characteristic')
        utilization_rate = data.get('utilization_rate')
        unit_price_min = data.get('unit_price_min')
        unit_price_max = data.get('unit_price_max')
        region = data.get('region')
        working_frequency = data.get('working_frequency')
        working_location = data.get('working_location')
        working_hope = data.get('working_hope')
        company_settings = data.get('company_settings')
        created_at = data.get('created_at')
        created_by = data.get('created_by')
        updated_at = data.get('updated_at')
        updated_by = data.get('updated_by')
        contract_types = data.get('contract_types')
        partner_types = data.get('partner_types')
        
        required_fields = [
            resume_visibility, status, 
            # user_id, cv_id, opp_id,user_name, 
            initial_name, gender, birthday, nationality, experience_pr, 
            # qualification, resident characteristic,
            utilization_rate,
            unit_price_min, unit_price_max, working_frequency, working_location, company_settings, partner_types, contract_types
            # working_hope, region
            # created_at, created_by, updated_at, updated_by
        ]


        if any(field in [None, ""] for field in required_fields):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}
        if not any(map(bool, [categories_consultation, categories_development, categories_infrastructure, categories_design])):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}
        if status == "will_be_available" and not date_period:
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}
        if resume_visibility == "limited" and not company_publish and not human_publish:
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}
        
        update_fields = {
            'resume_visibility': resume_visibility,
            'status': status,
            'company_publish': company_publish,
            'human_publish': human_publish, 
            'date_period': date_period,
            'user_id': user_id,
            'cv_id': cv_id,
            'opp_id': opp_id,
            'user_name': user_name,
            'initial_name': initial_name,
            'gender': gender,
            'birthday': birthday,
            'nationality': nationality,
            'resident': resident,
            'categories_consultation': categories_consultation,
            'categories_development': categories_development,
            'categories_infrastructure': categories_infrastructure,
            'categories_design': categories_design,
            'experience_pr': experience_pr,
            'qualification': qualification,
            'characteristic': characteristic,
            'utilization_rate': utilization_rate,
            'unit_price_min': unit_price_min,
            'unit_price_max': unit_price_max,
            'region': region,
            'working_frequency': working_frequency,
            'working_location': working_location,
            'working_hope': working_hope,
            'company_settings': company_settings,
            'updated_at': updated_at,
            'contract_types': contract_types,
            'partner_types': partner_types
        }

        db_res.sudo().write(update_fields)
        
        if db_res:
            return {'success': True, 'message': 'Update successful'}
        
        return {'success': False, 'message': 'Update failed'}


    @http.route('/api/res_close', type='http', auth='public', methods=['POST'], csrf=False)
    def del_res(self):
        data = request.httprequest.get_json(silent=True)

        res_id = data.get('id')
        if not res_id:
            return {'success': False, 'message': 'ID is required'}
        
        db_res = request.env['vit.partner'].sudo().search([('id', '=', res_id)], limit=1)
        if not db_res:
            return {'success': False, 'message': 'Resume not found'}
        
        db_res.sudo().unlink()

        return request.make_response(
            json.dumps({'success': True, 'message': 'Delete successful'}),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/resume_memo_update', type='json', auth='public', methods=['POST'], csrf=False)
    def update_resume_memo(self):
        """
        API để cập nhật memo của resume sử dụng vit.comments model
        """
        data = request.httprequest.get_json(silent=True)

        resume_id = data.get('resume_id')
        memo = data.get('memo', '')

        if not resume_id:
            return {'success': False, 'message': 'Resume ID is required'}

        # Tìm resume trong database
        resume = request.env['vit.partner'].sudo().search([('id', '=', resume_id)], limit=1)

        if not resume:
            return {'success': False, 'message': 'Resume not found'}

        # Tìm comment memo hiện tại cho resume này (status = 1 để phân biệt với comment thường)
        existing_memo = request.env['vit.comments'].sudo().search([
            ('resume_id', '=', resume_id),
            ('status', '=', 1)  # status = 1 cho memo
        ], limit=1)

        from datetime import datetime

        if existing_memo:
            # Cập nhật memo hiện tại
            existing_memo.sudo().write({
                'content': memo,
                'updated_at': datetime.now()
            })
        else:
            # Tạo memo mới
            request.env['vit.comments'].sudo().create({
                'resume_id': resume_id,
                'content': memo,
                'status': 1,  # status = 1 cho memo
                'created_by': 'system',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            })

        return {
            'success': True,
            'message': 'Memo updated successfully',
            'resume_id': resume_id,
            'memo': memo
        }
    