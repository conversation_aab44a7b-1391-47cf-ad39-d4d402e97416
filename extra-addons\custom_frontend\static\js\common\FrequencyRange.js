const FrequencyRangeSlider = {
    props: {
        min: { type: Number, default: 0 },
        max: { type: Number, default: 6 },
        step: { type: Number, default: 1 },
        startValues: {
            type: Array,
            default: () => [0, 6] // Default range: フルリモート to 5日
        },
    },
    data() {
        return {
            selectedRange: [0, 6], // Default range
            slider: null,
            frequencyMap: {
                "full_remote": 0,
                "less_than_1day": 1,
                "1day": 2,
                "2days": 3,
                "3days": 4,
                "4days": 5,
                "5days": 6
            },
            displayMap: {
                0: "フルリモート",
                1: "1日未満",
                2: "1日",
                3: "2日",
                4: "3日",
                5: "4日",
                6: "5日"
            },
            frequencyValues: ["full_remote", "less_than_1day", "1day", "2days", "3days", "4days", "5days"]
        };
    },
    watch: {
        startValues: {
            handler(newValues) {
                if (Array.isArray(newValues) && newValues.length >= 2) {
                    // Convert string values to numbers if needed
                    let minVal = 0, maxVal = 6;
                    
                    if (typeof newValues[0] === 'string') {
                        minVal = this.frequencyMap.hasOwnProperty(newValues[0]) ? this.frequencyMap[newValues[0]] : 0;
                    } else {
                        minVal = newValues[0] || 0;
                    }
                    
                    if (typeof newValues[1] === 'string') {
                        maxVal = this.frequencyMap.hasOwnProperty(newValues[1]) ? this.frequencyMap[newValues[1]] : 6;
                    } else {
                        maxVal = newValues[1] || 6;
                    }
                    
                    this.selectedRange = [minVal, maxVal];
                    if (this.slider) {
                        this.slider.set([minVal, maxVal]);
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) {
                // Initialize dual-handle range slider
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedRange,
                    connect: true, // Connect between handles
                    range: { min: this.min, max: this.max },
                    step: this.step,
                    margin: 0, // Allow same value selection
                });

                // Handle slider update events
                this.slider.on("update", (values) => {
                    const minValue = Math.round(parseFloat(values[0]));
                    const maxValue = Math.round(parseFloat(values[1]));
                    this.selectedRange = [minValue, maxValue];
                    
                    // Emit range of frequency values
                    const minKey = this.frequencyValues[minValue];
                    const maxKey = this.frequencyValues[maxValue];
                    this.$emit("update:frequency", [minKey, maxKey]);
                });
            }
        });
    },
    methods: {
        getDisplayValue(value) {
            return this.displayMap[value] || "フルリモート";
        }
    },
    computed: {
        displayRange() {
            const minDisplay = this.getDisplayValue(this.selectedRange[0]);
            const maxDisplay = this.getDisplayValue(this.selectedRange[1]);
            
            if (this.selectedRange[0] === this.selectedRange[1]) {
                // Single value selected
                return minDisplay;
            } else {
                // Range selected
                return `${minDisplay} 〜 ${maxDisplay}`;
            }
        },
    },
    template: `
      <div class="mb-5">
        <p class="d-flex justify-content-center" id="frequency-range">
          {{ displayRange }}
        </p>
        <div class="slider-styled mx-4" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>フルリモート</span><span>5日</span>
        </div>
      </div>
    `,
};
export default FrequencyRangeSlider;
