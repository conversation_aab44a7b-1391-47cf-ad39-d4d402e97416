.matching-request-form {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 5rem !important;
}

.matching-request-form h3 {
    color: #1072e9;
    font-weight: 600;
}

.matching-request-form label {
    font-weight: bold;
    color: #333;
    position: relative;
}
.matching-request-form label::after{
    content: "";
    background-color: #455965;
    display: block;
    height: 100%;
    width: .428rem;
    position: absolute;
    left: -1.285rem;
    top: 0;
}

.matching-request-form .required-badge {
    background-color: #ff6b88;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.matching-request-form .form-control {
    border: none;
    border-bottom: 1px solid #1072e9;
    border-radius: 0;
    background-color: #eee;
    color: rgba(0, 0, 0, 0.87);
    height: inherit;
    padding: .75rem .75rem;
    cursor: text;
}

.matching-request-form .form-control:focus {
    border: none;

    border-radius: 0;
    background-color: #eee;
    color: rgba(0, 0, 0, 0.87);
    height: inherit;
    padding: .75rem .75rem;
    cursor: text;
}
.matching-request-form input[type="text"] {
    border: none;
    border-bottom: 1px solid #1072e9;
    border-radius: 0;
    background-color: #eee;
    color: rgba(0, 0, 0, 0.87);
    height: inherit;
    padding: .75rem .75rem;
    cursor: text;
}
.matching-request-form .form-control[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

.matching-request-form textarea {
    resize: vertical;
    min-height: 120px;
}

.matching-request-form .btn-primary {
    background-color: #1072e9;
    border-color: #1072e9;
    font-weight: 500;
    padding: 0.5rem 2rem;
    border-radius: 4px;
}

.matching-request-form .btn-primary:hover {
    background-color: #1072e9;
    border-color: #1072e9;
}

.title {
    background-color: #1072e9;
    color: white;
}


.breadcrumb-arrow {
    display: inline-block;
    margin: 0 0.5rem;
    position: relative;
    width: 10px;
    height: 10px;
}

.tabButton {
    height: 5vh;
    width: 15.8%;
    border: 1px solid rgb(10, 0, 0);
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-top: 9px;
}

.current {
    color: #666;
}
.label-com{
    font-weight: bold;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.5); /* More transparent background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(3px); /* Add a slight blur effect */
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.55);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.loading-text {
    color: #4472C4;
    font-weight: bold;
    text-align: center;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.25rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* Add shadow for better visibility */
}

.grabient {
    padding-top: 32px !important;
}

@media screen and (max-width: 767px) {
    .matching-request-form {
        padding: 1.5rem !important;
        margin-left: 1rem !important;
        margin-right: 1rem !important;
    }

    .matching-request-form label::after {
        background-color: transparent;
    }

    .container-fluid  {
        padding-top: 5.2rem !important;
        padding-left: 0 !important;
    }

    h1 {
        font-size: 1.8rem !important;
    }


    .grabient {
        padding-top: 0 !important;
    }

    .hihi{
        padding-right: 0 !important;
    }

    /* Mobile CSS for tab container */
    .container-fluid div[style*="display: flex"] {
        
        padding: 0 15px !important;
        margin-top: -90px !important;
        gap: 4% !important;
    }
    

    /* Active state for both tabs */
    .tabButton[style*="background: linear-gradient"] {
        background: linear-gradient(to right, rgb(236, 228, 111), rgb(252, 189, 17)) !important;
        color: white !important;
        border-color: #f5c542 !important;
        box-shadow: 0 2px 4px rgba(245, 197, 66, 0.3) !important;
        width: 54%;
    }

    .tabButton {
        width: 50%;
    }

    /* Touch feedback */
    .tabButton:active {
        transform: scale(0.98) !important;
    }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
    .container-fluid  {
        padding-top: 2.1rem !important;
        padding-left: 0 !important;
    }

    .matching-request-form {
        padding: 2rem !important;
    }

    .matching-request-form label::after {
        background-color: transparent;
    }

    .text-center {
        margin: 0 !important;
    }

    .pb-3.margin-header {
        margin-top: 5.5rem !important;
    }

    .grabient {
        padding-top: 32px !important;
    }

    h1 {
        font-size: 1.8rem !important;
    }


}
