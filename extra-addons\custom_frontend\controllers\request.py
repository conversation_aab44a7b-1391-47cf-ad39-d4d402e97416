from odoo import http
from odoo.http import request
from .common import send_email_from_template

class RequestController(http.Controller):
    @http.route('/api/send_request', type='json', auth="public", methods=['POST'])
    def send_request(self):
        data = request.httprequest.get_json(silent=True)

        referral_code = data.get('referral_code')
        content = data.get('content')
        type = data.get('type')

        if not referral_code or not content:
            return {'success': False, 'message': 'Thiếu thông tin bắt buộc'}

        try:
            vals = {
                'referral_code': referral_code,
                'department': '',  # Set empty string as default
                'position': '',    # Set empty string as default
                'type': type,
                'content': content,
                'status': 0
            }

            request_obj = request.env['vit.request'].sudo().create(vals)

            return {
                'success': True,
                'message': 'Request created successfully'
            }

        except Exception as e:
            return {'success': False, 'message': str(e)}

    @http.route('/api/send_mail_request', type='json', auth='public', methods=['POST'], csrf=False)
    def send_mail_request(self):
        data = request.httprequest.get_json(silent=True)

        reference_code = data.get('reference_code')
        type = data.get('type')
        message = data.get('message')
        company = data.get('company')
        user_name = data.get('user_name')
        email = data.get('email')
        phone = data.get('phone')

        if not type or not message or not user_name or not email or not reference_code:
            return {'success': False, 'message': 'Missing required fields'}

        # Sử dụng template từ database
        
        # Xác định template dựa trên loại request
        template_name = "Request: Matching Opportunity"  # Default to opportunity
        if "人財" in type:
            template_name = "Request: Matching Resume"
        
        context_data = {
            'type': type,
            'reference_code': reference_code,
            'message': message,
            'company': company or '',
            'user_name': user_name,
            'email': email,
            'phone': phone,
            'user_email': "<EMAIL>"  # Email admin
        }
        
        result = send_email_from_template(template_name, context_data)
        
        if not result['success']:
            return {'success': False, 'message': result['message']}

        return {'success': True, 'message': 'Request sent successfully'}