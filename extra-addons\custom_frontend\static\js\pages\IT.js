import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const it = {
    'template': `
<div style = "margin-top:79px" >
${createBreadcrumb([
    { text: 'Mi52TOP', link: '/' },
    { text: '案件を探す', link: null, current: true }
])}
<div class="container-fluid title py-2 py-md-4"><h1 class="mb-0">案件を探す</h1></div>
<div class="container-fluid grabient pt-5">
    <div class="row">
        <div class="d-none d-md-block col-12 col-md-4 col-lg-3" id="side-search">
            <div class="card py-4 side-card"><i
                    class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
                <div class="mb-3">
                    <div class="reset_link_area-pc mx-3 d-none"><a
                            class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light"
                            href="/opportunities/active"><span>保存済み条件で検索</span></a></div>
                    <div class="reset_link_area-sp d-none"><a class="reset_search_condition mx-0"
                                                              href="/opportunities/active"><span>保存条件で検索</span></a>
                    </div>
                </div>
                <form class="new_opportunity_search_condition" id="opportunity_search_condition_form" novalidate=""
                      action="/opportunity_search_conditions/search" accept-charset="UTF-8" method="get"><input
                        name="utf8" type="hidden" autocomplete="off" value="✓"><input id="hidden_unit_price_min"
                                                                                      autocomplete="off" type="hidden"
                                                                                      name="opportunity_search_condition[unit_price_min]"
                                                                                      value=""><input
                        id="hidden_unit_price_max" autocomplete="off" type="hidden"
                        name="opportunity_search_condition[unit_price_max]" value="">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 px-0 px-md-3"><label
                                    class="font-middle mb-3 ex-bold">フリーワード</label>
                                <div class="mb-4">
                                    <div class="vertical-top d-inline-block w-100" data-html="true"
                                                           data-toggle="tooltip" title=""
                                                           data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
                                        <div class="mb-1"><input class="form-control"
                                                                 autocomplete="off" id="free_keyword_field" type="text"
                                                                 name="opportunity_search_condition[free_keyword]">
                                        </div>
                                    </div>
                                    <div class="mb-3"><span class="font-middle">を含む</span></div>
                                    <div class="mb-1"><input class="form-control"
                                                             autocomplete="off" id="negative_keyword_field" type="text"
                                                             name="opportunity_search_condition[negative_keyword]">
                                    </div>
                                    <div class="mb-3"><span class="font-middle">を除く</span></div>
                                </div>
                                <div class="mb-5"><label class="font-middle mb-3 ex-bold">得意領域</label>
                                    <div class="consul_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">コンサル</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_up</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_consul]"
                                           id="opportunity_search_condition_accordion_open_consul" value="yes">
                                    <div class="accordion_contents_consul" style="display: block;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul0" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_pmo"><label id="opp_categories_field_label_0"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul1" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_pmpl"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="consul1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul2" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_strategy"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="consul2">戦略</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul3" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_work"><label id="opp_categories_field_label_3"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="consul3">業務</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul4" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_it"><label id="opp_categories_field_label_4"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="consul4">IT</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul5" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_rpa"><label id="opp_categories_field_label_5"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul5">RPA</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul6" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_erp"><label id="opp_categories_field_label_6"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul6">ERP・PKG</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul7" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_security"><label
                                                        id="opp_categories_field_label_7"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="consul7">セキュリティ</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dev_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear"><span
                                            class="font-size-middle ex-bold">開発</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_dev]"
                                           id="opportunity_search_condition_accordion_open_dev" value="no">
                                    <div class="accordion_contents_dev" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev0" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_pmo"><label id="opp_categories_field_label_0"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev1" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_pmpl"><label id="opp_categories_field_label_1"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev2" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_architect"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev2">アーキテクト</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev3" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_bridge_se"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev3">ブリッジSE</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev4" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_work"><label id="opp_categories_field_label_4"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev4">業務システム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev5" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_web"><label id="opp_categories_field_label_5"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev5">Webシステム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev6" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_ios"><label id="opp_categories_field_label_6"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev6">iOS</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev7" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_android"><label id="opp_categories_field_label_7"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev7">Android</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev8" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_mainframe"><label
                                                        id="opp_categories_field_label_8"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev8">メインフレーム</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev9" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_control"><label id="opp_categories_field_label_9"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev9">制御</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev10" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_embedded"><label
                                                        id="opp_categories_field_label_10"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev10">組込</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev11" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_iot"><label id="opp_categories_field_label_11"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev11">IoT</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev12" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_ai_dl"><label id="opp_categories_field_label_12"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="dev12">AI・DL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev13" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_frontend"><label
                                                        id="opp_categories_field_label_13"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev13">フロントエンド</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev14" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_backend"><label id="opp_categories_field_label_14"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev14">バックエンド</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev15" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_game"><label id="opp_categories_field_label_15"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev15">ゲーム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev16" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_rpa"><label id="opp_categories_field_label_16"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev16">RPA</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev17" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_erp"><label id="opp_categories_field_label_17"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev17">ERP</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev18" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_operation"><label
                                                        id="opp_categories_field_label_18"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev18">運用・保守</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev19" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_test"><label id="opp_categories_field_label_19"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev19">テスト</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="infra_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">インフラ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_infra]"
                                           id="opportunity_search_condition_accordion_open_infra" value="no">
                                    <div class="accordion_contents_infra" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra0" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_pmo"><label id="opp_categories_field_label_0"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="infra0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra1" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_pmpl"><label id="opp_categories_field_label_1"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="infra1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra2" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_server"><label id="opp_categories_field_label_2"
                                                                                       class="custom-control-label anavi-select-label mb-3"
                                                                                       for="infra2">サーバー</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra3" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_network"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra3">ネットワーク</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra4" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_db"><label id="opp_categories_field_label_4"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="infra4">データベース</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra5" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_cloud"><label id="opp_categories_field_label_5"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="infra5">クラウド</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra6" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_virtualized"><label
                                                        id="opp_categories_field_label_6"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra6">仮想化</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra7" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_mainframe"><label
                                                        id="opp_categories_field_label_7"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra7">メインフレーム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra8" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_operation"><label
                                                        id="opp_categories_field_label_8"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra8">運用・保守</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="helpdesk_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">ヘルプデスク</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_helpdesk]"
                                           id="opportunity_search_condition_accordion_open_helpdesk" value="no">
                                    <div class="accordion_contents_helpdesk" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk0"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_self"><label
                                                        id="opp_categories_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="helpdesk0">ヘルプデスク</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk1"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_se"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="helpdesk1">社内SE</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk2"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_support"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="helpdesk2">PMOサポート</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="marke_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">Webマーケ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_marke]"
                                           id="opportunity_search_condition_accordion_open_marke" value="no">
                                    <div class="accordion_contents_marke" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke0" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_direction"><label
                                                        id="opp_categories_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke0">ディレクション</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke1" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_seo"><label id="opp_categories_field_label_1"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="marke1">SEO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke2" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_email"><label id="opp_categories_field_label_2"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="marke2">Eメール</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke3" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_sns"><label id="opp_categories_field_label_3"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="marke3">SNS</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke4" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_listing"><label
                                                        id="opp_categories_field_label_4"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke4">リスティング</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke5" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_affiliate"><label
                                                        id="opp_categories_field_label_5"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke5">アフィリエイト</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke6" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_video"><label id="opp_categories_field_label_6"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="marke6">動画</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="design_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">デザイン</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_design]"
                                           id="opportunity_search_condition_accordion_open_design" value="no">
                                    <div class="accordion_contents_design" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design0" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_web"><label id="opp_categories_field_label_0"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="design0">Webシステム</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design1" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_uiux"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="design1">UI/UX</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design2" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_character"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="design2">キャラクター</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design3" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_graphic"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="design3">グラフィック</label></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <label class="font-middle mb-3 ex-bold">単価</label>
                                <div class="mb-5"><p class="d-flex justify-content-center" id="price-range">
                                    下限なし〜上限なし</p>
                                    <div class="slider-styled mx-4 noUi-target noUi-ltr noUi-horizontal noUi-txt-dir-ltr"
                                         id="slider-handles">
                                        <div class="noUi-base">
                                            <div class="noUi-connects">
                                                <div class="noUi-connect"
                                                     style="transform: translate(0%, 0px) scale(1, 1);"></div>
                                            </div>
                                            <div class="noUi-origin"
                                                 style="transform: translate(-100%, 0px); z-index: 5;">
                                                <div class="noUi-handle noUi-handle-lower" data-handle="0" tabindex="0"
                                                     role="slider" aria-orientation="horizontal" aria-valuemin="35.0"
                                                     aria-valuemax="200.0" aria-valuenow="35.0" aria-valuetext="35.00">
                                                    <div class="noUi-touch-area"></div>
                                                </div>
                                            </div>
                                            <div class="noUi-origin" style="transform: translate(0%, 0px); z-index: 4;">
                                                <div class="noUi-handle noUi-handle-upper" data-handle="1" tabindex="0"
                                                     role="slider" aria-orientation="horizontal" aria-valuemin="40.0"
                                                     aria-valuemax="205.0" aria-valuenow="205.0"
                                                     aria-valuetext="205.00">
                                                    <div class="noUi-touch-area"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between font-small custom-grey-text my-2"><span>下限なし</span><span>上限なし</span>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold" for="">稼働率</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[utilization_rate][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_0"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="full_utilization"><label id="utilization_rate_field_label_0"
                                                                                class="custom-control-label anavi-select-label mb-3"
                                                                                for="utilization_rate_field_opportunity_search_condition_0">100%（フル稼働）</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_1"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="80to99"><label id="utilization_rate_field_label_1"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_1">80
                                            〜 99%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_2"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="60to79"><label id="utilization_rate_field_label_2"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_2">60
                                            〜 79%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_3"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="40to59"><label id="utilization_rate_field_label_3"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_3">40
                                            〜 59%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_4"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="20to39"><label id="utilization_rate_field_label_4"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_4">20
                                            〜 39%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_5"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="less_than_20"><label id="utilization_rate_field_label_5"
                                                                            class="custom-control-label anavi-select-label mb-3"
                                                                            for="utilization_rate_field_opportunity_search_condition_5">20%未満</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">案件の商流</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[opp_type_id][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_0" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="clnt"><label
                                                id="opp_type_id_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_0">エンド</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_1" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="prim"><label
                                                id="opp_type_id_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_1">元請</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_2" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="subc"><label
                                                id="opp_type_id_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_2">一次請</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_3" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="msubc"><label
                                                id="opp_type_id_field_label_3"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_3">二次請以降</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">商流への関与</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[involvement][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="involvement_field_opportunity_search_condition_0" type="checkbox"
                                                name="opportunity_search_condition[involvement][]"
                                                value="enter_sales_channels"><label id="involvement_field_label_0"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="involvement_field_opportunity_search_condition_0">掲載企業が商流に入る案件のみ表示する</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">出社頻度</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[work_frequencies][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_0"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="5days"><label id="work_frequencies_field_label_0"
                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                     for="work_frequencies_field_opportunity_search_condition_0">週5日出社</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_1"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="2to4days"><label id="work_frequencies_field_label_1"
                                                                        class="custom-control-label anavi-select-label mb-3"
                                                                        for="work_frequencies_field_opportunity_search_condition_1">週4
                                            〜 2日出社</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_2"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="less_than_1day"><label id="work_frequencies_field_label_2"
                                                                              class="custom-control-label anavi-select-label mb-3"
                                                                              for="work_frequencies_field_opportunity_search_condition_2">週1日以下出社</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_3"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="full_remote"><label id="work_frequencies_field_label_3"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="work_frequencies_field_opportunity_search_condition_3">フルリモート</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto 2jmICoA mb-5">
                                    <div class="row ml-0 mr-0"><label class="font-middle mb-3 ex-bold"
                                                                      for="">就業場所</label>
                                        <button class="mdb-modal-form btn btn-outline-default btn-sm mb-3 mt-0 ml-3 waves-effect waves-light"
                                                type="button" data-toggle="modal"
                                                data-reference="#specifies_workplaces_base">選択
                                        </button>
                                        <p class="pref-select p-0 m-0 w-100" id="check_item">選択されていません</p>
                                        <input id="specifies_workplaces" multiple="" autocomplete="off" type="hidden"
                                               name="opportunity_search_condition[specifies_workplaces][]">
                                        <div class="modal" id="specifies_workplaces_base" tabindex="-1" role="dialog"
                                             aria-labelledby="specifies_workplaces_modal" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header"><h4 class="modal-title w-100">
                                                        就業場所を選択</h4>
                                                        <button class="close btn-modal-close" aria-label="Close"
                                                                type="button"
                                                                data-reference="#specifies_workplaces_base"><i
                                                                class="material-icons md-dark mb-36" aria-hidden="true">clear</i>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row multiple-prefecture-select">
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">北海道</a><input
                                                                    type="hidden" value="1"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">青森県</a><input
                                                                    type="hidden" value="2"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岩手県</a><input
                                                                    type="hidden" value="3"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">宮城県</a><input
                                                                    type="hidden" value="4"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">秋田県</a><input
                                                                    type="hidden" value="5"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山形県</a><input
                                                                    type="hidden" value="6"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福島県</a><input
                                                                    type="hidden" value="7"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">茨城県</a><input
                                                                    type="hidden" value="8"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">栃木県</a><input
                                                                    type="hidden" value="9"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">群馬県</a><input
                                                                    type="hidden" value="10"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">埼玉県</a><input
                                                                    type="hidden" value="11"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">千葉県</a><input
                                                                    type="hidden" value="12"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">東京都</a><input
                                                                    type="hidden" value="13"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">神奈川県</a><input
                                                                    type="hidden" value="14"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">新潟県</a><input
                                                                    type="hidden" value="15"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">富山県</a><input
                                                                    type="hidden" value="16"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">石川県</a><input
                                                                    type="hidden" value="17"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福井県</a><input
                                                                    type="hidden" value="18"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山梨県</a><input
                                                                    type="hidden" value="19"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">長野県</a><input
                                                                    type="hidden" value="20"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岐阜県</a><input
                                                                    type="hidden" value="21"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">静岡県</a><input
                                                                    type="hidden" value="22"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">愛知県</a><input
                                                                    type="hidden" value="23"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">三重県</a><input
                                                                    type="hidden" value="24"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">滋賀県</a><input
                                                                    type="hidden" value="25"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">京都府</a><input
                                                                    type="hidden" value="26"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">大阪府</a><input
                                                                    type="hidden" value="27"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">兵庫県</a><input
                                                                    type="hidden" value="28"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">奈良県</a><input
                                                                    type="hidden" value="29"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">和歌山県</a><input
                                                                    type="hidden" value="30"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">鳥取県</a><input
                                                                    type="hidden" value="31"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">島根県</a><input
                                                                    type="hidden" value="32"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岡山県</a><input
                                                                    type="hidden" value="33"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">広島県</a><input
                                                                    type="hidden" value="34"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山口県</a><input
                                                                    type="hidden" value="35"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">徳島県</a><input
                                                                    type="hidden" value="36"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">香川県</a><input
                                                                    type="hidden" value="37"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">愛媛県</a><input
                                                                    type="hidden" value="38"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">高知県</a><input
                                                                    type="hidden" value="39"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福岡県</a><input
                                                                    type="hidden" value="40"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">佐賀県</a><input
                                                                    type="hidden" value="41"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">長崎県</a><input
                                                                    type="hidden" value="42"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">熊本県</a><input
                                                                    type="hidden" value="43"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">大分県</a><input
                                                                    type="hidden" value="44"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">宮崎県</a><input
                                                                    type="hidden" value="45"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">鹿児島県</a><input
                                                                    type="hidden" value="46"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">沖縄県</a><input
                                                                    type="hidden" value="47"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">海外</a><input
                                                                    type="hidden" value="48"></div>
                                                        </div>
                                                        <div class="row pt-5">
                                                            <button class="btn btn-blue-grey mx-auto btn-modal-close waves-effect waves-light"
                                                                    aria-label="Close"
                                                                    data-reference="#specifies_workplaces_base">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">契約形態</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_search_condition[contract_types][]">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_0"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="quas"><label id="contract_types_field_label_0"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_0">業務委託（準委任）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_1"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="subc"><label id="contract_types_field_label_1"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_1">業務委託（請負）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_2"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="temp"><label id="contract_types_field_label_2"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_2">派遣契約</label>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">案件の特徴</label>
                                    <div class="pl-3 mb-5"><p id="opp_quality_text">選択されていません</p><input
                                            multiple="" autocomplete="off" type="hidden"
                                            name="opportunity_search_condition[opp_qualities][]"
                                            id="opportunity_search_condition_opp_qualities">
                                        <button class="mdb-modal-form btn btn-outline-default m-0 waves-effect waves-light"
                                                data-reference="#quality" data-toggle="modal" href="" type="button">
                                            案件の特徴を選択
                                        </button>
                                        <div aria-hidden="true" aria-labelledby="quality_modal" class="modal"
                                             id="quality" role="dialog" tabindex="-1">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header"><h4 class="modal-title w-100">
                                                        案件の特徴を選択</h4>
                                                        <button aria-label="Close" class="close" data-dismiss="modal"
                                                                type="button"><span aria-hidden="true"><i
                                                                class="material-icons md-dark mb-36">clear</i></span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row opp-quality-select">
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">70万円以上</a><input
                                                                    type="hidden" value="700thousand_or_more"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">100万円以上</a><input
                                                                    type="hidden" value="1million_or_more"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">1年未満OK</a><input
                                                                    type="hidden" value="less_1year_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">新人でもOK</a><input
                                                                    type="hidden" value="newcomer_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">50代以上OK</a><input
                                                                    type="hidden" value="over_50years_old_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">外国籍OK</a><input
                                                                    type="hidden" value="foreign_nationality_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">リーダー募集</a><input
                                                                    type="hidden" value="leader_recruitment"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">英語力</a><input
                                                                    type="hidden" value="english_skill"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">面談1回</a><input
                                                                    type="hidden" value="interview_once"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">持ち帰りOK</a><input
                                                                    type="hidden" value="take_home_project"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">チーム提案OK</a><input
                                                                    type="hidden" value="team_proposal_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">ウェブ面談可能</a><input
                                                                    type="hidden" value="web_interview_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">案件場所から遠隔地居住でもOK</a><input
                                                                    type="hidden" value="remote__location_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">日本以外の居住者OK</a><input
                                                                    type="hidden" value="overseas_resident_ok"></div>
                                                        </div>
                                                        <div class="row mt-3">
                                                            <button aria-label="Close"
                                                                    class="btn btn-blue-grey mx-auto waves-effect waves-light"
                                                                    data-dismiss="modal">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">募集対象</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[allowable_trading_restriction][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_0"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_own_employee"><label
                                                id="allowable_trading_restriction_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_0">自社社員</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_1"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_via_another_company"><label
                                                id="allowable_trading_restriction_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_1">協力会社社員</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_2"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_freelance_self"><label
                                                id="allowable_trading_restriction_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_2">フリーランス（本人）</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_3"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_freelance"><label
                                                id="allowable_trading_restriction_field_label_3"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_3">フリーランス（企業登録）</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion_details accordion_close py-3 px-2 clear border-top"><span
                                        class="float-left pr-7">詳細検索条件</span><i
                                        class="material-icons md-dark float-right d-block">keyboard_arrow_down</i></div>
                                <input autocomplete="off" type="hidden"
                                       name="opportunity_search_condition[accordion_open]"
                                       id="opportunity_search_condition_accordion_open" value="no">
                                <div class="accordion_contents_details mb-4 pt-3" style="display: none;">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">案件内容の確定状況</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_search_condition[order_accuracy_id][]">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="order_accuracy_id_field_opportunity_search_condition_0"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[order_accuracy_id][]"
                                                       value="afte"><label id="order_accuracy_id_field_label_0"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="order_accuracy_id_field_opportunity_search_condition_0">確定済み</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="order_accuracy_id_field_opportunity_search_condition_1"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[order_accuracy_id][]"
                                                       value="befo"><label id="order_accuracy_id_field_label_1"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="order_accuracy_id_field_opportunity_search_condition_1">確定前</label>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">契約開始日</label>
                                    <div class="row pl-3">
                                        <div class="col-9">
                                            <div class="mx-auto mb-3"><input class="form-control picker__input"
                                                                             autocomplete="off"
                                                                             id="contract_startdate_at_field"
                                                                             type="text"
                                                                             name="opportunity_search_condition[contract_startdate_at]"
                                                                             readonly="" aria-haspopup="true"
                                                                             aria-expanded="false" aria-readonly="false"
                                                                             aria-owns="contract_startdate_at_field_root">
                                                <div class="picker" id="contract_startdate_at_field_root"
                                                     aria-hidden="true">
                                                    <div class="picker__holder" tabindex="-1">
                                                        <div class="picker__frame">
                                                            <div class="picker__wrap">
                                                                <div class="picker__box">
                                                                    <div class="picker__header">
                                                                        <div class="picker__date-display">
                                                                            <div class="picker__weekday-display">
                                                                                木曜日,
                                                                            </div>
                                                                            <div class="picker__month-display">
                                                                                <div>2月</div>
                                                                            </div>
                                                                            <div class="picker__day-display">
                                                                                <div>13</div>
                                                                            </div>
                                                                            <div class="picker__year-display">
                                                                                <div>2025</div>
                                                                            </div>
                                                                        </div>
                                                                        <select class="picker__select--year" disabled=""
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Select a year">
                                                                            <option value="2018">2018</option>
                                                                            <option value="2019">2019</option>
                                                                            <option value="2020">2020</option>
                                                                            <option value="2021">2021</option>
                                                                            <option value="2022">2022</option>
                                                                            <option value="2023">2023</option>
                                                                            <option value="2024">2024</option>
                                                                            <option value="2025" selected="">2025
                                                                            </option>
                                                                            <option value="2026">2026</option>
                                                                            <option value="2027">2027</option>
                                                                            <option value="2028">2028</option>
                                                                            <option value="2029">2029</option>
                                                                            <option value="2030">2030</option>
                                                                            <option value="2031">2031</option>
                                                                            <option value="2032">2032</option>
                                                                        </select><select class="picker__select--month"
                                                                                         disabled=""
                                                                                         aria-controls="contract_startdate_at_field_table"
                                                                                         title="Select a month">
                                                                        <option value="0">1月</option>
                                                                        <option value="1" selected="">2月</option>
                                                                        <option value="2">3月</option>
                                                                        <option value="3">4月</option>
                                                                        <option value="4">5月</option>
                                                                        <option value="5">6月</option>
                                                                        <option value="6">7月</option>
                                                                        <option value="7">8月</option>
                                                                        <option value="8">9月</option>
                                                                        <option value="9">10月</option>
                                                                        <option value="10">11月</option>
                                                                        <option value="11">12月</option>
                                                                    </select>
                                                                        <button class="picker__nav--prev btn btn-flat"
                                                                                data-nav="-1" role="button"
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Previous month"></button>
                                                                        <button class="picker__nav--next btn btn-flat"
                                                                                data-nav="1" role="button"
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Next month"></button>
                                                                    </div>
                                                                    <table class="picker__table"
                                                                           id="contract_startdate_at_field_table"
                                                                           role="grid"
                                                                           aria-controls="contract_startdate_at_field"
                                                                           aria-readonly="true">
                                                                        <thead>
                                                                        <tr>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="月曜日">月
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="火曜日">火
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="水曜日">水
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="木曜日">木
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="金曜日">金
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="土曜日">土
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="日曜日">日
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1737910800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月27日">27
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1737997200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月28日">28
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738083600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月29日">29
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738170000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月30日">30
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738256400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月31日">31
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738342800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月01日">1
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738429200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月02日">2
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738515600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月03日">3
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738602000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月04日">4
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738688400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月05日">5
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738774800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月06日">6
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738861200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月07日">7
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738947600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月08日">8
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739034000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月09日">9
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739120400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月10日">10
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739206800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月11日">11
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739293200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月12日">12
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus picker__day--today picker__day--highlighted"
                                                                                     data-pick="1739379600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月13日"
                                                                                     aria-activedescendant="true">13
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739466000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月14日">14
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739552400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月15日">15
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739638800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月16日">16
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739725200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月17日">17
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739811600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月18日">18
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739898000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月19日">19
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739984400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月20日">20
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740070800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月21日">21
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740157200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月22日">22
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740243600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月23日">23
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740330000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月24日">24
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740416400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月25日">25
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740502800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月26日">26
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740589200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月27日">27
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740675600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月28日">28
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740762000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月01日">1
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740848400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月02日">2
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740934800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月03日">3
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741021200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月04日">4
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741107600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月05日">5
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741194000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月06日">6
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741280400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月07日">7
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741366800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月08日">8
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741453200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月09日">9
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        </tbody>
                                                                    </table>
                                                                    <div class="picker__footer">
                                                                        <button class="picker__button--today"
                                                                                type="button" data-pick="1739379600000"
                                                                                disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            今日
                                                                        </button>
                                                                        <button class="picker__button--clear"
                                                                                type="button" data-clear="1" disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            消去
                                                                        </button>
                                                                        <button class="picker__button--close"
                                                                                type="button" data-close="true"
                                                                                disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            Close
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <input type="hidden"
                                                       name="opportunity_search_condition[contract_startdate_at]"></div>
                                        </div>
                                        <div class="col-1 p-0"><i
                                                class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">募集人数</label>
                                    <div class="row pl-3">
                                        <div class="col-8">
                                            <div class="mx-auto mb-5"><input type="number" min="0" class="form-control"
                                                                             autocomplete="off" id="participants_field"
                                                                             name="opportunity_search_condition[participants]">
                                            </div>
                                        </div>
                                        <div class="col pl-0"><label class="inline-unit-label">人</label></div>
                                    </div>
                                    <div class="mx-auto mb-5"><label class="font-middle mb-3 ex-bold">面談回数</label>
                                        <div class="select-wrapper mdb-select anavi-select mb-5"><span
                                                class="caret material-icons">keyboard_arrow_down</span><input
                                                type="text" class="select-dropdown form-control" readonly="true"
                                                required="false"
                                                data-activates="select-options-interview_count_id_field" value=""
                                                role="listbox" aria-multiselectable="false" aria-disabled="false"
                                                aria-required="false" aria-haspopup="true" aria-expanded="false">
                                            <ul id="select-options-interview_count_id_field"
                                                class="dropdown-content select-dropdown w-100 " style="display: none;">
                                                <li class=" active " role="option" aria-selected="true"
                                                    aria-disabled="false"><span class="filtrable "> 未選択    </span>
                                                </li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 1回    </span></li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 1〜2回    </span>
                                                </li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 2回    </span></li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 3回以上    </span>
                                                </li>
                                            </ul>
                                            <select class="mdb-select anavi-select mb-5 initialized"
                                                    id="interview_count_id_field"
                                                    name="opportunity_search_condition[interview_count_id]">
                                                <option value="">未選択</option>
                                                <option value="once">1回</option>
                                                <option value="twice">2回</option>
                                                <option value="other">その他</option>
                                            </select></div>
                                    </div>
                                    <div class="accordion-close-area text-center">閉じる<i
                                            class="material-icons md-dark md-18">close</i></div>
                                </div>
                                <div class="border-bottom mb-5"></div>
                                <input autocomplete="off" type="hidden" name="opportunity_search_condition[switch_type]"
                                       id="opportunity_search_condition_switch_type" value="button">
                                <div class="text-center d-none d-md-block search-area py-2">
                                    <button name="button" type="submit"
                                            class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light"
                                            id="opp-search-btn" data-disable-with="検索中">
                                        <div class="py-2 d-none" id="loader">
                                            <div class="loader"></div>
                                        </div>
                                        <div id="btn-text"><span class="font-extralarge" id="search-count">135</span> 件<br>この条件で検索
                                        </div>
                                    </button>
                                    <div class="py-2"><a
                                            href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="container-fluid d-block d-md-none search-fixed-btn search-area-sm">
                    <div class="row py-3">
                        <div class="col-12">
                            <button name="button" type="submit"
                                    class="btn btn-default btn-block font-middle mb-3 submit-btn waves-effect waves-light"
                                    form="opportunity_search_condition_form" data-disable-with="検索中">
                                <div class="py-2 d-none" id="loader-sm">
                                    <div class="loader"></div>
                                </div>
                                <div id="btn-text-sm"><span class="font-extralarge" id="search-count-sm">135</span>
                                    件<br>この条件で検索
                                </div>
                            </button>
                            <div class="text-center"><a
                                    href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-8 col-lg-9">
            <div class="row">
                <div class="col-12 d-none d-md-block mb-4">
                    <div class="d-flex"><span class="mr-3 nowrap">よく検索されるキーワード</span>
                        <div><a class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/107"><span>SAP</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/106"><span>PMO</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/102"><span>コンサル</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/105"><span>Java</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/110"><span>PHP</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/142"><span>RPA</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/140"><span>AI</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/44"><span>Python</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/144"><span>セキュリティ</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/145"><span>Salesforce</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/101"><span>PM</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/146"><span>React</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/147"><span>テスト</span></a></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="d-flex justify-content-between search-toggle d-md-none bg-white w-100 p-3 mt-4 mx-3"><a
                        class="font-middle custom-grey-6-text m-0" href="javascript:void(0);">検索条件指定</a><i
                        class="material-icons custom-grey-6-text">keyboard_arrow_down</i></div>
            </div>
            <div class="row mr-0 ml-0">
                <div class="col-12 mt-3 border border-light p-3 px-md-4 bg-grey-2 rounded-sm">
                    <div class="position-relative">
                        <div class="d-sm-flex search-condition-area line-height-180">
                            <div class="text-nowrap mb-2 mb-sm-n2 ml-3 ml-sm-0">検索条件</div>
                            <div class="pl-3">
                                <div class="d-sm-inline boder-left"><span
                                        class="mr-3 custom-grey-5-text">得意領域</span>
                                    <div class="d-sm-inline mb-2 mb-sm-0"><span
                                            class="border-grey-3 bg-white mr-2 px-2">コンサル</span>
                                        <div class="d-sm-inline"><span class="mr-3">IT</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion_search_condition_btn"><a
                                class="accordion_search_condition accordion_close text-right btn-block pr-3 bg-grey-2 pl-5"
                                href="javascript:void(0);" style="display: none;"><span
                                class="vertical-middle font-middle">すべて表示</span><i class="material-icons">keyboard_arrow_down</i></a>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="font-middle"><span class="font-ll ex-bold" id="total-count">135</span> 件中 1〜24件</div>
                <div data-v-7d20fe15="" class="position-relative pl-3"><!-- ソート中の項目を表示 -->
                    <div data-v-7d20fe15="" class="d-flex justify-content-end sort-display-area p-2"><label
                            data-v-7d20fe15="" class="pr-2 mb-0">新着（降順）</label><i data-v-7d20fe15=""
                                                                                      class="material-icons custom-grey-6-text pl-1">keyboard_arrow_down</i>
                    </div>
                    <ul data-v-7d20fe15="" class="bg-white sort-options-area text-right" style="display: none;">
                        <li data-v-7d20fe15="" class="sort-active">新着（降順） <i data-v-7d20fe15=""
                                                                                 class="material-icons custom-grey-6-text">done</i>
                        </li>
                        <li data-v-7d20fe15="" class="">更新（降順） <!--v-if--></li>
                        <li data-v-7d20fe15="" class="">単価（降順） <!--v-if--></li>
                    </ul>
                </div>
            </div>
            <div class="row">
                <div data-v-1c51a755="" id="jsonld-125531">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "クラウドソリューション提案作成支援",
                            "value": 125531
                        },
                        "datePosted": "2025/02/13 16:55:32",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": []
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 160,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125531/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">クラウドソリューション提案作成支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/17 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            ・大手Sier様における独自ソリューションであるクラウド環境での開発支援サービスについて、提案のストーリー及び提案資料の作成支援を依頼したい
                            ・体制イメージ
                            　元請けコンサルタント（PM）：20％
                            　メンバー：100％　※こちらのポジションの依頼</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 160万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125531" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    254407126017<br>◆案件名: クラウドソリューション提案作成支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>・大手Sier様における独自ソリューションであるクラウド環境での開発支援サービスについて、提案のストーリー及び提案資料の作成支援を依頼したい<br>・体制イメージ<br>　元請けコンサルタント（PM）：20％<br>　メンバー：100％　※こちらのポジションの依頼<br>◆人財要件:
                                    <br>[必須スキル]<br>・資料作成含めたコンサルティングベーススキル<br>・Big4レベルでのコンサルティングファームでの経験<br>・クラウド上での開発に関する知見<br>・セールスの知見、経験<br>◆単価:
                                    120万円 〜 160万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: フルリモート<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年02月17日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125560">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "大手物流会社向けDX/IT化施策に関するプロジェクト管理支援",
                            "value": 125560
                        },
                        "datePosted": "2025/02/13 15:49:24",
                        "validThrough": "2025/02/21 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 70,
                                "maxValue": 90,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125560/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    大手物流会社向けDX/IT化施策に関するプロジェクト管理支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/14 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【案件内容】
                            ●案件名：大手物流会社向けDX/IT化施策に関するプロジェクト管理支援
                            ●案件概要：
                            大手物流企業における全社DX推進プロジェクトの一環として、プロジェクト管理のIT化を推進する。
                            特にプラント建設関連事業において、
                            適切なプロジェクト管理ツールの導入および活用を目指し、要件定義から企画書作成までを担当。
                            自ら主体的に動...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/20</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">70万円 〜 90万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125560" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    157429366692<br>◆案件名: 大手物流会社向けDX/IT化施策に関するプロジェクト管理支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>【案件内容】<br>●案件名：大手物流会社向けDX/IT化施策に関するプロジェクト管理支援<br>●案件概要：<br>大手物流企業における全社DX推進プロジェクトの一環として、プロジェクト管理のIT化を推進する。<br>特にプラント建設関連事業において、<br>適切なプロジェクト管理ツールの導入および活用を目指し、要件定義から企画書作成までを担当。<br>自ら主体的に動き、関係者と調整を行いながらプロジェクトを進める役割。<br>●仕事内容：<br>
                                    -プロジェクト管理ツールの導入企画支援（要件定義、比較検討、選定）<br>
                                    -導入後の業務フローの設計および最適化<br> -関係部署との調整および導入推進<br>
                                    -業務改善の提案および実施支援<br>●勤務地（頻度）：都内（週1～2日出社）+ リモートワーク併用<br>●期間：ASAP
                                    ～ 長期予定<br>●稼働率：100%<br>●単価：80万円/月（税抜）
                                    ※スキル見合い<br>●募集人数：1名<br>●面談回数：1～2回<br>●商流：弊社2次請け<br>●備考：<br>
                                    -応募時には必須スキルおよび尚可スキルの該当箇所についてコメントをお願いいたします。<br>◆人財要件:
                                    <br>●求めるスキル：<br>〈必須〉<br> -プロジェクト管理ツールの使用経験（MS
                                    Project、Backlog、Jiraのいずれか）<br> -プラント建設またはゼネコン関連の業務知見<br>
                                    -自発的に業務を推進できる能力（リーダーシップ・主体性）<br>〈尚可〉<br>
                                    -大規模DXプロジェクトへの参画経験<br>
                                    -プロジェクト管理ツールの導入および活用経験<br> -コンサルティング業務の経験<br>◆単価:
                                    70万円 〜 90万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1〜2回<br>◆契約期間:
                                    2025年02月14日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125545">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【ハイブリ/ 調査・評価】元請支援_SAP×人給Success Factors領域におけるIT×業務コンサル募集_008038",
                            "value": 125545
                        },
                        "datePosted": "2025/02/13 11:19:49",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 150,
                                "maxValue": 180,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125545/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【ハイブリ/ 調査・評価】元請支援_SAP×人給Success
                                    Factors領域におけるIT×業務コンサル募集_008038
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/24 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            人事・給与業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、ワークショップの準備、ファシリテーション、成果物作成などの担当を想定
                            関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。
                            【場所】
                            ハイブリッド/ 有楽町駅周辺
                            詳細な場所は面談時に元請から共有予定。
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">150万円 〜 180万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125545" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    541413182556<br>◆案件名: 【ハイブリ/ 調査・評価】元請支援_SAP×人給Success
                                    Factors領域におけるIT×業務コンサル募集_008038<br>◆案件への関わり: 商流に入る<br>◆案件の商流:
                                    一次請<br>◆案件内容: <br>人事・給与業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、ワークショップの準備、ファシリテーション、成果物作成などの担当を想定<br>関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。<br>【場所】<br>ハイブリッド/
                                    有楽町駅周辺<br>詳細な場所は面談時に元請から共有予定。<br>※沖縄への出張対応の可能性あり。
                                    （1週間程度を想定）<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ： 当月末締め<br>稼働日数 ： 週5日<br>【備考】<br>勤務時間 ： 9:00～18:00<br>年齢制限 ：
                                    50歳まで<br>商流制限 ： 原則として貴社所属の方を優先致します。フリーランスの方もご相談ください。<br>国籍制限
                                    ： 日本籍の方を優先させていただきます。（日本語がネイティブレベルが求められるため）<br>服装指定
                                    ： スーツ or ビジネスカジュアル<br>リモート対応 ： 在宅業務での環境は貴社 or
                                    稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・SAP
                                    HCM（給与計算含む）とSuccessFactorsの専門知識<br>・一般的な人事領域のビジネスプロセスに精通している<br>・HRプロセスのワークショップの準備、実施、文書化の経験がある<br>・日本語：ネイティブレベル<br>・英語：ビジネスレベル（相当も可）<br>◆単価:
                                    150万円 〜 180万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年02月24日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125538">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【注力！！】ITアーキテクト募集",
                            "value": 125538
                        },
                        "datePosted": "2025/02/13 10:45:13",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 110,
                                "maxValue": 130,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125538/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【注力！！】ITアーキテクト募集</div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/13 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            ●案件概要：新たなデータ基盤構築プロジェクトにおいて、プリセールスエンジニアとしてアーキテクチャ設計や見積作成を担当していただきます。
                            プロジェクト受注後は、基盤開発フェーズで技術リーダーとしてチームを牽引していただく役割です。
                            現在は、Snowflakeの導入およびオンプレミスDWHやCRMシステムとのリアルタイムデータ連携手法の検討・提案...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">110万円 〜 130万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 40 〜 59%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125538" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    163411111310<br>◆案件名: 【注力！！】ITアーキテクト募集<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>●案件概要：新たなデータ基盤構築プロジェクトにおいて、プリセールスエンジニアとしてアーキテクチャ設計や見積作成を担当していただきます。<br>プロジェクト受注後は、基盤開発フェーズで技術リーダーとしてチームを牽引していただく役割です。<br>現在は、Snowflakeの導入およびオンプレミスDWHやCRMシステムとのリアルタイムデータ連携手法の検討・提案が予定されています。<br>●フェーズ：アーキテクチャ設計、基盤開発リード<br>●仕事内容：<br>・アーキテクチャの検討および技術提案<br>・システム基盤の見積作成<br>・受注後の基盤開発フェーズでの技術リード<br>・Snowflake導入およびリアルタイムデータ連携方式の検討・提案<br>●勤務地（頻度）：ハイブリッド勤務（週2回程度の出社）<br>●期間：ASAP（即日開始可能）<br>●稼働率：50%〜100%（相談可能）<br>●面談回数：1～2回程度<br>●商流：弊社二次請け<br>◆人財要件:
                                    <br>●求めるスキル<br>〈必須〉<br>・システム基盤開発を単独で遂行可能なスキル<br>・AWSに関する知識<br>・データ基盤およびデータ活用ツールへの技術探求心<br>・金融業界（特に銀行関連）の業務経験<br>（完全一致でなくても可）<br>〈尚可〉<br>・SnowflakeなどのDWH製品に関する知識<br>・Qlik、Tableauなどのデータ活用ツールに精通していること<br>◆単価:
                                    110万円 〜 130万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働） / 40 〜 59%<br>◆出社頻度:
                                    週4 〜 2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    1〜2回<br>◆契約期間: 2025年02月13日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125537">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【コンサルタント募集】PMO/SAP導入/マルチベンダー/リモートあり",
                            "value": 125537
                        },
                        "datePosted": "2025/02/13 10:10:31",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 150,
                                "maxValue": 170,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125537/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【コンサルタント募集】PMO/SAP導入/マルチベンダー/リモートあり
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2026/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">"＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            案件名：SAP導入プロジェクト周辺システムPMO支援

                            商流：元請け（コンサル）→弊社
                            内容：：
                            アパレル企業におけるSAP S4導入プログラムにおいて、
                            SAPとBTPを介したIFによる連携を行う複数システムの刷新（改修及び新規導入）プロジェクトの進捗や予算をとりまとめる...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">150万円 〜 170万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125537" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    419409036754<br>◆案件名: 【コンサルタント募集】PMO/SAP導入/マルチベンダー/リモートあり<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>"＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>案件名：SAP導入プロジェクト周辺システムPMO支援<br><br>商流：元請け（コンサル）→弊社<br>内容：：<br>アパレル企業におけるSAP
                                    S4導入プログラムにおいて、<br>SAPとBTPを介したIFによる連携を行う複数システムの刷新（改修及び新規導入）プロジェクトの進捗や予算をとりまとめる周辺PMOリード<br><br>■必須要件：<br>・コンサルティングファーム出身者もしくはアンダーにて複数経験がある<br>・SAP導入プロジェクトでPM/PMO経験のある<br>・マルチベンダー体制でのPMO経験のある<br>・ユーザーとのコミュニケーション、課題を正確にとらえ分かりやすく伝えることができる方<br><br>■尚可要件<br>・アパレルメーカー、小売業界の経験<br>・BTPのIntegration
                                    SuiteやData Spiderなどのデータ連携基盤の経験<br><br>■条件面：<br>金額：スキル見合い（150万～170万円ほどで想定）<br>時期：3月～<br>想定職位：シニアコンサルタント～マネージャー<br>場所：23区西部もしくは御成門（オンサイトメインでリモート併用）<br>面談：1回※WEB（事前お打ち合わせ等あり）<br>備考：<br>50代前半まで<br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br><br>"<br>◆人財要件:
                                    <br>■必須要件：<br>・コンサルティングファーム出身者もしくはアンダーにて複数経験がある<br>・SAP導入プロジェクトでPM/PMO経験のある<br>・マルチベンダー体制でのPMO経験のある<br>・ユーザーとのコミュニケーション、課題を正確にとらえ分かりやすく伝えることができる方<br><br>■尚可要件<br>・アパレルメーカー、小売業界の経験<br>・BTPのIntegration
                                    SuiteやData Spiderなどのデータ連携基盤の経験<br>◆単価: 150万円 〜 170万円 / 月
                                    <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1〜2回<br>◆契約期間: 2025年03月01日 〜 2026年02月28日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125529">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【印刷業】基幹システム刷新PJ",
                            "value": 125529
                        },
                        "datePosted": "2025/02/12 21:22:41",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 140,
                                "maxValue": 160,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125529/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【印刷業】基幹システム刷新PJ</div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/17 〜 2026/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">案件概要：
                            ・現在、印刷業のお客様向けに基幹システム刷新の構想策定を実施中
                            ・複数社の共通基幹システムとなる予定
                            ・2028年3月目安に導入し、同4月からサービスイン予定

                            単価：160万円程度（応相談）
                            稼働率：100％
                            期間：即日～長期
                            場所：都内（リモート頻度などは応相談）
                            募集人数：2名</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">140万円 〜 160万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社 / 週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125529" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    918362969826<br>◆案件名: 【印刷業】基幹システム刷新PJ<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>案件概要：<br>・現在、印刷業のお客様向けに基幹システム刷新の構想策定を実施中<br>・複数社の共通基幹システムとなる予定<br>・2028年3月目安に導入し、同4月からサービスイン予定<br><br>単価：160万円程度（応相談）<br>稼働率：100％<br>期間：即日～長期<br>場所：都内（リモート頻度などは応相談）<br>募集人数：2名<br>◆人財要件:
                                    <br>必須スキル：<br>・コンサルティングファームでのシニアマネージャークラス<br>・印刷業界向けの基幹システム刷新の経験<br>・印刷業界に関する知見<br>・多様なステークホルダーと柔軟にコミュニケーションが取れる事<br>◆単価:
                                    140万円 〜 160万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社
                                    / 週4 〜 2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    2人<br>◆面談回数: 2回<br>◆契約期間: 2025年02月17日 〜 2026年02月28日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125528">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【大阪/出社】大手エネルギー企業向けDXコンサルタント募集",
                            "value": 125528
                        },
                        "datePosted": "2025/02/12 20:58:56",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "大阪府",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 110,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125528/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【大阪/出社】大手エネルギー企業向けDXコンサルタント募集
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/17 〜 2026/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">案件概要：
                            ・既存メンバーのリプレイス
                            ・現在エンド企業内にグループ全体のDX企画室が有り、そこでITの目線からサポートする役割
                            ・グループ各社から「これはITで実現可能か」といったご相談がくるので、それをオンサイトで対応していく

                            単価：110万円程度（応相談）
                            稼働率：100％
                            期間：即日～長期
                            場所：大阪オンサイト週5（関西...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 110万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">大阪府</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125528" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    807361532595<br>◆案件名: 【大阪/出社】大手エネルギー企業向けDXコンサルタント募集<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>案件概要：<br>・既存メンバーのリプレイス<br>・現在エンド企業内にグループ全体のDX企画室が有り、そこでITの目線からサポートする役割<br>・グループ各社から「これはITで実現可能か」といったご相談がくるので、それをオンサイトで対応していく<br><br>単価：110万円程度（応相談）<br>稼働率：100％<br>期間：即日～長期<br>場所：大阪オンサイト週5（関西内で出張も有）<br>※関西在住の方のご応募を優先いたします。<br>◆人財要件:
                                    <br>必須スキル：<br>・DXコンサルタントとしての経験<br>・コミュニケーション良好なこと<br>・小回りきかせながら動けること<br>◆単価:
                                    100万円 〜 110万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社<br>◆就業場所:
                                    大阪府<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 2回<br>◆契約期間:
                                    2025年02月17日 〜 2026年02月28日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125488">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "通信販売業_インフラ領域におけるシステム企画支援",
                            "value": 125488
                        },
                        "datePosted": "2025/02/12 16:50:16",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 90,
                                "maxValue": 110,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125488/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    通信販売業_インフラ領域におけるシステム企画支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2025/12/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">a.インフラ領域の残課題洗い出しと実施準備
                            ⇒未対応のクラウド移行計画や支障をきたすシステム設計の見直しなど
                            システム設計については、ADのOU構成の見直しなども想定される
                            b.インフラ運用の効率化に向けた施策検討
                            ⇒運用管理、定常運用・非定常運用などシステムインフラ領域の業務効率化を目指す
                            運用設計の見直しやツール化などを検討
                            c....</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">90万円 〜 110万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 80 〜 99%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125488" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    474328742282<br>◆案件名: 通信販売業_インフラ領域におけるシステム企画支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>a.インフラ領域の残課題洗い出しと実施準備<br>⇒未対応のクラウド移行計画や支障をきたすシステム設計の見直しなど<br>システム設計については、ADのOU構成の見直しなども想定される<br>b.インフラ運用の効率化に向けた施策検討<br>⇒運用管理、定常運用・非定常運用などシステムインフラ領域の業務効率化を目指す<br>運用設計の見直しやツール化などを検討<br>c.事業戦略に沿ったIT・情シス戦略の検討<br>⇒クライアントマネジメントライン中心で対応をする予定<br>◆人財要件:
                                    <br>[必須スキル]<br>・システムインフラ領域（ＮＷ、サーバ、セキュリティなど）に幅広い知見を持つ<br>・対応方針や実施計画、実行における要求・要件整理など企画や計画段階の上流工程の対応が可能<br><br>[尚可スキル]<br>　・クラウド移行のプロジェクト対応<br>　・運用設計や運用改善の実務経験<br>　・ＩＴ戦略、構想策定支援の実務経験<br>◆単価:
                                    90万円 〜 110万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働） / 80 〜 99%<br>◆出社頻度:
                                    週1日未満出社 / フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年04月01日 〜 2025年12月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125500">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【コンサルタント募集】稼動40～60％/商社（輸出入業務、貿易業務の知見）経験・知見",
                            "value": 125500
                        },
                        "datePosted": "2025/02/12 16:38:54",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 150,
                                "maxValue": 200,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125500/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【コンサルタント募集】稼動40～60％/商社（輸出入業務、貿易業務の知見）経験・知見
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2026/09/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            案件：貿易システム刷新時の関連システム要件定義（商社）
                            　　　要件定義担当者またはリーダー 1名募集

                            商流：大手コンサル→弊社

                            ■必須要件：
                            ①商社の輸出入業務、貿易業務の知見
                            （輸出入の見積、手配、諸掛計上、関税支払）の業務知見。
                            　※要件定義を行うため、上記...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">150万円 〜 200万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">60 〜 79% / 40 〜 59%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社 / 週4 〜 2日出社 / 週1日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125500" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    552345933459<br>◆案件名: 【コンサルタント募集】稼動40～60％/商社（輸出入業務、貿易業務の知見）経験・知見<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>案件：貿易システム刷新時の関連システム要件定義（商社）<br>　　　要件定義担当者またはリーダー
                                    1名募集<br><br>商流：大手コンサル→弊社<br><br>■必須要件：<br>①商社の輸出入業務、貿易業務の知見<br>（輸出入の見積、手配、諸掛計上、関税支払）の業務知見。　<br>
                                    　※要件定義を行うため、上記について細かいレベルでの要件定義の経験やオペレーションの経験があるとBest<br><br>②システム開発経験（業務知見優先のため、なくても可）<br><br>■条件面：<br>金額：スキル見合い<br>想定職位：コンサルタント～シニアコンサルタント<br>場所：日比谷予定<br>開始：4月～<br>稼働：40～60%以上。要相談。<br>面談：1回※WEB（事前お打ち合わせ等あり）<br>備考：<br>50歳ぐらいまで<br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br><br>◆人財要件:
                                    <br>■必須要件：<br>①商社の輸出入業務、貿易業務の知見<br>（輸出入の見積、手配、諸掛計上、関税支払）の業務知見。　<br>
                                    　※要件定義を行うため、上記について細かいレベルでの要件定義の経験やオペレーションの経験があるとBest<br><br>②システム開発経験（業務知見優先のため、なくても可）<br>◆単価:
                                    150万円 〜 200万円 / 月 ※スキル見合い<br>◆稼働率: 60 〜 79% / 40 〜 59%<br>◆出社頻度:
                                    週5日出社 / 週4 〜 2日出社 / 週1日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1回<br>◆契約期間: 2025年04月01日 〜 2026年09月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125467">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【コンサル募集】大規模基幹システム/ステークホルダーが多岐に渡るのでフットワーク軽い方/リモートあり",
                            "value": 125467
                        },
                        "datePosted": "2025/02/10 19:16:44",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 150,
                                "maxValue": 170,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125467/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【コンサル募集】大規模基幹システム/ステークホルダーが多岐に渡るのでフットワーク軽い方/リモートあり
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2026/02/28</div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            案件名：大規模基幹システムのアプリチームのPMO支援(弊社増員)

                            商流：大手コンサル→弊社
                            内容：：
                            アプリチームのPMO支援を想定しており、
                            テスト、移行工程における計画策定、課題解決を実施いただきたいと考えているステークホルダーが多岐にわたり、クイックな対応が求められる...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">150万円 〜 170万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社 / 週1日出社 / 週1日未満出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125467" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    568182608332<br>◆案件名: 【コンサル募集】大規模基幹システム/ステークホルダーが多岐に渡るのでフットワーク軽い方/リモートあり<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>案件名：大規模基幹システムのアプリチームのPMO支援(弊社増員)<br><br>商流：大手コンサル→弊社<br>内容：：<br>アプリチームのPMO支援を想定しており、<br>テスト、移行工程における計画策定、課題解決を実施いただきたいと考えているステークホルダーが多岐にわたり、クイックな対応が求められる<br><br>■必須要件：<br>コンサルティングファーム出身者<br>システム開発のサイクルを分かっている事<br>フットワークの軽さ<br>資料作成能力（ＰＰＴ、エクセル）<br><br>■尚可要件<br>保険の基幹システムへの理解<br>アプリ知見<br><br>■条件面：<br>金額：スキル見合い（150万～170万ほどで想定）<br>想定職位：シニアコンサルタント<br>場所：リモート併用（必要に応じ新宿近辺）<br>精算：精算あり<br>面談：1回※WEB（事前お打ち合わせ等あり）<br>備考：<br>45歳まで<br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>◆人財要件:
                                    <br>■必須要件：<br>コンサルティングファーム出身者<br>システム開発のサイクルを分かっている事<br>フットワークの軽さ<br>資料作成能力（ＰＰＴ、エクセル）<br><br>■尚可要件<br>保険の基幹システムへの理解<br>アプリ知見<br>◆単価:
                                    150万円 〜 170万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社 / 週1日出社 / 週1日未満出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1〜2回<br>◆契約期間: 2025年03月01日 〜 2026年02月28日<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125464">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "飲食店向けシステム開発・導入組織PMO",
                            "value": 125464
                        },
                        "datePosted": "2025/02/10 17:10:37",
                        "validThrough": "2025/03/03 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 85,
                                "maxValue": 110,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125464/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">飲食店向けシステム開発・導入組織PMO
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755=""
                                  class="badge-pill font-small ml-md-2 badge-pill font-small badge-blue">元請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">飲食店向けシステムの機能開発/保守、自社製品開発を進行中。
                            ・既にサービス展開しており、3ヶ月に1回ペースで機能追加実施中。
                            ・営業、開発チーム間連携、WBS更新などPM支援業務、PMO支援業務を希望

                            希望業務範囲
                            ・スケジュール管理・更新・状況確認
                            ・課題管理、課題解決支援
                            ・会議のファシリテーション、運営
                            ・報告資料の作成
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/2</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">85万円 〜 110万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社 / 週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125464" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    661175031987<br>◆案件名: 飲食店向けシステム開発・導入組織PMO<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 元請<br>◆案件内容: <br>飲食店向けシステムの機能開発/保守、自社製品開発を進行中。<br>・既にサービス展開しており、3ヶ月に1回ペースで機能追加実施中。<br>・営業、開発チーム間連携、WBS更新などPM支援業務、PMO支援業務を希望<br><br>希望業務範囲<br>・スケジュール管理・更新・状況確認<br>・課題管理、課題解決支援<br>・会議のファシリテーション、運営<br>・報告資料の作成<br>（上記業務等を想定しております。）求めるスキル・経験<br><br>ワークスタイル<br>週3日以上出社<br><br>◆人財要件:
                                    <br>必須<br>・フットワークが軽く、クライアントサイドを巻き込んで主体的に動いていける方<br>・開発経験よりもディレクション経験、進行管理経験あり、連携して組織をスムーズに動かすことができる方<br>・タスク管理、課題管理、会議運営等のプロジェクト管理業務の経験<br>・Excel、Power
                                    Point等を使用し、論点を整理して、状況把握・意思決定しやすい資料を作ることができる方<br><br><br>尚可（下記いずれかの経験のある方）<br>・LAMP環境でのシステム開発、要件定義、基本設計、詳細設計等の経験<br>・POS端末、またはサービス業向けのシステム・サービス開発の経験<br>・要件定義、基本設計、試験計画等各種ドキュメントの作成経験<br>・開発レビュアー経験<br><br>年齢：39歳まで<br><br><br><br>◆単価:
                                    85万円 〜 110万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社 / 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 2人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年03月01日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125463">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "POSシステム・PM・PMO案件（又はテックリード）",
                            "value": 125463
                        },
                        "datePosted": "2025/02/10 17:05:27",
                        "validThrough": "2025/03/03 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 130,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125463/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    POSシステム・PM・PMO案件（又はテックリード）
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755=""
                                  class="badge-pill font-small ml-md-2 badge-pill font-small badge-blue">元請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">サービス業向け（飲食、美容、小売業種）向けのPOSシステムの機能開発/保守を実施中。
                            ・現在既にサービス展開しており、3ヶ月に1回ペースで機能追加実施中。
                            ・現行システムの仕様・開発工程については、候補者あり次第詳細説明（可能）
                            ・開発要員は各機能3～10名規模。プロジェクト運営に問題がありPMO業務を希望。

                            希望業務範囲
                            ・スケジ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/2</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 130万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社 / 週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125463" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    494174725494<br>◆案件名: POSシステム・PM・PMO案件（又はテックリード）<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 元請<br>◆案件内容: <br>サービス業向け（飲食、美容、小売業種）向けのPOSシステムの機能開発/保守を実施中。<br>・現在既にサービス展開しており、3ヶ月に1回ペースで機能追加実施中。<br>・現行システムの仕様・開発工程については、候補者あり次第詳細説明（可能）<br>・開発要員は各機能3～10名規模。プロジェクト運営に問題がありPMO業務を希望。<br><br>希望業務範囲<br>・スケジュール策定、管理<br>・課題管理、課題解決支援<br>・成果物管理、納品管理<br>・会議のファシリテーション、運営<br>・報告資料の作成<br>（上記業務等を想定しております。）求めるスキル・経験<br><br>ワークスタイル<br>週3日以上出社<br><br>◆人財要件:
                                    <br>必須<br>・タスク管理、課題管理、会議運営等のプロジェクト管理業務の経験<br>・Excel、Power
                                    Point等を使用し、論点を整理して、状況把握・意思決定しやすい資料を作ることができる方<br>・フットワークが軽く、クライアントサイドを巻き込んで主体的に動いていける方<br><br>尚可（下記いずれかの経験のある方）<br>・LAMP環境でのシステム開発、要件定義、基本設計、詳細設計等の経験<br>・POS端末、またはサービス業向けのシステム・サービス開発の経験<br>・要件定義、基本設計、試験計画等各種ドキュメントの作成経験<br>・開発レビュアー経験<br><br>年齢：53歳まで<br><br><br><br>◆単価:
                                    100万円 〜 130万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社 / 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 2人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年03月01日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125416">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "小売り業_SCMシステム構築におけるグランドデザイン策定支援",
                            "value": 125416
                        },
                        "datePosted": "2025/02/10 17:01:54",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 110,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125416/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    小売り業_SCMシステム構築におけるグランドデザイン策定支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/10 〜 2025/08/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">・小売業のSCMシステム構築におけるグランドデザイン支援を行う
                            ・現在SCM全体のシステム構築/刷新を進めており、ネクストスコープは工場発注～生産管理～物流管理。
                            ・今後2026/8末までを目途に新システムでの運用を開始すべく、2025/6-8頃よりシステム要件定義を開始したい。
                            ・現状業務要件の整理やシステム化に向けたプランニングリソース...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">110万円 〜 150万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125416" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    165152997915<br>◆案件名:
                                    小売り業_SCMシステム構築におけるグランドデザイン策定支援<br>◆案件への関わり: 商流に入る<br>◆案件の商流:
                                    二次請以降<br>◆案件内容: <br>・小売業のSCMシステム構築におけるグランドデザイン支援を行う<br>・現在SCM全体のシステム構築/刷新を進めており、ネクストスコープは工場発注～生産管理～物流管理。<br>・今後2026/8末までを目途に新システムでの運用を開始すべく、2025/6-8頃よりシステム要件定義を開始したい。<br>・現状業務要件の整理やシステム化に向けたプランニングリソースが不足。<br>・エンドの社員代替としてグランドデザインが描ける有識者にご支援いただきたい。<br>◆人財要件:
                                    <br>[必須スキル]<br>・SCMの知見<br>・業務要件整理の経験<br>・システム導入に向けたロードマップ策定経験<br>・小売りの知見<br>[尚可スキル]<br>・工場・倉庫の知見<br>◆単価:
                                    110万円 〜 150万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 3回以上<br>◆契約期間:
                                    2025年02月10日 〜 2025年08月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125457">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "水処理装置メーカー_データ基盤構築要件定義支援",
                            "value": 125457
                        },
                        "datePosted": "2025/02/10 16:42:30",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 110,
                                "maxValue": 120,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125457/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    水処理装置メーカー_データ基盤構築要件定義支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">●案件概要：
                            《対象》
                            ・水処理装置を納入した企業先から装置のIoTデータを収集して、水質分析・品質改善・予兆保全などをリモートメンテナンスのサービスがあり、そのデータを蓄積する基盤
                            《背景》
                            ・マスタデータ自体は既に存在しているが、メンテナンスに課題がある
                            ・ 設計以降はお客様側で内製していく事が基本だが、業務要件とデータ構造を整合さ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">110万円 〜 120万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日未満出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125457" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    102173354573<br>◆案件名: 水処理装置メーカー_データ基盤構築要件定義支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>●案件概要：<br>《対象》<br>・水処理装置を納入した企業先から装置のIoTデータを収集して、水質分析・品質改善・予兆保全などをリモートメンテナンスのサービスがあり、そのデータを蓄積する基盤<br>《背景》<br>・マスタデータ自体は既に存在しているが、メンテナンスに課題がある<br>・
                                    設計以降はお客様側で内製していく事が基本だが、業務要件とデータ構造を整合させて機能要件としてまとめるまでの支援が欲しい<br>・対象の設備のカテゴリによってメンテナンスの手順や必須項目が異なる事が分かっており、概要レベルでは整理進行中<br>《支援して欲しい領域》<br>・設備の情報を活用するためのカタログ的なマスタのメンテナンスアプリの要件定義支援<br>└要件のまとめかた提言、実行支援、アウトプットに対するレビューなど<br>└要件とデータモデルの整合性
                                    などをチェック<br>└データモデルなどは既存案件メンバが理解している状況<br>└メンテナンス業務に携わる方の業務分析（分かりやすいUIをデザインする）も含む<br>●募集人数：1名<br>●勤務地：基本リモート（クライアント先出社時は中野）<br>●期間：2025/3/1～2025/3/31
                                    ※以降延長の可能性有<br>●稼働率：100％<br>●面談回数：2回<br>●商流：弊社3次請け<br>◆人財要件:
                                    <br>●求めるスキル<br>《必須》<br>・データ基盤構築における、画面やアプリの要件定義の経験・知見（インフラ構築やデータ分析の文脈ではなく、アプリの要件定義をリード出来る方）<br>・顧客から業務要件を吸い上げるスキル<br>《尚可》<br>・以下、利用システムの知見<br>　-
                                    基盤：Azure<br>　- 現行マスタ管理：Salesforce<br>　- データ連携：Matillion<br>　‐
                                    データ蓄積：Snowflake<br>◆単価: 110万円 〜 120万円 / 月 ※スキル見合い<br>◆稼働率:
                                    100%（フル稼働）<br>◆出社頻度: 週1日未満出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年03月01日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125415">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "大手向けB2Bマーケティングの企画、運用支援、全般のPM支援",
                            "value": 125415
                        },
                        "datePosted": "2025/02/10 16:41:57",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 80,
                                "maxValue": 120,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125415/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    大手向けB2Bマーケティングの企画、運用支援、全般のPM支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/10 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            大規模顧客のBtoBデジタルマーケティング施策の立ち上げ・運用・改善を、クライアント先の企業の各分野の専門スキルメンバーから成るチームを率いて、総合的に支援します。

                            ◇業務内容
                            B2Bマーケティングの企画、運用支援、全般のプロデューサー
                            顧客の事業や目標、抽出したデジタルマーケティング上の課題を踏まえ、マーケティングKPI達成のための施...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">80万円 〜 120万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125415" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    869152734188<br>◆案件名: 大手向けB2Bマーケティングの企画、運用支援、全般のPM支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>大規模顧客のBtoBデジタルマーケティング施策の立ち上げ・運用・改善を、クライアント先の企業の各分野の専門スキルメンバーから成るチームを率いて、総合的に支援します。<br><br>◇業務内容<br>B2Bマーケティングの企画、運用支援、全般のプロデューサー<br>顧客の事業や目標、抽出したデジタルマーケティング上の課題を踏まえ、マーケティングKPI達成のための施策を提案し、自身によるハンズオンや各分野の専門スキルを有するメンバーを率いて実践します。<br>施策成果を通じて顧客企業のキーマンから信頼を得ることにより、支援範囲（部署、スコープ、規模）の拡大を図ります。<br>施策内容には以下を含みます。<br>・B2Bマーケティング全体プランニング、進捗管理、品質管理　（PoCプロジェクト含む）<br>・個別施策のプランニング、進捗管理、品質管理<br>・Webマーケティング（集客施策、リード獲得施策、コンバージョン改善、等<br>・メールマーケティング（マーケティングオートメーション（以下MA）活用、コンテンツ制作）<br>・マーケティングテクノロジーの導入、システム連携、運用支援<br>◆人財要件:
                                    <br>[必須スキル]<br>◇必要なスキル<br>・顧客課題を解決に導くソリューション営業的能力<br>・プロジェクトにおける課題管理、進捗管理、品質管理マネジメントスキル<br>・一連のBtoBデジタルマーケティングの実践スキル<br>　（リード獲得～育成～評価～営業＆インサイドセールスとの連携）<br>・施策結果をデータで検証することで新たなPDCAを回し、改善につなげる分析力・企画力・提案力<br><br>◇必要な業務経験<br>・BtoBデジタルマーケティングの大規模案件（数千万規模）においてビジネスPM的立場でチームを率いて、お客様の売上向上を導いた成功体験があること。<br>・BtoB向けマーケティングオートメーションの導入・運用支援または実務経験があることが望ましい。<br><br>[尚可スキル]<br>◇求められる資質<br>・積極性＆提案力＆コミュニケーション能力<br>・ロジカルシンキング＆管理能力＆責任感<br>・多様なマーケ手法におけるハンズオンの経験<br>◆単価:
                                    80万円 〜 120万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年02月10日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125452">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【急募】水処理装置メーカー_データ基盤構築要件定義支援",
                            "value": 125452
                        },
                        "datePosted": "2025/02/10 16:25:46",
                        "validThrough": "2025/02/21 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 110,
                                "maxValue": 120,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125452/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【急募】水処理装置メーカー_データ基盤構築要件定義支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">●案件名：水処理装置メーカー_データ基盤構築要件定義支援
                            ●案件概要：
                            《対象》
                            ・水処理装置を納入した企業先から装置のIoTデータを収集して、水質分析・品質改善・予兆保全などをリモートメンテナンスのサービスがあり、そのデータを蓄積する基盤
                            《背景》
                            ・マスタデータ自体は既に存在しているが、メンテナンスに課題がある
                            ・ 設計以降はお客...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/20</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">110万円 〜 120万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社 / 週1日未満出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125452" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    730172344056<br>◆案件名: 【急募】水処理装置メーカー_データ基盤構築要件定義支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>●案件名：水処理装置メーカー_データ基盤構築要件定義支援<br>●案件概要：<br>《対象》<br>・水処理装置を納入した企業先から装置のIoTデータを収集して、水質分析・品質改善・予兆保全などをリモートメンテナンスのサービスがあり、そのデータを蓄積する基盤<br>《背景》<br>・マスタデータ自体は既に存在しているが、メンテナンスに課題がある<br>・
                                    設計以降はお客様側で内製していく事が基本だが、業務要件とデータ構造を整合させて機能要件としてまとめるまでの支援が欲しい<br>・対象の設備のカテゴリによってメンテナンスの手順や必須項目が異なる事が分かっており、概要レベルでは整理進行中<br>《支援して欲しい領域》<br>・設備の情報を活用するためのカタログ的なマスタのメンテナンスアプリの要件定義支援<br>└要件のまとめかた提言、実行支援、アウトプットに対するレビューなど<br>└要件とデータモデルの整合性
                                    などをチェック<br>└データモデルなどは既存案件メンバが理解している状況<br>└メンテナンス業務に携わる方の業務分析（分かりやすいUIをデザインする）も含む<br>●募集人数：1名<br>●勤務地：基本リモート（クライアント先出社時は中野）<br>●期間：2025/3/1～2025/3/31
                                    ※以降延長の可能性有<br>●稼働率：100％ <br>●単価：110-120万円<br>●面談回数：2回<br>●商流：弊社3次請け<br><br>◆人財要件:
                                    <br>●求めるスキル <br>《必須》<br>・データ基盤構築における、画面やアプリの要件定義の経験・知見（インフラ構築やデータ分析の文脈ではなく、アプリの要件定義をリード出来る方）<br>・顧客から業務要件を吸い上げるスキル<br>《尚可》<br>・以下、利用システムの知見<br>　-
                                    基盤：Azure<br>　- 現行マスタ管理：Salesforce<br>　- データ連携：Matillion<br>　‐
                                    データ蓄積：Snowflake<br>◆単価: 110万円 〜 120万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度:
                                    週1日出社 / 週1日未満出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年03月01日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125417">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【ハイブリ/ 調査・評価】元請支援_SAP会計領域におけるIT×業務コンサル募集_008031",
                            "value": 125417
                        },
                        "datePosted": "2025/02/10 11:26:29",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 180,
                                "maxValue": 190,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125417/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【ハイブリ/
                                    調査・評価】元請支援_SAP会計領域におけるIT×業務コンサル募集_008031
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/24 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            会計業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、改修に合ったってのシステム要件定義も行う想定。
                            関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。
                            【場所】
                            ハイブリッド/ 有楽町駅周辺
                            詳細な場所は面談時に元請から共有予定。
                            ※沖縄への出張対応の可能性...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">180万円 〜 190万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125417" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    847154384302<br>◆案件名: 【ハイブリ/ 調査・評価】元請支援_SAP会計領域におけるIT×業務コンサル募集_008031<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>会計業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、改修に合ったってのシステム要件定義も行う想定。<br>関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。<br>【場所】<br>ハイブリッド/
                                    有楽町駅周辺<br>詳細な場所は面談時に元請から共有予定。<br>※沖縄への出張対応の可能性あり。
                                    （1週間程度を想定）<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ： 当月末締め<br>稼働日数 ： 週5日<br>【備考】<br>ただし弊社未面談の方は事前にお会いさせていただきます。（計2回）<br>勤務時間
                                    ： 9:00～18:00<br>年齢制限 ： 50歳まで<br>商流制限 ： 貴社所属の方を優先致します<br>国籍制限
                                    ： 日本籍の方を優先させていただきます<br>服装指定 ： スーツ or ビジネスカジュアル<br>リモート対応
                                    ： 在宅業務での環境は貴社 or 稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・SAPシステム会計領域の知見と、企業財務の専門知識<br>・一般的なビジネスプロセス（AP、AR、GL、トレジャリーなど）<br>・財務プロセスのワークショップの準備、実施、文書化の経験がある<br>・英語：ビジネスレベル<br>◆単価:
                                    180万円 〜 190万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年02月24日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先）
                                    / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125414">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "生産管理・会計システムリプレイス支援",
                            "value": 125414
                        },
                        "datePosted": "2025/02/10 10:56:04",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 140,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125414/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    生産管理・会計システムリプレイス支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">●案件名：生産管理・会計システムリプレイス支援
                            ●案件概要：
                            大手製造業向けの生産管理および会計システムのリプレイスPJにおけるPMO業務を担当。
                            ベンダーやクライアントとの調整を行い、
                            システム導入に関する業務設計および要件定義のリードを実施。
                            特に生産管理領域の知見が豊富な方を優先して募集。
                            ●仕事内容：
                            -生産管理および会計...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 140万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 60 〜 79% / 40 〜 59%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125414" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    984152564814<br>◆案件名: 生産管理・会計システムリプレイス支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>●案件名：生産管理・会計システムリプレイス支援<br>●案件概要：<br>大手製造業向けの生産管理および会計システムのリプレイスPJにおけるPMO業務を担当。<br>ベンダーやクライアントとの調整を行い、<br>システム導入に関する業務設計および要件定義のリードを実施。<br>特に生産管理領域の知見が豊富な方を優先して募集。<br>●仕事内容：<br>
                                    -生産管理および会計システムの業務設計・要件定義のリード<br> -プロジェクト全体の進捗管理、課題管理<br>
                                    -ステークホルダー（クライアント、ベンダーなど）との調整・折衝<br>
                                    -システム導入に向けた業務プロセスの整理・改善提案<br>●勤務地（頻度）：西新宿（週2回出社）および大手町（週1回出社）他リモート<br>●期間：2025年4月～長期（1～2年以上を予定）<br>●稼働率：100%<br>●備考：<br>
                                    -現在参画しているメンバーのリプレイス枠<br> -チーム体制は3名<br>●面談回数：1回<br>●商流：弊社2次請け<br>◆人財要件:
                                    <br>●求めるスキル：<br>〈必須〉<br> -システム導入プロジェクトのPL、PM、またはPMO経験（事務局業務は除く）<br>
                                    -生産管理または会計システムに関する知見<br> -システム導入案件の業務設計～要件定義経験<br>
                                    -クライアントおよびベンダーとの折衝経験<br>
                                    -長期プロジェクト参画可能な方<br>〈尚可〉<br> -PMBOKの知識<br> -Microsoft
                                    Dynamics（FOまたはPO）の知見<br> -Microsoft Dynamics関連プロジェクトの経験<br>
                                    -大手コンサルファームでの勤務経験<br>◆単価: 120万円 〜 140万円 / 月 ※スキル見合い<br>◆稼働率:
                                    100%（フル稼働） / 60 〜 79% / 40 〜 59%<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年03月01日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125412">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【要員募集：ヘルプデスク】ERP導入/知見/リモート",
                            "value": 125412
                        },
                        "datePosted": "2025/02/10 10:31:32",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 50,
                                "maxValue": 70,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125412/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【要員募集：ヘルプデスク】ERP導入/知見/リモート
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2026/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            ■ERPパッケージシステムヘルプデスク
                            〈作業内容〉
                            　ERPパッケージ（Microsoft Dynamic365BC）導入済の顧客からの
                            　問合せを受付け、開発チームの支援を受けながら対応する。

                            ■必須要件：
                            ・ERPパッケージ（何でも可）導入経験またはそれに準ずる知...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">50万円 〜 70万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社 / 週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125412" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    458151095871<br>◆案件名: 【要員募集：ヘルプデスク】ERP導入/知見/リモート<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>■ERPパッケージシステムヘルプデスク<br>〈作業内容〉<br>　ERPパッケージ（Microsoft
                                    Dynamic365BC）導入済の顧客からの<br> 　問合せを受付け、開発チームの支援を受けながら対応する。<br><br>■必須要件：<br>・ERPパッケージ（何でも可）導入経験またはそれに準ずる知見<br>→参画後にレクチャーはあるが、一定の知識は必要<br>・コミュニケーション能力<br>・フットワークの軽さ<br><br>■尚可要件：<br>開発経験<br>英語力（対応可だと有難いです。）<br><br>■条件：<br>期間：4月～<br>稼働：100％<br>人数：1名<br>金額：50万～70万ほど<br>面談：2回※WEB<br>場所：基本リモート（必要に応じ天王洲アイル）<br>支払い：45日<br>勤務：フレックス・拘束時間なし<br><br>■備考：<br>55歳ぐらいまで<br>外国籍不可<br>弊社から1社先まで<br>地方も可だが、初日出社や毎月1回程度の出社にかかる交通費は実費でお願いします。<br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>◆人財要件:
                                    <br>■必須要件：<br>・ERPパッケージ（何でも可）導入経験またはそれに準ずる知見<br>→参画後にレクチャーはあるが、一定の知識は必要<br>・コミュニケーション能力<br>・フットワークの軽さ<br><br>■尚可要件：<br>開発経験<br>英語力（対応可だと有難いです。）<br>◆単価:
                                    50万円 〜 70万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日出社
                                    / 週1日未満出社 / フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年04月01日 〜 2026年02月28日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125410">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "必見18案件！　開発・サポート色々(リモート含む)",
                            "value": 125410
                        },
                        "datePosted": "2025/02/08 20:51:10",
                        "validThrough": "2025/02/18 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 55,
                                "maxValue": 100,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125410/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    必見18案件！　開発・サポート色々(リモート含む)
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月08日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/13 〜 2025/12/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">⑥保険業向け監査自動化ツール開発支援
                            【月額報酬】　60万～75万円※スキル見合い
                            【業種】　金融業、保険業
                            【業務内容】
                            ・保険業向け監査自動化ツールの開発支援
                            ・お客様プロパとともに5名チームのメンバとして参画
                            ・実施工程：設計・実装・テスト
                            ・プロダクトマネージャやテクノロジープロジェクトマネージャの管理のもと、テクニカルビジ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/17</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">55万円 〜 100万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 80 〜 99%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">協力会社社員（一社先） / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125410" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    369015471390<br>◆案件名: 必見18案件！　開発・サポート色々(リモート含む)<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>⑥保険業向け監査自動化ツール開発支援<br>【月額報酬】　60万～75万円※スキル見合い<br>【業種】　金融業、保険業<br>【業務内容】<br>・保険業向け監査自動化ツールの開発支援<br>・お客様プロパとともに5名チームのメンバとして参画<br>・実施工程：設計・実装・テスト<br>・プロダクトマネージャやテクノロジープロジェクトマネージャの管理のもと、テクニカルビジネスアナリストの要求仕様に従って業務<br>・Azure環境上で動作するWebアプリケーションをC#で開発<br>【必須経験/スキル】<br>・.NetFramework(C#)を用いたWebアプリケーションシステム（API開発を含む）開発経験<br>・Gitなどを用いたバージョン管理経験<br>・Azureの開発経験<br>【任意経験/スキル】<br>・会計に関する知識<br>・ReactやPythonを用いた開発経験<br>・分からないことがあれば自ら質問し、解決に向けて積極的に動くことができる方<br>・チャットやredmineのチケットでの連絡や報告が具体的かつ簡潔に発信できる方<br>【勤務地・最寄り駅】　神奈川県横浜市
                                    / フルリモート（在宅) / 関内駅<br>【リモート状況】　フルリモート<br>【稼働率】　100%<br>【期間】　即日～長期予定<br>【英語力】<br>会話：不要<br>読み書き：不要<br>【精算時間幅】　140H-180H<br>【募集人数】　1名<br>【契約形態】　　契約社員or転籍出向<br>【備考】<br>・打ち合わせ回数:2回<br>・PCセットアップで元請けオフィスに(関内駅)出社。その他必要に応じて月1日程度出社の可能性あり。<br>※出社頻度相談可能<br><br>⑧『M365/SharePoint』開発及びサポート支援<br>【月額報酬】
                                    60万～120万円※スキル見合い<br>【業種】　生活関連サービス業、娯楽業<br>【業務内容】<br>・M365を中心としたクラウド製品の運用・設計・開発業務<br>・M365製品(Exchange,SharePoint,TeamsからPowerPlatform等、今後リリースされる製品も含めて全て)において、社内のユーザーからの依頼や要件に対する調査、検証、実装業務<br>・新機能や製品の検証・実装・告知、およびユーザーサポート<br>・システムトラブルの対応<br>【必須経験/スキル】<br>・M365全般の運用知見<br>・SharePointのカスタマイズ、PowerPlatformを使ったアプリや自動化ツールの構築等の経験<br>・M365管理者としてのMicrosoft(Graph)PowerShellなどを使ったスクリプト作成・管理・改修の経験<br>【任意経験/スキル】<br>・Azure環境管理の経験<br>・ADおよびWindowsServerの知見(タスク実行、GPO等)<br>・AtlassianCloud(主にJira、Confluence)の運用経験<br>・Linuxサーバーの知見<br>・GitHubの知見<br>・EntraID(Azure)の知見<br>・コミュニケーション能力(オンサイトまたは音声会議を利用したユーザー対応が発生します)<br>・積極的にユーザーのトラブルやリクエストに対応する働き方ができる方<br>【勤務地・最寄り駅】　東京都新宿区<br>【リモート状況】　月2回程度のクライアント訪問<br>【稼働率】　100%<br>【期間】　即日～2025年4月末(延長想定)<br>【英語力】<br>会話：不要<br>読み書き：不要<br>【募集人数】<br>【契約形態】　　契約社員or転籍出向<br><br>⑭『C/Linux』インフラミドル開発支援（通信・課題解決）<br>【月額報酬】　80〜95万円※スキル見合い<br>【業種】
                                    情報通信業<br>【業務内容】<br>・通信業におけるインフラミドル開発支援業務<br>・クライアントプロパーの方針のもと、課題解決に向けた作業を行う<br>・構造改革中に発生している様々な問題のハンドリングと解決に向けた調査や検討<br>-必要に応じてリーダーに報告/課題解決に向けた提案<br>・PJ内ステークホルダーとの調整作業<br>【必須経験/スキル】<br>・C言語、Linuxの知見<br>・コミュニケーション力<br>・課題解決に向けて主体的に取り組む姿勢<br>【任意経験/スキル】<br>・PL/TL等のマネジメント経験<br>【勤務地・最寄り駅】
                                    東京都新宿区 / 新宿駅<br>【リモート状況】 週4日程度のリモート<br>【稼働率】 100%<br>【期間】
                                    2025年2月～5月末(延長可能性有)<br>【精算時間幅】 140H-180H<br>【勤務時間】
                                    09:30-18:30<br>【募集人数】 1名<br>【契約形態】 契約社員or転籍出向<br>【備考】<br>・本件終了後、PJ内別ポジションでの参画可能性あり<br>・トラブル発生時は出社可能性あり<br>・お打合せ回数1回（Web）<br>◆人財要件:
                                    <br>⑥保険業向け監査自動化ツール開発支援<br>【月額報酬】　60万～75万円※スキル見合い<br>【業種】　金融業、保険業<br>【業務内容】<br>・保険業向け監査自動化ツールの開発支援<br>・お客様プロパとともに5名チームのメンバとして参画<br>・実施工程：設計・実装・テスト<br>・プロダクトマネージャやテクノロジープロジェクトマネージャの管理のもと、テクニカルビジネスアナリストの要求仕様に従って業務<br>・Azure環境上で動作するWebアプリケーションをC#で開発<br>【必須経験/スキル】<br>・.NetFramework(C#)を用いたWebアプリケーションシステム（API開発を含む）開発経験<br>・Gitなどを用いたバージョン管理経験<br>・Azureの開発経験<br>【任意経験/スキル】<br>・会計に関する知識<br>・ReactやPythonを用いた開発経験<br>・分からないことがあれば自ら質問し、解決に向けて積極的に動くことができる方<br>・チャットやredmineのチケットでの連絡や報告が具体的かつ簡潔に発信できる方<br>【勤務地・最寄り駅】　神奈川県横浜市
                                    / フルリモート（在宅) / 関内駅<br>【リモート状況】　フルリモート<br>【稼働率】　100%<br>【期間】　即日～長期予定<br>【英語力】<br>会話：不要<br>読み書き：不要<br>【精算時間幅】　140H-180H<br>【募集人数】　1名<br>【契約形態】　　契約社員or転籍出向<br>【備考】<br>・打ち合わせ回数:2回<br>・PCセットアップで元請けオフィスに(関内駅)出社。その他必要に応じて月1日程度出社の可能性あり。<br>※出社頻度相談可能<br><br>⑧『M365/SharePoint』開発及びサポート支援<br>【月額報酬】
                                    60万～120万円※スキル見合い<br>【業種】　生活関連サービス業、娯楽業<br>【業務内容】<br>・M365を中心としたクラウド製品の運用・設計・開発業務<br>・M365製品(Exchange,SharePoint,TeamsからPowerPlatform等、今後リリースされる製品も含めて全て)において、社内のユーザーからの依頼や要件に対する調査、検証、実装業務<br>・新機能や製品の検証・実装・告知、およびユーザーサポート<br>・システムトラブルの対応<br>【必須経験/スキル】<br>・M365全般の運用知見<br>・SharePointのカスタマイズ、PowerPlatformを使ったアプリや自動化ツールの構築等の経験<br>・M365管理者としてのMicrosoft(Graph)PowerShellなどを使ったスクリプト作成・管理・改修の経験<br>【任意経験/スキル】<br>・Azure環境管理の経験<br>・ADおよびWindowsServerの知見(タスク実行、GPO等)<br>・AtlassianCloud(主にJira、Confluence)の運用経験<br>・Linuxサーバーの知見<br>・GitHubの知見<br>・EntraID(Azure)の知見<br>・コミュニケーション能力(オンサイトまたは音声会議を利用したユーザー対応が発生します)<br>・積極的にユーザーのトラブルやリクエストに対応する働き方ができる方<br>【勤務地・最寄り駅】　東京都新宿区<br>【リモート状況】　月2回程度のクライアント訪問<br>【稼働率】　100%<br>【期間】　即日～2025年4月末(延長想定)<br>【英語力】<br>会話：不要<br>読み書き：不要<br>【募集人数】<br>【契約形態】　　契約社員or転籍出向<br><br>⑭『C/Linux』インフラミドル開発支援（通信・課題解決）<br>【月額報酬】　80〜95万円※スキル見合い<br>【業種】
                                    情報通信業<br>【業務内容】<br>・通信業におけるインフラミドル開発支援業務<br>・クライアントプロパーの方針のもと、課題解決に向けた作業を行う<br>・構造改革中に発生している様々な問題のハンドリングと解決に向けた調査や検討<br>-必要に応じてリーダーに報告/課題解決に向けた提案<br>・PJ内ステークホルダーとの調整作業<br>【必須経験/スキル】<br>・C言語、Linuxの知見<br>・コミュニケーション力<br>・課題解決に向けて主体的に取り組む姿勢<br>【任意経験/スキル】<br>・PL/TL等のマネジメント経験<br>【勤務地・最寄り駅】
                                    東京都新宿区 / 新宿駅<br>【リモート状況】 週4日程度のリモート<br>【稼働率】 100%<br>【期間】
                                    2025年2月～5月末(延長可能性有)<br>【精算時間幅】 140H-180H<br>【勤務時間】
                                    09:30-18:30<br>【募集人数】 1名<br>【契約形態】 契約社員or転籍出向<br>【備考】<br>・本件終了後、PJ内別ポジションでの参画可能性あり<br>・トラブル発生時は出社可能性あり<br>・お打合せ回数1回（Web）<br>◆単価:
                                    55万円 〜 100万円 / 月 <br>◆稼働率: 100%（フル稼働） / 80 〜 99%<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 3人<br>◆面談回数:
                                    1〜2回<br>◆契約期間: 2025年02月13日 〜 2025年12月31日 ※継続の可能性あり<br>◆募集対象:
                                    協力会社社員（一社先） / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125403">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【リモート可】不動産業_Intune/MDE導入支援",
                            "value": 125403
                        },
                        "datePosted": "2025/02/07 19:31:31",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 80,
                                "maxValue": 90,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125403/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【リモート可】不動産業_Intune/MDE導入支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/03 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■作業(概要）:
                            ・Windows 端末管理強化に伴う、Intuneデバイス登録およびDefender for Endpointの導入支援
                            ・対象端末は2,000台程度、Windows11

                            ■作業場所（勤務地・最寄駅等）:大崎（リモート可）
                            ■作業期間（開始時期）:2025年3月3日
                            ■作業期間（終了時期）:2025年4月30日
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">80万円 〜 90万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125403" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    602924297854<br>◆案件名: 【リモート可】不動産業_Intune/MDE導入支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>■作業(概要）:<br>・Windows
                                    端末管理強化に伴う、Intuneデバイス登録およびDefender for Endpointの導入支援<br>・対象端末は2,000台程度、Windows11<br><br>■作業場所（勤務地・最寄駅等）:大崎（リモート可）<br>■作業期間（開始時期）:2025年3月3日<br>■作業期間（終了時期）:2025年4月30日<br>■精算条件（超過・控除の有無）:160h±20h<br>■外国籍（可・不可）:不可<br>◆人財要件:
                                    <br>■スキル要件:<br>・Windows端末を対象とした Intune/MDEの設計、構築経験がある方<br>・その他<br>　-
                                    主体的に課題解決していける方<br>　- コミュニケーションが円滑に行える方<br>　-
                                    ドキュメント（設計書、手順書等）作成できる方<br>　- 勤怠に問題が無い方<br>◆単価:
                                    80万円 〜 90万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 2回<br>◆契約期間:
                                    2025年03月03日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125402">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【リモート可】金融_Exchange Online導入支援",
                            "value": 125402
                        },
                        "datePosted": "2025/02/07 19:28:23",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 80,
                                "maxValue": 90,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125402/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【リモート可】金融_Exchange
                                    Online導入支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/03 〜 2025/05/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■作業(概要）:
                            ・Exchange Online新規導入に伴う要件定義
                            ・セキュリティ強化のため、以下製品の評価を実施する
                            　- HENNGE Email DLP
                            　- Menlo Security セキュア Office 365

                            ■作業場所（勤務地・最寄駅等）:大崎（リモート可）
                            ■作業期間（開始時期）:2025年3月3日
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">80万円 〜 90万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125402" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    409924108810<br>◆案件名: 【リモート可】金融_Exchange Online導入支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>■作業(概要）:<br>・Exchange
                                    Online新規導入に伴う要件定義<br>・セキュリティ強化のため、以下製品の評価を実施する<br>　-
                                    HENNGE Email DLP<br>　- Menlo Security セキュア Office 365<br><br>■作業場所（勤務地・最寄駅等）:大崎（リモート可）<br>■作業期間（開始時期）:2025年3月3日<br>■作業期間（終了時期）:2025年5月30日
                                    備考:延長の可能性あり<br>■精算条件（超過・控除の有無）:160h±20h<br>■外国籍（可・不可）:不可<br>◆人財要件:
                                    <br>■スキル要件:<br>・Microsoft 365 / Exchange Online 導入に伴う要件定義フェーズの経験がある方<br>・3rd製品の評価/検証経験がある方<br>・要件定義書作成経験がある方<br>・その他<br>　-
                                    主体的に課題解決していける方<br>　- コミュニケーションが円滑に行える方<br>　-
                                    ドキュメント（設計書、手順書等）作成できる方<br>　- 勤怠に問題が無い方<br>◆単価:
                                    80万円 〜 90万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年03月03日 〜 2025年05月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125401">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【リモート可】大手製造業_GWS→M365移行支援",
                            "value": 125401
                        },
                        "datePosted": "2025/02/07 19:24:51",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都 ",
                                    "addressCountry": "JP"
                                },
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": " 大阪府",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 80,
                                "maxValue": 90,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125401/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【リモート可】大手製造業_GWS→M365移行支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/03 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■作業(概要）:
                            ・既存GWS環境からM365への切換支援
                            ・切換に伴うM365環境の要件定義・設計・構築・テスト・運用設計・切換設計支援
                            ・M365対象機能：Exchange Online/Teams/SharePoint Online/OneDrive/EOP/Forms
                            ※データ移行はAvePoint FLY（別チームにて実施のため...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">80万円 〜 90万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都 / 大阪府</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125401" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    482923894540<br>◆案件名: 【リモート可】大手製造業_GWS→M365移行支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>■作業(概要）:<br>・既存GWS環境からM365への切換支援<br>・切換に伴うM365環境の要件定義・設計・構築・テスト・運用設計・切換設計支援<br>・M365対象機能：Exchange
                                    Online/Teams/SharePoint Online/OneDrive/EOP/Forms<br>※データ移行はAvePoint
                                    FLY（別チームにて実施のため作業範囲外）<br><br>■作業場所（勤務地・最寄駅等）:大崎または大阪近郊（リモート可）<br>■作業期間（開始時期）:2025年3月3日
                                    備考:参画時期前倒しなど調整可能<br>■作業期間（終了時期）:2025年4月30日 備考:延長の可能性あり<br>■精算条件（超過・控除の有無）:160h±20h<br>■外国籍（可・不可）:不可<br>◆人財要件:
                                    <br>■スキル要件:<br>・メール領域：<br>Exchange Online, Exchange OnlineProtection,
                                    Exchange OnlineArchivingMicrosoft365 Tenant, EntraIDに関する、設計構築のために必要な要件定義の経験がある方<br>・SPS/Teams領域：<br>Teams,
                                    SharePointOnline, OneDrive に関する、設計構築のために必要な要件定義の経験がある方<br>・運用ツール領域：<br>Power
                                    AutomateやPower AppsなどM365のサービスを使ったSPSやTeamsサイト申請アプリ開発に伴う要件定義の経験がある方<br>・GWS支援領域：　<br>GWS／M365両方のサービスもしくはGWSに対し技術的に詳しい方で設計や移行計画にあたりGWS観点での技術支援が可能な方<br>・その他<br>　-
                                    主体的に課題解決していける方<br>　- コミュニケーションが円滑に行える方<br>　-
                                    ドキュメント（設計書、手順書等）作成できる方<br>　- 勤怠に問題が無い方<br>◆単価:
                                    80万円 〜 90万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都 / 大阪府<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    2人<br>◆面談回数: 2回<br>◆契約期間: 2025年03月03日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125400">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【急募】複数ポジション募集PJ",
                            "value": 125400
                        },
                        "datePosted": "2025/02/07 19:09:19",
                        "validThrough": "2025/03/09 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 130,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125400/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【急募】複数ポジション募集PJ</div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2026/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">案件概要：
                            ・現在エンドクライアント内にて複数PJが走っている中、大規模な体制変更を推進することに
                            ・アプリの組織、インフラの組織に集約
                            ・データマネジメントはインフラ組織に集約
                            ・専門職の部門を新設し、PM、BA, ITAのリソースプールに
                            現状の課題：
                            ・基幹システム刷新においてPM,BA,ITAのリソースが足りていない
                            ・セキ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/8</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 130万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125400" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    245922954697<br>◆案件名: 【急募】複数ポジション募集PJ<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>案件概要：<br>・現在エンドクライアント内にて複数PJが走っている中、大規模な体制変更を推進することに<br>・アプリの組織、インフラの組織に集約<br>・データマネジメントはインフラ組織に集約<br>・専門職の部門を新設し、PM、BA,
                                    ITAのリソースプールに<br>現状の課題：<br>・基幹システム刷新においてPM,BA,ITAのリソースが足りていない<br>・セキュリティ観点ではUS/ASIAで強力なガバナンスが必要<br>・海外のITをサポートしている部隊を吸収する
                                    (海外ITもミッションになる)<br>・M&amp;A案件も進行中<br>募集ポジション：<br>・発注側のPM/5名程度<br>・社内アプリ全般を担当するアーキテクト人財：1名<br>・PJのインフラ全般を担当するアーキテクト：1名<br>・セキュリティアーキテクト（実装観点）：1名<br>・Azureに知見が深いアーキテクト：1名<br>・BCPに知見のあるセキュリティコンサルタント：1名→急募<br>・海外ITサポートチーム責任者（海外支社のIT投資が適切か否か判断できる方）：1名→急募<br><br>単価：～130万円<br>年齢：～50歳<br>開始時期:3月頭～長期<br>面談回数：2回（プライム
                                    事業部長/既存参画メンバー→エンド担当者）<br>稼働率：100％<br>働き方：週4オンサイト、週1リモート<br>場所：都内23区内<br>◆人財要件:
                                    <br>全ポジションにおける共通必須スキル：<br>・ユーザー/ベンダーコントロールが必要な為、柔軟にコミュニケーションを取ることができる事<br>・ユーザーサイド/ベンダーサイドどちらも経験している事<br>・小回りがきく事<br>◆単価:
                                    100万円 〜 130万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 11人<br>◆面談回数: 2回<br>◆契約期間:
                                    2025年03月01日 〜 2026年02月28日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <nav aria-label="pagination example">
                <nav role="navigation" class="pagination">
                    <ul class="pagination">
                        <li class="prev previous_page disabled page-item"><a class="page-link waves-effect" href="#">←
                            前</a></li>
                        <li class="active page-item"><a class="page-link waves-effect"
                                                        href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_it&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=1">1</a>
                        </li>
                        <li class="page-item"><a rel="next" class="page-link waves-effect"
                                                 href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_it&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=2">2</a>
                        </li>
                        <li class="page-item"><a class="page-link waves-effect"
                                                 href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_it&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=3">3</a>
                        </li>
                        <li class="page-item disabled"><a class="page-link waves-effect" href="#">…</a></li>
                        <li class="page-item"><a class="page-link waves-effect"
                                                 href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_it&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=6">6</a>
                        </li>
                        <li class="next next_page page-item"><a class="page-link waves-effect" rel="next"
                                                                href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_it&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=2">次
                            →</a></li>
                    </ul>
                </nav>
            </nav>
        </div>
    </div>
</div>
<div style = "margin-top:79px" >
<link rel="stylesheet" href="/custom_frontend/static/css/IT.css"/>
    `,
    data() {
        return {

        }
    }
}
export default it