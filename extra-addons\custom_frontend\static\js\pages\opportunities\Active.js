import { userInfo } from "../../router/router.js";
import { createSearchBreadcrumb } from "../../utils/breadcrumbHelper.js";

const Active = {
    'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
    ${createSearchBreadcrumb('案件を探す')}
    <div class="container-fluid pt-1">
        <div class="row justify-content-between align-items-center mb-2">
            <div class="tab-container">
                <div class="btn-default tab-button" @click="tabChange(1)">
                    案件を探す
                </div>
                <div class="tab-button" @click="tabChange(2)" style="margin-left: 7px;">
                    人財を探す
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center results-sort-container">
                <div class="font-middle result-count-container"><span class="font-ll ex-bold"
                        id="total-count">{{this.totalRecord}}</span>&nbsp;件中&nbsp;{{ getStartItemIndex() }}〜{{
                    getEndItemIndex() }}件</div>
                <div class="position-relative" ref="dropdown">
                    <!-- Khu vực hiển thị và bấm vào -->
                    <div style="margin-right: 17px;" class="d-flex justify-content-end sort-display-area p-2"
                        @click="isOpen5 = !isOpen5">
                        <label class="pr-2 mb-0">{{ selectedOption }}</label>
                        <i class="material-icons custom-grey-6-text pl-1" style="line-height: 1 !important;">
                            {{ isOpen5 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                        </i>
                    </div>

                    <!-- Danh sách hiển thị khi bấm vào -->
                    <ul style="margin-right: 17px;" class="bg-white sort-options-area text-right" v-show="isOpen5">
                        <li v-for="option in options" :key="option"
                            :class="{ 'sort-active': option === selectedOption }" @click="selectOption(option)">
                            {{ option }}
                            <i v-if="option === selectedOption" class="material-icons custom-grey-6-text">done</i>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="container-fluid grabient">
            <div class="row">
                <div style="padding-left: 0px;" class="d-none d-md-block col-12 col-md-5 col-lg-4" id="side-search">
                    <div style="padding-top: 10px !important;" class="card py-4 side-card"><i
                            class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
                        <div class="mb-3">
                            <div class="reset_link_area-pc mx-3 d-none"><a
                                    class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light"
                                    href="/opportunities/active"><span>保存済み条件で検索</span></a></div>
                            <div class="reset_link_area-sp d-none"><a class="reset_search_condition mx-0"
                                    href="/opportunities/active"><span>保存条件で検索</span></a>
                            </div>
                        </div>
                        <form class="new_opportunity_search_condition" id="opportunity_search_condition_form"
                            novalidate="" @submit.prevent="opp_search" accept-charset="UTF-8" method="get"
                            @input="handleInputChange">
                            <div class="container">
                                <div class="row">
                                    <div class="col-12 px-0 px-md-3"><label
                                            class="font-middle mb-3 ex-bold">フリーワード</label>
                                        <div class="mb-4" style="width: 100%;">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="vertical-top d-inline-block w-100" data-html="true"
                                                        data-toggle="tooltip" title=""
                                                        data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
                                                        <div class="mb-1"><input class="form-control" autocomplete="off"
                                                                id="free_keyword_field" type="text"
                                                                v-model="free_keyword"
                                                                name="opportunity_search_condition[free_keyword]">
                                                        </div>
                                                    </div>
                                                    <div class="mb-3"><span class="font-middle">を含む</span></div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-1"><input class="form-control" autocomplete="off"
                                                            id="negative_keyword_field" type="text"
                                                            v-model="negative_keyword"
                                                            name="opportunity_search_condition[negative_keyword]">
                                                    </div>
                                                    <div class="mb-3"><span class="font-middle">を除く</span></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-5"><label class="font-middle mb-3 ex-bold">案件領域</label>
                                            <div v-dropdown="{ modelValue: opp_categories, listType: 'opp_categories' }"
                                                @selected="select_opp_categories"></div>
                                            <input type="hidden" v-model="opp_categories"
                                                name="opportunity_search_condition[opp_categories]">
                                        </div>
                                        <workplace-selector :required-label="false"
                                            :inputName="'opportunity_search_condition[workplaces][]'"
                                            v-model="specifies_workplaces" @update:modelValue="val => {
                                        console.log('Workplace selector value changed:', val);
                                        specifies_workplaces = val;
                                    }"></workplace-selector>
                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">業種</label>
                                            <div v-dropdown="{ modelValue: '', listType: 'business_field' }"
                                                @selected="select_business_field"></div>
                                            <input type="hidden" v-model="business_field"
                                                name="opportunity_search_condition[business_field]">
                                        </div>
                                        <label class="font-middle mb-3 ex-bold">単価</label>
                                        <PriceRange :min="0" :max="300" :step="5" :startValues="[priceMin, priceMax]"
                                            @update:price_min="updatePriceMin" @update:price_max="updatePriceMax" />
                                        <input type="hidden" :value="priceMin !== null ? priceMin : ''"
                                            name="opportunity_search_condition[unit_price_min]">
                                        <input type="hidden" :value="priceMax !== null ? priceMax : ''"
                                            name="opportunity_search_condition[unit_price_max]">

                                        <label class="font-middle mb-3 ex-bold">契約開始日</label>
                                        <div class="row pl-3">
                                            <div class="col-9">
                                                <div class="mx-auto mb-3"><input class="form-control picker__input"
                                                        autocomplete="off"
                                                        v-pickadate="{ model: 'contract_startdate_at' }"
                                                        v-model="contract_startdate_at" id="contract_startdate_at_field"
                                                        type="text"
                                                        name="opportunity_search_condition[contract_startdate_at]"
                                                        readonly="" aria-haspopup="true" aria-expanded="false"
                                                        aria-readonly="false"
                                                        aria-owns="contract_startdate_at_field_root">

                                                </div>
                                            </div>
                                            <div class="col-1 p-0"><i
                                                    class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                            </div>
                                        </div>

                                        <label class="font-middle mb-3 ex-bold">募集人数</label>
                                        <ParticipantsSlider :min="1" :max="100" :step="1"
                                            :startValue="[participantsMin, participantsMax]"
                                            @update:participants="updateParticipants" />
                                        <input type="hidden" :value="participantsMin !== null ? participantsMin : ''"
                                            name="opportunity_search_condition[participants_min]">
                                        <input type="hidden" :value="participantsMax !== null ? participantsMax : ''"
                                            name="opportunity_search_condition[participants_max]">

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">募集対象</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="custom-control custom-checkbox pl-4 mr-4">
                                                        <input class="custom-control-input"
                                                            id="trading_restriction_field_opportunity_search_condition_0"
                                                            type="checkbox" v-model="trading_restriction"
                                                            name="opportunity_search_condition[trading_restriction][]"
                                                            value="own_employee"><label
                                                            id="trading_restriction_field_label_0"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="trading_restriction_field_opportunity_search_condition_0">自社社員</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox pl-4">
                                                        <input class="custom-control-input"
                                                            id="trading_restriction_field_opportunity_search_condition_1"
                                                            type="checkbox" v-model="trading_restriction"
                                                            name="opportunity_search_condition[trading_restriction][]"
                                                            value="one_subcontract_employee"><label
                                                            id="trading_restriction_field_label_1"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="trading_restriction_field_opportunity_search_condition_1">協力会社社員</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">国籍</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="pl-4 mr-4" id="radio-options">
                                                        <input class="form-check-input" id="nationality0" type="radio"
                                                            value="japanese_only" v-model="nationality"
                                                            name="opportunity_search_condition[nationality]"><label
                                                            class="form-check-label" for="nationality0">日本人のみ</label>
                                                    </div>
                                                    <div class="pl-4" id="radio-options">
                                                        <input class="form-check-input" id="nationality1" type="radio"
                                                            value="any_nationality" v-model="nationality"
                                                            name="opportunity_search_condition[nationality]"><label
                                                            class="form-check-label" for="nationality1">国籍問わず</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3" v-show="nationality === 'any_nationality'"><label
                                                class="font-middle mb-3 ex-bold" for="">在留資格有無</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="pl-4 mr-4" id="radio-options">
                                                        <input class="form-check-input" id="status_resident0"
                                                            type="radio" value="yes" v-model="status_resident"
                                                            name="opportunity_search_condition[status_resident]"><label
                                                            class="form-check-label" for="status_resident0">有</label>
                                                    </div>
                                                    <div class="pl-4" id="radio-options">
                                                        <input class="form-check-input" id="status_resident1"
                                                            type="radio" value="no" v-model="status_resident"
                                                            name="opportunity_search_condition[status_resident]"><label
                                                            class="form-check-label" for="status_resident1">無し</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">稼働率</label>
                                            <RateRangeSlider :min="25" :max="100" :step="25"
                                                :startValues="utilization_rate_range"
                                                @update:rate="updateUtilizationRateRange" />
                                            <input type="hidden" :value="utilization_rate_min || ''"
                                                name="opportunity_search_condition[utilization_rate_min]">
                                            <input type="hidden" :value="utilization_rate_max || ''"
                                                name="opportunity_search_condition[utilization_rate_max]">
                                        </div>

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">出社頻度</label>
                                            <FrequencyRangeSlider :min="0" :max="6" :step="1"
                                                :startValues="work_frequencies_range"
                                                @update:frequency="updateWorkFrequenciesRange" />
                                            <input type="hidden" :value="work_frequencies_min || ''"
                                                name="opportunity_search_condition[work_frequencies_min]">
                                            <input type="hidden" :value="work_frequencies_max || ''"
                                                name="opportunity_search_condition[work_frequencies_max]">
                                        </div>

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">案件確度</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="custom-control custom-checkbox pl-4 mr-4">
                                                        <input class="custom-control-input"
                                                            id="order_accuracy_id_field_opportunity_search_condition_0"
                                                            type="checkbox" v-model="order_accuracy_id"
                                                            name="opportunity_search_condition[order_accuracy_id][]"
                                                            value="afte"><label id="order_accuracy_id_field_label_0"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="order_accuracy_id_field_opportunity_search_condition_0">確定済み</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox pl-4">
                                                        <input class="custom-control-input"
                                                            id="order_accuracy_id_field_opportunity_search_condition_1"
                                                            type="checkbox" v-model="order_accuracy_id"
                                                            name="opportunity_search_condition[order_accuracy_id][]"
                                                            value="befo"><label id="order_accuracy_id_field_label_1"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="order_accuracy_id_field_opportunity_search_condition_1">確定前</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">案件の商流</label>
                                            <div v-dropdown="{ modelValue: '', listType: 'opp_type_id' }"
                                                @selected="select_opp_type_id"></div>
                                            <input type="hidden" v-model="opp_type_id"
                                                name="opportunity_search_condition[opp_type_id]">
                                        </div>
                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">商流への関与</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div
                                                    class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                    <input class="custom-control-input"
                                                        id="involvements_field_opportunity_search_condition_0"
                                                        type="checkbox"
                                                        :checked="involvements === 'enter_sales_channels'"
                                                        @change="e => involvements = e.target.checked ? 'enter_sales_channels' : ''"
                                                        name="opportunity_search_condition[involvements]"
                                                        value="enter_sales_channels"><label
                                                        id="involvements_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="involvements_field_opportunity_search_condition_0">掲載企業が商流に入る案件のみ表示する</label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                for="">契約形態</label>
                                            <div v-dropdown="{ modelValue: '', listType: 'contract_types' }"
                                                @selected="select_contract_types"></div>
                                            <input type="hidden" v-model="contract_types"
                                                name="opportunity_search_condition[contract_types]">
                                        </div>

                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 ex-bold">面談回数</label>
                                            <div v-dropdown="{modelValue: '', listType: 'interview_count_id'}"
                                                @selected="select_interview"><input type="hidden"
                                                    v-model="interview_count_id"
                                                    name="opportunity_search_condition[interview_count_id]"></div>
                                        </div>

                                        <div class="text-center d-none d-md-block search-area py-2">
                                            <button name="button" id="resume-search-button"
                                                class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light"
                                                data-disable-with="検索中" @click="opp_search">
                                                <div id="btn-text" v-if="isFinding" class="">検索中</div>
                                                <div class="py-2" id="loader" v-else-if="isLoading">
                                                    <div class="loader"></div>
                                                </div>
                                                <div id="btn-text" v-else class=""><span class="font-extralarge"
                                                        id="search-count">{{opp_count}}</span> 件<br>この条件で検索</div>
                                            </button>
                                            <div class="py-2"><a
                                                    href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div style="padding-right:0px;" class="col-12 col-md-7 col-lg-8">
                    <div class="row">

                    </div>
                    <hr class="mt-0 mb-3">

                    <!-- Table container with shadow -->
                    <div class="table-container">
                        <!-- Table header -->
                        <div class="opp-table-header">
                            <div class="header-cell status-header" style="width: 130px;">ステータス</div>
                            <div class="header-cell" style="width: 12%;">案件領域</div>
                            <div class="header-cell" style="width: 12%;">就業場所</div>
                            <div class="header-cell" style="width: 14%;">業種</div>
                            <div class="header-cell" style="width: 18%;">単価（万円）</div>
                            <div class="header-cell" style="width: 12%;">開始日</div>
                            <div class="header-cell" style="width: 10%;">募集人数（人）</div>
                            <div class="header-cell" style="width: 22%;">案件名</div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <!-- Hiển thị loading khi đang tải dữ liệu -->
                                <div v-if="!dataReady" class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">読み込み中...</span>
                                    </div>
                                    <p class="mt-2">データを読み込んでいます...</p>
                                </div>

                                <!-- Chỉ hiển thị khi dữ liệu đã sẵn sàng - Dạng bảng -->
                                <div v-else class="table-rows-container">
                                    <a v-for="opp in opps" :key="opp.id" class="opp-table-row"
                                        @click="add_viewer(opp.id, this.isloggedin)"
                                        :href="'/opportunities/' + opp.id + '/detail?prev_next_display=display'">
                                        <!-- Status và bookmark -->
                                        <div class="status-cell">
                                            <div v-if="viewedStatuses[opp.id] === true" class="viewed-flag"><span
                                                    class="font-small">既読</span></div>
                                            <div v-else-if="check_new(opp.created_at)" class="new-flag"><span
                                                    class="font-small">新着</span></div>
                                            <div v-else class="unread-flag"><span class="font-small">未読</span></div>

                                            <div class="bookmark-area" data-toggle="tooltip" title="ブックマーク">
                                                <span @click.stop.prevent="toggleBookmark(opp.id)">
                                                    <i class="material-icons" style="color: #1072e9;">
                                                        {{ opp.is_react ? 'bookmark' : 'bookmark_border' }}
                                                    </i>
                                                </span>
                                                <span class="pl-1 font-small" style="color: black;">{{ opp.react_number
                                                    }}</span>
                                            </div>
                                        </div>

                                        <!-- 案件領域 -->
                                        <div class="cell" style="width: 12%;">
                                            <span>
                                                {{ getCategoryName(opp) }}
                                            </span>
                                        </div>

                                        <!-- 就業場所 -->
                                        <div class="cell" style="width: 12%;">
                                            <span>{{ opp.specifies_workplaces }}</span>
                                        </div>

                                        <!-- 業種 -->
                                        <div class="cell" style="width: 14%;">
                                            <span>{{ categoryMap[opp.business_field] || '-' }}</span>
                                        </div>

                                        <!-- 単価 -->
                                        <div class="cell" style="width: 18%;">
                                            <span v-if="!opp.skill_matching_flg">{{opp.unit_price_min}} 〜 {{opp.unit_price_max}}</span>
                                            <span v-else>スキル見合い</span>
                                        </div>

                                        <!-- 開始日 -->
                                        <div class="cell date-cell" style="width: 12%;">
                                            <span>{{ formatDate(opp.contract_startdate_at) }}</span>
                                        </div>

                                        <!-- 募集人数 -->
                                        <div class="cell" style="width: 10%;">
                                            <span>{{ opp.participants }}</span>
                                        </div>

                                        <!-- 案件名 -->
                                        <div class="cell cell-subject" style="width: 22%;">
                                            <span class="default-main-color">{{ opp.subject }}</span>
                                        </div>
                                    </a>
                                </div>

                                <!-- Modal for each opportunity -->
                                <div v-for="opp in opps" :key="'modal-'+opp.id"
                                    aria-labelledby="display_text_format_modal" class="modal"
                                    :id="'display_text_format_' + opp.id" tabindex="-1" aria-modal="true" role="dialog">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title w-100">案件詳細</h4>
                                                <button aria-label="Close" class="close" data-dismiss="modal"
                                                    type="button"><span aria-hidden="true"><i
                                                            class="material-icons md-dark mb-36">clear</i></span></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-4">
                                                    **********************************************************<br>
                                                    ◆案件ID: {{opp.id}}<br>
                                                    ◆案件名: {{opp.subject}}<br>
                                                    ◆案件への関わり: {{categoryMap[opp.involvements]}}<br>
                                                    ◆案件の商流: {{categoryMap[opp.opp_type_id] || opp.opp_type_id}}<br>
                                                    ◆案件内容: <br>{{opp.requirements}}<br>
                                                    ◆人財要件: <br>{{opp.skill_requirements}}<br>
                                                    ◆単価: {{opp.unit_price_min}} 〜 {{opp.unit_price_max}} / 月<br>
                                                    ◆稼働率: {{ categoryMap[opp?.utilization_rate] ||
                                                    (opp?.utilization_rate || '').split(',').map(rate =>
                                                    categoryMap[rate] || '').join('／') }}<br>
                                                    ◆出社頻度: {{ categoryMap[opp?.work_frequencies] ||
                                                    (opp?.work_frequencies || '').split(',').map(fr => categoryMap[fr]
                                                    || '').join('／') || '' }}<br>
                                                    ◆就業場所: {{opp.specifies_workplaces}}<br>
                                                    ◆契約形態: {{ (opp?.contract_types || '').split(',').map(type =>
                                                    categoryMap[type] || '').join('／') || '' }}<br>
                                                    ◆募集人数: {{opp.participants}}<br>
                                                    ◆面談回数: {{ (opp?.interview_count_id || '').split(',').map(it =>
                                                    categoryMap[it] || '').join('／') || '' }}<br>
                                                    ◆契約期間: {{convertDateToJapanese(opp.contract_startdate_at)}} 〜
                                                    {{convertDateToJapanese(opp.contract_enddate_at)}}<br>
                                                    ◆募集対象: {{ (opp?.trading_restriction || '').split(',').map(tr =>
                                                    categoryMap[tr] || '').join('／') || '' }}<br>
                                                    **********************************************************
                                                </div>
                                                <div class="text-center"><a aria-label="Close"
                                                        class="btn btn-blue-grey waves-effect waves-light"
                                                        data-dismiss="modal">閉じる</a></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Pagination :currentPage="currentPage" :totalPages="totalPages" @page-change="updateURLAndFetch" v-if="opps.length"/>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Mobile search toggle button -->
<button class="mobile-search-toggle d-md-none" @click="toggleMobileSearch" v-if="!isMobileSearchOpen">
    <i class="material-icons">search</i>
</button>

<!-- Mobile search overlay -->
<div class="mobile-search-overlay" :class="{ 'active': isMobileSearchOpen }" @click="closeMobileSearch"></div>

<link rel="stylesheet" href="/custom_frontend/static/css/opportunities/active.css" />
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css" />
    `,
    data() {
        return {
            isOpen: false,
            isOpen1: false,
            isOpen2: false,
            isOpen3: false,
            isOpen4: false,
            isOpen5: false,
            isLoading: false,
            isFinding: false,
            isSearching: false, // Trạng thái đang tìm kiếm
            showLoadingOverlay: false, // Hiển thị overlay loading
            isMobileSearchOpen: false, // Mobile search form state
            priceMin: null,
            priceMax: null,
            participantsMin: 1,
            participantsMax: 100,
            opps: [],
            viewedStatuses: {},
            isloggedin: userInfo ? userInfo.user_id : '',
            currentPage: 1,
            totalPages: 1,
            totalRecord: 0,
            opp_count: 0,
            sortType: '',
            free_keyword: '',
            negative_keyword: '',
            nationality: '',
            status_resident: '',
            business_field: '',
            opp_categories: '',
            opp_type_id: '',
            order_accuracy_id: [],
            contract_types: '',
            utilization_rate: null,
            utilization_rate_range: [25, 100], // Range for search
            utilization_rate_min: null,
            utilization_rate_max: null,
            work_frequencies: null,
            work_frequencies_range: [0, 6], // Range for search
            work_frequencies_min: null,
            work_frequencies_max: null,
            specifies_workplaces: [],
            involvements: null,
            trading_restriction: [],
            contract_startdate_at: '',
            participants: null,
            interview_count_id: '',
            selectedOption: "新着（降順）", // Mặc định
            options: ["新着（降順）", "更新（降順）", "単価（降順）"],
            sortMap: {
                "新着（降順）": "created_at",
                "更新（降順）": "updated_at",
                "単価（降順）": "unit_price_max"
            },
            categories: [
                { value: "design_pmo", label: "PMO" },
                { value: "design_pmpl", label: "PM・PL" },
                { label: "DX", value: "design_DX" },
                { label: "クラウド", value: "design_cloud" },
                { label: "モダナイゼション", value: "design_strategy" },
                { label: "セキュリティ", value: "design_work" },
                { label: "ITインフラ", value: "design_it" },
                { label: "AI", value: "design_ai" },
            ],
            categoriesDev: [
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_embedded", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
            ],
            categoriesInfra: [
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
            ],
            categories3: [
                { value: "operation_pmo", label: "業務システム" },
                { value: "operation_pmpl", label: "オープン" },
                { label: "クラウド", value: "operation_DX" },
                { label: "メインフレーム", value: "operation_mainframe" },
                { label: "ヘルプデスク", value: "operation_strategy" },
            ],
            dataReady: false,
        }
    },
    methods: {
        tabChange(tabNumber) {
            this.activeTab = tabNumber;

            if (tabNumber === 1) {
                window.location.href ='/opportunities/active';
            } else if (tabNumber === 2) {
                window.location.href ='/resumes/active';
            }
        },
        toggleAccordion() {
            this.isOpen = !this.isOpen;
        },
        toggleAccordion1() {
            this.isOpen1 = !this.isOpen1;
        },
        toggleAccordion2() {
            this.isOpen2 = !this.isOpen2;
        },
        toggleAccordion3() {
            this.isOpen3 = !this.isOpen3;
        },
        toggleAccordion4() {
            this.isOpen4 = !this.isOpen4;
        },
        async selectOption(option) {
            this.selectedOption = option; // Cập nhật nội dung hiển thị
            this.isOpen5 = false; // Đóng dropdown
            const sort_type = this.sortMap[option]; // Lấy giá trị sort_type từ map
            await this.updateURLAndFetch(1, sort_type); // Cập nhật URL & tải dữ liệu mới
        },
        async updateURLAndFetch(page, sort_type) {
            // Hiển thị hiệu ứng loading
            this.showLoadingOverlay = true;

            try {
                // Lấy tất cả các tham số hiện tại từ URL
                const currentUrl = new URL(window.location.href);
                const params = new URLSearchParams(currentUrl.search);

                // Cập nhật tham số page và sort_type
                params.set('page', page);
                params.set('sort_type', sort_type);
                this.sortType = sort_type;

                // Cập nhật URL mà không reload trang
                window.history.pushState({}, '', `?${params.toString()}`);

                // Gọi API để lấy dữ liệu mới
                if (params.has('opportunity_search_condition[free_keyword]') ||
                    Array.from(params.keys()).some(key => key.startsWith('opportunity_search_condition'))) {
                    // Nếu có tham số tìm kiếm, sử dụng API tìm kiếm
                    await this.fetchSearchResults(params, page, sort_type);
                } else {
                    // Nếu không có tham số tìm kiếm, sử dụng API getOpp
                    await this.getOpp(page, sort_type);
                }
            } catch (error) {
                console.error('Error updating sort:', error.message);
            } finally {
                this.showLoadingOverlay = false;
            }
        },
        closeDropdown(e) {
            if (!this.$refs.dropdown.contains(e.target)) this.isOpen5 = false;
        },
        toggleMobileSearch() {
            this.isMobileSearchOpen = !this.isMobileSearchOpen;
            const sideSearch = document.getElementById('side-search');
            if (sideSearch) {
                if (this.isMobileSearchOpen) {
                    sideSearch.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Prevent body scroll
                } else {
                    sideSearch.classList.remove('active');
                    document.body.style.overflow = ''; // Restore body scroll
                }
            }
        },
        closeMobileSearch() {
            this.isMobileSearchOpen = false;
            const sideSearch = document.getElementById('side-search');
            if (sideSearch) {
                sideSearch.classList.remove('active');
                document.body.style.overflow = ''; // Restore body scroll
            }
        },
        async getOpp(page = 1, sort_type = 'created_at') {
            try {
                const response = await fetch(`/api/getOpp?page=${page}&sort_type=${sort_type}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': odoo.csrf_token,
                        'X-User-ID': this.userId || ''

                    },
                });

                const data = await response.json();

                if (data.success === true) {
                    this.opps = data.data;
                    this.currentPage = data.current_page;
                    this.totalPages = data.total_pages;
                    this.totalRecord = data.total_records;
                } else {
                    console.warn("API data is not in the correct format:", data);
                }
            } catch (error) {
                console.log('Error API:', error.message);
                this.errorMessage = error.message;
            }
        },

        // Hàm gọi API tìm kiếm và cập nhật dữ liệu
        async fetchSearchResults(params, page = 1, sort_type = 'created_at') {
            try {
                // Hiển thị hiệu ứng loading
                this.showLoadingOverlay = true;
                this.isSearching = true;

                // Nếu params là URLSearchParams, chuyển thành string
                const paramsString = params instanceof URLSearchParams ? params.toString() : params;

                // Gọi API lấy dữ liệu tìm kiếm

                const user_id = userInfo ? userInfo.user_id : '';
                const response = await fetch(`/api/opportunity_search_condition/search?${paramsString}&page=${page}&sort_type=${sort_type}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-User-ID': user_id,
                     },
                });

                const data = await response.json();
                if (data.success === true) {
                    // Cập nhật dữ liệu
                    this.opps = data.data;
                    this.currentPage = data.current_page;
                    this.totalPages = data.total_pages;
                    this.totalRecord = data.total_records;

                    // Kiểm tra trạng thái xem cho tất cả dữ liệu
                    if (this.opps.length > 0) {
                        await this.checkAllViewStatuses();
                    }

                    return true;
                } else {
                    console.warn("API data is not in the correct format:", data);
                    return false;
                }
            } catch (error) {
                console.error('Error fetching search results:', error.message);
                this.errorMessage = `Error fetching opportunities: ${error.message}`;
                return false;
            } finally {
                // Ẩn hiệu ứng loading sau 500ms để người dùng thấy sự thay đổi
                setTimeout(() => {
                    this.showLoadingOverlay = false;
                    this.isSearching = false;
                }, 500);
            }
        },
        async count_opp() {
            this.isLoading = true;
            try {
                let params = new URLSearchParams();
                params.append("opportunity_search_condition[free_keyword]", this.free_keyword || '');
                params.append("opportunity_search_condition[negative_keyword]", this.negative_keyword || '');
                params.append("opportunity_search_condition[interview_count_id]", this.interview_count_id === "未選択" ? '' : this.interview_count_id);
                params.append("opportunity_search_condition[participants_min]", this.participantsMin || '');
                params.append("opportunity_search_condition[participants_max]", this.participantsMax || '');
                params.append("opportunity_search_condition[contract_startdate_at]", this.contract_startdate_at || '');
                params.append("opportunity_search_condition[unit_price_min]", this.priceMin || '');
                params.append("opportunity_search_condition[unit_price_max]", this.priceMax || '');
                params.append("opportunity_search_condition[involvements]", this.involvements || '');
                params.append("opportunity_search_condition[nationality]", this.nationality || '');
                params.append("opportunity_search_condition[business_field]", this.business_field || '');

                // Chỉ gửi status_resident khi nationality là any_nationality
                if (this.nationality === 'any_nationality' && this.status_resident) {
                    params.append("opportunity_search_condition[status_resident]", this.status_resident);
                }


                // Xử lý mảng status[]
                if (this.trading_restriction.length > 0) {
                    this.trading_restriction.forEach(value => {
                        params.append("opportunity_search_condition[trading_restriction][]", value);
                    });
                }

                // Xử lý mảng exp_categories[]
                if (this.opp_categories) {
                    params.append("opportunity_search_condition[opp_categories]", this.opp_categories);
                }

                if (this.order_accuracy_id.length > 0) {
                    this.order_accuracy_id.forEach(value => {
                        params.append("opportunity_search_condition[order_accuracy_id][]", value);
                    });
                }

                if (this.opp_type_id) {
                    params.append("opportunity_search_condition[opp_type_id]", this.opp_type_id);
                }

                if (this.contract_types) {
                    params.append("opportunity_search_condition[contract_types]", this.contract_types);
                }

                // Send range parameters for utilization rate (only if not full range)
                if (this.utilization_rate_min !== null && this.utilization_rate_min !== undefined &&
                    this.utilization_rate_max !== null && this.utilization_rate_max !== undefined) {
                    // Check if it's not the full range (25% to 100%)
                    if (!(this.utilization_rate_min === 25 && this.utilization_rate_max === 100)) {
                        params.append("opportunity_search_condition[utilization_rate_min]", this.utilization_rate_min);
                        params.append("opportunity_search_condition[utilization_rate_max]", this.utilization_rate_max);
                    }
                }

                // Send range parameters for work frequencies (only if not full range)
                if (this.work_frequencies_min !== null && this.work_frequencies_min !== undefined &&
                    this.work_frequencies_max !== null && this.work_frequencies_max !== undefined) {
                    // Check if it's not the full range (0 to 6, which is フルリモート to 5日)
                    if (!(this.work_frequencies_min === 0 && this.work_frequencies_max === 6)) {
                        params.append("opportunity_search_condition[work_frequencies_min]", this.work_frequencies_min);
                        params.append("opportunity_search_condition[work_frequencies_max]", this.work_frequencies_max);
                    }
                }

                if (this.specifies_workplaces.length > 0) {
                    this.specifies_workplaces.forEach(value => {
                        params.append("opportunity_search_condition[workplaces][]", value);
                    });
                }
                // Gọi API lấy dữ liệu
                const response = await fetch(`/api/opportunity_count?${params.toString()}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                });
                const data = await response.json();
                if (data.success === true) {
                    this.opp_count = data.total_records;
                } else {
                    console.warn("API data is not in the correct format:", data);
                }
            } catch (error) {
                console.error('Error fetching API:', error.message);
                this.errorMessage = `Error fetching opportunities: ${error.message}`;
            } finally {
                this.isLoading = false; // Dừng loading khi API trả về kết quả
            }
        },
        // Hàm gọi khi có sự thay đổi của form
        handleInputChange() {
            // Nếu đã có một timeout trước đó, xóa nó đi để tránh gọi API quá nhiều
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            // Thiết lập timeout mới để trì hoãn việc gọi API
            this.searchTimeout = setTimeout(() => {
                this.count_opp(); // Gọi API sau khi người dùng ngừng thay đổi trong 500ms
            }, 500); // 500ms là khoảng thời gian trì hoãn
        },
        async opp_keyword(keyword) {
            // Hiển thị hiệu ứng loading
            this.showLoadingOverlay = true;

            let params = new URLSearchParams();
            params.append("opportunity_search_condition[free_keyword]", keyword);

            try {
                // Cập nhật URL mà không reload trang
                const newUrl = new URL(window.location.href);
                // Xóa tất cả các tham số hiện tại
                Array.from(newUrl.searchParams.keys()).forEach(key => {
                    newUrl.searchParams.delete(key);
                });

                // Thêm tham số từ keyword
                newUrl.searchParams.append("opportunity_search_condition[free_keyword]", keyword);

                // Cập nhật URL mà không reload trang
                window.history.pushState({}, '', newUrl);

                // Cập nhật biến free_keyword
                this.free_keyword = keyword;

                // Gọi API để lấy kết quả tìm kiếm
                await this.fetchSearchResults(params);
            } catch (error) {
                console.error('Error during keyword search:', error);
            } finally {
                this.showLoadingOverlay = false;
            }
        },
        async opp_search() {
            this.isFinding = true;
            let params = new URLSearchParams();

            // Thêm free_keyword vào params nếu có giá trị
            if (this.free_keyword) {
                params.append("opportunity_search_condition[free_keyword]", this.free_keyword);
            }

            params.append("opportunity_search_condition[negative_keyword]", this.negative_keyword || '');
            params.append("opportunity_search_condition[interview_count_id]", this.interview_count_id || '');
            params.append("opportunity_search_condition[participants_min]", this.participantsMin || '');
            params.append("opportunity_search_condition[participants_max]", this.participantsMax || '');
            params.append("opportunity_search_condition[contract_startdate_at]", this.contract_startdate_at || '');
            params.append("opportunity_search_condition[unit_price_min]", this.priceMin || '');
            params.append("opportunity_search_condition[unit_price_max]", this.priceMax || '');
            params.append("opportunity_search_condition[involvements]", this.involvements || '');
            params.append("opportunity_search_condition[nationality]", this.nationality || '');
            params.append("opportunity_search_condition[business_field]", this.business_field || '');

            // Chỉ gửi status_resident khi nationality là any_nationality
            if (this.nationality === 'any_nationality' && this.status_resident) {
                params.append("opportunity_search_condition[status_resident]", this.status_resident);
            }

            // Xử lý mảng status[]
            if (this.trading_restriction.length > 0) {
                this.trading_restriction.forEach(value => {
                    params.append("opportunity_search_condition[trading_restriction][]", value);
                });
            }

            // Xử lý opp_categories
            if (this.opp_categories) {
                params.append("opportunity_search_condition[opp_categories]", this.opp_categories);
            }

            if (this.order_accuracy_id.length > 0) {
                this.order_accuracy_id.forEach(value => {
                    params.append("opportunity_search_condition[order_accuracy_id][]", value);
                });
            }

            if (this.opp_type_id) {
                params.append("opportunity_search_condition[opp_type_id]", this.opp_type_id);
            }

            if (this.contract_types) {
                params.append("opportunity_search_condition[contract_types]", this.contract_types);
            }

            // Send range parameters for utilization rate (only if not full range)
            if (this.utilization_rate_min !== null && this.utilization_rate_min !== undefined &&
                this.utilization_rate_max !== null && this.utilization_rate_max !== undefined) {
                // Check if it's not the full range (25% to 100%)
                if (!(this.utilization_rate_min === 25 && this.utilization_rate_max === 100)) {
                    params.append("opportunity_search_condition[utilization_rate_min]", this.utilization_rate_min);
                    params.append("opportunity_search_condition[utilization_rate_max]", this.utilization_rate_max);
                }
            }

            // Send range parameters for work frequencies (only if not full range)
            if (this.work_frequencies_min !== null && this.work_frequencies_min !== undefined &&
                this.work_frequencies_max !== null && this.work_frequencies_max !== undefined) {
                // Check if it's not the full range (0 to 6, which is フルリモート to 5日)
                if (!(this.work_frequencies_min === 0 && this.work_frequencies_max === 6)) {
                    params.append("opportunity_search_condition[work_frequencies_min]", this.work_frequencies_min);
                    params.append("opportunity_search_condition[work_frequencies_max]", this.work_frequencies_max);
                }
            }

            if (this.specifies_workplaces.length > 0) {
                this.specifies_workplaces.forEach(value => {
                    params.append("opportunity_search_condition[workplaces][]", value);
                });
            }

            try {
                // Cập nhật URL mà không reload trang
                const newUrl = new URL(window.location.href);
                // Xóa tất cả các tham số hiện tại
                Array.from(newUrl.searchParams.keys()).forEach(key => {
                    newUrl.searchParams.delete(key);
                });

                // Thêm các tham số mới từ form tìm kiếm
                const searchParams = new URLSearchParams(params.toString());
                Array.from(searchParams.entries()).forEach(([key, value]) => {
                    if (value) { // Chỉ thêm vào URL nếu có giá trị
                        newUrl.searchParams.append(key, value);
                    }
                });

                // Cập nhật URL mà không reload trang
                window.history.pushState({}, '', newUrl);

                // Gọi API để lấy kết quả tìm kiếm
                await this.fetchSearchResults(params);

                // Đóng mobile search form sau khi search thành công
                this.closeMobileSearch();
            } catch (error) {
                console.error('Error during search:', error);
            } finally {
                this.isFinding = false;
            }
        },

        select_interview(event) {
            this.interview_count_id = event.detail; // Nhận giá trị từ dropdown
            this.handleInputChange();
        },
        select_business_field(event) {
            this.business_field = event.detail; // Nhận giá trị từ dropdown
            this.handleInputChange();
        },
        select_opp_type_id(event) {
            this.opp_type_id = event.detail; // Nhận giá trị từ dropdown
            this.handleInputChange();
        },
        select_contract_types(event) {
            this.contract_types = event.detail; // Nhận giá trị từ dropdown
            this.handleInputChange();
        },
        select_opp_categories(event) {
            this.opp_categories = event.detail;
            this.handleInputChange();
        },
        updatePriceMin(value) {
            if (this.priceMin !== value) {
                this.priceMin = value;
                this.handleInputChange();
            }
        },
        updatePriceMax(value) {
            if (this.priceMax !== value) {
                this.priceMax = value;
                this.handleInputChange();
            }
        },
        updateUtilizationRate(values) {
            this.utilization_rate = values;
            this.handleInputChange();
        },
        updateUtilizationRateRange(values) {
            // values is an array [minValue, maxValue]
            if (Array.isArray(values) && values.length >= 2) {
                this.utilization_rate_min = values[0];
                this.utilization_rate_max = values[1];
                this.handleInputChange();
            }
        },
        updateWorkFrequencies(values) {
            this.work_frequencies = values;
            this.handleInputChange();
        },
        updateWorkFrequenciesRange(values) {
            // values is an array [minKey, maxKey]
            if (Array.isArray(values) && values.length >= 2) {
                this.work_frequencies_min = values[0];
                this.work_frequencies_max = values[1];
                this.handleInputChange();
            }
        },
        updateParticipants(values) {
            this.participantsMin = values[0] !== null ? values[0] : 1;
            this.participantsMax = values[1] !== null ? values[1] : 100;
            this.handleInputChange();
        },
        restoreSelection() {
            const urlParams = new URLSearchParams(window.location.search);
            const sort_type = urlParams.get('sort_type') || 'created_at';
            this.selectedOption = Object.keys(this.sortMap).find(key => this.sortMap[key] === sort_type) || "新着（降順）";
        },
        openModal(oppId) {
            // Mở modal với ID tương ứng
            const modalId = '#display_text_format_' + oppId;

            // Dùng jQuery để mở modal
            $(modalId).modal('show');
        },
        convertDateToJapanese(date) {
            if (!date) return "日付なし"; // Nếu giá trị rỗng/null, trả về "Không có ngày"

            // Nếu date là chuỗi, chuyển thành Date object
            if (typeof date === "string") {
                date = new Date(date);
            }

            // Kiểm tra nếu date không hợp lệ
            if (!(date instanceof Date) || isNaN(date)) {
                return "無効な日付"; // Trả về "Ngày không hợp lệ"
            }

            // Chuyển đổi sang format Nhật Bản
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        },
        async add_viewer(opp_id, user_id) {
            try {
                // Tạo URL với user_id nếu có
                let url = `/api/view_opp?opp_id=${encodeURIComponent(opp_id)}`;
                if (user_id) {
                    url += `&user_id=${encodeURIComponent(user_id)}`;
                }

                // Gửi yêu cầu GET đến API
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                });

                // Kiểm tra nếu có lỗi từ phía server
                if (!response.ok) {
                    throw new Error('Error: ' + response.statusText);
                }

                // Xử lý kết quả
                const data = await response.json();
                //console.log('Response:', data);

            } catch (error) {
                console.error('Fetch error:', error);
                this.errorMessage = error.message;
            }
        },
        async isViewed(oppId) {
            if (this.viewedStatuses[oppId] === undefined) {
                await this.fetchViewedStatus(oppId); // Chỉ gọi API nếu chưa có trong viewedStatuses
            }
            return this.viewedStatuses[oppId] || false; // Trả về trạng thái đã xem nếu có, false nếu chưa xem
        },

        // Lấy trạng thái đã xem của opp từ API
        async fetchViewedStatus(oppId) {
            try {
                const response = await fetch(`/api/check_view_opp?user_id=${this.isloggedin}&opp_id=${oppId}`);
                const data = await response.json();
                this.viewedStatuses[oppId] = data.success ? data.viewed : false; // Lưu kết quả vào viewedStatuses
            } catch (error) {
                console.error('Error fetching viewed status:', error);
                this.viewedStatuses[oppId] = false; // Mặc định là chưa xem nếu có lỗi
            }
        },

        // Kiểm tra tất cả các opp đã được xem hay chưa (chỉ gọi API 1 lần cho mỗi opp)
        async checkAllViewStatuses() {
            const oppIds = this.opps.map(opp => opp.id);
            await Promise.all(oppIds.map(id => this.isViewed(id))); // Kiểm tra tất cả oppId trong danh sách resumes
        },
        check_new(date) {
            var today = new Date();
            var createdDate = new Date(date);

            // Tính số ngày từ ngày tạo đến hiện tại
            var diffTime = today - createdDate;
            var diffDays = diffTime / (1000 * 60 * 60 * 24);

            // Nếu số ngày <= 7, coi là mới
            return diffDays <= 7;
        },

        // Phương thức để lấy tên danh mục của opportunity
        getCategoryName(opp) {
            const categoryResults = [];

            // Kiểm tra các danh mục và thêm vào kết quả
            if (opp.categories_design) {
                const designCategories = opp.categories_design.split(',');
                const designNames = designCategories.map(cat => {
                    const category = this.categories.find(c => c.value === cat);
                    return category ? `設計 - ${category.label}` : '';
                }).filter(Boolean);

                categoryResults.push(...designNames);
            }

            if (opp.categories_development) {
                const devCategories = opp.categories_development.split(',');
                const devNames = devCategories.map(cat => {
                    const category = this.categoriesDev.find(c => c.value === cat);
                    return category ? `開発 - ${category.label}` : '';
                }).filter(Boolean);

                categoryResults.push(...devNames);
            }

            if (opp.categories_infrastructure) {
                const infraCategories = opp.categories_infrastructure.split(',');
                const infraNames = infraCategories.map(cat => {
                    const category = this.categoriesInfra.find(c => c.value === cat);
                    return category ? `インフラ - ${category.label}` : '';
                }).filter(Boolean);

                categoryResults.push(...infraNames);
            }

            if (opp.categories_operation_maintenance) {
                const opCategories = opp.categories_operation_maintenance.split(',');
                const opNames = opCategories.map(cat => {
                    const category = this.categories3.find(c => c.value === cat);
                    return category ? `運用・保守 - ${category.label}` : '';
                }).filter(Boolean);

                categoryResults.push(...opNames);
            }

            return categoryResults.length > 0 ? categoryResults.join('／') : '-';
        },

        async toggleBookmark(id) {
            try {
                const response = await fetch('/api/react_opp', {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        user_id: this.userId,
                        opportunities_id: id,
                    }),
                });

                const result = await response.json();
                if (result.result.success) {
                    // Kiểm tra nếu có params thì gọi fetchSearch, không thì gọi getOpp
                    // if (this.hasSearchParams()) {
                    //     await this.fetchSearchResults(this.page, this.sortType);
                    // } else {
                        await this.getOpp(this.page, this.sortType);
                    // }
                }
            } catch (error) {
                console.error("Bookmark failed:", error);
            }
        },

        // format date
        formatDate(dateStr) {
            if (!dateStr) return 'N/A';
            const date = new Date(dateStr);
            const yyyy = date.getFullYear();
            const mm = String(date.getMonth() + 1).padStart(2, '0');
            const dd = String(date.getDate()).padStart(2, '0');
            return `${yyyy}/${mm}/${dd}`;
        },
        // Hàm tính toán chỉ số item bắt đầu
        getStartItemIndex() {
            if (this.totalRecord === 0) return 0;
            return (this.currentPage - 1) * 20 + 1; // Mỗi trang hiển thị 24 item
        },
        // Hàm tính toán chỉ số item kết thúc
        getEndItemIndex() {
            if (this.totalRecord === 0) return 0;
            return Math.min(this.currentPage * 20, this.totalRecord); // Không vượt quá tổng số item
        },

        // Khôi phục các giá trị form từ URL
        async restoreFormValues() {
            const params = new URLSearchParams(window.location.search);

            // Khôi phục các giá trị text input
            this.free_keyword = params.get('opportunity_search_condition[free_keyword]') || '';
            this.negative_keyword = params.get('opportunity_search_condition[negative_keyword]') || '';
            this.participantsMin = params.get('opportunity_search_condition[participants_min]') || 1;
            this.participantsMax = params.get('opportunity_search_condition[participants_max]') || 100;
            this.contract_startdate_at = params.get('opportunity_search_condition[contract_startdate_at]') || '';
            this.priceMin = params.get('opportunity_search_condition[unit_price_min]') || null;
            this.priceMax = params.get('opportunity_search_condition[unit_price_max]') || null;
            this.interview_count_id = params.get('opportunity_search_condition[interview_count_id]') || '';
            this.business_field = params.get('opportunity_search_condition[business_field]') || '';
            this.opp_type_id = params.get('opportunity_search_condition[opp_type_id]') || '';
            this.contract_types = params.get('opportunity_search_condition[contract_types]') || '';
            this.opp_categories = params.get('opportunity_search_condition[opp_categories]') || '';

            // Khôi phục các giá trị checkbox
            this.involvements = params.get('opportunity_search_condition[involvements]') || null;
            this.nationality = params.get('opportunity_search_condition[nationality]') || '';
            this.status_resident = params.get('opportunity_search_condition[status_resident]') || '';

            // Khôi phục các giá trị mảng
            this.trading_restriction = params.getAll('opportunity_search_condition[trading_restriction][]');
            this.order_accuracy_id = params.getAll('opportunity_search_condition[order_accuracy_id][]');
            // Handle single values for sliders
            const utilizationRateParams = params.getAll('opportunity_search_condition[utilization_rate][]');
            this.utilization_rate = utilizationRateParams.length > 0 ? utilizationRateParams[0] : null;

            const workFrequenciesParams = params.getAll('opportunity_search_condition[work_frequencies][]');
            this.work_frequencies = workFrequenciesParams.length > 0 ? workFrequenciesParams[0] : null;
            this.specifies_workplaces = params.getAll('opportunity_search_condition[workplaces][]');
        },
    },
    async mounted() {
        document.querySelectorAll('label').forEach(function(label) {
            if (label.textContent.trim() === '就業場所') {
                label.style.height = '75%';
            }
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });
        document.addEventListener("click", this.closeDropdown);

        // Handle window resize to close mobile search on desktop
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileSearch();
            }
        });

        // Add event listener for close button
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('search-close-btn')) {
                this.closeMobileSearch();
            }
        });

        // Đặt ở đầu script, trước khi bất kỳ code nào khác chạy
		(function() {
			// Kiểm tra ngay khi script được thực thi, trước khi DOM load
			const previousUrl = sessionStorage.getItem('previous_url');
			const isRefresh = performance.navigation.type === 1;

			// Nếu đây là refresh và có params
			if (isRefresh && window.location.search) {
				// Redirect ngay lập tức trước khi trang render
				window.location.replace('/resumes/active');
				// Ngăn phần còn lại của script chạy
				throw new Error('Redirecting to clean URL');
			}

			// Xóa previous_url để không gây nhầm lẫn trong các lần load sau
			sessionStorage.removeItem('previous_url');

			// Vẫn giữ event beforeunload để set previous_url cho lần refresh tiếp theo
			window.addEventListener('beforeunload', () => {
				sessionStorage.setItem('previous_url', window.location.href);
			});
		})();

        const params = new URLSearchParams(window.location.search);
        this.userId = userInfo ? userInfo.user_id : null;
        this.sortType = params.get('sort_type') || 'created_at';
        const page = parseInt(params.get('page')) || 1;

        try {
            // Kiểm tra xem URL có chứa tham số tìm kiếm không
            // const hasSearchParams = Array.from(params.keys()).some(key =>
            //     key.startsWith('opportunity_search_condition'));

            // if (hasSearchParams) {
            //     // Hiển thị hiệu ứng loading
            //     this.showLoadingOverlay = true;

            //     // Nếu có tham số tìm kiếm, sử dụng API tìm kiếm
            //     await this.fetchSearchResults(params, page, this.sortType);

            //     // Khôi phục các giá trị form từ URL
            //     await this.restoreFormValues();
            // } else {
                // Nếu không có tham số tìm kiếm, sử dụng API getOpp
                await this.getOpp(page, this.sortType);

                // Đợi kiểm tra trạng thái xem cho tất cả dữ liệu
                if (this.opps.length > 0) {
                    await this.checkAllViewStatuses();
                }
            // }

            // Đếm số lượng cơ hội
            await this.count_opp();

            // Khôi phục lựa chọn
            this.restoreSelection();

            // Đánh dấu dữ liệu đã sẵn sàng
            this.dataReady = true;
        } catch (error) {
            console.error('Error loading data:', error);
            // Vẫn đánh dấu là sẵn sàng để người dùng thấy nội dung, dù có lỗi
            this.dataReady = true;
        } finally {
            // Ẩn hiệu ứng loading
            this.showLoadingOverlay = false;
        }
    },
    computed: {
        displayedRecords() {
            return this.totalRecord < 24 ? this.totalRecord : 24;
        },
        categoryMap() {
            return {
                // categories
                "design_pmo": "PMO",
                "design_pmpl": "PM・PL",
                "design_DX": "DX",
                "design_cloud": "クラウド",
                "design_strategy": "モダナイゼション",
                "design_work": "セキュリティ",
                "design_it": "ITインフラ",
                "design_rpa": "AI",

                // categoriesDev
                "dev_pmo": "PMO",
                "dev_pmpl": "PM・PL",
                "dev_web": "Webシステム",
                "dev_ios": "IOS",
                "dev_android": "Android",
                "dev_control": "制御",
                "dev_embedded": "組込",
                "dev_ai": "AI・DL・ML",
                "dev_test": "テスト",
                "dev_cloud": "クラウド",
                "dev_architect": "サーバ",
                "dev_bridge_se": "データベース",
                "dev_network": "ネットワーク",
                "dev_mainframe": "メインフレーム",

                // categoriesInfra
                "infra_pmo": "PMO",
                "infra_pmpl": "PM・PL",
                "infra_server": "サーバー",
                "infra_network": "ネットワーク",
                "infra_db": "データベース",
                "infra_cloud": "クラウド",
                "infra_virtualized": "仮想化",
                "infra_mainframe": "メインフレーム",

                // categories3
                "operation_pmo": "業務システム",
                "operation_pmpl": "オープン",
                "operation_DX": "クラウド",
                "operation_mainframe": "メインフレーム",
                "operation_strategy": "ヘルプデスク",

                // contract type
                "quas": "業務委託（準委任）",
                "subc": "業務委託（請負）",
                "temp": "派遣契約",

                //invol
                "enter_sales_channels": "商流に入る",
                "agent_only": "仲介のみ",

                //opp_qualities
                "700thousand_or_more": "70万円以上",
                "1million_or_more": "100万円以上",
                "less_1year_ok": "1年未満OK",
                "newcomer_ok": "新人でもOK",
                "over_50years_old_ok": "50代以上OK",
                "foreign_nationality_ok": "外国籍OK",
                "leader_recruitment": "リーダー募集",
                "english_skill": "英語力",
                "interview_once": "面談1回",
                "take_home_project": "持ち帰りOK",
                "team_proposal_ok": "チーム提案OK",
                "web_interview_ok": "ウェブ面談可能",
                "remote__location_ok": "案件場所から遠隔地居住でもOK",
                "long_term": "日本以外の居住者OK",
                "overseas_resident_ok": "長期（２年以上継続可能性）",

                //restriction
                "own_employee": "自社社員",
                "one_subcontract_employee": "協力会社社員（一社先）",
                "more_subcontract_employee": "協力会社社員（二社先以降）",
                "freelance_person": "フリーランス（本人）",
                "subcontract_freelance": "フリーランス（一社先）",
                "more_subcontract_freelance": "フリーランス（二社先以降）",

                //order_accuracy_ids
                "afte": "確定済み",
                "befo": "確定前",

                //opp_type_id
                "clnt": "エンド",
                "prim": "元請",
                "subc": "一次請",
                "msubc": "二次請以降",

                //utilization_rate
                '100': '100%（フル稼働）',
                '75': '75%',
                '50': '50%',
                '25': '25%',

                //work_frequencies
                '5days': '週5日出社',
                '4days': '週4日出社',
                '3days': '週3日出社',
                '2days': '週2日出社',
                '2to4days': '週4 〜 2日出社',
                'less_than_1day': '週1日未満出社',
                'full_remote': 'フルリモート',
                '1day': '週1日出社',

                //interview_count_id
                "once": "1回",
                "twice": "2回",
                "other": "その他",

                //business_field
                "it_telecom_internet": "情報通信・インターネット",
                "automobile_machinery": "自動車・機械",
                "electronics": "エレクトロニクス機器",
                "resources_energy_materials": "資源・エネルギー・素材",
                "finance_corporate_services": "金融・法人サービス",
                "food_agriculture": "食品・農業",
                "consumer_goods_pharmaceuticals": "生活用品・嗜好品・薬",
                "entertainment_media": "娯楽・エンタメ・メディア",
                "construction_real_estate": "建設・不動産",
                "logistics_transport": "運輸・物流",
                "retail_dining": "流通・外食",
                "public_services": "生活・公共サービス"
            };
        },
    },
    watch: {
        specifies_workplaces: {
            handler() {
                this.handleInputChange();
            },
            deep: true
        },
        contract_startdate_at: {
            handler() {
                this.handleInputChange();
            },
            deep: true
        }
    }
}

export default Active