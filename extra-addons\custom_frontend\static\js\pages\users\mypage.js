import { checkToastrMessage } from "/custom_frontend/static/js/common/Toastr.js";
import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const My = {
    'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="" style="background-color: white;">
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: '探す', link: null },
        { text: 'マッチング状況', link: null, current: true }
    ], 'container-fluid', '')}

        <div style="padding-top: 0px !important;padding-bottom: 0px !important;" class="container-fluid py-2 py-md-4">
        <div class="mypage-tabs left-aligned">
            <div class="tab-container">
                <div class="tab-button" :class="{ active: activeTab === 'project' }" @click="setActiveTab('project')">案件マッチング状況</div>
                <div class="tab-button" :class="{ active: activeTab === 'talent' }" @click="setActiveTab('talent')">人財マッチング状況</div>
            </div>
        </div>
    </div>
    <div style="margin-top: -5px !important;" class="container-fluid grabient pt-4 px-0 px-md-4">
        <div class="row max-width-1136 max-width-752-for-tb mx-auto">
            <div class="col-12">

                <div class="row">
                    <div class="col-12 px-0 px-md-3">
                        <!-- Project Tab Content -->
                        <section class="mb-5" v-show="activeTab === 'project'">
                            <!-- Flex container for tables -->
                            <div class="tables-flex-container">
                                <!-- Project Workflow Table -->
                                <div class="table-section">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <p class="font-middle bold mb-0">案件ワークフロー</p>
                                        <div class="filter-section">
                                            <span class="filter-label">絞り込み:</span>
                                            <div v-dropdown="{ modelValue: '', listType: 'optionIssueFilter'}"
                                                @selected="changeTalentFilter"></div>
                                        </div>
                                    </div>
                                    <div class="table-wrapper">
                                        <table class="activity-table w-100">
                                            <thead class="stylish-color white-text">
                                                <tr>
                                                    <th class="text-left">案件名</th>
                                                    <th class="text-left">人財名</th>
                                                    <th>状況</th>
                                                    <th>入力期限</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="row in paginatedDataOppFlow" :key="row.id" :class="isExpired(row.expire_date) && ![5, 6].includes(row.status) ? 'disable' : ''">
                                                    <td class="text-left">{{ row.opportunities_name }}</td>
                                                    <td class="text-left">{{ row.resumes_name }}</td>
                                                    <td>
                                                        <template v-if="[0, 1].includes(row.status)">
                                                            <a
                                                                :href=" row.opportunities_id + '/' + row.resumes_id + '/schedules'">
                                                                {{ convertStatus_opp[row.status] }}
                                                            </a>
                                                        </template>
                                                        <template v-else-if="row.status === 2">
                                                            <a
                                                                :href="'/interview_result/new?opp_id=' + row.opportunities_id + '&resume_id=' + row.resumes_id + '&flow_id=' + row.id">
                                                                {{ convertStatus_opp[row.status] }}
                                                            </a>
                                                        </template>
                                                        <template v-else-if="row.status === 3">
                                                            <a :href="'/interview_result/' + row.id + '/view'">
                                                                {{ convertStatus_opp[row.status] }}
                                                            </a>
                                                        </template>
                                                        <template v-else-if="row.status === 4">
                                                                <a :href="'/personal_rating/' + row.id ">
                                                                    {{ convertStatus_opp[row.status] }}
                                                                </a>
                                                        </template>
                                                        <template v-else-if="row.status === 6">
                                                            <a :href="'/interview_result/' + row.id + '/view'">
                                                                {{ convertStatus_opp[row.status] }}
                                                            </a>
                                                        </template>
                                                        <template v-else>
                                                                {{ convertStatus_opp[row.status] }}
                                                        </template>
                                                    </td>
                                                    <td>{{ [3, 5, 6].includes(row.status) ? '-' : (isExpired(row.expire_date) ? '入力期限切れ' : formatDate(row.expire_date)) }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="pagination-controls mt-3">
                                        <button @click="goToFirstPageOppFlow" :disabled="currentPage_oppflow === 1"
                                            class="pagination-btn">最初のページ</button>
                                        <button @click="goToPrevPageOppFlow" :disabled="currentPage_oppflow === 1"
                                            class="pagination-btn">前のページ</button>
                                        <button @click="goToNextPageOppFlow"
                                            :disabled="currentPage_oppflow === totalPagesOppFlow || totalPagesOppFlow === 0"
                                            class="pagination-btn">次のページ</button>
                                    </div>
                                </div>

                                <!-- Project Usage Results Table -->
                                <div class="table-section">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <p class="font-middle bold mb-0">案件利用実績</p>
                                        <div class="text-right only-pc">
                                            <a class="font-middle" href="#" @click="exportCSVOpportunitiesSummary">CSV出力</a>
                                        </div>
                                    </div>
                                    <div class="table-wrapper">
                                        <table class="text-center activity-table w-100">
                                            <thead class="stylish-color white-text">
                                                <tr>
                                                    <th class="text-left">対象月</th>
                                                    <th>案件登録</th>
                                                    <th class="keep-length">閲覧</th>
                                                    <th>応募受領</th>
                                                    <th>スカウト</th>
                                                    <th>面談</th>
                                                    <th>成約</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="row in paginatedDataOpp" :key="row.month">
                                                    <td class="text-left">{{row.month}}</td>
                                                    <td>{{row.total_opportunities}}</td>
                                                    <td>{{row.total_views}}</td>
                                                    <td>{{row.total_workflows}}</td>
                                                    <td>{{row.total_scouts}}</td>
                                                    <td>{{row.total_interviews}}</td>
                                                    <td>{{row.total_passed_interviews}}</td>
                                                </tr>
                                                <tr class="bg-grey-3">
                                                    <td class="text-left">累計（通算）</td>
                                                    <td>{{totalOpp_opp}}</td>
                                                    <td>{{totalViews_opp}}</td>
                                                    <td>{{totalWorkflows_opp}}</td>
                                                    <td>{{totalScouts_opp}}</td>
                                                    <td>{{totalInterviews_opp}}</td>
                                                    <td>{{totalPassedInterviews_opp}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="pagination-controls mt-3">
                                        <button class="pagination-btn" @click="goToFirstPageOpp"
                                            :disabled="currentPage_opp === 1">最初のページ</button>
                                        <button class="pagination-btn" @click="goToPrevPageOpp"
                                            :disabled="currentPage_opp === 1">前のページ</button>
                                        <button class="pagination-btn" @click="goToNextPageOpp"
                                            :disabled="currentPage === totalPagesOpp || totalPagesOpp === 0">次のページ</button>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- Talent Tab Content -->
                        <section class="mb-5" v-show="activeTab === 'talent'">
                            <!-- Flex container for tables -->
                            <div class="tables-flex-container">
                                <!-- Talent Workflow Table -->
                                <div class="table-section">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <p class="font-middle bold mb-0">人財ワークフロー</p>
                                        <div class="filter-section">
                                            <span class="filter-label">絞り込み:</span>
                                            <div v-dropdown="{
                modelValue: '',
                listType: 'optionTalentFilter'
            }" @selected="changeResumeFilter">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="table-wrapper">
                                        <table class="activity-table w-100">
                                            <thead class="stylish-color white-text">
                                                <tr>
                                                    <th class="text-left">人財名</th>
                                                    <th class="text-left">案件名</th>
                                                    <th>状況</th>
                                                    <th>入力期限</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="row in paginatedDataResumeFlow" :key="row.id" :class="isExpired(row.expire_date) && ![2, 3, 5, 6].includes(row.status) ? 'disable' : ''">
                                                    <td class="text-left">{{ row.resumes_name }}</td>
                                                    <td class="text-left">{{ row.opportunities_name }}</td>
                                                    <td>
                                                        <template v-if="[2, 5].includes(row.status)">
                                                            {{ convertStatus_res[row.status] }}
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="[0, 1].includes(row.status)">
                                                                <a
                                                                    :href=" row.opportunities_id + '/' + row.resumes_id + '/schedules'">
                                                                    {{ convertStatus_res[row.status] }}
                                                                </a>
                                                            </template>
                                                            <template v-else-if="row.status === 3">
                                                                <a :href="'/interview_result/' + row.id + '/view'">
                                                                    {{ convertStatus_res[row.status] }}
                                                                </a>
                                                            </template>
                                                            <template v-else-if="row.status === 6">
                                                                <a :href="'/interview_result/' + row.id + '/view'">
                                                                    {{ convertStatus_res[row.status] }}
                                                                </a>
                                                            </template>
                                                            <template v-else>
                                                                <a :href="'/interview_result/' + row.id + '/view'">
                                                                    {{ convertStatus_res[row.status] }}
                                                                </a>
                                                            </template>
                                                        </template>
                                                    </td>
                                                    <td>{{ [2, 3, 5, 6].includes(row.status) ? '-' : (isExpired(row.expire_date) ? '入力期限切れ' : formatDate(row.expire_date)) }}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="pagination-controls mt-3">
                                        <button @click="goToFirstPageResumeFlow" :disabled="currentPage_resumeflow === 1"
                                            class="pagination-btn">最初のページ</button>
                                        <button @click="goToPrevPageResumeFlow" :disabled="currentPage_resumeflow === 1"
                                            class="pagination-btn">前のページ</button>
                                        <button @click="goToNextPageResumeFlow"
                                            :disabled="currentPage_resumeflow === totalPagesResumeFlow || totalPagesResumeFlow === 0"
                                            class="pagination-btn">次のページ</button>
                                    </div>
                                </div>

                                <!-- Talent Usage Results Table -->
                                <div class="table-section">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <p class="font-middle bold mb-0">人財利用実績</p>
                                        <div class="text-right only-pc">
                                            <a class="font-middle" href="#" @click="exportCSVMonthlySummary">CSV出力</a>
                                        </div>
                                    </div>
                                    <div class="table-wrapper">
                                        <table class="text-center activity-table w-100">
                                            <thead class="stylish-color white-text">
                                                <tr>
                                                    <th class="text-left">対象月</th>
                                                    <th>人財登録</th>
                                                    <th class="keep-length">閲覧</th>
                                                    <th>スカウト受領</th>
                                                    <th>応募</th>
                                                    <th>面談</th>
                                                    <th>成約</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="row in paginatedData" :key="row.month">
                                                    <td class="text-left">{{row.month}}</td>
                                                    <td>{{row.total_resumes}}</td>
                                                    <td>{{row.total_views}}</td>
                                                    <td>{{row.total_scouts}}</td>
                                                    <td>{{row.total_workflows}}</td>
                                                    <td>{{row.total_interviews}}</td>
                                                    <td>{{row.total_passed_interviews}}</td>
                                                </tr>
                                                <tr class="bg-grey-3">
                                                    <td class="text-left">累計（通算）</td>
                                                    <td>{{totalResumes_res}}</td>
                                                    <td>{{totalViews_res}}</td>
                                                    <td>{{totalScouts_res}}</td>
                                                    <td>{{totalWorkflows_res}}</td>
                                                    <td>{{totalInterviews_res}}</td>
                                                    <td>{{totalPassedInterviews_res}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="pagination-controls mt-3">
                                        <button class="pagination-btn" @click="goToFirstPage"
                                            :disabled="currentPage === 1">最初のページ</button>
                                        <button class="pagination-btn" @click="goToPreviousPage"
                                            :disabled="currentPage === 1">前のページ</button>
                                        <button class="pagination-btn" @click="goToNextPage"
                                            :disabled="currentPage === totalPages || totalPages === 0">次のページ</button>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/users/mypage.css" />
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css">
    `,
    data() {
        return {
            activeTab: 'project', // Default active tab
            currentPage: 1,
            rowsPerPage: 3, // Số item cho bảng workflow
            rowsPerPageResults: 2, // Số item cho bảng 実績
            data: [],
            referral_list: [],
            data_opp: [],
            currentPage_opp: 1,
            oppFlow: [],
            currentPage_oppflow: 1,
            resumeFlow: [],
            currentPage_resumeflow: 1,
            selecteded: "全て",
            number_msg_unread: 0,
            number_msg: 0,
            company_name: "",
            referral_code: "",
            news: [],
            currentPage_new: 1,
            totalPages_new: 1,
            limit_new: 2,
            expanded_new: false,
        }
    },
    mounted() {
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        });
        this.companyId = userInfo ? userInfo.user_company_id : null;
        this.checkInterviewResult();
        this.checkExpire();
        this.getData();
        this.getData_Opp();
        this.getOppFlow();
        this.getResumeFlow();
        this.getNumberMsg();
        this.getNews();
        this.getCompanyName();
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        const toastrCSS = document.createElement("link");
        toastrCSS.rel = "stylesheet";
        toastrCSS.href = "/custom_frontend/static/css/Toastr.css";
        toastrCSS.onload = function () {
            console.log("Toastr CSS loaded successfully!");
        };
        document.head.appendChild(toastrCSS);
        checkToastrMessage()
    },
    computed: {
        convertStatus_opp() {
            return {
                0: '日程調整待ち', // redirect to schedueles
                1: '日程調整済', // redirect to schedueles
                2: '面談結果入力待ち', // redirect to interview result
                3: '面談済', // redirect to interview view?
                4: '成約済(評価待ち)', // redirect to evaluation
                5: '成約済', // redirect to evaluation view only
                6: 'キャンセル済', // reject
            }
        },
        convertStatus_res() {
            return {
                0: '日程調整待ち', // redirect to schedueles
                1: '日程調整済', // redirect to schedueles
                2: '面談結果待ち', // redirect to interview result
                3: '面談済', // redirect to interview view?
                4: '成約済',
                5: '成約済', // redirect to evaluation view only
                6: 'キャンセル済', // reject
            }
        },
        totalResumes_res() {
            return this.data.reduce((sum, row) => sum + row.total_resumes, 0);
        },
        totalWorkflows_res() {
            return this.data.reduce((sum, row) => sum + row.total_workflows, 0);
        },
        totalInterviews_res() {
            return this.data.reduce((sum, row) => sum + row.total_interviews, 0);
        },
        totalPassedInterviews_res() {
            return this.data.reduce((sum, row) => sum + row.total_passed_interviews, 0);
        },
        totalViews_res() {
            return this.data.reduce((sum, row) => sum + row.total_views, 0);
        },
        totalScouts_res() {
            return this.data.reduce((sum, row) => sum + row.total_scouts, 0);
        },
        totalOpp_opp() {
            return this.data_opp.reduce((sum, row) => sum + row.total_opportunities, 0);
        },
        totalWorkflows_opp() {
            return this.data_opp.reduce((sum, row) => sum + row.total_workflows, 0);
        },
        totalInterviews_opp() {
            return this.data_opp.reduce((sum, row) => sum + row.total_interviews, 0);
        },
        totalPassedInterviews_opp() {
            return this.data_opp.reduce((sum, row) => sum + row.total_passed_interviews, 0);
        },
        totalViews_opp() {
            return this.data_opp.reduce((sum, row) => sum + row.total_views, 0);
        },
        totalScouts_opp() {
            return this.data_opp.reduce((sum, row) => sum + row.total_scouts, 0);
        },
        paginatedData() {
            let start = (this.currentPage - 1) * this.rowsPerPageResults;
            let end = start + this.rowsPerPageResults;
            let pageData = this.data.slice(start, end);

            while (pageData.length < this.rowsPerPageResults) {
                pageData.push({
                    month: "",
                    total_resumes: "",
                    total_views: "",
                    total_scouts: "",
                    total_workflows: "",
                    total_interviews: "",
                    total_passed_interviews: ""
                });
            }

            return pageData;
        },
        totalPages() {
            return Math.ceil(this.data.length / this.rowsPerPageResults);
        },
        paginatedDataOpp() {
            let start = (this.currentPage_opp - 1) * this.rowsPerPageResults;
            let end = start + this.rowsPerPageResults;
            let pageData = this.data_opp.slice(start, end);

            while (pageData.length < this.rowsPerPageResults) {
                pageData.push({
                    month: "",
                    total_opportunities: "",
                    total_views: "",
                    total_workflows: "",
                    total_scouts: "",
                    total_interviews: "",
                    total_passed_interviews: ""
                });
            }

            return pageData;
        },
        totalPagesOpp() {
            return Math.ceil(this.data_opp.length / this.rowsPerPageResults);
        },
        paginatedDataOppFlow() {
            let start = (this.currentPage_oppflow - 1) * this.rowsPerPage;
            let end = start + this.rowsPerPage;
            let pageData = this.oppFlow.slice(start, end);

            // Chỉ thêm 1 dòng trống thay vì 3 dòng như trước đây để cân bằng với bảng bên phải
            while (pageData.length < this.rowsPerPage - 1) {
                pageData.push({
                    opportunities_name: "",
                    resumes_name: "",
                    status: "",
                    expire_date: "",
                });
            }

            return pageData;
        },
        totalPagesOppFlow() {
            return Math.ceil(this.oppFlow.length / this.rowsPerPage);
        },
        paginatedDataResumeFlow() {
            let start = (this.currentPage_resumeflow - 1) * this.rowsPerPage;
            let end = start + this.rowsPerPage;
            let pageData = this.resumeFlow.slice(start, end);

            while (pageData.length < this.rowsPerPage) {
                pageData.push({
                    opportunities_name: "",
                    resumes_name: "",
                    status: "",
                    expire_date: "",
                });
            }

            return pageData;
        },
        totalPagesResumeFlow() {
            return Math.ceil(this.resumeFlow.length / this.rowsPerPage);
        },
        visiblePages() {
            if (this.totalPages_new < 1) return [];

            let start = this.currentPage_new;
            if (this.currentPage_new === this.totalPages_new && this.totalPages_new > 1) {
                start = this.totalPages_new - 1;
            }

            const pages = [start, start + 1].filter(p => p <= this.totalPages_new);
            return pages;
        }
    },
    methods: {
        setActiveTab(tab) {
            this.activeTab = tab;
        },
        formatDate(dateString) {
            if (!dateString) return "";
            const date = new Date(dateString);
            return `${(date.getMonth() + 1).toString().padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")}`;
        },
        goToFirstPage() {
            this.currentPage = 1;
        },
        goToPreviousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },
        goToNextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },
        goToFirstPageOpp() {
            this.currentPage_opp = 1;
        },
        goToPrevPageOpp() {
            if (this.currentPage_opp > 1) this.currentPage_opp--;
        },
        goToNextPageOpp() {
            if (this.currentPage_opp < this.totalPagesOpp) this.currentPage_opp++;
        },
        goToFirstPageOppFlow() {
            this.currentPage_oppflow = 1;
        },
        goToPrevPageOppFlow() {
            if (this.currentPage_oppflow > 1) this.currentPage_oppflow--;
        },
        goToNextPageOppFlow() {
            if (this.currentPage_oppflow < this.totalPagesOppFlow) this.currentPage_oppflow++;
        },
        goToFirstPageResumeFlow() {
            this.currentPage_resumeflow = 1;
        },
        goToPrevPageResumeFlow() {
            if (this.currentPage_resumeflow > 1) this.currentPage_resumeflow--;
        },
        goToNextPageResumeFlow() {
            if (this.currentPage_resumeflow < this.totalPagesResumeFlow) this.currentPage_resumeflow++;
        },
        convertDateToJapanese(date) {
            if (!date) return null;
            date = new Date(date);
            const year = date.getFullYear();
            const month = ('0' + (date.getMonth() + 1)).slice(-2);  // Đảm bảo tháng luôn 2 chữ số
            const day = ('0' + date.getDate()).slice(-2);  // Đảm bảo ngày luôn 2 chữ số
            return `${year}年${month}月${day}日`;
        },
        async getData() {
            try {
                let response = await fetch(`/api/dashboard_resumes?id=${this.companyId}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                let result = await response.json();
                console.log("Dashboard data:", result);

                if (result.success) {
                    this.data = result.data
                    console.log()
                } else {
                    console.error("Lỗi từ API:", result.message);
                }
            } catch (error) {
                console.error("Error fetching dashboard data:", error);
            }
        },

        async getData_Opp() {
            try {
                let response = await fetch(`/api/dashboard_opp?id=${this.companyId}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                let result = await response.json();
                console.log("Dashboard data_opp:", result);

                if (result.success) {
                    this.data_opp = result.data
                    console.log()
                } else {
                    console.error("Lỗi từ API:", result.message);
                }
            } catch (error) {
                console.error("Error fetching dashboard data:", error);
            }
        },

        async getOppFlow(filter = "") {
            try {
                let response = await fetch(`/api/get_workflow_by_opp?id=${this.companyId}&filter=${filter}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                let result = await response.json();
                console.log("Opp flow:", result);

                if (result.success) {
                    this.oppFlow = result.data;
                    this.currentPage_oppflow = 1;
                } else {
                    console.error("Lỗi từ API:", result.message);
                }
            } catch (error) {
                console.error("Error fetching dashboard data:", error);
            }
        },

        async getResumeFlow(filter = "") {
            try {
                let response = await fetch(`/api/get_workflow_by_resume?id=${this.companyId}&filter=${filter}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                let result = await response.json();
                console.log("Resume flow:", result);

                if (result.success) {
                    this.resumeFlow = result.data;
                    this.currentPage_resumeflow = 1;
                } else {
                    console.error("Error:", result.message);
                }
            } catch (error) {
                console.error("Error fetching dashboard data:", error);
            }
        },

        changeTalentFilter(event) {
            this.selecteded = event.detail;
            this.getOppFlow(this.selecteded);
        },

        changeResumeFilter(event) {
            this.selecteded = event.detail;
            this.getResumeFlow(this.selecteded);
        },

        async checkInterviewResult() {
            try {
                const response = await fetch(`/api/check_interview`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                const result = await response.json();
                console.log("Check interview result:", result);
            } catch (error) {
                console.error("API Error:", error);
            }
        },

        async checkExpire() {
            try {
                const response = await fetch('/api/check_expire', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                const result = await response.json();
                console.log("Check expire result:", result);
            } catch (error) {
                console.error("Error checking expire status:", error);
            }
        },

        async getNumberMsg() {
            try {
                const response = await fetch(`/api/get_number_msg?id=${this.companyId}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                const result = await response.json();
                console.log("Msg:", result);
                if (result.success) {
                    this.number_msg = result.total;
                    this.number_msg_unread = result.unread_count;
                }
            } catch (error) {
                console.error("Error:", error);
            }
        },

        downloadCSV(headers, rows, filename) {
            let csvContent = "\uFEFF" + [headers, ...rows].map(e => e.join(",")).join("\n");

            const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
            const link = document.createElement("a");
            const url = URL.createObjectURL(blob);

            const today = new Date();
            const formattedDate = today.getFullYear() +
                String(today.getMonth() + 1).padStart(2, "0") +
                String(today.getDate()).padStart(2, "0");

            link.setAttribute("href", url);
            link.setAttribute("download", `${filename}_${formattedDate}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        exportCSV() {
            const headers = ["案件名", "人財名", "状況", "入力期限"];
            const rows = this.oppFlow.map(row => [
                `"${row.opportunities_name}"`,
                `"${row.resumes_name}"`,
                `"${this.convertStatus_opp[row.status] || "N/A"}\t"`,  // Thêm tab để giữ khoảng trống
                `"${row.expire_date ? this.formatDate(row.expire_date) : ""}"`
            ]);

            this.downloadCSV(headers, rows, "利用実績(人財側)");
        },

        exportCSVResumeFlow() {
            const headers = ["人財名", "案件名", "状況", "入力期限"];
            const rows = this.resumeFlow.map(row => [
                row.resumes_name,
                row.opportunities_name,
                this.convertStatus_res[row.status] || "N/A",
                row.expire_date ? `${this.formatDate(row.expire_date)}` : ""
            ]);

            this.downloadCSV(headers, rows, "利用実績(人財側)");
        },

        exportCSVMonthlySummary() {
            const headers = ["対象月", "人財登録", "閲覧", "スカウト受領", "応募", "面談", "成約"];
            const rows = this.data.map(row => [
                row.month,
                row.total_resumes,
                row.total_views,
                row.total_scouts,
                row.total_workflows,
                row.total_interviews,
                row.total_passed_interviews
            ]);

            rows.push([
                "累計（通算）",
                this.totalResumes_res,
                this.totalViews_res,
                this.totalScouts_res,
                this.totalWorkflows_res,
                this.totalInterviews_res,
                this.totalPassedInterviews_res
            ]);

            this.downloadCSV(headers, rows, "月間実績");
        },

        async getNews(page = 1) {
            try {
                const res = await fetch(`/api/news?page=${page}&limit=${this.limit_new}`);
                const json = await res.json();
                if (json.success) {
                    this.news = json.data;
                    this.totalPages_new = json.pages;
                    this.currentPage_new = json.page;
                }
            } catch (err) {
                console.error("Lỗi khi gọi API:", err);
            }
        },
        expandNews() {
            this.limit_new = 5;
            this.expanded_new = true;
            this.getNews(1); // Gọi lại trang đầu tiên với limit = 5
        },
        async getCompanyName() {
            try {
                const response = await fetch('/api/get_company_name', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: this.companyId }),
                });

                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

                const company = await response.json();
                console.log("Company:", company);

                if (company.result.success) {
                    this.company_name = company.result.company_name;
                    this.referral_code = company.result.referral_code;
                } else {
                    console.error("Lỗi từ API:", company.result.message);
                }
            } catch (error) {
                console.error("Error:", error);
            }
        },

        exportCSVOpportunitiesSummary() {
            const headers = ["対象月", "案件登録", "閲覧", "応募受領", "スカウト", "面談", "成約"];
            const rows = this.data_opp.map(row => [
                row.month,
                row.total_opportunities,
                row.total_views,
                row.total_scouts,
                row.total_workflows,
                row.total_interviews,
                row.total_passed_interviews
            ]);

            rows.push([
                "累計（通算）",
                this.totalOpp_opp,
                this.totalViews_opp,
                this.totalScouts_opp,
                this.totalWorkflows_opp,
                this.totalInterviews_opp,
                this.totalPassedInterviews_opp
            ]);

            this.downloadCSV(headers, rows, "案件実績");
        },

        isExpired(dateStr) {
            const today = new Date()
            const date = new Date(dateStr)
            // So sánh đến hết ngày (không chỉ theo giờ)
            today.setHours(0, 0, 0, 0)
            date.setHours(0, 0, 0, 0)
            return date < today
        },

        goToPage(page) {
            if (page < 1 || page > this.totalPages_new || page === this.currentPage_new) return;
            this.getNews(page);
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
    },
}

export default My