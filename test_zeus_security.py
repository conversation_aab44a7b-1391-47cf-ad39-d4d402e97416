#!/usr/bin/env python3
"""
Test script cho ZEUS Security Implementation
Chạy local để test tr<PERSON><PERSON><PERSON> <PERSON>hi push lên server
"""

import requests
import os
import tempfile

def test_file_upload_security():
    """Test file upload với các tr<PERSON><PERSON><PERSON> hợp khác nhau"""
    
    base_url = "http://localhost:8069"  # Odoo local
    upload_url = f"{base_url}/api/resume/upload_cv"
    
    print("🧪 Testing ZEUS Security Implementation...")
    print("=" * 50)
    
    # Test case 1: File type hợp lệ (PDF)
    print("\n1. Testing valid PDF file...")
    test_valid_pdf(upload_url)
    
    # Test case 2: File type không hợp lệ
    print("\n2. Testing invalid file type...")
    test_invalid_file_type(upload_url)
    
    # Test case 3: File quá lớn
    print("\n3. Testing oversized file...")
    test_oversized_file(upload_url)
    
    # Test case 4: EICAR test virus
    print("\n4. Testing EICAR virus signature...")
    test_eicar_virus(upload_url)
    
    print("\n" + "=" * 50)
    print("✅ Test completed! Check logs/security.log for details")

def test_valid_pdf(url):
    """Test upload file PDF hợp lệ"""
    try:
        # Tạo file PDF giả đơn giản
        pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(pdf_content)
            temp_file_path = temp_file.name
        
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test.pdf', f, 'application/pdf')}
            data = {'resume_id': '1'}
            
            response = requests.post(url, files=files, data=data)
            result = response.json()
            
            if result.get('success'):
                print("   ✅ Valid PDF upload: PASSED")
            else:
                print(f"   ❌ Valid PDF upload: FAILED - {result.get('error')}")
        
        os.unlink(temp_file_path)
        
    except Exception as e:
        print(f"   ❌ Valid PDF test error: {str(e)}")

def test_invalid_file_type(url):
    """Test upload file type không hợp lệ"""
    try:
        # Tạo file .exe giả
        exe_content = b"MZ\x90\x00"  # DOS header signature
        
        with tempfile.NamedTemporaryFile(suffix='.exe', delete=False) as temp_file:
            temp_file.write(exe_content)
            temp_file_path = temp_file.name
        
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('malware.exe', f, 'application/octet-stream')}
            data = {'resume_id': '2'}
            
            response = requests.post(url, files=files, data=data)
            result = response.json()
            
            if not result.get('success') and 'ファイル形式が許可されていません' in result.get('error', ''):
                print("   ✅ Invalid file type rejection: PASSED")
            else:
                print(f"   ❌ Invalid file type rejection: FAILED - {result}")
        
        os.unlink(temp_file_path)
        
    except Exception as e:
        print(f"   ❌ Invalid file type test error: {str(e)}")

def test_oversized_file(url):
    """Test upload file quá lớn"""
    try:
        # Tạo file 15MB (vượt quá limit 10MB)
        large_content = b"A" * (15 * 1024 * 1024)
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(large_content)
            temp_file_path = temp_file.name
        
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('large.pdf', f, 'application/pdf')}
            data = {'resume_id': '3'}
            
            response = requests.post(url, files=files, data=data)
            result = response.json()
            
            if not result.get('success') and 'ファイルサイズが大きすぎます' in result.get('error', ''):
                print("   ✅ Oversized file rejection: PASSED")
            else:
                print(f"   ❌ Oversized file rejection: FAILED - {result}")
        
        os.unlink(temp_file_path)
        
    except Exception as e:
        print(f"   ❌ Oversized file test error: {str(e)}")

def test_eicar_virus(url):
    """Test EICAR virus signature detection"""
    try:
        # EICAR test virus string
        eicar_content = b'X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*'
        
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_file.write(eicar_content)
            temp_file_path = temp_file.name
        
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('eicar.txt', f, 'text/plain')}
            data = {'resume_id': '4'}
            
            response = requests.post(url, files=files, data=data)
            result = response.json()
            
            if not result.get('success') and 'セキュリティスキャン' in result.get('error', ''):
                print("   ✅ EICAR virus detection: PASSED")
            else:
                print(f"   ❌ EICAR virus detection: FAILED - {result}")
        
        os.unlink(temp_file_path)
        
    except Exception as e:
        print(f"   ❌ EICAR virus test error: {str(e)}")

def check_security_logs():
    """Kiểm tra security logs"""
    log_file = "./logs/security.log"
    if os.path.exists(log_file):
        print(f"\n📋 Security log entries:")
        with open(log_file, 'r') as f:
            lines = f.readlines()
            for line in lines[-10:]:  # Show last 10 entries
                print(f"   {line.strip()}")
    else:
        print(f"\n⚠️  Security log not found at {log_file}")

if __name__ == "__main__":
    # Tạo thư mục logs nếu chưa có
    os.makedirs("./logs", exist_ok=True)
    
    print("🚀 Starting ZEUS Security Tests...")
    print("Make sure Odoo is running on localhost:8069")
    
    try:
        test_file_upload_security()
        check_security_logs()
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
