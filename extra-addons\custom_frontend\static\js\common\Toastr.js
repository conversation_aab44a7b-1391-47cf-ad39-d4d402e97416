function checkToastrMessage() {
    const checkToastr = setInterval(() => {
        if (window.toastr) { 
            clearInterval(checkToastr);
            
            let message = sessionStorage.getItem('toastrMessage'); 
            let type = sessionStorage.getItem('toastrType') || "info"; 

            if (message) {
                window.toastr[type](message, "", {
                    showDuration: 300,
                    hideDuration: 2000,
                    extendedTimeOut: 0,
                    positionClass: 'toast-top-full-width',
                    showEasing: 'swing',
                    hideEasing: 'linear',
                    progressBar: true,
                    closeButton: true,
                    closeHtml: '<button>&times;</button>',
                    preventDuplicates: true,
                    toastClass: `toast-${type} custom-toastr`
                });

                sessionStorage.removeItem('toastrMessage'); 
                sessionStorage.removeItem('toastrType'); 
            }
        }
    }, 100);
}

export { checkToastrMessage };
