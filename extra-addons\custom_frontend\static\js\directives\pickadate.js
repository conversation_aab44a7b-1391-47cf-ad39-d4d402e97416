export default {
    mounted(el, binding) {
        const $el = $(el); // <PERSON>hai báo jQuery element
        $el.pickadate({
            format: 'yyyy年mm月dd日',
            today: '今日',
            clear: '消去',
            close: 'CLOSE',
            firstDay: 1,
            weekdaysShort: ['日', '月', '火', '水', '木', '金', '土'],
            weekdaysFull: ['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'],
            monthsFull: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            monthsShort: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            selectYears: true,
            selectMonths: true,
            onOpen: function () {
                const pickerInstance = $el.pickadate('picker');
                addCustomDateDisplay(pickerInstance);
                if (binding.value?.onOpen) binding.value.onOpen(pickerInstance);
            },

            onSet: function (context) {
                const pickerInstance = $el.pickadate('picker');
                addCustomDateDisplay(pickerInstance);

                // 🛠 Cập nhật giá trị v-model
                if (context.select) {
                    const selectedDate = pickerInstance.get('select', 'yyyy年mm月dd日');
                    // 🛠 Xác định biến v-model nào cần cập nhật
                    if (binding.instance && binding.value?.model) {
                        const modelName = binding.value.model; // Tên biến v-model
                        binding.instance[modelName] = selectedDate;
                    }
                }

                if (binding.value?.onSet) binding.value.onSet(pickerInstance);
            }
        });

        function addCustomDateDisplay(picker) {
            if (!picker) return;

            $('.picker__date-display').remove();

            let selectedDate = picker.get('highlight');
            if (!selectedDate) return;

            let dateObj = new Date(selectedDate.year, selectedDate.month, selectedDate.date);
            let weekdayNames = ['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'];

            let displayHtml = `
                <div class="picker__date-display">
                    <div class="picker__weekday-display">${weekdayNames[dateObj.getDay()]},</div>
                    <div class="picker__month-display"><div>${dateObj.getMonth() + 1}月</div></div>
                    <div class="picker__day-display"><div>${dateObj.getDate()}</div></div>
                    <div class="picker__year-display"><div>${dateObj.getFullYear()}</div></div>
                </div>`;

            $('.picker__header').prepend(displayHtml);
        }
    },

    // Add static method for updating pickadate
    updatePickadate(elementId, date) {
        if (date) {
            const picker = $(`#${elementId}`).pickadate('picker');
            if (picker) {
                if (typeof date === 'string' && date.includes('年')) {
                    // Handle Japanese date format
                    const matches = date.match(/(\d{4})年(\d{2})月(\d{2})日/);
                    if (matches) {
                        const [_, year, month, day] = matches;
                        picker.set('select', new Date(year, parseInt(month) - 1, day));
                    }
                } else if (date.includes('-')) {
                    // Handle ISO format (YYYY-MM-DD)
                    const [year, month, day] = date.split('-');
                    picker.set('select', new Date(year, parseInt(month) - 1, day.split(' ')[0]));
                }
            }
        }
    }
};