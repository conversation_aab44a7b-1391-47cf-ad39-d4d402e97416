import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const bookmarkList = {
    'template': `
${createBreadcrumb([
    { text: 'サービスメニュー', link: null },
    { text: '探す', link: null },
    { text: 'ブックマーク', link: null, current: true }
])}
<div class="container-fluid">
    <!-- Tab navigation -->
    <div class="bookmark-tabs">
        <div class="tab-container" style="display: flex; justify-content: flex-start;">
            <button class="tab-button" :class="{ 'active': activeTab === 'opps' }"
                @click="setActiveTab('opps')" @touchstart="handleTouchStart" @touchend="handleTouchEnd">案件ブックマーク</button>
            <button class="tab-button" :class="{ 'active': activeTab === 'resumes' }"
                @click="setActiveTab('resumes')" @touchstart="handleTouchStart" @touchend="handleTouchEnd">人財ブックマーク</button>
            <button class="tab-button" :class="{ 'active': activeTab === 'companies' }"
                @click="setActiveTab('companies')" @touchstart="handleTouchStart" @touchend="handleTouchEnd">会社ブックマーク</button>
        </div>
    </div>
</div>
<div class="container">

    <!-- Tab content -->
    <div class="section" v-show="activeTab === 'opps'">
        <div v-for="item in paginatedOpps" :key="item.id">
            <a :href="'/opportunities/' + item.id + '/detail'" class="link-box">
                {{item.name}}
                <span class="delete-icon" @click.stop.prevent="deleteItem('opportunities_id', item.id)">
                    <i class="material-icons">delete</i>
                </span>
            </a>
        </div>
        <p v-if="opps.length === 0">ブックマーク中の案件がありません。</p>
        <div v-else class="pagination">
            <button @click="oppPage = 1" :disabled="oppPage === 1">先頭</button>
            <button @click="oppPage++" :disabled="oppPage >= totalOppPages">次へ</button>
            <button @click="oppPage = totalOppPages" :disabled="oppPage === totalOppPages">最後</button>
        </div>
    </div>

    <div class="section" v-show="activeTab === 'resumes'">
        <div v-for="item in paginatedResumes" :key="item.id">
            <a :href="'/resumes/' + item.id + '/detail'" class="link-box">
                {{item.name}}
                <span class="delete-icon" @click.stop.prevent="deleteItem('resumes_id', item.id)">
                    <i class="material-icons">delete</i>
                </span>
            </a>
        </div>
        <p v-if="resumes.length === 0">ブックマーク中の人財がありません。</p>
        <div v-else class="pagination">
            <button @click="resumePage = 1" :disabled="resumePage === 1">先頭</button>
            <button @click="resumePage++" :disabled="resumePage >= totalResumePages">次へ</button>
            <button @click="resumePage = totalResumePages" :disabled="resumePage === totalResumePages">最後</button>
        </div>
    </div>

    <div class="section" v-show="activeTab === 'companies'">
        <div v-for="item in paginatedCompanies" :key="item.id">
            <a :href="'/company/' + item.id + '/detail'" class="link-box">
                {{item.name}}
                <span class="delete-icon" @click.stop.prevent="deleteItem('company_id', item.id)">
                    <i class="material-icons">delete</i>
                </span>
            </a>
        </div>
        <p v-if="companies.length === 0">ブックマーク中の会社がありません。</p>
        <div v-else class="pagination">
            <button @click="companyPage = 1" :disabled="companyPage === 1">先頭</button>
            <button @click="companyPage++" :disabled="companyPage >= totalCompanyPages">次へ</button>
            <button @click="companyPage = totalCompanyPages" :disabled="companyPage === totalCompanyPages">最後</button>
        </div>
    </div>
</div>
<link rel="stylesheet" href="custom_frontend/static/css/bookmark_list.css" />
    `,
    data(){
        return{
            opps: [],
            resumes: [],
            companies: [],
            user_id: null,
            oppPage: 1,
            resumePage: 1,
            companyPage: 1,
            perPage: 5,
            activeTab: 'opps', // Default active tab
            touchStartTime: null
        }
    },
    computed: {
        totalOppPages() {
            return Math.ceil(this.opps.length / this.perPage);
          },
        paginatedOpps() {
          const start = (this.oppPage - 1) * this.perPage;
          return this.opps.slice(start, start + this.perPage);
        },
        paginatedResumes() {
          const start = (this.resumePage - 1) * this.perPage;
          return this.resumes.slice(start, start + this.perPage);
        },
        paginatedCompanies() {
          const start = (this.companyPage - 1) * this.perPage;
          return this.companies.slice(start, start + this.perPage);
        },
        totalResumePages() {
            return Math.ceil(this.resumes.length / this.perPage);
        },
        totalCompanyPages() {
            return Math.ceil(this.companies.length / this.perPage);
        },
    },
    mounted() {
        this.user_id = userInfo ? userInfo.user_id : null;
        this.getReaction();
    },
    methods: {
        setActiveTab(tab) {
            this.activeTab = tab;
        },
        handleTouchStart(event) {
            this.touchStartTime = Date.now();
            event.target.style.transform = 'scale(0.95)';
        },
        handleTouchEnd(event) {
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - this.touchStartTime;

            // Reset visual feedback
            if (event && event.target) {
                event.target.style.transform = 'scale(1)';
            }

            // Only trigger if it's a quick tap (not a long press)
            if (touchDuration < 500) {
                // Touch event handled, click will also fire
            }
        },
        async getReaction(){
            try{
                const respone = await fetch('/api/get_reaction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: this.user_id
                    })
                });

                const result = await respone.json();
                console.log("Data respone: ", result);
                if(result.result.success){
                    this.opps = result.result.opportunities;
                    this.resumes = result.result.resumes;
                    this.companies = result.result.companies;
                }
            } catch(error){
                console.log("Error catch: ", error);
            }
        },

        async deleteItem(type, id){
            try{
                const response = await fetch('/api/delete_reaction', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type: type,
                        id: id
                    })
                });

                const result = await response.json();
                console.log("Delete result: ", result);

                if(result.result.success) {
                    await this.getReaction();
                }
            }catch(error){
                console.log("Error catch: ", error);
            }
        }
    },
}
export default bookmarkList;