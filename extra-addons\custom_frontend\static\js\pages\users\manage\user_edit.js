import { userInfo } from "../../../router/router.js";
import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const UserEdit = {
    template: `
        <main class="pb-3 margin-header" id="vue-app">
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '登録・管理', link: null },
                { text: '会社データ管理', link: null },
                { text: 'プロフィール', link: null, current: true }
            ])}
            <div class="container-fluid">
                <div class="d-flex justify-content-end align-items-center">
                    <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a href="/companies/manage/edit">会社データ</a></li>
                        <li><a class="active" href="/users/edit">プロフィール</a></li>
                        <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a href="/users/profile/edit_password">パスワード</a></li>
                        <li><a href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a href="/mypage/plan">プラン</a></li>
                        <li><a href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
            <div class="container-fluid grabient pt-5 position-relative">
                <div class="row mb-4 mb-md-0">
                    <div class="d-md-block col-md-4 col-lg-3 side-menu-contents">
                        <div class="card px-3 pb-3 side-card collapsible">
                            <ul class="collapsible mb-0">
                                <div class="d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3 active" aria-current="page" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                                <li class="my-md-1" hidden><a class="d-block py-1 px-3" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                        <div class="card mb-4">
                            <div class="card-body p-5">
                                <form class="edit_user" @submit.prevent="updateProfile">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="mb-0">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3 active" for="">姓<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                            <input class="form-control" autocomplete="off" id="last_name_field" type="text" v-model="last_name"
                                                            name="user[last_name]">
                                                            <div v-if="!missLastname" class="text-danger text-left">姓を入力してください。</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="mb-0">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3 active" for="">名<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                            <input class="form-control" autocomplete="off" id="first_name_field" type="text"
                                                            v-model="first_name" name="user[first_name]">
                                                            <div v-if="!missFirstname" class="text-danger text-left">名を入力してください。</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="mx-auto mb-5">
                                                <label class="mb-3 font-middle active" for="">電話番号<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                <input class="form-control" autocomplete="off" id="tel_field" type="text" v-model="phone"
                                                name="user[tel]">
                                                <div v-if="!missPhone" class="text-danger text-left">電話番号を入力してください。</div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-4 mt-2">
                                <button name="button" type="button" class="btn btn-default btn-block btn-lg font-middle user-edit-btn waves-effect waves-light" form="edit_user" style="color: white" @click="updateProfile">変更</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/users/manage/user_edit.css">
        <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css">
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            categories: [
                { href: "/bookmark_opportunities", label: "案件" },
                { href: "/bookmark_resumes", label: "人財" },
                { href: "/bookmark_users", label: "会社" },
            ],
            phone: '',
            name: '',
            first_name: '',
            last_name: '',
            user_id: userInfo ? userInfo.user_id : null,
            missLastname: true,
            missFirstname: true,
            missPhone: true,
            isMobileMenuOpen: false
        }
    },
    methods: {
        async fetchProfile() {
            try {
                const response = await fetch("/api/get_profile", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({'user_id': this.user_id}),
                });
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const data = await response.json();
                if (data.result.success) {
                    this.phone = data.result.phone ? data.result.phone : '';
                    this.name = data.result.username;
                } else {
                    console.error('Error fetching profile:', data.message);
                }
            } catch (error) {
                console.error('There was a problem with the fetch operation:', error);
            }
        },
        splitName(fullName) {
            const parts = fullName?.trim().split(" ") || '';
            if (parts.length > 0) {
                // Trong tiếng Nhật, họ đứng trước tên
                // Lấy phần tử đầu tiên là họ (last_name)
                this.last_name = parts[0];
                // Phần còn lại là tên (first_name)
                this.first_name = parts.slice(1).join(" ");
            }
        },

        validateInput() {
            // Reset validate states
            this.missFirstname = false;
            this.missLastname = false;
            this.missPhone = false;

            // Validate first name
            if (this.first_name?.trim()) {
                this.missFirstname = true;
            }

            // Validate last name
            if (this.last_name?.trim()) {
                this.missLastname = true;
            }

            // Validate phone
            const phone = this.phone?.trim() || '';
            if (/^0\d{9,10}$/.test(phone)) {
                this.missPhone = true;
            }

            // Return true if all fields are valid
            return this.missFirstname && this.missLastname && this.missPhone;
        },

        async updateProfile() {
            if (!this.validateInput()) {
                return;
            }
            try {
                const response = await fetch("/api/update_profile", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        user_id: this.user_id,
                        username: `${this.last_name} ${this.first_name}`,
                        phone: this.phone,
                    }),
                });

                if (!response.ok) throw new Error("Network response was not ok");

                const data = await response.json();
                if (data.result.success) {
                    // Hiển thị toast message trực tiếp thay vì reload trang
                    if (window.toastr) {
                        window.toastr.success("プロフィールが正常に更新されました。", "", {
                            showDuration: 300,
                            hideDuration: 2000,
                            extendedTimeOut: 0,
                            positionClass: 'toast-top-full-width',
                            showEasing: 'swing',
                            hideEasing: 'linear',
                            progressBar: true,
                            closeButton: true,
                            closeHtml: '<button>&times;</button>',
                            preventDuplicates: true,
                            toastClass: 'toast-success custom-toastr'
                        });
                    } else {
                        // Fallback nếu toastr chưa được load
                        sessionStorage.setItem('toastrMessage', "プロフィールが正常に更新されました。");
                        sessionStorage.setItem('toastrType', "success");
                        window.location.reload();
                    }
                } else {
                    if (window.toastr) {
                        window.toastr.error(data.result.message || "エラーが発生しました。");
                    } else {
                        console.error("Error updating profile:", data.result.message);
                    }
                }
            } catch (error) {
                console.error("There was a problem with the fetch operation:", error);
            }
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function() {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function() {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function() {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function() {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        }
    },
    async mounted() {
        // Load toastr và các script cần thiết
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        const toastrCSS = document.createElement("link");
        toastrCSS.rel = "stylesheet";
        toastrCSS.href = "/custom_frontend/static/css/Toastr.css";
        toastrCSS.onload = function() {
            console.log("Toastr CSS loaded successfully!");
        };
        document.head.appendChild(toastrCSS);

        await this.fetchProfile();
        this.splitName(this.name);

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });
    },
    watch: {
        first_name: {
            handler() {
                this.missFirstname = true;
            },
        },
        last_name: {
            handler() {
                this.missLastname = true;
            },
        },
        phone: {
            handler() {
                this.missPhone = true;
            },
        },
    },
}

export default UserEdit