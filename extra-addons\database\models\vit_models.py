from odoo import models, fields, api
import uuid
import hashlib
from datetime import datetime, timedelta
import logging
import requests
import random
import string
import os
from odoo.addons.custom_frontend.controllers import common

_logger = logging.getLogger(__name__)

class Categories(models.Model):
    _name = 'vit.categories'
    _description = 'Categories'

    name = fields.Char(string='Name', required=True)
    parent_id = fields.Many2one('vit.categories', string='Parent Category')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')
    parent_id = fields.Many2one('vit.categories', string='Parent Category')

class Plans(models.Model):
    _name = 'vit.plans'
    _description = 'Plans'

    title = fields.Char(string='Title', required=True)
    price = fields.Float(string='Price', required=True)
    type = fields.Selection([
        ('free', 'Free'),
        ('standard', 'Standard'),
        ('premium', 'Premium')
    ], string='Type', required=True)
    unit = fields.Selection([
        ('month', 'Month'),
        ('year', 'Year')
    ], string='Unit', required=True)
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')


class Partner(models.Model):
    _name = 'vit.partner'
    _description = 'Partner'

    user_id = fields.Many2one('vit.users', string='User', ondelete='cascade')
    cv_id = fields.Many2one('vit.curriculum_vitae', string='CV')
    opp_id = fields.Many2one('vit.opportunities', string='Opp ID')
    status = fields.Char(string='Status')
    date_period = fields.Datetime(string='Date Period')
    resume_visibility = fields.Char(string='Resume Visibility')
    company_publish = fields.Char(string='Company Publisher')
    human_publish = fields.Char(string='Human Publisher')
    user_name = fields.Char(string='User Name')
    initial_name = fields.Char(string='Initial Name')
    gender = fields.Char(string='Gender')
    birthday = fields.Datetime(string='Birthday')
    nationality = fields.Char(string='Nationality')
    resident = fields.Char(string='Resident')
    categories_consultation = fields.Char(string='Categories Consultation')
    categories_development = fields.Char(string='Categories Development')
    categories_infrastructure = fields.Char(string='Categories Infrastructure')
    categories_design = fields.Char(string='Categories Design')
    experience_pr = fields.Char(string='Experience PR')
    qualification = fields.Char(string='Qualification')
    characteristic  = fields.Char(string='Characteristic')
    utilization_rate = fields.Char(string='Utilization Rate')
    unit_price_min = fields.Integer(string='Unit Price Min')
    unit_price_max = fields.Integer(string='Unit Price Max')
    region = fields.Char(string='Region')
    working_frequency = fields.Char(string='Working Frequency')
    working_location = fields.Char(string='Working Location')
    working_hope = fields.Char(string='Working Hope')
    company_settings = fields.Char(string='Company Settings')
    contract_types = fields.Char(string='Contract Types')
    partner_types = fields.Char(string='Partner Types')
    rating_flag = fields.Char(string='Rating Flag')
    internal_comment = fields.Boolean(string='Internal Comment')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Many2one('vit.users', string='Created By', ondelete='cascade')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Many2one('vit.users', string='Updated By', ondelete='cascade')
    view = fields.Integer(string='View')
    scout_number = fields.Integer(string='Scout Number')
    company_id = fields.Many2one('vit.companies', string='Company', ondelete='set null', required=False)
    viewer_id = fields.Char(string='Viewer Id')
    del_flg = fields.Boolean(string='Delete Flag')

class CurriculumVitae(models.Model):
    _name = 'vit.curriculum_vitae'
    _description = 'Curriculum Vitae'

    name = fields.Char("File Name")
    # Không dùng fields.Binary nữa, chỉ dùng attachment public
    file_attachment_id = fields.Many2one('ir.attachment', string='CV Attachment', ondelete='set null')
    resume_id = fields.Many2one("vit.partner", string="Resume", ondelete='cascade')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class ExpertiseFields(models.Model):
    _name = 'vit.expertise_fields'
    _description = 'Expertise Fields'

    category = fields.Char(string='Category', required=True)
    subcategory = fields.Char(string='Subcategory', required=True)
    experience = fields.Float(string='Experience', required=True)
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class ResumeExpertise(models.Model):
    _name = 'vit.resume_expertise'
    _description = 'Resume Expertise'

    resume_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    expertise_field_id = fields.Many2one('expertise.fields', string='Expertise Field')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class SkillsAndPreferences(models.Model):
    _name = 'vit.skills_and_preferences'
    _description = 'Skills and Preferences'

    resume_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    skill_type = fields.Char(string='Skill Type')
    preference_details = fields.Text(string='Preference Details')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class DesiredWorkConditions(models.Model):
    _name = 'vit.desired_work_conditions'
    _description = 'Desired Work Conditions'

    resume_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    work_type = fields.Char(string='Work Type')
    work_region = fields.Char(string='Work Region')
    monthly_salary_min = fields.Integer(string='Monthly Salary Min')
    monthly_salary_max = fields.Integer(string='Monthly Salary Max')
    work_schedule = fields.Char(string='Work Schedule')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class JobApplications(models.Model):
    _name = 'vit.job_applications'
    _description = 'Job Applications'

    user_id = fields.Many2one('vit.users', string='User')
    opp_id = fields.Many2one('vit.opportunities', string='Opp Id', ondelete='cascade')
    application_date = fields.Datetime(string='Application Date')
    status = fields.Char(string='Status')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class Comments(models.Model):
    _name = 'vit.comments'
    _description = 'Comments'

    resume_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    opportunities_id = fields.Many2one('vit.opportunities', string='Opportunities', ondelete='cascade')
    content = fields.Char(string='Content')
    status = fields.Integer(string='Status')
    created_by = fields.Char(string='Created By')
    created_at = fields.Datetime(string='Created At')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class Projects(models.Model):
    _name = 'vit.projects'
    _description = 'Projects'

    title = fields.Char(string='Title', required=True)
    work_ratio = fields.Char(string='Work Ratio')
    office_frequency = fields.Char(string='Office Frequency')
    work_location = fields.Char(string='Work Location')
    contract_duration = fields.Char(string='Contract Duration')
    project_category = fields.Char(string='Project Category')
    project_content = fields.Text(string='Project Content')
    project_requirements = fields.Text(string='Project Requirements')
    detailed_info = fields.Text(string='Detailed Info')
    content_confirmation_status = fields.Char(string='Content Confirmation Status')
    contract_form = fields.Char(string='Contract Form')
    commercial_involvement_level = fields.Char(string='Commercial Involvement Level')
    commercial_flow = fields.Text(string='Commercial Flow')
    application_deadline = fields.Datetime(string='Application Deadline')
    recruitment_count = fields.Integer(string='Recruitment Count')
    recruitment_target = fields.Text(string='Recruitment Target')
    project_features = fields.Text(string='Project Features')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class ApplicationSettings(models.Model):
    _name = 'vit.application_settings'
    _description = 'Application Settings'

    opp_id = fields.Many2one('vit.opportunities', string='Project', ondelete='cascade')
    contract_type = fields.Char(string='Contract Type')
    work_ratio = fields.Char(string='Work Ratio')
    salary_min = fields.Integer(string='Salary Min')
    salary_max = fields.Integer(string='Salary Max')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class ProjectExpertise(models.Model):
    _name = 'vit.project_expertise'
    _description = 'Project Expertise'

    opp_id = fields.Many2one('vit.opportunities', string='Opp ID', ondelete='cascade')
    expertise_field_id = fields.Many2one('expertise.fields', string='Expertise Field')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class Companies(models.Model):
    _name = 'vit.companies'
    _description = 'Companies'

    name = fields.Char(string='Name')
    addresses = fields.Text(string='Addresses')
    phone_number = fields.Char(string='Phone Number')
    email = fields.Char(string='Email')
    representative = fields.Char(string='Representative')
    charter_capital = fields.Integer(string='Charter Capital')
    employee_count = fields.Integer(string='Employee Count')
    industry = fields.Char(string='Industry')
    specialty_industry = fields.Char(string='Specialty Industry')
    description = fields.Text(string='Description')
    public_status = fields.Char(string='Public Status')
    options_bussiness = fields.Char(string='Options Business')
    head_office_location = fields.Char(string='Head Office Location')
    brand_location = fields.Char(string='Brand Location')
    established_year = fields.Integer(string='Established Year')
    has_dispatch_license = fields.Boolean(string='Has Dispatch License')
    response_rate = fields.Float(string='Response Rate')
    interview_count = fields.Integer(string='Interview Count')
    successful_contracts = fields.Integer(string='Successful Contracts')
    high_ratings_count = fields.Integer(string='High Ratings Count')
    referral_code = fields.Char(string='Referral Code')
    company_site_url = fields.Char(string='Company Site URL')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Many2one('vit.users', string='Created By', ondelete='cascade')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Many2one('vit.users', string='Updated By', ondelete='cascade')

class InterviewResult(models.Model):
    _name = 'vit.interviewresult'
    _description = 'Interview Result'

    contract_types = fields.Char(string='Contract Types')
    opp_id = fields.Many2one('vit.opportunities', string='Opp ID', ondelete='cascade')
    resume_id = fields.Many2one('vit.partner', string='Resume ID', ondelete='cascade')
    flow_id = fields.Many2one('vit.workflow', string='Flow ID', ondelete='cascade')
    result = fields.Char(string='Result')
    reject_reason = fields.Text(string='Message')

    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class InterviewBooking(models.Model):
    _name = 'vit.interviewbooking'
    _description = 'Interview Booking'

    contract_types = fields.Char(string='Contract Types')
    opp_id = fields.Many2one('vit.opportunities', string='Opp ID', ondelete='cascade')
    resume_id = fields.Many2one('vit.partner', string='Resume ID', ondelete='cascade')
    is_booking = fields.Boolean(string='Is Booking')
    interview_start = fields.Datetime(string='Interview Start Time')
    interview_end = fields.Datetime(string='Interview End Time')
    meeting_url = fields.Char(string='Meeting URL', default= None)
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class InvoiceTracking(models.Model):
    _name = 'vit.invoice_tracking'
    _description = 'Invoice Tracking'

    issue_date = fields.Datetime(string='Issue Date')
    invoice_number = fields.Char(string='Invoice Number')
    amount = fields.Float(string='Amount')
    payment_status = fields.Char(string='Payment Status')
    paid_amount = fields.Float(string='Paid Amount')
    payment_date = fields.Datetime(string='Payment Date')
    notes = fields.Text(string='Notes')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class CCInfor(models.Model):
    _name = 'vit.cc_infor'
    _description = 'CC Information'

    cc_number = fields.Char(string='CC Number')
    cc_name = fields.Char(string='CC Name')
    cc_expired = fields.Datetime(string='CC Expired')
    cc_code = fields.Integer(string='CC Code')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')


class Users(models.Model):
    _name = 'vit.users'
    _description = 'User Information'

    username = fields.Char(string='Username', required=True)
    email = fields.Char(string='Email', required=True)
    password = fields.Char(string='Password', required=True)
    old_password = fields.Char(string='Old Password')
    phone = fields.Char(string='Phone')
    role = fields.Char(string='Role')
    plan_id = fields.Many2one('vit.plans', string='Plan', required=False)
    payment_status = fields.Selection([
        ('unpaid', 'Unpaid'),
        ('paid', 'Paid'),
        ('expired', 'Expired')
    ], string='Payment Status', default='unpaid')
    plan_updated_at = fields.Datetime(string='Plan Updated At')
    payment_history_ids = fields.One2many('vit.payment_history', 'user_id', string='Payment History')
    company_id = fields.Many2one('vit.companies', string='Company', ondelete='set null', required=False)
    purpose_of_use = fields.Char(string='Purpose of Use')
    purpose_of_use_note = fields.Text(string='Purpose of Use Note')
    learn_about_this_site = fields.Char(string='Learn About This Site')
    learn_about_this_site_note = fields.Text(string='Learn About This Site Note')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')
    active = fields.Boolean(string='Active', default=False)
    token = fields.Char(string="Token")
    token_expiry = fields.Datetime(string="Token Expiry")
    token_login = fields.Char(string="Token Login")

    # Credit Card fields
    credit_card_ids = fields.One2many('vit.user_credit_card', 'user_id', string='Credit Cards')
    default_credit_card_id = fields.Many2one('vit.user_credit_card', string='Default Credit Card',
        domain="[('user_id', '=', id), ('is_active', '=', True)]")

class Notification(models.Model):
    _name = 'vit.notification'
    _description = 'Notification'

    user_id = fields.Many2one('users', string='User', ondelete='cascade')
    message = fields.Text(string='Message')
    is_read = fields.Boolean(string='Is Read')
    resume_id = fields.Integer( string='Resume ID')
    read_at = fields.Datetime(string='Read At')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')


class ResumesScore(models.Model):
    _name = 'vit.resumes_score'
    _description = 'Resumes Score'

    user_id = fields.Many2one('vit.users', string='User', ondelete='cascade')
    resumes_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    criteria1 = fields.Char(string='Criteria 1')
    criteria2 = fields.Char(string='Criteria 2')
    criteria3 = fields.Char(string='Criteria 3')
    criteria4 = fields.Char(string='Criteria 4')
    criteria5 = fields.Char(string='Criteria 5')
    final_score = fields.Integer(string='Final Score')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class Opportunities(models.Model):
    _name = 'vit.opportunities'
    _description = 'Opportunities'

    company_id = fields.Many2one('vit.companies', string='Company')
    subject = fields.Char(string='Subject')
    categories_design = fields.Char(string='Categories Design')
    categories_development = fields.Char(string='Categories Development')
    categories_infrastructure = fields.Char(string='Categories Infrastructure')
    categories_operation_maintenance = fields.Char(string='Categories Operation and Maintenance')
    utilization_rate = fields.Char(string='Utilization rate')
    unit_price_min = fields.Float(string='Unit Price Min')
    unit_price_max = fields.Float(string='Unit Price Max')
    skill_matching_flg = fields.Boolean(string='Skill Matching Flag')
    work_frequencies = fields.Char(string='Work Frequency')
    specifies_workplaces = fields.Char(string='Specifies Workplaces')
    contract_startdate_at = fields.Datetime(string='Contract Start Date')
    contract_enddate_at = fields.Datetime(string='Contract End Date')
    possible_continue_flg = fields.Boolean(string='Possible Continue Flag')
    requirements = fields.Text(string='Requirements')
    skill_requirements = fields.Text(string='Skill Requirements')
    order_accuracy_ids = fields.Char(string='Order Accuracy ID')
    involvements = fields.Char(string='Involvement')
    opp_type_id = fields.Char(string='Opp Type ID')
    contract_types = fields.Char(string='Contract Types')
    expired_at = fields.Datetime(string='Expired At')
    participants = fields.Integer(string='Participants')
    interview_count_id = fields.Char(string='Interview Count ID')
    trading_restriction = fields.Char(string='Trading Restriction')
    opp_qualities = fields.Char(string='Opp Qualities')
    charge_accounts = fields.Char(string='Charge Accounts')
    public_status_id = fields.Char(string='Public Status ID')
    publish_company_name_status_id = fields.Char(string='Publish Company Name Status ID')
    business_field = fields.Char(string='Business Field')
    status = fields.Char(string='Status')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Many2one('vit.users', string='Created By', ondelete='cascade')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Many2one('vit.users', string='Updated By', ondelete='cascade')
    scout_count = fields.Integer(string='Scout Count', default=0)
    view_count = fields.Integer(string='View Count', default=0)
    application_receipt = fields.Integer(string='Application Receipt', default=0)
    nationality = fields.Char(string='Nationality')
    resident = fields.Char(string='Resident', default='no')
    interview_start_time = fields.Char(string='Interview Start Time')
    interview_end_time = fields.Char(string='Interview End Time')
    available_monday = fields.Boolean(string='Available Monday', default=False)
    available_tuesday = fields.Boolean(string='Available Tuesday', default=False)
    available_wednesday = fields.Boolean(string='Available Wednesday', default=False)
    available_thursday = fields.Boolean(string='Available Thursday', default=False)
    available_friday = fields.Boolean(string='Available Friday', default=False)
    available_saturday = fields.Boolean(string='Available Saturday', default=False)

class Dashboard(models.Model):
    _name = "vit.dashboard"
    _description = "Dashboard Data"

    name = fields.Char(string="Name")
    company_count = fields.Integer(string="Companies Count")
    project_count = fields.Integer(string="Projects Count")
    resume_count_status1 = fields.Integer(string="Resumes Count Status 1")
    resume_count_status2 = fields.Integer(string="Resumes Count Status 2")
    resume_count_status3 = fields.Integer(string="Resumes Count Status 3")
    invoice_amount = fields.Float(string="Invoice Total Amount")


SECRET_SALT = "vit_secret_salt"

class VitUserSessions(models.Model):
    _name = 'vit.user_sessions'
    _description = 'User Sessions'

    user_id = fields.Many2one('vit.users', string="User", required=True, ondelete='cascade')
    session_token = fields.Char(string="Session Token", required=True)
    created_at = fields.Datetime(string="Created At", default=fields.Datetime.now)

    @staticmethod
    def generate_token():
        """Tạo token ngẫu nhiên"""
        raw_token = f"{uuid.uuid4()}{SECRET_SALT}"
        return hashlib.sha256(raw_token.encode()).hexdigest()
    # TODO: update theo yêu cầu KH
    # @api.model
    # def update_dashboard_data(self):
    #     # Xóa dữ liệu cũ và tạo mới (tránh dữ liệu bị lặp)
    #     self.search([]).unlink()
    #     return self.create({
    #         "name": "Dashboard Overview",
    #         "company_count": self.env["vit.companies"].search_count([]),
    #         "user_count": self.env["vit.users"].search_count([]),
    #         "plan_count": self.env["vit.plans"].search_count([]),
    #     })

    # @api.model
    # def search(self, args, offset=0, limit=None, order=None, count=False):
    #     # Cập nhật dashboard trước khi trả về dữ liệu
    #     self.update_dashboard_data()
    #     return super(Dashboard, self).search(args, offset, limit, order, count)

class WorkFlow(models.Model):
    _name = 'vit.workflow'
    _description = 'Opportunities flow and resumes flow'

    opportunities_id = fields.Many2one('vit.opportunities', string='Opportunities', ondelete='cascade')
    resumes_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    status = fields.Integer(string='Criteria 1')
    expire_date = fields.Datetime(string='Criteria 2')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class Reaction(models.Model):
    _name = 'vit.reaction'
    _description = 'Reaction'

    user_id = fields.Many2one('vit.users', string='User')
    opportunities_id = fields.Many2one('vit.opportunities', string='Opportunities', ondelete='cascade')
    resumes_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    company_id = fields.Many2one('vit.companies', string='Company')
    status = fields.Integer(string='Criteria 1')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class EvaluationCriteria(models.Model):
    _name = 'vit.evaluation_criteria'
    _description = 'Evaluation Criteria'

    name = fields.Char(string='Criteria Name', required=True)
    description = fields.Text(string='Description')
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    created_by = fields.Integer(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Integer(string='Updated By')

class WorkFlowEvaluation(models.Model):
    _name = 'vit.workflow_evaluation'
    _description = 'Workflow Evaluation Scores'

    workflow_id = fields.Many2one('vit.workflow', string='Workflow', required=True, ondelete='cascade')
    criteria_id = fields.Many2one('vit.evaluation_criteria', string='Criteria', required=True, ondelete='cascade')
    score = fields.Integer(string='Score')
    reason = fields.Text(string='Reason')
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    created_by = fields.Integer(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Integer(string='Updated By')

class Scout(models.Model):
    _name = 'vit.scout'
    _description = 'Scout'

    opportunities_id = fields.Many2one('vit.opportunities', string='Opportunities', ondelete='cascade')
    resumes_id = fields.Many2one('vit.partner', string='Resume', ondelete='cascade')
    company_id = fields.Many2one('vit.companies', string='Company', ondelete='cascade')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Many2one('vit.users', string='Created By', ondelete='cascade')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Many2one('vit.users', string='Updated By', ondelete='cascade')

class ViewOpportunities(models.Model):
    _name = 'vit.viewopportunities'
    _description = 'ViewOpportunities'

    opportunities_id = fields.Many2one('vit.opportunities', string='Opportunities', ondelete='cascade')
    user_id = fields.Many2one('vit.users', string='User', ondelete='cascade')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class ViewResumes(models.Model):
    _name = 'vit.viewresumes'
    _description = 'ViewResumes'

    resumes_id = fields.Many2one('vit.partner', string='Resumes', ondelete='cascade')
    user_id = fields.Many2one('vit.users', string='User', ondelete='cascade')
    created_at = fields.Datetime(string='Created At')
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class News(models.Model):
    _name = 'vit.news'
    _description = 'News'

    title = fields.Char(string='Title', required=True)
    content = fields.Text(string='Content', required=True)
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    created_by = fields.Char(string='Created By')
    updated_at = fields.Datetime(string='Updated At')
    updated_by = fields.Char(string='Updated By')

class Note(models.Model):
    _name = 'vit.note'
    _description = 'Note'

    content = fields.Text(string='Content', required=True)
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    updated_at = fields.Datetime(string='Updated At')

class Request(models.Model):
    _name = 'vit.request'
    _description = 'Request'

    referral_code = fields.Char('referral code', required=True)
    department = fields.Char(string='department', required=False)
    position = fields.Char(string='posintion', required=False)
    type = fields.Char(string='type', required=True)
    content = fields.Char(string='content', required=True)
    status = fields.Integer(string='status', default=0)

class Contact(models.Model):
    _name = 'vit.contact'
    _description = 'Contact'

    referral_code = fields.Char('referral code', required=True)
    content = fields.Char(string='content', required=True)
    status = fields.Integer(string='status', default=0)

class Setting(models.Model):
    _name = 'vit.setting'
    _description = 'Setting'

    user_id = fields.Many2one('vit.users', required=True, ondelete='cascade')
    receive_mail = fields.Boolean(string='Receive email', required=True, default = True)
    receive_opportunities = fields.Boolean(string='Receive opportunities', required=True, default = True)
    receive_resumes = fields.Boolean(string='Receive resumes', required=True, default = True)

class Video(models.Model):
    _name = 'vit.video'
    _description = 'Video'

    name = fields.Char(string='Video Title', required=True)
    video_file = fields.Binary(string='Video File', attachment=True, required=True)
    mimetype = fields.Char(string='Mime Type')

class VitMailAuto(models.Model):
    _name = 'vit.mailauto'
    _description = 'Gửi mail tự động định kỳ'

    def send_weekly_mails(self):
        _logger.warning("🔥 Scheduled Action đã chạy!")
        mail_server = self.env['ir.mail_server'].sudo().search([], limit=1)
        if not mail_server:
            return

        users = self.env['vit.users'].sudo().search([('email', '!=', False)])
        if not users:
            return

        now = fields.Datetime.now()
        last_week = now - timedelta(days=7)
        recent_opps = self.env['vit.opportunities'].sudo().search([
            '|',
            ('created_at', '>=', last_week),
            ('updated_at', '>=', last_week)
        ])

        recent_resumes = self.env['vit.partner'].sudo().search([
            '|',
            ('created_at', '>=', last_week),
            ('updated_at', '>=', last_week)
        ])

        base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')

        opp_html = ""
        for opp in recent_opps:
            link = f"{base_url}/opportunities/{opp.id}/detail"
            opp_html += f"""
                <p>
                <strong>案件名：</strong>{opp.subject}<br/>
                <strong>案件内容：</strong>{opp.requirements or 'N/A'}<br/>
                <strong>案件URL：</strong><a href="{link}" target="_blank">{link}</a>
                </p>
            """

        resume_html = ""
        for resume in recent_resumes:
            link = f"{base_url}/resumes/{resume.id}/detail"
            # Tính tuổi từ ngày sinh
            age = ""
            if resume.birthday:
                today = fields.Datetime.now()
                born = resume.birthday
                age = today.year - born.year - ((today.month, today.day) < (born.month, born.day))
                age = f"{age}歳"

            resume_html += f"""
                <p>
                <strong>人財基本情報：</strong>{resume.initial_name or '匿名'}、{'男性' if resume.gender == 'male' else '女性' if resume.gender == 'female' else 'N/A'}{f"、{age}" if age else ""}<br/>
                <strong>人財URL：</strong><a href="{link}" target="_blank">{link}</a>
                </p>
            """

        for user in users:
            setting = self.env['vit.setting'].sudo().search([('user_id', '=', user.id)], limit=1)

            # Nếu có setting và không muốn nhận mail thì bỏ qua
            if setting and not setting.receive_mail:
                continue

            # Xác định có nên gửi phần opportunities và resumes
            send_opp = not setting or setting.receive_opportunities
            send_resume = not setting or setting.receive_resumes

            opp_part = ""
            if send_opp:
                opp_part = f"""
                    {opp_html if recent_opps else '<p>新着案件情報はございません。</p>'}
                """

            resume_part = ""
            if send_resume:
                resume_part = f"""
                    {resume_html if recent_resumes else '<p>新着人財情報はございません。</p>'}
                """

            now = fields.Datetime.now()
            formatted_date = now.strftime("%m月%d日")

            # Nếu không có gì để gửi thì skip
            if not opp_part and not resume_part:
                continue

            # Sử dụng template từ database
            context_data = {
                'company_name': user.company_id.name if user.company_id else '',
                'user_name': user.username,
                'opp_part': opp_part,
                'resume_part': resume_part,
                'opp_count': len(recent_opps),
                'resume_count': len(recent_resumes),
                'formatted_date': formatted_date,
                'user_email': user.email
            }
            
            if common.send_email_from_template('Auto: Weekly News', context_data)['success']:
                _logger.info(f"🔥 Sent weekly mails to {user.email}")
            else:
                _logger.warning(f"🔥 Failed to send weekly mails to {user.email}")

    def send_mail_remind_interview(self):
        _logger.warning("🔥 Scheduled Action remind interview!")

        # Tính toán ngày hiện tại và ngày mai
        now = fields.Datetime.now()
        tomorrow = now + timedelta(days=1)

        # Tìm các interview booking có interview_start trong vòng 1 ngày tới
        interview_bookings = self.env['vit.interviewbooking'].sudo().search([
            ('interview_start', '>=', now),
            ('interview_start', '<=', tomorrow),
            ('is_booking', '=', True)  # Chỉ lấy những booking đã được confirm
        ])

        _logger.warning(f"🔥 Found {len(interview_bookings)} interview bookings to remind")

        for booking in interview_bookings:
            try:
                # Lấy thông tin workflow
                workflow = self.env['vit.workflow'].sudo().search([
                    ('opportunities_id', '=', booking.opp_id.id),
                    ('resumes_id', '=', booking.resume_id.id)
                ], limit=1)

                if not workflow:
                    _logger.warning(f"🔥 No workflow found for booking {booking.id}")
                    continue

                # Lấy thông tin opportunity và user của opportunity
                opportunity = booking.opp_id
                if not opportunity:
                    _logger.warning(f"🔥 No opportunity found for booking {booking.id}")
                    continue

                opp_user = self.env['vit.users'].sudo().browse(int(opportunity.created_by))
                if not opp_user:
                    _logger.warning(f"🔥 No user found for opportunity {opportunity.id}")
                    continue

                # Lấy thông tin resume và user của resume
                partner = booking.resume_id
                if not partner:
                    _logger.warning(f"🔥 No partner found for booking {booking.id}")
                    continue

                partner_user = self.env['vit.users'].sudo().browse(int(partner.created_by))
                if not partner_user:
                    _logger.warning(f"🔥 No user found for partner {partner.id}")
                    continue

                # Chuẩn bị thông tin để gửi mail
                date = booking.interview_start.strftime('%Y/%m/%d')
                time_start = booking.interview_start.strftime('%H:%M')
                time_end = booking.interview_end.strftime('%H:%M')
                meeting_url = booking.meeting_url or "https://~"

                # Thông tin cho opportunity owner
                company_name = opp_user.company_id.name if opp_user.company_id else 'なし'
                user_name = opp_user.username if opp_user.username else 'なし'
                user_email = opp_user.email if opp_user.email else None
                opportunities_name = opportunity.subject if opportunity.subject else 'なし'

                # Thông tin cho resume owner
                company_name_res = partner_user.company_id.name if partner_user.company_id else 'なし'
                user_name_res = partner_user.username if partner_user.username else 'なし'
                user_email_res = partner_user.email if partner_user.email else None
                partner_name = partner.initial_name if partner.initial_name else 'なし'

                # Gửi mail nhắc nhở cho opportunity owner
                if user_email:
                    try:
                        context_data = {
                            'company_name': company_name,
                            'user_name': user_name,
                            'opp_name': opportunities_name,
                            'status_text': f'<p>▼面談日程：{date} {time_start}～{time_end}</p><p>▼面談URL：{meeting_url}</p>',
                            'interview_info': '',
                            'link': f"{self.env['ir.config_parameter'].sudo().get_param('web.base.url')}/opportunities/{opportunity.id}/detail",
                            'user_email': user_email
                        }
                        
                        if common.send_email_from_template('Workflow: Opportunity Status Change', context_data)['success']:
                            _logger.info(f"🔥 Sent remind mail to opportunity owner: {user_email}")
                        else:
                            _logger.error(f"🔥 Failed to send remind mail to opportunity owner {user_email}")
                    except Exception as e:
                        _logger.error(f"🔥 Failed to send remind mail to opportunity owner {user_email}: {str(e)}")

                # Gửi mail nhắc nhở cho resume owner
                if user_email_res:
                    try:
                        context_data = {
                            'company_name': company_name_res,
                            'user_name': user_name_res,
                            'resume_name': partner_name,
                            'status_text': f'<p>▼面談日程：{date} {time_start}～{time_end}</p><p>▼面談URL：{meeting_url}</p>',
                            'interview_info': '',
                            'link': f"{self.env['ir.config_parameter'].sudo().get_param('web.base.url')}/resumes/{partner.id}/detail",
                            'user_email': user_email_res
                        }
                        
                        if common.send_email_from_template('Workflow: Resume Status Change', context_data)['success']:
                            _logger.info(f"🔥 Sent remind mail to resume owner: {user_email_res}")
                        else:
                            _logger.error(f"🔥 Failed to send remind mail to resume owner {user_email_res}")
                    except Exception as e:
                        _logger.error(f"🔥 Failed to send remind mail to resume owner {user_email_res}: {str(e)}")

            except Exception as e:
                _logger.error(f"🔥 Error processing booking {booking.id}: {str(e)}")
                continue

        _logger.warning(f"🔥 Completed remind interview process for {len(interview_bookings)} bookings")



class PaymentHistory(models.Model):
    _name = 'vit.payment_history'
    _description = 'Payment History'
    _order = 'created_at desc'

    user_id = fields.Many2one('vit.users', string='User')
    plan = fields.Char(string='Plan')
    amount = fields.Float(string='Amount')
    payment_date = fields.Datetime(string='Payment Date')
    payment_method = fields.Selection([
        ('credit_card', 'Credit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('other', 'Other')
    ], string='Payment Method', default='credit_card')
    payment_type = fields.Selection([
        ('registration', '入会 (Registration)'),
        ('plan_change', 'プラン変更 (Plan Change)')
    ], string='Payment Type', default='plan_change')
    credit_card_id = fields.Many2one('vit.user_credit_card', string='Credit Card Used')
    card_brand = fields.Char(string='Card Brand', store=True)
    masked_card_number = fields.Char(string='Masked Card Number', store=True)
    card_expiration = fields.Char(string='Card Expiration', store=True)
    transaction_id = fields.Char(string='Transaction ID')
    order_id = fields.Char(string='Order ID')
    status = fields.Selection([
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded')
    ], string='Status', default='pending')
    error_message = fields.Text(string='Error Message')
    error_code = fields.Char(string='Error Code')
    token = fields.Char(string='Payment Token', help='Token used for auto-renewal payments')
    token_key = fields.Char(string='Token Key', help='Token key used for auto-renewal payments')
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now)

    @api.model
    def create(self, vals):
        """Override create method to set created_at and next_payment_date if auto_renew is True"""
        vals['created_at'] = fields.Datetime.now()

        # Set next_payment_date if auto_renew is True
        if vals.get('auto_renew'):
            plan = vals.get('plan')
            if plan:
                # Set next payment date based on plan
                if '月' in plan:  # Monthly plan
                    vals['next_payment_date'] = (fields.Datetime.now() + timedelta(days=30)).date()
                elif '年' in plan:  # Yearly plan
                    vals['next_payment_date'] = (fields.Datetime.now() + timedelta(days=365)).date()

        return super(PaymentHistory, self).create(vals)

    def write(self, vals):
        """Override write method to set updated_at and update next_payment_date if auto_renew changes"""
        vals['updated_at'] = fields.Datetime.now()

        # Update next_payment_date if auto_renew changes
        if 'auto_renew' in vals:
            if vals['auto_renew']:
                plan = self.plan
                if plan:
                    # Set next payment date based on plan
                    if '月' in plan:  # Monthly plan
                        vals['next_payment_date'] = (fields.Datetime.now() + timedelta(days=30)).date()
                    elif '年' in plan:  # Yearly plan
                        vals['next_payment_date'] = (fields.Datetime.now() + timedelta(days=365)).date()
            else:
                vals['next_payment_date'] = False

        return super(PaymentHistory, self).write(vals)

    def process_auto_renewals(self):
        """Process auto renewals for payments that are due"""
        today = fields.Date.today()
        due_payments = self.search([
            ('auto_renew', '=', True),
            ('next_payment_date', '<=', today),
            ('status', '=', 'success')
        ])

        for payment in due_payments:
            try:
                # Lưu ý: Không thể gọi trực tiếp controller từ cron job
                # Thay vì gọi controller, chúng ta xử lý thanh toán trực tiếp ở đây
                # hoặc gọi một service khác

                # TODO: Triển khai xử lý thanh toán tự động
                # Ví dụ:
                # 1. Lấy cấu hình ZEUS
                zeus_config = self.env['vit.zeus_configuration'].sudo().get_active_configuration()
                if not zeus_config:
                    raise Exception("No active ZEUS configuration found")

                # 2. Tạo order ID
                order_id = f"RENEWAL_{payment.user_id.id}_{fields.Datetime.now().strftime('%Y%m%d%H%M%S')}"

                # 3. Gọi API của ZEUS để xử lý thanh toán (cần triển khai)
                # payment_result = self._call_zeus_payment_api(...)

                # 4. Lưu thông tin thanh toán mới (giả lập thành công)
                payment_result = {'success': True, 'transaction_id': f"AUTO_{order_id}"}

                if payment_result.get('success'):
                    # Tạo bản ghi thanh toán mới
                    self.create({
                        'user_id': payment.user_id.id,
                        'plan': payment.plan,
                        'amount': payment.amount,
                        'payment_date': fields.Datetime.now(),
                        'payment_method': 'credit_card',
                        'card_brand': payment.card_brand,
                        'masked_card_number': payment.masked_card_number,
                        'card_expiration': payment.card_expiration,
                        'transaction_id': payment_result.get('transaction_id', ''),
                        'order_id': order_id,
                        'status': 'success',
                        'payment_type': 'renewal',
                        'auto_renew': payment.auto_renew,
                        'token': payment.token,
                        'token_key': payment.token_key
                    })

                    # Update next payment date
                    if '月' in payment.plan:  # Monthly plan
                        payment.next_payment_date = (fields.Datetime.now() + timedelta(days=30)).date()
                    elif '年' in payment.plan:  # Yearly plan
                        payment.next_payment_date = (fields.Datetime.now() + timedelta(days=365)).date()

                    _logger.info(f"Auto renewal processed for payment ID {payment.id}, user ID {payment.user_id.id}")
                else:
                    _logger.error(f"Failed to process auto renewal for payment ID {payment.id}")
            except Exception as e:
                _logger.error(f"Error processing auto renewal for payment ID {payment.id}: {str(e)}")


class ZeusConfiguration(models.Model):
    _name = 'vit.zeus_configuration'
    _description = 'ZEUS Payment Configuration'

    name = fields.Char(string='Configuration Name', required=True)
    environment = fields.Selection([
        ('test', 'Test Environment'),
        ('production', 'Production Environment')
    ], string='Environment', default='test', required=True)
    merchant_id = fields.Char(string='Merchant ID', required=True)
    service_id = fields.Char(string='Service ID', required=True)
    secret_key = fields.Char(string='Secret Key', required=True)
    api_url = fields.Char(string='API URL', required=True)
    token_url = fields.Char(string='Token URL', required=True)
    active = fields.Boolean(string='Active', default=True)
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now)

    @api.model
    def create(self, vals):
        """Override create method to set created_at"""
        vals['created_at'] = fields.Datetime.now()
        return super(ZeusConfiguration, self).create(vals)

    def write(self, vals):
        """Override write method to set updated_at"""
        vals['updated_at'] = fields.Datetime.now()
        return super(ZeusConfiguration, self).write(vals)

    @api.model
    def get_active_configuration(self):
        """Get the active configuration"""
        return self.search([('active', '=', True)], limit=1)

    @api.model
    def initialize_test_configuration(self):
        """Initialize test configuration with default values"""
        if not self.search_count([('environment', '=', 'test')]):
            self.create({
                'name': 'ZEUS Test Configuration',
                'environment': 'test',
                'merchant_id': 'TEST_MERCHANT',
                'service_id': 'TEST_SERVICE',
                'secret_key': 'TEST_SECRET_KEY',
                'api_url': 'https://linkpt.cardservice.co.jp/cgi-bin/token/charge.cgi',
                'token_url': 'https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token_test.js',
                'active': True
            })
            _logger.info("ZEUS test configuration initialized with default values")
        else:
            _logger.info("ZEUS test configuration already exists")

class UserCreditCard(models.Model):
    _name = 'vit.user_credit_card'
    _description = 'User Credit Card Information'
    _order = 'is_default desc, created_at desc'

    user_id = fields.Many2one('vit.users', string='User', required=True, ondelete='cascade')
    send_id = fields.Char(string='MM Send ID')
    masked_card_number = fields.Char(string='Masked Card Number')
    card_brand = fields.Char(string='Card Brand')
    card_expiration = fields.Char(string='Card Expiration')
    is_default = fields.Boolean(string='Is Default', default=False)
    is_active = fields.Boolean(string='Is Active', default=True)
    last_used = fields.Datetime(string='Last Used')
    auto_renew = fields.Boolean(string='Auto Renew', default=False)
    telno = fields.Char(string='Telno')
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now)

    @api.model
    def create(self, vals):
        """Override create method to handle default card"""
        if vals.get('is_default'):
            self.search([
                ('user_id', '=', vals['user_id']),
                ('is_default', '=', True)
            ]).write({'is_default': False})

        vals['created_at'] = fields.Datetime.now()
        vals['updated_at'] = fields.Datetime.now()

        return super(UserCreditCard, self).create(vals)

    def write(self, vals):
        """Override write method to handle default card and update timestamps"""
        if vals.get('is_default'):
            self.search([
                ('user_id', '=', self.user_id.id),
                ('is_default', '=', True),
                ('id', '!=', self.id)
            ]).write({'is_default': False})

        vals['updated_at'] = fields.Datetime.now()

        return super(UserCreditCard, self).write(vals)

    def set_as_default(self):
        """Set this card as default for the user"""
        self.ensure_one()
        self.write({'is_default': True})

    def deactivate(self):
        """Deactivate this card"""
        self.ensure_one()
        self.write({'is_active': False})

    def activate(self):
        """Activate this card"""
        self.ensure_one()
        self.write({'is_active': True})

    def update_last_used(self):
        """Update last used timestamp"""
        self.ensure_one()
        self.write({'last_used': fields.Datetime.now()})

    @api.model
    def get_default_card(self, user_id):
        """Get default card for user"""
        return self.search([
            ('user_id', '=', user_id),
            ('is_default', '=', True),
            ('is_active', '=', True)
        ], limit=1)

    @api.model
    def get_active_cards(self, user_id):
        """Get all active cards for user"""
        return self.search([
            ('user_id', '=', user_id),
            ('is_active', '=', True)
        ])

class PaymentHistoryAutoRenew(models.Model):
    _name = 'vit.payment_history_autorenew'
    _description = 'Gửi thanh toán định kỳ tự động'

    def generate_sendid(self, length=20):
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    def parse_response(self, response_text, user_id):
        # Chuyển response thành dictionary
        response_dict = {}
        for line in response_text.splitlines():
            if "=" in line:
                key, value = line.split("=", 1)
                response_dict[key.strip()] = value.strip()

        # Kiểm tra trạng thái
        if response_dict.get("rel") == "Success_order":
            status = "success"
            order_id = response_dict.get("ordd")
            money = response_dict.get("money")
            sendid = response_dict.get("sendid")
            sendpoint = response_dict.get("sendpoint")



            masked_card_number = self.env['vit.user_credit_card'].search([('send_id', '=', sendid)], limit=1).masked_card_number
            expiration = self.env['vit.user_credit_card'].search([('send_id', '=', sendid)], limit=1).card_expiration

            try:
                payment_history = self.env['vit.payment_history'].create({
                    'user_id': user_id,
                    'payment_method': 'credit_card',
                    'payment_type': 'plan_change',
                    'masked_card_number': masked_card_number,
                    'card_expiration': expiration,
                    'transaction_id': sendpoint,
                    'order_id': order_id,
                    'status': status,
                    'payment_date': fields.Datetime.now(),
                    'amount': money,
                })
                self.env.cr.commit()
                _logger.warning(f"🔥 Created payment_history ID: {payment_history.id}")
            except Exception as e:
                _logger.warning(f"🔥 Failed: {e}")
            _logger.warning(f"🔥 Done")
            return {
                "status": "success",
                "order_id": response_dict.get("ordd"),
                "money": response_dict.get("money"),
                "sendid": response_dict.get("sendid"),
                "sendpoint": response_dict.get("sendpoint")
            }
        else:
            status = "failed"
            order_id = response_dict.get("ordd")
            sendid = response_dict.get("sendid")
            sendpoint = response_dict.get("sendpoint")
            error_code = response_dict.get("err_code")
            money = response_dict.get("money")
            _logger.warning(f"🔥 sendid: {sendid}")

            masked_card_number = self.env['vit.user_credit_card'].search([('send_id', '=', sendid)], limit=1).masked_card_number
            expiration = self.env['vit.user_credit_card'].search([('send_id', '=', sendid)], limit=1).card_expiration
            _logger.warning(f"🔥 masked_card_number: {masked_card_number}")
            _logger.warning(f"🔥 expiration: {expiration}")
            try:
                payment_history = self.env['vit.payment_history'].create({
                    'user_id': user_id,
                    'payment_method': 'credit_card',
                    'payment_type': 'plan_change',
                    'masked_card_number': masked_card_number,
                    'card_expiration': expiration,
                    'transaction_id': sendpoint,
                    'order_id': order_id,
                    'status': status,
                    'error_code': error_code,
                    'amount': money,
                    'payment_date': fields.Datetime.now(),
                })
                self.env.cr.commit()
                _logger.warning(f"🔥 Created payment_history ID: {payment_history.id}")
            except Exception as e:
                _logger.warning(f"🔥 Failed: {e}")
            _logger.warning(f"🔥 Failed")
            return {
                "status": "failed",
                "order_id": response_dict.get("ordd"),
                "error_code": response_dict.get("err_code"),
                "sendid": response_dict.get("sendid"),
                "sendpoint": response_dict.get("sendpoint")
            }

    def run_auto_renew_cron(self):
        _logger.warning("🔥 Scheduled Action auto-renew payment đã chạy!")


        # Lấy user_id từ payment_history có auto_renew và thành công
        user_ids = self.env['vit.payment_history'].search([
            ('status', '=', 'success')
        ]).mapped('user_id')

        user_ids = user_ids.ids

        _logger.warning(f"🔥 User IDs: {user_ids}")

        # Lọc lại user active và có plan
        users = self.env['vit.users'].search([
            ('id', 'in', user_ids),
            ('active', '=', True),
            ('plan_id', '!=', None)
        ])

        _logger.warning(f"🔥 Users: {users}")

        for user in users:
            credit_card = self.env['vit.user_credit_card'].search([('user_id', '=', user.id), ('is_active', '=', True), ('auto_renew', '=', True), ('is_default', '=', True)], limit=1)
            amout = self.env['vit.plans'].sudo().search([('id', '=', user.plan_id.id)], limit=1).price
            amout = int(amout)
            _logger.warning(f"🔥 Amout: {amout}")
            _logger.warning(f"🔥 Credit Card: {credit_card}")
            if credit_card:
                url = "https://secure2-sandbox.cardservice.co.jp/cgi-bin/secure.cgi"
                send_point = self.generate_sendid()
                data = {
                    "clientip": os.getenv('ZEUS_CLIENT_IP'),
                    "cardnumber": "8888888888888882",
                    "money": str(amout) if amout > 0 else 0,
                    "send": "mall",
                    "telno": credit_card.telno,
                    "email": user.email,
                    "sendid": credit_card.send_id,
                    "sendpoint": send_point,
                    "pubsec": "YES",
                    "Printord": "yes",
                    "div": "01",
                    "return_value": "yes"
                }
                headers = {
                    "Content-Type": "application/x-www-form-urlencoded; charset=Shift_JIS"
                }

                response = requests.post(url, data=data, headers=headers)
                _logger.warning(f"🔥 Response: {response.text}")
                response_dict = self.parse_response(response.text, user.id)

class EmailTemplate(models.Model):
    _name = 'vit.email_template'
    _description = 'Email Template'

    name = fields.Char(string='Template Name', required=True)
    subject = fields.Char(string='Subject', required=True)
    body_html = fields.Html(string='Body HTML', required=True)
    is_active = fields.Boolean(string='Active', default=True)
    created_at = fields.Datetime(string='Created At', default=fields.Datetime.now)
    updated_at = fields.Datetime(string='Updated At', default=fields.Datetime.now)






