/* Law Page Styles */

/* General Law page styles */
.law-page .guide-section {
    padding-top: 1rem;
}

.law-page .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.law-page .table {
    margin-bottom: 0;
}

.law-page .table td {
    padding: 1rem;
    vertical-align: top;
}

.law-page .table td:first-child {
    font-weight: bold;
    color: rgba(84, 110, 122, 0.87);
    width: 25%;
}

/* Links styling */
.law-page .table a {
    color: #007bff;
    text-decoration: none;
}

.law-page .table a:hover {
    text-decoration: underline;
}

/* iPhone 12 Pro responsive styles */
@media screen and (min-width: 375px) and (max-width: 390px) {

    /* Fix viewport and container width issues */
    .law-page {
        width: 100% !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .law-page .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Container adjustments */
    .law-page .guide-section {
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin: 0 !important;
        width: 100% !important;
    }

    .law-page .guide-section .row {
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
    }

    .law-page .guide-section .card {
        margin: 10px 5px !important;
        border-radius: 8px;
        width: calc(100% - 10px) !important;
        max-width: calc(100% - 10px) !important;
    }

    .law-page .guide-section .col-12.col-md-8 {
        padding: 0 5px !important;
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* Table responsive layout */
    .law-page .guide-section .table {
        font-size: 14px;
        width: 100% !important;
        max-width: 100% !important;
        table-layout: fixed !important;
        word-wrap: break-word !important;
    }

    .law-page .guide-section .table td {
        padding: 12px 8px !important;
        border-bottom: 1px solid #e9ecef;
        vertical-align: top;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
    }
    
    /* Make table responsive by stacking cells */
    .law-page .guide-section .table tr {
        display: block;
        margin-bottom: 20px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background: #f8f9fa;
    }
    
    .law-page .guide-section .table td:first-child {
        display: block;
        width: 100% !important;
        background: rgba(84, 110, 122, 0.1);
        font-weight: bold !important;
        color: rgba(84, 110, 122, 0.87) !important;
        border-bottom: 1px solid #dee2e6;
        border-radius: 6px 6px 0 0;
        padding: 10px 12px !important;
    }
    
    .law-page .guide-section .table td:last-child {
        display: block;
        width: 100% !important;
        background: white;
        border-radius: 0 0 6px 6px;
        padding: 12px !important;
        line-height: 1.5;
    }
    
    /* Nested table for addresses */
    .law-page .guide-section .table .table {
        margin: 0 !important;
        background: transparent;
    }
    
    .law-page .guide-section .table .table tr {
        display: table-row !important;
        margin-bottom: 8px !important;
        border: none !important;
        background: transparent !important;
    }
    
    .law-page .guide-section .table .table td {
        display: table-cell !important;
        padding: 4px 8px !important;
        border: none !important;
        background: transparent !important;
        font-size: 13px;
    }
    
    .law-page .guide-section .table .table td:first-child {
        width: 30% !important;
        font-weight: bold;
        color: rgba(84, 110, 122, 0.87);
        background: transparent !important;
        border-radius: 0 !important;
    }
    
    .law-page .guide-section .table .table td:last-child {
        width: 70% !important;
        background: transparent !important;
        border-radius: 0 !important;
    }
    
    /* Links styling */
    .law-page .guide-section .table a {
        color: #007bff;
        text-decoration: none;
        word-break: break-all !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
        max-width: 100% !important;
        display: inline-block !important;
    }

    .law-page .guide-section .table a:hover {
        text-decoration: underline;
    }

    /* Fix any text overflow */
    .law-page .guide-section .table td * {
        max-width: 100% !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }
    
    /* Breadcrumbs adjustment */
    .law-page .breadcrumbs {
        margin-left: 0.5rem !important;
        font-size: 14px;
    }
    
    /* Card body padding */
    .law-page .guide-section .card-body {
        padding: 15px;
    }
}

/* Tablet responsive styles */
@media screen and (min-width: 768px) and (max-width: 1023px) {
    .law-page .guide-section .table td:first-child {
        width: 30%;
    }
}
