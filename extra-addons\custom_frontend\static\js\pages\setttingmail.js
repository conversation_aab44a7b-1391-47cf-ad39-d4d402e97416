import { checkToastrMessage } from "/custom_frontend/static/js/common/Toastr.js";
import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const SettingMail = {
    'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: '登録・管理', link: null },
        { text: '会社データ管理', link: null },
        { text: 'メール受信設定', link: null, current: true }
    ])}
    <div class="container-fluid">
        <div class="mail-settings-container">
            <div class="d-flex justify-content-end align-items-center">
                <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a href="/companies/manage/edit">会社データ</a></li>
                        <li><a href="/users/edit">プロフィール</a></li>
                        <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a href="/users/profile/edit_password">パスワード</a></li>
                        <li><a class="active" href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a href="/mypage/plan">プラン</a></li>
                        <li><a href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
            </div>
            <!-- Settings form -->
            <div class="container-fluid grabient pt-5 position-relative">
                <div class="row mb-4 mb-md-0">
                    <div class="d-md-block col-md-4 col-lg-3 side-menu-contents">
                        <div class="card px-3 pb-3 side-card collapsible">
                            <ul class="collapsible mb-0">
                                <div class="d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                                <li><a class="d-block py-1 px-3 active" aria-current="page" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                                <li class="my-md-1" hidden><a class="d-block py-1 px-3" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                        <div class="card mb-5 px-md-3">
                            <div class="card-body py-5">
                                <div class="setting-row">
                                    <div class="d-flex align-items-center" style="padding-left: 30px;">
                                        <span class="setting-label me-5"><strong>新着情報メール</strong></span>
                                        <div class="form-check form-check-inline ms-5" style="padding-left: 70px;">
                                            <input class="form-check-input" type="radio" name="newPersonnelMail" id="receivePersonnel"
                                                :value="true" v-model="newPersonnelMail">
                                            <label class="form-check-label" for="receivePersonnel">受信する</label>
                                        </div>
                                        <div class="form-check form-check-inline ms-5" style="padding-left: 40px;">
                                            <input class="form-check-input" type="radio" name="newPersonnelMail"
                                                id="notReceivePersonnel" :value="false" v-model="newPersonnelMail">
                                            <label class="form-check-label" for="notReceivePersonnel">受信しない</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-4">
                                <button class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light" @click="updateMailSetting">設定変更</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css" />
<link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css">
<link rel="stylesheet" href="/custom_frontend/static/css/settingmail.css">
    `,
    data() {
        return {
            newPersonnelMail: null,
            isMobileMenuOpen: false
        }
    },
    methods: {
        // Add methods if needed
    },
    mounted() {
        this.user_id = userInfo ? userInfo.user_id : null;
        this.getSetting();
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });
    },
    methods:{
        async getSetting(){
            try{
                const res = await fetch('/api/get_setting', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: parseInt(this.user_id)
                    })
                });

                const result = await res.json();
                console.log("Get setting: ", result);

                if(result.result.success){
                    this.newPersonnelMail = result.result.receive_mail;
                } else{
                    console.log("Fail to get mail setting.");
                }
            } catch(error){
                console.log("Error API: ", error);
            }
        },

        async updateMailSetting() {
            try {
                const res = await fetch('/api/setting_email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: parseInt(this.user_id),
                        receive_mail: this.newPersonnelMail,
                    })
                });

                const result = await res.json();
                if (result.result.success) {
                    console.log("Update setting successful: ", result);
                    window.toastr.success('設定を変更しました。');
                } else {
                    console.error("Failed to update setting: ", result);
                    window.toastr.error(result.result.message);
                }
            } catch (error) {
                console.error("Error API: ", error);
                alert("エラーが発生しました。");
            }
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function() {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function() {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function() {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function() {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        }
    }
}

export default SettingMail