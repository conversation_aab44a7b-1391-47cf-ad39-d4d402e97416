import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const RequestSuccess = {
	'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: 'コンシェルジュサービス', link: '/request_select' },
        { text: '{{title}}', link: null, current: true }
    ])}
    <div class="container-fluid">
        <div class="mail-settings-container">
            <!-- Settings form -->
            <h3 style="text-align: center; padding-top: 5vw;">申し込みが完了しました</h3>
            <h3 style="text-align: center;">担当者からご連絡を差し上げますので、お待ちください。</h3>

            <div class="text-center" style="padding-top: 10vw;">
                <button class="btn btn-default waves-effect waves-light" style="color: white;"
                    @click="goToHome">トップページへ</button>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/settingmail.css" />
	`,
    data() {
        return {
            newProjectMail: null,
            newPersonnelMail: null,
            type: this.$route.params.type
        }
    },
    computed: {
        title() {
            const type = this.type;
            if (type === 'opportunity') {
                return 'マッチングリクエスト(案件)';
            } else if (type === 'resume') {
                return 'マッチングリクエスト(人財)';
            }
            return '【Mi52】マッチングリクエスト';
        }
    },
    methods: {
        goToHome() {
            this.$router.push('/home');
        }
    }
}

export default RequestSuccess