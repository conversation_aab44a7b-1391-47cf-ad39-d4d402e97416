import { createSingleBreadcrumb } from "../../utils/breadcrumbHelper.js";

const Userguild = {
	'template': `
       <main class="margin-header userguide-page">
		${createSingleBreadcrumb('ご利用ガイドライン', 'container-fluid', 'padding: 0;')}
	<div class="container-fluid grabient pt-5">
		<div class="row">
			<div class="col-12">
				<section class="guide-section pt-7 userguide-text-gray" id="guide-section01">
					<div class="row">
						<div class="col-12 col-md-8 mb-5 mx-auto">
							<div class="card sp_sides_uniter">
								<div class="card-body userguide-content">
									<div class="userguide-intro">
										<p>皆様が安心してご利用できるように、本ガイドラインを作成しました。ご理解の上ご利用願います。</p>
										<p>また、必要に応じて都度更新します。</p>
									</div>

									<div class="userguide-section">
										<h5 class="section-title">禁止</h5>

										<div class="category-section">
											<h6 class="category-title">案件・人財登録</h6>
											<ul class="prohibition-list">
												<li>・虚偽情報の登録</li>
												<li>・重複登録
													<ul class="sub-list">
														<li>複数メールIDによる同一会社登録</li>
														<li>同一会社による案件及び人財重複登録</li>
													</ul>
												</li>
												<li>・マッチング進行中の登録情報更新</li>
												<li>・案件やスキルシートに会社や個人を特定できる情報（名称、メールアドレス、電話番号など）の掲載</li>
											</ul>
										</div>

										<div class="category-section">
											<h6 class="category-title">マッチング</h6>
											<ul class="prohibition-list">
												<li>・常識に逸脱した、大量の同時マッチング依頼</li>
											</ul>
										</div>

										<div class="disclaimer">
											<p>＊ガイドラインを逸脱した状況を継続している、その他の理由で運営チームが不適切と判断した場合、該当企業に対しサービスの停止することもあります。</p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</section>
			</div>
		</div>
	</div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/our_services/userguild.css"/>
    `,
}
export default Userguild