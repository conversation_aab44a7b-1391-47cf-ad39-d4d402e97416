import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const companyDetail = {
    template: `
        <main class="pb-3 margin-header" id="vue-app" data-v-app="" style="background-color: #eeeeee; margin-top: 80px" >
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: '案件・人財を探す', link: '/opportunities/active' },
                { text: '会社詳細', link: null, current: true }
            ], 'container-fluid', '')}
            <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5 mt-3">
                <div class="card px-3 px-md-4 pt-5 form-card sp_sides_uniter">
                    <div class="content">
                        <div class="d-flex align-items-center">
                            <div class="d-flex align-items-center nowrap" data-toggle="tooltip" title="" data-original-title="ブックマーク">
                                <a id="anchor-bookmark48810" class="z-2 py-2 d-flex align-items-center" data-remote="true" rel="nofollow"
                                    @click="toggleBookmark">
                                    <i class="material-icons ml-3" style="color: #1072e9;">
                                        {{ isBookmarked ? 'bookmark' : 'bookmark_border' }}
                                    </i>
                                </a>
                                <span class="pl-1 font-middle z-2 py-2 pr-2" style="line-height: 1;">{{numberReact}}</span>
                            </div>
                            <div class="company-name border-bottom">
                                会社名：{{name}}
                            </div>
                        </div>

                        <div class="stats-container d-flex mb-3">
                            <div class="stats-section col mr-3 border py-3">
                                <!-- Info section for this stats section -->
                                <div class="info-section border-bottom mb-3 pb-2">
                                    <a v-if="opp.length > 0" :href="'/company/' + id + '/list_opportunities'" class="registration-link">
                                        登録中案件: ({{opp.length}} 件)
                                    </a>
                                    <span v-else class="registration-link disabled" style="color: #999; cursor: not-allowed;">
                                        登録中案件: ({{opp.length}} 件)
                                    </span>
                                </div>

                                <h3>利用実績</h3>
                                <div class="stat-item border-bottom mb-3">
                                    <span>会社評価：</span>
                                    <span>{{ averageScores }} <i class="material-icons" style="color: #1072e9; font-size: 16px; vertical-align: middle;">star</i></span>
                                </div>
                                <div class="stat-item border-bottom mb-3">
                                    <span>面談数：</span>
                                    <span>{{ interview_count }}</span>
                                </div>
                                <div class="stat-item border-bottom mb-3">
                                    <span>成約数：</span>
                                    <span>{{ successful_contracts }}</span>
                                </div>
                            </div>

                            <div class="stats-section col mr-3 border py-3">
                                <!-- Info section for this stats section -->
                                <div class="info-section border-bottom mb-3 pb-2">
                                    <a v-if="res.length > 0" :href="'/company/' + id + '/list_resumes'" class="registration-link">
                                        登録中人財: ({{res.length}} 件)
                                    </a>
                                    <span v-else class="registration-link disabled" style="color: #999; cursor: not-allowed;">
                                        登録中人財: ({{res.length}} 件)
                                    </span>
                                </div>

                                <h3>基本情報</h3>
                                <div class="stat-item border-bottom mb-3">
                                    <span>企業URL：</span>
                                    <a :href="company_site_url">{{ company_site_url }}</a>
                                </div>
                                <div class="stat-item border-bottom mb-3">
                                    <span>設立年：</span>
                                    <span>{{ established_year }} 年</span>
                                </div>
                                <div class="stat-item border-bottom mb-3">
                                    <span>従業員数：</span>
                                    <span>{{ employee_count }}人</span>
                                </div>
                                <div class="stat-item border-bottom mb-3">
                                    <span>資本金：</span>
                                    <span>{{ charter_capital }} 万円</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/company/company_detail.css" type="text/css"/>
    `,
    props: ["id"],
    data() {
        return {
            established_year: "",
            employee_count: "",
            charter_capital: "",
            has_dispatch_license: "",
            company_site_url: "",
            response_rate: "",
            interview_count: "",
            successful_contracts: "",
            high_ratings_count: "",
            name: "",
            res: [],
            opp: [],
            averageScores: 0,
            isBookmarked: false,
            numberReact: 0,
            userID: userInfo ? userInfo.user_id : null,
            company_id: this.id,
        }
    },
    async mounted() {
        await this.getAverageScores();
        await this.getCompany();
        await this.getReaction();
    },
    methods: {
        async getCompany() {
            try {
                const response = await fetch('/api/get_company', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: this.id,
                    }),
                });

                const com = await response.json();
                if (com.result.success) {
                    console.log('Company hihi', com.result.data);
                    this.established_year = com.result.data.established_year ? com.result.data.established_year : "N/A";
                    this.employee_count = com.result.data.employee_count ? com.result.data.employee_count : "N/A";
                    this.charter_capital = com.result.data.charter_capital ? com.result.data.charter_capital : "N/A";
                    this.company_site_url = com.result.data.company_site_url ? (com.result.data.company_site_url.startsWith("http://") || com.result.data.company_site_url.startsWith("https://")
                        ? com.result.data.company_site_url
                        : ("https://" + com.result.data.company_site_url)) : "";
                    this.has_dispatch_license = com.result.data.has_dispatch_license ? com.result.data.has_dispatch_license : "N/A";
                    this.response_rate = com.result.data.response_rate ? com.result.data.response_rate : "0";
                    this.interview_count = com.result.data.interview_count ? com.result.data.interview_count : "0";
                    this.successful_contracts = com.result.data.successful_contracts ? com.result.data.successful_contracts : "0";
                    this.high_ratings_count = com.result.data.high_ratings_count ? com.result.data.high_ratings_count : "N/A";
                    this.name = com.result.data.name ? com.result.data.name : "N/A";
                    this.res = com.result.data.res ? com.result.data.res : [];
                    this.opp = com.result.data.opp ? com.result.data.opp : [];
                } else {
                    console.log(com.result.message);
                }
            } catch (error) {
                console.error('Có lỗi xảy ra khi lấy dữ liệu:', error);
            }
        },

        async toggleBookmark() {
            try {
                const response = await fetch('/api/react_company', {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        user_id: parseInt(this.userID),
                        company_id: parseInt(this.id),
                    }),
                });

                const result = await response.json();
                console.log(result);
                if (result.result.success) {
                    this.getReaction();
                }
            } catch (error) {
                console.error("Bookmark failed:", error);
            }
        },

        async getReaction() {
            const response = await fetch("/api/react_count_company", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ user_id: parseInt(this.userID), company_id: parseInt(this.id) })
            });

            const result = await response.json();
            console.log("react: ", result);
            if (result.result.success) {
                this.numberReact = result.result.likes;
                this.isBookmarked = result.result.liked;
            }
        },

        async getAverageScores() {
            try {
                const response = await fetch("/api/get_average_scores", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ company_id: parseInt(this.id) })
                });

                const result = await response.json();
                console.log("average scores: ", result);
                if (result.result.success) {
                    this.averageScores = result.result.data;
                } else {
                    console.log(result.result.message);
                }
            } catch (error) {
                console.error("Error fetching average scores:", error);
            }
        }

    }
}

export default companyDetail;