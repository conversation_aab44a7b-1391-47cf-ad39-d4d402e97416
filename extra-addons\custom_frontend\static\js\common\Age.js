const AgeSlider = {
    props: {
        min: { type: Number, default: 20 },
        max: { type: Number, default: 80 },
        step: { type: Number, default: 1 },
        startValues: {
            type: Array,
            default: () => [null, null] // null = không giới hạn
        },
    },
    data() {
        return {
            selectedRange: [
                this.startValues[0] !== null && this.startValues[0] !== '未選択' ? parseInt(this.startValues[0]) : this.min,
                this.startValues[1] !== null && this.startValues[1] !== '未選択' ? parseInt(this.startValues[1]) : this.max
            ],
            slider: null,
        };
    },
    watch: {
        startValues: {
            handler(newValues) {
                if (!Array.isArray(newValues)) return;

                const newMin = newValues[0] !== null && newValues[0] !== '未選択' ? parseInt(newValues[0]) : this.min;
                const newMax = newValues[1] !== null && newValues[1] !== '未選択' ? parseInt(newValues[1]) : this.max;

                if (newMin !== this.selectedRange[0] || newMax !== this.selectedRange[1]) {
                    this.selectedRange = [newMin, newMax];
                    if (this.slider) {
                        this.slider.set(this.selectedRange);
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) {
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedRange,
                    connect: true,
                    range: { min: this.min, max: this.max },
                    step: this.step,
                    margin: this.step,
                });

                this.slider.on("update", (values, handle) => {
                    let newValue = Math.round(parseFloat(values[handle]));

                    if (handle === 0) {
                        this.selectedRange[0] = newValue;
                    } else {
                        this.selectedRange[1] = newValue;
                    }

                    // Kiểm tra nếu là giá trị tối thiểu/tối đa thì đặt thành null
                    const ageMin = this.selectedRange[0] === this.min ? null : this.selectedRange[0];
                    const ageMax = this.selectedRange[1] === this.max ? null : this.selectedRange[1];

                    this.$emit("update:age", [ageMin, ageMax]);
                });
            }
        });
    },
    computed: {
        displayMin() {
            return this.selectedRange[0] === this.min ? "下限なし" : this.selectedRange[0] + "歳";
        },
        displayMax() {
            return this.selectedRange[1] === this.max ? "上限なし" : this.selectedRange[1] + "歳";
        },
        displayRange() {
            return `${this.displayMin} 〜 ${this.displayMax}`;
        },
    },
    template: `
      <div class="mb-5">
        <p class="d-flex justify-content-center" id="age-range">
          {{ displayRange }}
        </p>
        <div class="slider-styled mx-4" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>20歳</span><span>80歳</span>
        </div>
      </div>
    `,
};
export default AgeSlider;
