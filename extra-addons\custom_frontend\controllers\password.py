from odoo import http
from odoo.http import request
import secrets
from datetime import datetime, timedelta
import os
import hashlib
import base64
from .common import send_email_from_template

class CustomMail(http.Controller):
    @http.route('/api/send_email', type='json', auth="public", methods=['POST'])
    def send_email(self):
        data = request.httprequest.get_json(silent=True)
        email_to = data.get('email')

        if not email_to:
            return {'success': False, 'message': 'Missing email'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email_to)], limit=1)
        if not user:
            return {'success': False, 'message': 'Email not found'}

        reset_token = secrets.token_urlsafe(32)
        expiry_time = datetime.now() + timedelta(hours=1)

        user.sudo().write({
            'token': reset_token,
            'token_expiry': expiry_time,
        })

        reset_link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}users/password/edit?token={reset_token}"

        # Sử dụng template từ database
        context_data = {
            'company_name': user.company_id.name if user.company_id else '',
            'user_name': user.username,
            'reset_link': reset_link,
            'user_email': email_to
        }
        
        result = send_email_from_template('Password: Reset', context_data)
        
        if not result['success']:
            return {'success': False, 'message': result['message']}

        return {'success': True, 'message': 'Email sent successfully'}

    @http.route('/api/reset_password_validate', type='json', auth="public", methods=['POST'])
    def reset_password_validate(self):
        data = request.httprequest.get_json(silent=True)
        token = data.get('token')

        if not token:
            return {'success': False, 'message': 'Missing token'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('token', '=', token)], limit=1)
        if not user or user.token_expiry < datetime.now():
            return {'success': False, 'message': 'Invalid or expired token'}

        return {'success': True, 'message': 'Token valid'}

    @http.route('/api/reset_password', type='json', auth="public", methods=['POST'])
    def reset_password(self):
        data = request.httprequest.get_json(silent=True)
        token = data.get('token')
        new_password = data.get('new_password')

        if not token or not new_password:
            return {'success': False, 'message': 'Missing token or password'}

        # Tìm user theo token
        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('token', '=', token)], limit=1)
        if not user or user.token_expiry < datetime.now():
            return {'success': False, 'message': 'Invalid or expired token'}

        # Băm mật khẩu trước khi lưu vào cơ sở dữ liệu
        hashed_password = self._hash_password(new_password)

        # Cập nhật mật khẩu mới
        user.sudo().write({
            'password': hashed_password,
            'token': False,  # Xóa token sau khi dùng
            'token_expiry': False,
        })

        return {'success': True, 'message': 'Password reset successfully'}

    def _hash_password(self, password, salt=None):
        #Sử dụng PBKDF2 để băm mật khẩu - không cần thư viện ngoài
        if not salt:
            # Tạo salt 16 bytes
            salt = secrets.token_bytes(16)
        # Sử dụng PBKDF2 với thuật toán SHA-256, 100000 vòng lặp
        # và độ dài key đầu ra 32 bytes
        key = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000,  # số vòng lặp, càng cao càng an toàn nhưng càng chậm
            dklen=32  # độ dài key
        )

        # Mã hóa salt và key để có thể lưu vào DB
        salt_b64 = base64.b64encode(salt).decode('utf-8')
        key_b64 = base64.b64encode(key).decode('utf-8')

        # Trả về chuỗi có định dạng: algorithm$iterations$salt$key
        password_hash = f"pbkdf2_sha256$100000${salt_b64}${key_b64}"
        return password_hash
