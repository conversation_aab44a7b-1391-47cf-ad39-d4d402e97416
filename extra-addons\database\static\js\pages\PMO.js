import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const PMO = {
    'template': `
<div style="margin-top: 79px;">
${createBreadcrumb([
    { text: 'Mi52TOP', link: '/' },
    { text: '案件を探す', link: null, current: true }
])}
<div class="container-fluid title py-2 py-md-4">
    <h1 class="mb-0">案件を探す</h1>
</div>
<div class="container-fluid grabient pt-5">
    <div class="row">
        <div class="d-none d-md-block col-12 col-md-4 col-lg-3" id="side-search">
            <div class="card py-4 side-card"><i
                    class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
                <div class="mb-3">
                    <div class="reset_link_area-pc mx-3 d-none"><a
                            class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light"
                            href="/opportunities/active"><span>保存済み条件で検索</span></a></div>
                    <div class="reset_link_area-sp d-none"><a class="reset_search_condition mx-0"
                                                              href="/opportunities/active"><span>保存条件で検索</span></a>
                    </div>
                </div>
                <form class="new_opportunity_search_condition" id="opportunity_search_condition_form" novalidate=""
                      action="/opportunity_search_conditions/search" accept-charset="UTF-8" method="get"><input
                        name="utf8" type="hidden" autocomplete="off" value="✓"><input id="hidden_unit_price_min"
                                                                                      autocomplete="off" type="hidden"
                                                                                      name="opportunity_search_condition[unit_price_min]"
                                                                                      value=""><input
                        id="hidden_unit_price_max" autocomplete="off" type="hidden"
                        name="opportunity_search_condition[unit_price_max]" value="">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 px-0 px-md-3"><label
                                    class="font-middle mb-3 ex-bold">フリーワード</label>
                                <div class="mb-4">
                                    <div class="vertical-top d-inline-block w-100" data-html="true"
                                                           data-toggle="tooltip" title=""
                                                           data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
                                        <div class="mb-1"><input placeholder="キーワード" class="form-control"
                                                                 autocomplete="off" id="free_keyword_field" type="text"
                                                                 name="opportunity_search_condition[free_keyword]">
                                        </div>
                                    </div>
                                    <div class="mb-3"><span class="font-middle">を含む</span></div>
                                    <div class="mb-1"><input placeholder="除外キーワード" class="form-control"
                                                             autocomplete="off" id="negative_keyword_field" type="text"
                                                             name="opportunity_search_condition[negative_keyword]">
                                    </div>
                                    <div class="mb-3"><span class="font-middle">を除く</span></div>
                                </div>
                                <div class="mb-5"><label class="font-middle mb-3 ex-bold">得意領域</label>
                                    <div class="consul_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">コンサル</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_up</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_consul]"
                                           id="opportunity_search_condition_accordion_open_consul" value="yes">
                                    <div class="accordion_contents_consul" style="display: block;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul0" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_pmo"><label id="opp_categories_field_label_0"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul1" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_pmpl"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="consul1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul2" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_strategy"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="consul2">戦略</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul3" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_work"><label id="opp_categories_field_label_3"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="consul3">業務</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul4" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_it"><label id="opp_categories_field_label_4"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="consul4">IT</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul5" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_rpa"><label id="opp_categories_field_label_5"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul5">RPA</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul6" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_erp"><label id="opp_categories_field_label_6"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul6">ERP・PKG</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul7" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_security"><label
                                                        id="opp_categories_field_label_7"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="consul7">セキュリティ</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dev_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"><span
                                            class="font-size-middle ex-bold">開発</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_up</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_dev]"
                                           id="opportunity_search_condition_accordion_open_dev" value="yes">
                                    <div class="accordion_contents_dev" style="display: block;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev0" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_pmo"><label id="opp_categories_field_label_0"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev1" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_pmpl"><label id="opp_categories_field_label_1"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev2" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_architect"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev2">アーキテクト</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev3" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_bridge_se"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev3">ブリッジSE</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev4" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_work"><label id="opp_categories_field_label_4"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev4">業務システム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev5" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_web"><label id="opp_categories_field_label_5"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev5">Webシステム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev6" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_ios"><label id="opp_categories_field_label_6"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev6">iOS</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev7" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_android"><label id="opp_categories_field_label_7"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev7">Android</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev8" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_mainframe"><label
                                                        id="opp_categories_field_label_8"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev8">メインフレーム</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev9" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_control"><label id="opp_categories_field_label_9"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev9">制御</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev10" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_embedded"><label
                                                        id="opp_categories_field_label_10"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev10">組込</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev11" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_iot"><label id="opp_categories_field_label_11"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev11">IoT</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev12" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_ai_dl"><label id="opp_categories_field_label_12"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="dev12">AI・DL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev13" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_frontend"><label
                                                        id="opp_categories_field_label_13"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev13">フロントエンド</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev14" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_backend"><label id="opp_categories_field_label_14"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev14">バックエンド</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev15" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_game"><label id="opp_categories_field_label_15"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev15">ゲーム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev16" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_rpa"><label id="opp_categories_field_label_16"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev16">RPA</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev17" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_erp"><label id="opp_categories_field_label_17"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev17">ERP</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev18" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_operation"><label
                                                        id="opp_categories_field_label_18"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev18">運用・保守</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev19" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_test"><label id="opp_categories_field_label_19"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev19">テスト</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="infra_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">インフラ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_up</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_infra]"
                                           id="opportunity_search_condition_accordion_open_infra" value="yes">
                                    <div class="accordion_contents_infra" style="display: block;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra0" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_pmo"><label id="opp_categories_field_label_0"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="infra0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra1" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_pmpl"><label id="opp_categories_field_label_1"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="infra1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra2" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_server"><label id="opp_categories_field_label_2"
                                                                                       class="custom-control-label anavi-select-label mb-3"
                                                                                       for="infra2">サーバー</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra3" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_network"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra3">ネットワーク</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra4" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_db"><label id="opp_categories_field_label_4"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="infra4">データベース</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra5" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_cloud"><label id="opp_categories_field_label_5"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="infra5">クラウド</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra6" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_virtualized"><label
                                                        id="opp_categories_field_label_6"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra6">仮想化</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra7" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_mainframe"><label
                                                        id="opp_categories_field_label_7"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra7">メインフレーム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra8" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_operation"><label
                                                        id="opp_categories_field_label_8"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra8">運用・保守</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="helpdesk_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">ヘルプデスク</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_helpdesk]"
                                           id="opportunity_search_condition_accordion_open_helpdesk" value="no">
                                    <div class="accordion_contents_helpdesk" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk0"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_self"><label
                                                        id="opp_categories_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="helpdesk0">ヘルプデスク</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk1"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_se"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="helpdesk1">社内SE</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk2"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_support"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="helpdesk2">PMOサポート</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="marke_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">Webマーケ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_marke]"
                                           id="opportunity_search_condition_accordion_open_marke" value="no">
                                    <div class="accordion_contents_marke" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke0" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_direction"><label
                                                        id="opp_categories_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke0">ディレクション</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke1" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_seo"><label id="opp_categories_field_label_1"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="marke1">SEO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke2" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_email"><label id="opp_categories_field_label_2"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="marke2">Eメール</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke3" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_sns"><label id="opp_categories_field_label_3"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="marke3">SNS</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke4" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_listing"><label
                                                        id="opp_categories_field_label_4"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke4">リスティング</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke5" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_affiliate"><label
                                                        id="opp_categories_field_label_5"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke5">アフィリエイト</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke6" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_video"><label id="opp_categories_field_label_6"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="marke6">動画</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="design_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">デザイン</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_design]"
                                           id="opportunity_search_condition_accordion_open_design" value="no">
                                    <div class="accordion_contents_design" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design0" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_web"><label id="opp_categories_field_label_0"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="design0">Webシステム</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design1" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_uiux"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="design1">UI/UX</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design2" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_character"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="design2">キャラクター</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design3" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_graphic"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="design3">グラフィック</label></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <label class="font-middle mb-3 ex-bold">単価</label>
                                <div class="mb-5"><p class="d-flex justify-content-center" id="price-range">
                                    下限なし〜上限なし</p>
                                    <div class="slider-styled mx-4 noUi-target noUi-ltr noUi-horizontal noUi-txt-dir-ltr"
                                         id="slider-handles">
                                        <div class="noUi-base">
                                            <div class="noUi-connects">
                                                <div class="noUi-connect"
                                                     style="transform: translate(0%, 0px) scale(1, 1);"></div>
                                            </div>
                                            <div class="noUi-origin"
                                                 style="transform: translate(-100%, 0px); z-index: 5;">
                                                <div class="noUi-handle noUi-handle-lower" data-handle="0" tabindex="0"
                                                     role="slider" aria-orientation="horizontal" aria-valuemin="35.0"
                                                     aria-valuemax="200.0" aria-valuenow="35.0" aria-valuetext="35.00">
                                                    <div class="noUi-touch-area"></div>
                                                </div>
                                            </div>
                                            <div class="noUi-origin" style="transform: translate(0%, 0px); z-index: 4;">
                                                <div class="noUi-handle noUi-handle-upper" data-handle="1" tabindex="0"
                                                     role="slider" aria-orientation="horizontal" aria-valuemin="40.0"
                                                     aria-valuemax="205.0" aria-valuenow="205.0"
                                                     aria-valuetext="205.00">
                                                    <div class="noUi-touch-area"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between font-small custom-grey-text my-2"><span>下限なし</span><span>上限なし</span>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold" for="">稼働率</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[utilization_rate][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_0"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="full_utilization"><label id="utilization_rate_field_label_0"
                                                                                class="custom-control-label anavi-select-label mb-3"
                                                                                for="utilization_rate_field_opportunity_search_condition_0">100%（フル稼働）</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_1"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="80to99"><label id="utilization_rate_field_label_1"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_1">80
                                            〜 99%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_2"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="60to79"><label id="utilization_rate_field_label_2"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_2">60
                                            〜 79%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_3"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="40to59"><label id="utilization_rate_field_label_3"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_3">40
                                            〜 59%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_4"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="20to39"><label id="utilization_rate_field_label_4"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_4">20
                                            〜 39%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_5"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="less_than_20"><label id="utilization_rate_field_label_5"
                                                                            class="custom-control-label anavi-select-label mb-3"
                                                                            for="utilization_rate_field_opportunity_search_condition_5">20%未満</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">案件の商流</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[opp_type_id][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_0" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="clnt"><label
                                                id="opp_type_id_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_0">エンド</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_1" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="prim"><label
                                                id="opp_type_id_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_1">元請</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_2" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="subc"><label
                                                id="opp_type_id_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_2">一次請</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_3" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="msubc"><label
                                                id="opp_type_id_field_label_3"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_3">二次請以降</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">商流への関与</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[involvement][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="involvement_field_opportunity_search_condition_0" type="checkbox"
                                                name="opportunity_search_condition[involvement][]"
                                                value="enter_sales_channels"><label id="involvement_field_label_0"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="involvement_field_opportunity_search_condition_0">掲載企業が商流に入る案件のみ表示する</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">出社頻度</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[work_frequencies][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_0"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="5days"><label id="work_frequencies_field_label_0"
                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                     for="work_frequencies_field_opportunity_search_condition_0">週5日出社</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_1"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="2to4days"><label id="work_frequencies_field_label_1"
                                                                        class="custom-control-label anavi-select-label mb-3"
                                                                        for="work_frequencies_field_opportunity_search_condition_1">週4
                                            〜 2日出社</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_2"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="less_than_1day"><label id="work_frequencies_field_label_2"
                                                                              class="custom-control-label anavi-select-label mb-3"
                                                                              for="work_frequencies_field_opportunity_search_condition_2">週1日以下出社</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_3"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="full_remote"><label id="work_frequencies_field_label_3"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="work_frequencies_field_opportunity_search_condition_3">フルリモート</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto y5AEdoW mb-5">
                                    <div class="row ml-0 mr-0"><label class="font-middle mb-3 ex-bold"
                                                                      for="">就業場所</label>
                                        <button class="mdb-modal-form btn btn-outline-default btn-sm mb-3 mt-0 ml-3 waves-effect waves-light"
                                                type="button" data-toggle="modal"
                                                data-reference="#specifies_workplaces_base">選択
                                        </button>
                                        <p class="pref-select p-0 m-0 w-100" id="check_item">選択されていません</p>
                                        <input id="specifies_workplaces" multiple="" autocomplete="off" type="hidden"
                                               name="opportunity_search_condition[specifies_workplaces][]">
                                        <div class="modal" id="specifies_workplaces_base" tabindex="-1" role="dialog"
                                             aria-labelledby="specifies_workplaces_modal" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header"><h4 class="modal-title w-100">
                                                        就業場所を選択</h4>
                                                        <button class="close btn-modal-close" aria-label="Close"
                                                                type="button"
                                                                data-reference="#specifies_workplaces_base"><i
                                                                class="material-icons md-dark mb-36" aria-hidden="true">clear</i>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row multiple-prefecture-select">
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">北海道</a><input
                                                                    type="hidden" value="1"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">青森県</a><input
                                                                    type="hidden" value="2"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岩手県</a><input
                                                                    type="hidden" value="3"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">宮城県</a><input
                                                                    type="hidden" value="4"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">秋田県</a><input
                                                                    type="hidden" value="5"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山形県</a><input
                                                                    type="hidden" value="6"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福島県</a><input
                                                                    type="hidden" value="7"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">茨城県</a><input
                                                                    type="hidden" value="8"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">栃木県</a><input
                                                                    type="hidden" value="9"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">群馬県</a><input
                                                                    type="hidden" value="10"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">埼玉県</a><input
                                                                    type="hidden" value="11"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">千葉県</a><input
                                                                    type="hidden" value="12"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">東京都</a><input
                                                                    type="hidden" value="13"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">神奈川県</a><input
                                                                    type="hidden" value="14"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">新潟県</a><input
                                                                    type="hidden" value="15"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">富山県</a><input
                                                                    type="hidden" value="16"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">石川県</a><input
                                                                    type="hidden" value="17"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福井県</a><input
                                                                    type="hidden" value="18"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山梨県</a><input
                                                                    type="hidden" value="19"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">長野県</a><input
                                                                    type="hidden" value="20"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岐阜県</a><input
                                                                    type="hidden" value="21"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">静岡県</a><input
                                                                    type="hidden" value="22"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">愛知県</a><input
                                                                    type="hidden" value="23"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">三重県</a><input
                                                                    type="hidden" value="24"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">滋賀県</a><input
                                                                    type="hidden" value="25"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">京都府</a><input
                                                                    type="hidden" value="26"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">大阪府</a><input
                                                                    type="hidden" value="27"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">兵庫県</a><input
                                                                    type="hidden" value="28"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">奈良県</a><input
                                                                    type="hidden" value="29"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">和歌山県</a><input
                                                                    type="hidden" value="30"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">鳥取県</a><input
                                                                    type="hidden" value="31"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">島根県</a><input
                                                                    type="hidden" value="32"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岡山県</a><input
                                                                    type="hidden" value="33"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">広島県</a><input
                                                                    type="hidden" value="34"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山口県</a><input
                                                                    type="hidden" value="35"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">徳島県</a><input
                                                                    type="hidden" value="36"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">香川県</a><input
                                                                    type="hidden" value="37"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">愛媛県</a><input
                                                                    type="hidden" value="38"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">高知県</a><input
                                                                    type="hidden" value="39"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福岡県</a><input
                                                                    type="hidden" value="40"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">佐賀県</a><input
                                                                    type="hidden" value="41"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">長崎県</a><input
                                                                    type="hidden" value="42"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">熊本県</a><input
                                                                    type="hidden" value="43"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">大分県</a><input
                                                                    type="hidden" value="44"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">宮崎県</a><input
                                                                    type="hidden" value="45"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">鹿児島県</a><input
                                                                    type="hidden" value="46"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">沖縄県</a><input
                                                                    type="hidden" value="47"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">海外</a><input
                                                                    type="hidden" value="48"></div>
                                                        </div>
                                                        <div class="row pt-5">
                                                            <button class="btn btn-blue-grey mx-auto btn-modal-close waves-effect waves-light"
                                                                    aria-label="Close"
                                                                    data-reference="#specifies_workplaces_base">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">契約形態</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_search_condition[contract_types][]">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_0"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="quas"><label id="contract_types_field_label_0"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_0">業務委託（準委任）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_1"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="subc"><label id="contract_types_field_label_1"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_1">業務委託（請負）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_2"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="temp"><label id="contract_types_field_label_2"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_2">派遣契約</label>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">案件の特徴</label>
                                    <div class="pl-3 mb-5"><p id="opp_quality_text">選択されていません</p><input
                                            multiple="" autocomplete="off" type="hidden"
                                            name="opportunity_search_condition[opp_qualities][]"
                                            id="opportunity_search_condition_opp_qualities">
                                        <button class="mdb-modal-form btn btn-outline-default m-0 waves-effect waves-light"
                                                data-reference="#quality" data-toggle="modal" href="" type="button">
                                            案件の特徴を選択
                                        </button>
                                        <div aria-hidden="true" aria-labelledby="quality_modal" class="modal"
                                             id="quality" role="dialog" tabindex="-1">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header"><h4 class="modal-title w-100">
                                                        案件の特徴を選択</h4>
                                                        <button aria-label="Close" class="close" data-dismiss="modal"
                                                                type="button"><span aria-hidden="true"><i
                                                                class="material-icons md-dark mb-36">clear</i></span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row opp-quality-select">
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">70万円以上</a><input
                                                                    type="hidden" value="700thousand_or_more"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">100万円以上</a><input
                                                                    type="hidden" value="1million_or_more"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">1年未満OK</a><input
                                                                    type="hidden" value="less_1year_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">新人でもOK</a><input
                                                                    type="hidden" value="newcomer_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">50代以上OK</a><input
                                                                    type="hidden" value="over_50years_old_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">外国籍OK</a><input
                                                                    type="hidden" value="foreign_nationality_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">リーダー募集</a><input
                                                                    type="hidden" value="leader_recruitment"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">英語力</a><input
                                                                    type="hidden" value="english_skill"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">面談1回</a><input
                                                                    type="hidden" value="interview_once"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">持ち帰りOK</a><input
                                                                    type="hidden" value="take_home_project"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">チーム提案OK</a><input
                                                                    type="hidden" value="team_proposal_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">ウェブ面談可能</a><input
                                                                    type="hidden" value="web_interview_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">案件場所から遠隔地居住でもOK</a><input
                                                                    type="hidden" value="remote__location_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">日本以外の居住者OK</a><input
                                                                    type="hidden" value="overseas_resident_ok"></div>
                                                        </div>
                                                        <div class="row mt-3">
                                                            <button aria-label="Close"
                                                                    class="btn btn-blue-grey mx-auto waves-effect waves-light"
                                                                    data-dismiss="modal">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">募集対象</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[allowable_trading_restriction][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_0"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_own_employee"><label
                                                id="allowable_trading_restriction_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_0">自社社員</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_1"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_via_another_company"><label
                                                id="allowable_trading_restriction_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_1">協力会社社員</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_2"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_freelance_self"><label
                                                id="allowable_trading_restriction_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_2">フリーランス（本人）</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_3"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_freelance"><label
                                                id="allowable_trading_restriction_field_label_3"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_3">フリーランス（企業登録）</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion_details accordion_close py-3 px-2 clear border-top"><span
                                        class="float-left pr-7">詳細検索条件</span><i
                                        class="material-icons md-dark float-right d-block">keyboard_arrow_down</i></div>
                                <input autocomplete="off" type="hidden"
                                       name="opportunity_search_condition[accordion_open]"
                                       id="opportunity_search_condition_accordion_open" value="no">
                                <div class="accordion_contents_details mb-4 pt-3" style="display: none;">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">案件内容の確定状況</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_search_condition[order_accuracy_id][]">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="order_accuracy_id_field_opportunity_search_condition_0"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[order_accuracy_id][]"
                                                       value="afte"><label id="order_accuracy_id_field_label_0"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="order_accuracy_id_field_opportunity_search_condition_0">確定済み</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="order_accuracy_id_field_opportunity_search_condition_1"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[order_accuracy_id][]"
                                                       value="befo"><label id="order_accuracy_id_field_label_1"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="order_accuracy_id_field_opportunity_search_condition_1">確定前</label>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">契約開始日</label>
                                    <div class="row pl-3">
                                        <div class="col-9">
                                            <div class="mx-auto mb-3"><input class="form-control picker__input"
                                                                             autocomplete="off"
                                                                             id="contract_startdate_at_field"
                                                                             type="text"
                                                                             name="opportunity_search_condition[contract_startdate_at]"
                                                                             readonly="" aria-haspopup="true"
                                                                             aria-expanded="false" aria-readonly="false"
                                                                             aria-owns="contract_startdate_at_field_root">
                                                <div class="picker" id="contract_startdate_at_field_root"
                                                     aria-hidden="true">
                                                    <div class="picker__holder" tabindex="-1">
                                                        <div class="picker__frame">
                                                            <div class="picker__wrap">
                                                                <div class="picker__box">
                                                                    <div class="picker__header">
                                                                        <div class="picker__date-display">
                                                                            <div class="picker__weekday-display">
                                                                                木曜日,
                                                                            </div>
                                                                            <div class="picker__month-display">
                                                                                <div>2月</div>
                                                                            </div>
                                                                            <div class="picker__day-display">
                                                                                <div>13</div>
                                                                            </div>
                                                                            <div class="picker__year-display">
                                                                                <div>2025</div>
                                                                            </div>
                                                                        </div>
                                                                        <select class="picker__select--year" disabled=""
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Select a year">
                                                                            <option value="2018">2018</option>
                                                                            <option value="2019">2019</option>
                                                                            <option value="2020">2020</option>
                                                                            <option value="2021">2021</option>
                                                                            <option value="2022">2022</option>
                                                                            <option value="2023">2023</option>
                                                                            <option value="2024">2024</option>
                                                                            <option value="2025" selected="">2025
                                                                            </option>
                                                                            <option value="2026">2026</option>
                                                                            <option value="2027">2027</option>
                                                                            <option value="2028">2028</option>
                                                                            <option value="2029">2029</option>
                                                                            <option value="2030">2030</option>
                                                                            <option value="2031">2031</option>
                                                                            <option value="2032">2032</option>
                                                                        </select><select class="picker__select--month"
                                                                                         disabled=""
                                                                                         aria-controls="contract_startdate_at_field_table"
                                                                                         title="Select a month">
                                                                        <option value="0">1月</option>
                                                                        <option value="1" selected="">2月</option>
                                                                        <option value="2">3月</option>
                                                                        <option value="3">4月</option>
                                                                        <option value="4">5月</option>
                                                                        <option value="5">6月</option>
                                                                        <option value="6">7月</option>
                                                                        <option value="7">8月</option>
                                                                        <option value="8">9月</option>
                                                                        <option value="9">10月</option>
                                                                        <option value="10">11月</option>
                                                                        <option value="11">12月</option>
                                                                    </select>
                                                                        <button class="picker__nav--prev btn btn-flat"
                                                                                data-nav="-1" role="button"
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Previous month"></button>
                                                                        <button class="picker__nav--next btn btn-flat"
                                                                                data-nav="1" role="button"
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Next month"></button>
                                                                    </div>
                                                                    <table class="picker__table"
                                                                           id="contract_startdate_at_field_table"
                                                                           role="grid"
                                                                           aria-controls="contract_startdate_at_field"
                                                                           aria-readonly="true">
                                                                        <thead>
                                                                        <tr>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="月曜日">月
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="火曜日">火
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="水曜日">水
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="木曜日">木
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="金曜日">金
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="土曜日">土
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="日曜日">日
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1737910800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月27日">27
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1737997200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月28日">28
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738083600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月29日">29
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738170000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月30日">30
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738256400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月31日">31
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738342800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月01日">1
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738429200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月02日">2
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738515600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月03日">3
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738602000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月04日">4
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738688400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月05日">5
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738774800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月06日">6
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738861200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月07日">7
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738947600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月08日">8
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739034000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月09日">9
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739120400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月10日">10
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739206800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月11日">11
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739293200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月12日">12
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus picker__day--today picker__day--highlighted"
                                                                                     data-pick="1739379600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月13日"
                                                                                     aria-activedescendant="true">13
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739466000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月14日">14
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739552400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月15日">15
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739638800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月16日">16
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739725200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月17日">17
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739811600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月18日">18
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739898000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月19日">19
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739984400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月20日">20
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740070800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月21日">21
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740157200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月22日">22
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740243600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月23日">23
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740330000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月24日">24
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740416400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月25日">25
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740502800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月26日">26
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740589200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月27日">27
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740675600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月28日">28
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740762000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月01日">1
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740848400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月02日">2
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740934800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月03日">3
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741021200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月04日">4
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741107600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月05日">5
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741194000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月06日">6
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741280400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月07日">7
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741366800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月08日">8
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741453200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月09日">9
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        </tbody>
                                                                    </table>
                                                                    <div class="picker__footer">
                                                                        <button class="picker__button--today"
                                                                                type="button" data-pick="1739379600000"
                                                                                disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            今日
                                                                        </button>
                                                                        <button class="picker__button--clear"
                                                                                type="button" data-clear="1" disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            消去
                                                                        </button>
                                                                        <button class="picker__button--close"
                                                                                type="button" data-close="true"
                                                                                disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            Close
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <input type="hidden"
                                                       name="opportunity_search_condition[contract_startdate_at]"></div>
                                        </div>
                                        <div class="col-1 p-0"><i
                                                class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">募集人数</label>
                                    <div class="row pl-3">
                                        <div class="col-8">
                                            <div class="mx-auto mb-5"><input type="number" min="0" class="form-control"
                                                                             autocomplete="off" id="participants_field"
                                                                             name="opportunity_search_condition[participants]">
                                            </div>
                                        </div>
                                        <div class="col pl-0"><label class="inline-unit-label">人</label></div>
                                    </div>
                                    <div class="mx-auto mb-5"><label class="font-middle mb-3 ex-bold">面談回数</label>
                                        <div class="select-wrapper mdb-select anavi-select mb-5"><span
                                                class="caret material-icons">keyboard_arrow_down</span><input
                                                type="text" class="select-dropdown form-control" readonly="true"
                                                required="false"
                                                data-activates="select-options-interview_count_id_field" value=""
                                                role="listbox" aria-multiselectable="false" aria-disabled="false"
                                                aria-required="false" aria-haspopup="true" aria-expanded="false">
                                            <ul id="select-options-interview_count_id_field"
                                                class="dropdown-content select-dropdown w-100 " style="display: none;">
                                                <li class=" active " role="option" aria-selected="true"
                                                    aria-disabled="false"><span class="filtrable "> 未選択    </span>
                                                </li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 1回    </span></li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 1〜2回    </span>
                                                </li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 2回    </span></li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 3回以上    </span>
                                                </li>
                                            </ul>
                                            <select class="mdb-select anavi-select mb-5 initialized"
                                                    id="interview_count_id_field"
                                                    name="opportunity_search_condition[interview_count_id]">
                                                <option value="">未選択</option>
                                                <option value="once">1回</option>
                                                <option value="few">1〜2回</option>
                                                <option value="twice">2回</option>
                                                <option value="over_three_times">3回以上</option>
                                            </select></div>
                                    </div>
                                    <div class="accordion-close-area text-center">閉じる<i
                                            class="material-icons md-dark md-18">close</i></div>
                                </div>
                                <div class="border-bottom mb-5"></div>
                                <input autocomplete="off" type="hidden" name="opportunity_search_condition[switch_type]"
                                       id="opportunity_search_condition_switch_type" value="button">
                                <div class="text-center d-none d-md-block search-area py-2">
                                    <button name="button" type="submit"
                                            class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light"
                                            id="opp-search-btn" data-disable-with="検索中">
                                        <div class="py-2 d-none" id="loader">
                                            <div class="loader"></div>
                                        </div>
                                        <div id="btn-text"><span class="font-extralarge" id="search-count">7</span>
                                            件<br>この条件で検索
                                        </div>
                                    </button>
                                    <div class="py-2"><a
                                            href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="container-fluid d-block d-md-none search-fixed-btn search-area-sm">
                    <div class="row py-3">
                        <div class="col-12">
                            <button name="button" type="submit"
                                    class="btn btn-default btn-block font-middle mb-3 submit-btn waves-effect waves-light"
                                    form="opportunity_search_condition_form" data-disable-with="検索中">
                                <div class="py-2 d-none" id="loader-sm">
                                    <div class="loader"></div>
                                </div>
                                <div id="btn-text-sm"><span class="font-extralarge" id="search-count-sm">7</span> 件<br>この条件で検索
                                </div>
                            </button>
                            <div class="text-center"><a
                                    href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-8 col-lg-9">
            <div class="row">
                <div class="col-12 d-none d-md-block mb-4">
                    <div class="d-flex"><span class="mr-3 nowrap">よく検索されるキーワード</span>
                        <div><a class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/107"><span>SAP</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/106"><span>PMO</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/102"><span>コンサル</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/105"><span>Java</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/110"><span>PHP</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/142"><span>RPA</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/140"><span>AI</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/44"><span>Python</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/144"><span>セキュリティ</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/145"><span>Salesforce</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/101"><span>PM</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/146"><span>React</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/147"><span>テスト</span></a></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="d-flex justify-content-between search-toggle d-md-none bg-white w-100 p-3 mt-4 mx-3"><a
                        class="font-middle custom-grey-6-text m-0" href="javascript:void(0);">検索条件指定</a><i
                        class="material-icons custom-grey-6-text">keyboard_arrow_down</i></div>
            </div>
            <div class="row mr-0 ml-0">
                <div class="col-12 mt-3 border border-light p-3 px-md-4 bg-grey-2 rounded-sm">
                    <div class="position-relative">
                        <div class="d-sm-flex search-condition-area line-height-180">
                            <div class="text-nowrap mb-2 mb-sm-n2 ml-3 ml-sm-0">検索条件</div>
                            <div class="pl-3">
                                <div class="d-sm-inline boder-left"><span
                                        class="mr-3 custom-grey-5-text">得意領域</span>
                                    <div class="d-sm-inline mb-2 mb-sm-0"><span
                                            class="border-grey-3 bg-white mr-2 px-2">コンサル</span>
                                        <div class="d-sm-inline"><span class="mr-3">PMO</span></div>
                                    </div>
                                    <div class="d-sm-inline mb-2 mb-sm-0"><span
                                            class="border-grey-3 bg-white mr-2 px-2">開発</span>
                                        <div class="d-sm-inline"><span class="mr-3">PMO</span></div>
                                    </div>
                                    <div class="d-sm-inline mb-2 mb-sm-0"><span
                                            class="border-grey-3 bg-white mr-2 px-2">インフラ</span>
                                        <div class="d-sm-inline"><span class="mr-3">PMO</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion_search_condition_btn"><a
                                class="accordion_search_condition accordion_close text-right btn-block pr-3 bg-grey-2 pl-5"
                                href="javascript:void(0);" style="display: none;"><span
                                class="vertical-middle font-middle">すべて表示</span><i class="material-icons">keyboard_arrow_down</i></a>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="font-middle"><span class="font-ll ex-bold" id="total-count">7</span> 件中 1〜7件</div>
                <div data-v-7d20fe15="" class="position-relative pl-3"><!-- ソート中の項目を表示 -->
                    <div data-v-7d20fe15="" class="d-flex justify-content-end sort-display-area p-2"><label
                            data-v-7d20fe15="" class="pr-2 mb-0">新着（降順）</label><i data-v-7d20fe15=""
                                                                                      class="material-icons custom-grey-6-text pl-1">keyboard_arrow_down</i>
                    </div>
                    <ul data-v-7d20fe15="" class="bg-white sort-options-area text-right" style="display: none;">
                        <li data-v-7d20fe15="" class="sort-active">新着（降順） <i data-v-7d20fe15=""
                                                                                 class="material-icons custom-grey-6-text">done</i>
                        </li>
                        <li data-v-7d20fe15="" class="">更新（降順） <!--v-if--></li>
                        <li data-v-7d20fe15="" class="">単価（降順） <!--v-if--></li>
                    </ul>
                </div>
            </div>
            <div class="row">
                <div data-v-1c51a755="" id="jsonld-125327">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "某省庁向け基盤更改プロジェクト支援【ネットワークチーム】",
                            "value": 125327
                        },
                        "datePosted": "2025/02/06 12:15:09",
                        "validThrough": "2025/03/08 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 90,
                                "maxValue": 130,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125327/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    某省庁向け基盤更改プロジェクト支援【ネットワークチーム】
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2026/12/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">●案件概要：
                            某省庁における全基盤の更改に向けた技術検討および提案活動を支援。
                            2025年度の調達に向けて、サーバ・移行チーム、ネットワークチーム、
                            アプリチームに分かれて技術検討を行い、入札対応を進める。
                            受注後は2026年度以降の設計・構築フェーズに移行予定。
                            ●フェーズ：
                            -提案フェーズ（～2025年度末）
                            -設計・構築フェー...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/7</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">90万円 〜 130万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社 / 週1日出社 / 週1日未満出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125327" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    823811705317<br>◆案件名: 某省庁向け基盤更改プロジェクト支援【ネットワークチーム】<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>●案件概要：<br>某省庁における全基盤の更改に向けた技術検討および提案活動を支援。<br>2025年度の調達に向けて、サーバ・移行チーム、ネットワークチーム、<br>アプリチームに分かれて技術検討を行い、入札対応を進める。<br>受注後は2026年度以降の設計・構築フェーズに移行予定。<br>●フェーズ：<br>-提案フェーズ（～2025年度末）<br>-設計・構築フェーズ（2026年度～2027年度）<br>●仕事内容：<br>-提案に向けた技術検討および協力会社（受注時請負予定先）との打ち合わせ・議論<br>-入札対応（提案書作成、お客様との技術対話等）<br>-受注後の設計・構築フェーズへの移行準備<br>-各チーム（サーバ・移行、ネットワーク）におけるPLおよびメンバーとしての技術支援<br>●勤務地（頻度）：都内（お客様と対面の打ち合わせ時は出社が必要）<br>●期間：<br>-提案フェーズ：～2025年度末<br>-設計・構築フェーズ：2026年度～（2か年を予定）<br>●稼働率：100%<br>●面談回数：2回<br>●商流：弊社2次請け<br>●備考：<br>-2025年度末までは提案フェーズ、2026年度から設計・構築フェーズに移行予定<br>◆人財要件:
                                    <br>●求めるスキル：<br>〈必須〉<br>-大規模ネットワークの構築・保守経験<br>-官公庁・公共案件でのPJ経験<br>-提案資料作成の経験<br>-ベンダーとの折衝経験<br>〈尚可〉<br>-PMまたはPL経験<br>-入札や提案のご経験<br><br>-長期参画が可能な方を優先<br>◆単価:
                                    90万円 〜 130万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社 / 週1日出社 / 週1日未満出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年04月01日 〜 2026年12月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125218">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【長期/急募】開発PJのマネジメント業務/若手/中堅メンバー歓迎/チーム提案可",
                            "value": 125218
                        },
                        "datePosted": "2025/02/04 11:53:30",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 70,
                                "maxValue": 120,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125218/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【長期/急募】開発PJのマネジメント業務/若手/中堅メンバー歓迎/チーム提案可
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/01 〜 2025/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755=""
                                  class="badge-pill font-small ml-md-2 badge-pill font-small badge-blue">元請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【作業内容】
                            複数チーム存在する大規模システム開発プロジェクトとなっており、その中で開発チームのマネジメント支援をご担当頂く想定です。


                            【条件】
                            作業場所：東京都都内
                            　　　　　※基本オンサイトとなります。一部リモートも相談可能です。
                            稼働率：100％
                            期間：即日 ～ 長期となる予定


                            【備考】
                            ・チームメンバーは2...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">70万円 〜 120万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社 / 週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125218" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    492637157137<br>◆案件名: 【長期/急募】開発PJのマネジメント業務/若手/中堅メンバー歓迎/チーム提案可<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 元請<br>◆案件内容: <br>【作業内容】<br>複数チーム存在する大規模システム開発プロジェクトとなっており、その中で開発チームのマネジメント支援をご担当頂く想定です。<br><br><br>【条件】<br>作業場所：東京都都内<br>　　　　　※基本オンサイトとなります。一部リモートも相談可能です。<br>稼働率：100％<br>期間：即日
                                    ～
                                    長期となる予定<br><br><br>【備考】<br>・チームメンバーは20代前半〜30代前半が主です。<br>・弊社参画メンバーによるフォロー体制があります。<br><br>ぜひ、ご応募お待ちしております！<br>◆人財要件:
                                    <br>【必須】<br>・システム開発プロジェクトの知見/参画経験<br>・メンバー管理経験<br>・能動的なコミュニケーション<br>・顧客折衝経験<br><br>【尚可】<br>・大規模プロジェクト参画経験<br>・クラウドの知見/経験<br>・ネイティブアプリの知見経験<br>・PL/PM/PMO経験<br>◆単価:
                                    70万円 〜 120万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社
                                    / 週4 〜 2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    5人<br>◆面談回数: 2回<br>◆契約期間: 2025年02月01日 〜 2025年02月28日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125215">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【2月中旬開始案件／大規模システム構築PJ発注者側支援／PMO】",
                            "value": 125215
                        },
                        "datePosted": "2025/02/04 11:37:23",
                        "validThrough": "2025/02/28 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 75,
                                "maxValue": 80,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125215/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【2月中旬開始案件／大規模システム構築PJ発注者側支援／PMO】
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/16 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">----------------------------------------------------------
                            【案件名】大規模システム構築PJ発注者側支援
                            【業種】官公庁
                            【業務内容】
                            　・官公庁向け大規模システム構築支援を実施
                            　・現行システムをホストからオープンに変更し、業務の抜本的な変更を実施
                            　・現フェーズ：結合テスト...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/27</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">75万円 〜 80万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社 / 週4 〜 2日出社 / 週1日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125215" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    799636647789<br>◆案件名: 【2月中旬開始案件／大規模システム構築PJ発注者側支援／PMO】<br>◆案件への関わり:
                                    仲介のみ<br>◆案件の商流: 二次請以降（仲介案件）<br>◆案件内容: <br>----------------------------------------------------------<br>【案件名】大規模システム構築PJ発注者側支援<br>【業種】官公庁<br>【業務内容】<br>　・官公庁向け大規模システム構築支援を実施<br>　・現行システムをホストからオープンに変更し、業務の抜本的な変更を実施<br>　・現フェーズ：結合テスト　※4月～　総合テストを想定<br>　・インフラチームBCPメンバとして以下の業務を実施想定<br>　　∟進捗管理、品質管理等のPJ管理<br>　　∟会議体管理<br>　　∟議事録・議事メモ、PJ資料作成<br>　　∟その他付随業務<br>【稼働】　2025年2月中旬～<br>【単価】　～80万<br>【勤務場所】　虎ノ門　※週1程度オンサイト（業務状況による）<br>【商流】　貴社プロパーまで<br>----------------------------------------------------------<br>◆人財要件:
                                    <br>【必須スキル】<br>　・PMO経験<br>　・大規模システム構築経験(基本設計以降)<br>　・システムテスト/移行経験<br>◆単価:
                                    75万円 〜 80万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社 / 週4 〜
                                    2日出社 / 週1日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年02月16日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125035">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "インフラPMO/システム刷新/複数名募集※常駐",
                            "value": 125035
                        },
                        "datePosted": "2025/01/29 14:08:21",
                        "validThrough": "2025/02/28 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 60,
                                "maxValue": 105,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125035/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    インフラPMO/システム刷新/複数名募集※常駐
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月03日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年01月29日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2026/01/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">3名程度募集（リーダー1名+メンバー2名程度）チーム提案歓迎
                            面談3回。

                            ＜概要＞
                            日本年金機構にて、システム刷新のプロジェクト実施中。
                            お客様サイドの基盤チーム社員代替として、PMO、開発要件の定義や成果物の査収などを行う。
                            開発自体は受注したベンダーにて対応するため、開発作業はありません。
                            外国籍不可

                            〇ロケーション
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/27</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">60万円 〜 105万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125035" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    681121221578<br>◆案件名: インフラPMO/システム刷新/複数名募集※常駐<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>3名程度募集（リーダー1名+メンバー2名程度）チーム提案歓迎<br>面談3回。<br><br>＜概要＞<br>日本年金機構にて、システム刷新のプロジェクト実施中。<br>お客様サイドの基盤チーム社員代替として、PMO、開発要件の定義や成果物の査収などを行う。<br>開発自体は受注したベンダーにて対応するため、開発作業はありません。<br>外国籍不可<br><br>〇ロケーション<br>京王井の頭線高井戸駅徒歩10分程度<br><br>〇勤務体系<br>上記ロケーションへのフルタイム勤務（リモートワークは一切なし）（セキュリティーの問題があるため）<br>9:00～18:00の8時間勤務<br>建物に喫煙スペースなし<br><br>〇環境<br>年金機構貸与端末からの外部アクセスは禁止（メール送付も不可）<br><br>〇期間<br>少なくともR8年1月まで。<br>その先も案件は続くため、R13年までは体制が続く見込み。<br>長期間従事可能な方を募集。(60歳以上不可）<br><br>◆人財要件:
                                    <br>※下記すべてを満たす必要はないが、上層部や顧客への説明経験は必須。<br>　それ以外は、体制の中でカバーできれば問題ない。<br><br>（必須）<br>・顧客折衝、顧客説明経験<br>・（必須気味）Javaスキル<br><br>（尚可）<br>・PMO経験
                                    or 大規模開発のマネジメント経験(100名参画の開発案件PMOなど)<br>・インフラ開発経験（Linux、AIX、Solaris、Oracle、DBMS、TERASOLUNA、ACP）<br>・ベンダーコントロール経験、ベンダーとの交渉経験<br>・デザインレビューができる（デザインを理解し、顧客に説明できる。ベンダーへの提言ができる。）<br>・基本設計経験、ドキュメントレビュー経験<br>・キャパシティー、CPU、パフォーマンスに関しての上流経験があること（素養があること（興味を持って調べたり、勉強できる人））<br>・（尚可）ニッセイでのアプリ開発経験、Salsaフレームワークやi-Winフレームワークでの開発経験<br>◆単価:
                                    60万円 〜 105万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 3人<br>◆面談回数: 3回以上<br>◆契約期間:
                                    2025年03月01日 〜 2026年01月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先）
                                    / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125029">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【3月開始／リモートワーク】大手生命保険会社向けインフラPMO",
                            "value": 125029
                        },
                        "datePosted": "2025/01/29 11:28:12",
                        "validThrough": "2025/02/22 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 65,
                                "maxValue": 80,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125029/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【3月開始／リモートワーク】大手生命保険会社向けインフラPMO
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年01月29日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/05/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【業務内容】
                            エンドグループ会社インフラプロジェクトにおけるPMO業務をお任せします。
                            ・定例会のファシリテーション
                            ・WBS作成
                            ・業務内で発生する知識、プロセス等に関するマニュアル、業務分析の資料作成
                            【時期】2025年3月～長期想定（4月からでも可能）
                            【就業時間】9：00～18：00
                            【その他】原則フルリモートで週1回は都内...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/21</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">65万円 〜 80万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125029" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    531117699631<br>◆案件名: 【3月開始／リモートワーク】大手生命保険会社向けインフラPMO<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>【業務内容】<br>エンドグループ会社インフラプロジェクトにおけるPMO業務をお任せします。<br>・定例会のファシリテーション<br>・WBS作成<br>・業務内で発生する知識、プロセス等に関するマニュアル、業務分析の資料作成<br>【時期】2025年3月～長期想定（4月からでも可能）<br>【就業時間】9：00～18：00<br>【その他】原則フルリモートで週1回は都内出社必須となります。<br>◆人財要件:
                                    <br>【必須条件】<br>・PM、PMOご経験者（定例会のファシリテーションやベンダー調整業務、WBSの作成等）<br>・インフラに関わる業務経験がある方（サーバやネットワーク等、短期でも実際に環境に触れた経験がある方）<br>◆単価:
                                    65万円 〜 80万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日出社
                                    / フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    2人<br>◆面談回数: 1〜2回<br>◆契約期間: 2025年03月01日 〜 2025年05月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-124808">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "大手海運会社のシステム構築PMO",
                            "value": 124808
                        },
                        "datePosted": "2025/01/19 14:59:02",
                        "validThrough": "2025/02/18 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/124808/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">大手海運会社のシステム構築PMO</div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月09日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年01月19日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/01 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【案件概要】
                            システムおよびDBの構築に向けたプロジェクトマネジメントを担当していただく。
                            以下内容をPMOとして推進いただく。
                            ・定例会（週３回程度）参加と議事録作成
                            ・関係者とのスケジュール調整
                            ・課題管理表の管理と課題の追い回し
                            ・報告資料作成（経営層へのマイルストン報告など）
                            ・ユーザーテスト計画立案・実施支援
                            など

                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/17</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 150万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 80 〜 99% / 60 〜 79%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社 / 週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_124808" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    516266344003<br>◆案件名: 大手海運会社のシステム構築PMO<br>◆案件への関わり: 商流に入る<br>◆案件の商流:
                                    一次請<br>◆案件内容: <br>【案件概要】<br>システムおよびDBの構築に向けたプロジェクトマネジメントを担当していただく。<br>以下内容をPMOとして推進いただく。<br>・定例会（週３回程度）参加と議事録作成<br>・関係者とのスケジュール調整<br>・課題管理表の管理と課題の追い回し<br>・報告資料作成（経営層へのマイルストン報告など）<br>・ユーザーテスト計画立案・実施支援<br>など<br><br>【出社条件】<br>1週間~2週間に1回程度出社予定。会議(1時間程度/日)のみの参加で、会議終われば帰宅可能(その他はリモート)<br>◆人財要件:
                                    <br>【必須スキル】<br>・大手事業会社における業務システム構築PJTでのマネジメント経験<br>・複数ベンダー・ユーザー部門を抱えるPJTでの要件取りまとめ・調整の経験<br><br>【尚可スキル】<br>・コンサルファーム出身者<br>◆単価:
                                    120万円 〜 150万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働） / 80 〜 99% / 60 〜
                                    79%<br>◆出社頻度: 週1日出社 / 週1日未満出社 / フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    2人<br>◆面談回数: 2回<br>◆契約期間: 2025年02月01日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-124736">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "急募【要員募集：PM/PMO】EOS対応/アプリ側/ベンダーコントロール/情シス部門としての経験/リモートあり",
                            "value": 124736
                        },
                        "datePosted": "2025/01/16 12:26:36",
                        "validThrough": "2025/02/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 90,
                                "maxValue": 100,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/124736/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    急募【要員募集：PM/PMO】EOS対応/アプリ側/ベンダーコントロール/情シス部門としての経験/リモートあり
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年01月22日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年01月16日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/01 〜 2025/12/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">"＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            ■RHEL7 EOS対応の推進
                            ■業界：不動産
                            ■業務内容：
                            RHEL7 EOS・ミドルウェアEOS対応の推進・アプリ側担当
                            ・作業内容：
                            プロジェクトは実施フェーズに移っており、各システムの検証などに問題がないこと、
                            業務調整などを行い、ＰＪをアプリ視点で推進する
                            実...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">90万円 〜 100万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_124736" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    487997995534<br>◆案件名: 急募【要員募集：PM/PMO】EOS対応/アプリ側/ベンダーコントロール/情シス部門としての経験/リモートあり<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>"＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>■RHEL7 EOS対応の推進<br>■業界：不動産<br>■業務内容：<br>RHEL7
                                    EOS・ミドルウェアEOS対応の推進・アプリ側担当<br>・作業内容：<br>プロジェクトは実施フェーズに移っており、各システムの検証などに問題がないこと、<br>業務調整などを行い、ＰＪをアプリ視点で推進する<br>実行ではベンダが作業を進めていく中で進捗管理、レビューを行う。<br>・進捗管理<br>　定期・不定期で打ち合わせを実施し進捗を報告させる、遅延要素を検知した場合、ベンダに対しリカバリ検討の指示や問題点を<br>報告させる、プロジェクト内部では上位にエスカレーションし対策を検討する、といった動き方をするイメージ。<br>・課題管理<br>　各システムごとに発生する課題から解決すべき点を明確にして記録、解決を推進する。<br>・品質管理<br>　ベンダの作成するドキュメントやテストエビデンスの妥当性確認、やレビューの実施<br><br>■必須要件：<br>・インフラプロジェクトのPM/PMO経験<br>→業務システムのサーバ等インフラ領域に踏み込んだ経験<br>　プロジェクト内でアプリとインフラを取り持つ役割やインフラ担当の経験<br>　運用保守の経験<br><br>・業務システムの構築、エンハンスを行った経験<br>・システムベンダの進捗、品質管理<br>・事業会社の情シス部門経験<br>　→担当者としての業務部門との調整経験<br>・MW以下のレイヤーに対する知識<br><br>■条件：<br>期間：2月～<br>人数：1名<br>勤務時間：9:30-18:30が基本<br>金額：90万～100万円<br>面談：1回※WEB<br>支払い：45日<br>場所：三越前（週3リモート）<br><br>■備考：<br>56歳ぐらいまで<br>外国籍不可<br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝"<br>◆人財要件:
                                    <br>■必須要件：<br>・インフラプロジェクトのPM/PMO経験<br>→業務システムのサーバ等インフラ領域に踏み込んだ経験<br>　プロジェクト内でアプリとインフラを取り持つ役割やインフラ担当の経験<br>　運用保守の経験<br><br>・業務システムの構築、エンハンスを行った経験<br>・システムベンダの進捗、品質管理<br>・事業会社の情シス部門経験<br>　→担当者としての業務部門との調整経験<br>・MW以下のレイヤーに対する知識<br>◆単価:
                                    90万円 〜 100万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年02月01日 〜 2025年12月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <nav aria-label="pagination example"></nav>
        </div>
    </div>
</div>
<div style="margin-top: 79px;">
<link rel="stylesheet" href="/custom_frontend/static/css/PMO.css"/>
    `,
    data() {
        return {
        }
    }
}

export default PMO