import { userInfo } from "../../../router/router.js";
import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";
import { parseJapaneseDate } from "../../../utils/dateHelper.js";

const New = {
    template: `
<main class="margin-header sp_fluid" id="vue-app">
    <div class="container-fluid d-none d-md-block">
        ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: '登録・管理', link: null },
        { text: '案件・人財登録', link: null },
        { text: '案件登録', link: null, current: true }
        ], 'container-fluid', '')}
    </div>
    <div class="container-fluid">
        <div style="display: flex; justify-content: flex-start; width: 100%; gap: 2.3%; margin-left: -4px;">
            <div @click="tabChange(1)" class="tabButton btn-default">
                案件登録
            </div>
            <div @click="tabChange(2)" style="margin-left: -1.5%" class="tabButton">
                人財登録
            </div>
        </div>
    </div>
    <div class="container-fluid grabient pt-5"><input type="hidden" name="is_dispatch_license" id="is_dispatch_license"
            value="false" autocomplete="off">
        <div class="row">
            <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                <form class="form_change_send_to" id="new_opportunity" novalidate="novalidate" @submit.prevent
                    accept-charset="UTF-8" method="post"><input name="utf8" type="hidden" value="✓"
                        autocomplete="off"><input type="hidden" name="authenticity_token"
                        value="dw3k+RdcMUu2VbpjI6nr7JBzQAxSLA/ozSejFVw7QLcYeKGxjesTbzdF4vC+9+PUqZUCikNPTMtjhv67IoKHzA=="
                        autocomplete="off"><input autocomplete="off" type="hidden" name="opportunity[company_id]"
                        id="opportunity_company_id"><input autocomplete="off" type="hidden" name="opportunity[id]"
                        id="opportunity_id"><input value="60" autocomplete="off" type="hidden"
                        name="opportunity[number_of_action_counter_remaining]"
                        id="opportunity_number_of_action_counter_remaining">
                    <div class="card px-3 px-md-4 form-card mt-2 pt-5 sp_sides_uniter">
                        <div class="pl-0 pl-sm-2 border-bottom mb-5">
                            <div class="row px-3 mb-2">
                                <div class="col-12 mb-2"><label class="font-middle mb-3" for="subject_field">案件名<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <div class="mx-auto mb-1">
                                        <input v-model="subject" class="form-control" autocomplete="off"
                                            id="subject_field" type="text" name="opportunity[subject]">
                                        <div class="text-danger text-left" v-if="hasAttemptedSubmit && !check_subject">
                                            タイトルを入力してください。</div>
                                    </div>
                                    <CharacterCounter targetId="subject_field" :max-length="70" />
                                </div>
                                <div class="col-12 mb-5">
                                    <label class="font-middle mb-3 opp_categories_label" for="opp_categories_field">
                                        案件領域
                                        <span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">
                                            必須
                                        </span>
                                    </label>
                                    <div class="text-danger text-left" v-if="hasAttemptedSubmit && !check_categories">
                                        得意領域を選択してください。</div>
                                    <div class="consul_details py-1 bg-grey-1 pl-3 mb-2 d-flex clear accordion_open"
                                        @click="toggleAccordion">
                                        <span class="font-size-middle ex-bold">設計</span>
                                        <i class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                                        </i>
                                    </div>

                                    <div class="accordion_contents_design" v-show="isOpen">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3">
                                                <div class="custom-control custom-checkbox pl-4 col-12 col-md-4 font-middle"
                                                    v-for="(item, index) in categories" :key="index">
                                                    <input v-model="categories_design" class="custom-control-input"
                                                        :id="'design' + index" id_params="design" type="checkbox"
                                                        :value="item.value" name="opportunity[opp_categories][]">
                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                        :for="'design' + index">
                                                        {{ item.label }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="dev_details  accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                        @click="toggleAccordion1">
                                        <span class="font-size-middle ex-bold">開発</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen1 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                                        </i>
                                    </div>
                                    <input value="no" autocomplete="off" type="hidden"
                                        name="opportunity[accordion_open_dev]" id="opportunity_accordion_open_dev">
                                    <div class="accordion_contents_dev" v-if="isOpen1">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3">
                                                <div class="custom-control custom-checkbox pl-4 col-12 col-md-4 font-middle"
                                                    v-for="(item, index) in categoriesDev" :key="index">
                                                    <input v-model="categories_development" class="custom-control-input"
                                                        :id="'dev' + index" id_params="dev" type="checkbox"
                                                        :value="item.value" name="opportunity[opp_categories][]">
                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                        :for="'dev' + index">
                                                        {{ item.label }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="infra_details  accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                        @click="toggleAccordion2">
                                        <span class="font-size-middle ex-bold">インフラ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen2 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                    </div>
                                    <input value="no" autocomplete="off" type="hidden"
                                        name="opportunity[accordion_open_infra]" id="opportunity_accordion_open_infra">
                                    <div class="accordion_contents_infra" v-show="isOpen2">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3">
                                                <input type="hidden" name="opportunity[opp_categories][]" value="">
                                                <div class="custom-control custom-checkbox pl-4 col-12 col-md-4 font-middle"
                                                    v-for="(item, index) in categoriesInfra" :key="index">
                                                    <input v-model="categories_infrastructure"
                                                        class="custom-control-input" :id="'infra' + index"
                                                        id_params="infra" type="checkbox" :value="item.value"
                                                        name="opportunity[opp_categories][]">
                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                        :for="'infra' + index">
                                                        {{ item.label }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="infra_details  accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                        @click="toggleAccordion3">
                                        <span class="font-size-middle ex-bold">運用・保守</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                            {{ isOpen3 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                    </div>
                                    <input value="no" autocomplete="off" type="hidden"
                                        name="opportunity[accordion_open_infra]" id="opportunity_accordion_open_infra">
                                    <div class="accordion_contents_operation" v-show="isOpen3">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3">
                                                <input type="hidden" name="opportunity[opp_categories][]" value="">
                                                <div class="custom-control custom-checkbox pl-4 col-12 col-md-4 font-middle"
                                                    v-for="(item, index) in categories3" :key="index">
                                                    <input v-model="categories_operation_maintenance"
                                                        class="custom-control-input" :id="'operation' + index"
                                                        id_params="operation" type="checkbox" :value="item.value"
                                                        name="opportunity[opp_categories][]">
                                                    <label class="custom-control-label anavi-select-label mb-3"
                                                        :for="'operation' + index">
                                                        {{ item.label }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <workplace-selector :required-label="true" :mb_5="margin_bot"
                                    v-model="specifies_workplaces"></workplace-selector>
                                <div class="text-danger text-left mb-2 ml-3"
                                    v-if="hasAttemptedSubmit && !check_specifies_workplaces">勤務地を選択してください。</div>

                                <div class="col-12 mb-5 mt-5">
                                    <label class="font-middle mb-3" for="">業種<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <div v-dropdown="{ modelValue: '', listType: 'business_field' }"
                                        @selected="handleBusinessFieldSelected"></div>
                                    <div class="text-danger text-left"
                                        v-if="hasAttemptedSubmit && !check_business_field">業種を選択してください。</div>
                                </div>

                                <!-- 国籍 -->
                                <div class="col-12 mb-5 mt-5">
                                    <label class="font-middle mb-3" for="">国籍<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <span class="custom-grey-text d-block mb-3">国籍が「日本以外」の方は、国名をご記入ください。</span>
                                    <div class="mb-0">
                                        <div class="form-inline mb-3">
                                            <div class="d-flex">
                                                <div class="form-check pl-0 mb-3 mb-sm-0">
                                                    <input id="nationality_id_field0" class="form-check-input"
                                                        v-model="selectedCountry" type="radio"
                                                        name="opportunity[nationality_id]" value="japan"
                                                        @click="handleCountryChange">
                                                    <label id="nationality_id_field_label_0" class="form-check-label"
                                                        for="nationality_id_field0">日本人のみ</label>
                                                </div>
                                                <div class="form-check pl-0 mb-3 mb-sm-0">
                                                    <input id="nationality_id_field1" class="form-check-input"
                                                        v-model="selectedCountry" type="radio"
                                                        name="opportunity[nationality_id]" value="foreign"
                                                        @click="handleCountryChange">
                                                    <label id="nationality_id_field_label_1" class="form-check-label"
                                                        for="nationality_id_field1">外国籍可</label>
                                                </div>
                                                <div class="col-12 col-sm-6 px-sm-3 px-0">
                                                    <input id="" class="form-control ml-sm-4 mt-sm-0 w-100"
                                                        autocomplete="off" type="text" name="opportunity[country_name]"
                                                        v-model="Nationality" :disabled="selectedCountry === 'japan'">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-sm-6 px-sm-3 px-0 d-flex mt-1 country">
                                            <label class="form-check-label no-end-line">在留資格有無</label>
                                            <div class="ml-3">
                                                <input id="reside_field1" class="form-check-input" type="radio"
                                                    v-model="isResidential" name="opportunity[reside]" value="yes"
                                                    :disabled="selectedCountry === 'japan'">
                                                <label class="form-check-label" for="reside_field1">有</label>
                                            </div>
                                            <div class="ml-3">
                                                <input id="reside_field2" class="form-check-input" type="radio"
                                                    v-model="isResidential" name="opportunity[reside]" value="no"
                                                    :disabled="selectedCountry === 'japan'">
                                                <label class="form-check-label" for="reside_field2">無</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mb-2"><label class="font-middle mb-3" for="subject_field">単価<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                        class="custom-grey-text d-block mb-3">稼働率100%（フル稼働）換算での単価を入力してください。</span>
                                    <div v-if="!skill_matching_flg">
                                        <PriceRange :min="0" :max="300" :step="5"
                                            :startValues="[unit_price_min, unit_price_max]"
                                            @update:price_min="val => unit_price_min = val !== null ? val : 0"
                                            @update:price_max="val => unit_price_max = val !== null ? val : 300" />
                                    </div>
                                    <input type="hidden" :value="unit_price_min !== null ? unit_price_min : ''"
                                        name="opportunity[unit_price_min]">
                                    <input type="hidden" :value="unit_price_max !== null ? unit_price_max : ''"
                                        name="opportunity[unit_price_max]">
                                    <div class="mx-auto mb-5">
                                        <div class="selecting-form custom-control custom-checkbox z-2">
                                            <input value="false" autocomplete="off" type="hidden"
                                                name="opportunity[unit_price_skill]" id="opportunity_unit_price_skill">
                                            <input id="unit_price_skill_field" v-model="skill_matching_flg"
                                                :true-value="true" :false-value="false" class="custom-control-input"
                                                type="checkbox" name="opportunity[unit_price_skill]">
                                            <label id="unit_price_skill_field_label" class="custom-control-label"
                                                for="unit_price_skill_field">スキル見合い</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mb-3">
                                    <div class="row">
                                        <div class="col-10 col-md-5 max-iphone-p-0">
                                            <div class="mx-auto mb-2"><label class="font-middle mb-3" for="">契約開始日<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input
                                                    v-pickadate="{ model: 'contract_startdate_at' }"
                                                    class="form-control picker__input" v-model="contract_startdate_at"
                                                    autocomplete="off" id="contract_startdate_at_field" type="text"
                                                    name="opportunity[contract_startdate_at]" readonly=""
                                                    aria-haspopup="true" aria-expanded="false" aria-readonly="false"
                                                    aria-owns="contract_startdate_at_field_root"
                                                    style="color: transparent;">
                                                <div class="text-danger text-left"
                                                    v-if="hasAttemptedSubmit && !check_contract_startdate_at">
                                                    {{ error_contract_startdate }}</div>
                                            </div>
                                        </div>
                                        <div class="col-2 col-md-1 px-0 pt-5 d-flex align-items-center">
                                            <span>から</span>
                                        </div>
                                        <div class="col-10 col-md-5 max-iphone-p-0">
                                            <div class="mx-auto mb-2"><label class="font-middle mb-3" for="">契約終了日<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input
                                                    v-pickadate="{ model: 'contract_enddate_at'}"
                                                    class="form-control picker__input" v-model="contract_enddate_at"
                                                    autocomplete="off" id="contract_enddate_at_field" type="text"
                                                    name="opportunity[contract_enddate_at]" style="color: transparent;"
                                                    readonly="" aria-haspopup="true" aria-expanded="false"
                                                    aria-readonly="false" aria-owns="contract_enddate_at_field_root">
                                                <div class="text-danger text-left"
                                                    v-if="hasAttemptedSubmit && !check_contract_enddate_at">
                                                    {{ error_contract_enddate }}</div>
                                            </div>
                                        </div>
                                        <div class="col-2 col-md-1 px-0 pt-5 d-flex align-items-center">
                                            <span>まで</span>
                                        </div>
                                    </div>
                                    <div class="mx-auto mb-5">
                                        <div class="selecting-form custom-control custom-checkbox z-2"><input
                                                value="false" autocomplete="off" type="hidden"
                                                name="opportunity[possible_continue]"
                                                id="opportunity_possible_continue"><input id="possible_continue_field"
                                                v-model="possible_continue_flg" class="custom-control-input"
                                                type="checkbox" name="opportunity[possible_continue]"><label
                                                id="possible_continue_field_label" class="custom-control-label"
                                                for="possible_continue_field">継続の可能性あり</label></div>
                                    </div>
                                </div>



                                <div class="col-12 mb-5">
                                    <div class="row mb-3">
                                        <div class="col-12 mb-1 max-iphone-p-0"><label class="font-middle mb-2"
                                                for="">応募期限<span
                                                    class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                                class="custom-grey-text d-block mb-1">案件登録日から最長30日先まで設定できます。</span>
                                        </div>
                                        <div class="col-10 col-sm-6 col-md-4">
                                            <div class="mx-auto"><input v-pickadate="{ model: 'expired_at' }"
                                                    class="form-control picker__input" autocomplete="off"
                                                    id="expired_at_field" type="text" name="opportunity[expired_at]"
                                                    readonly="" aria-haspopup="true" aria-expanded="false"
                                                    v-model="expired_at" aria-readonly="false"
                                                    aria-owns="expired_at_field_root">
                                                <div class="picker" id="expired_at_field_root" aria-hidden="true">
                                                    <div class="picker__holder" tabindex="-1">
                                                        <div class="picker__frame">
                                                            <div class="picker__wrap">
                                                                <div class="picker__box">
                                                                    <div class="picker__header">
                                                                        <button class="picker__nav--prev btn btn-flat"
                                                                            data-nav="-1" role="button"
                                                                            aria-controls="expired_at_field_table"
                                                                            title="Previous month"></button>
                                                                        <button class="picker__nav--next btn btn-flat"
                                                                            data-nav="1" role="button"
                                                                            aria-controls="expired_at_field_table"
                                                                            title="Next month"></button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <input type="hidden" name="opportunity[expired_at]">
                                            </div>
                                        </div>
                                        <div class="col-2 col-md-1 px-0 pt-1 d-flex align-items-center">
                                            <span>まで</span>
                                        </div>
                                        <div class="col-12 mt-1 pl-3 custom-grey-text">
                                        </div>
                                        <div class="text-danger text-left ml-3" v-if="!check_expired_at">
                                            {{error_expired_at}}</div>
                                    </div>
                                </div>

                                <div class="col-12 mb-5">
                                    <div class="row">
                                        <div class="col-12 mb-1 max-iphone-p-0"><label class="font-middle mb-2"
                                                for="">募集人数<span
                                                    class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                                class="custom-grey-text d-block mb-1">半角数字で20文字以内で入力してください。</span>
                                        </div>
                                        <div class="col-10 col-sm-6 col-md-4">
                                            <div class="mx-auto"><input type="number" min="0" class="form-control"
                                                    v-model="participants" autocomplete="off" id="participants_field"
                                                    name="opportunity[participants]"></div>
                                        </div>
                                        <div class="col-2 col-md-1 px-0 pt-3 d-flex"><span>人</span></div>
                                    </div>
                                    <div class="text-danger text-left mt-2" v-if="!check_participants">
                                        必要に応じて正しい応募者数を入力してください。
                                    </div>
                                </div>

                                <div class="col-12 mb-5"><label class="font-middle mb-3">募集対象</label><span
                                        class="badge-pill badge-danger pink lighten-2 font-small ml-3">必須</span>
                                    <button
                                        class="mdb-modal-form btn btn-outline-default btn-sm btn-action ml-3 my-0 waves-effect waves-light"
                                        data-target="#select_trading_restriction_modal" data-toggle="modal" href=""
                                        type="button">選択
                                    </button>
                                    <p class="type-id-selected" id="trading_restriction">{{ selectedLabel2 ||
                                        "選択されていません" }}</p>
                                    <div class="text-danger text-left" v-if="!check_trading_restriction">募集対象を選択してください。
                                    </div>
                                    <div aria-labelledby="select_trading_restriction_modal" class="modal"
                                        id="select_trading_restriction_modal" tabindex="-1" style="display: none;"
                                        aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h4 class="modal-title w-100">
                                                        募集対象を選択</h4>
                                                    <button aria-label="Close" class="close" data-dismiss="modal"
                                                        type="button"><span aria-hidden="true"><i
                                                                class="material-icons md-dark mb-36">clear</i></span>
                                                    </button>
                                                </div>
                                                <div class="modal-body flex-column">
                                                    <div class="row mb-3 justify-content-center">
                                                        <div class="col-12 mb-2 justify-content-center">
                                                            <div class="trading_restriction_checkboxes">
                                                                <div class="selecting-form row px-3">
                                                                    <input type="hidden"
                                                                        name="opportunity[trading_restriction][]"
                                                                        value="">

                                                                    <div
                                                                        class="custom-control custom-checkbox pl-4 col-12 col-sm-6 col-md-4">
                                                                        <input class="custom-control-input"
                                                                            id="checkbox_0" type="checkbox"
                                                                            value="own_employee"
                                                                            v-model="selectedRestrictions">
                                                                        <label
                                                                            class="custom-control-label anavi-select-label mb-3"
                                                                            for="checkbox_0">自社社員</label>
                                                                    </div>

                                                                    <div
                                                                        class="custom-control custom-checkbox pl-4 col-12 col-sm-6 col-md-4">
                                                                        <input class="custom-control-input"
                                                                            id="checkbox_1" type="checkbox"
                                                                            value="one_subcontract_employee"
                                                                            v-model="selectedRestrictions">
                                                                        <label
                                                                            class="custom-control-label anavi-select-label mb-3"
                                                                            for="checkbox_1">協力会社社員</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="max-width-200">
                                                            <button aria-label="Close"
                                                                class="btn btn-blue-grey mx-auto btn-modal-close waves-effect waves-light"
                                                                data-dismiss="modal">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mb-2"><label class="active font-middle mb-3" for="_field">稼働率<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                        class="custom-grey-text d-block mb-3">希望する稼働率を選択してください。</span>
                                    <RateSlider :min="25" :max="100" :step="25" :startValue="utilization_rate"
                                        @update:rate="val => utilization_rate = val" />
                                    <input type="hidden" :value="utilization_rate" name="opportunity[utilization_rate]">
                                    <div class="text-danger text-left"
                                        v-if="hasAttemptedSubmit && !check_utilization_rate">
                                        稼働率を選択してください。</div>
                                </div>

                                <div class="col-12 mb-5"><label class="active font-middle mb-3" for="_field">出社頻度<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <FrequencySlider :min="0" :max="6" :step="1" :startValue="work_frequencies"
                                        @update:frequency="val => work_frequencies = val" />
                                    <input type="hidden" :value="work_frequencies" name="opportunity[work_frequencies]">
                                    <div class="text-danger text-left"
                                        v-if="hasAttemptedSubmit && !check_work_frequencies">
                                        出社頻度を選択してください。</div>
                                </div>

                                <div class="col-12 mb-2">
                                    <div class="mb-5"><label class="font-middle mb-0">案件確度<span
                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2">必須</span></label>
                                        <div class="form-inline my-3">
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="order_accuracy_ids" id="order_accuracy_id_field0"
                                                    required="required" type="radio" value="afte"
                                                    name="opportunity[order_accuracy_id]"><label
                                                    id="order_accuracy_id_field_label_0" class="form-check-label"
                                                    for="order_accuracy_id_field0" required="required">確定済み</label>
                                            </div>
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="order_accuracy_ids" id="order_accuracy_id_field1"
                                                    required="required" type="radio" value="befo"
                                                    name="opportunity[order_accuracy_id]"><label
                                                    id="order_accuracy_id_field_label_1" class="form-check-label"
                                                    for="order_accuracy_id_field1" required="required">確定前</label>
                                            </div>
                                        </div>
                                        <div class="text-danger text-left"
                                            v-if="hasAttemptedSubmit && !check_order_accuracy_ids">
                                            案件確度を選択してください。</div>
                                    </div>
                                </div>

                                <div class="col-12 mb-5"><label class="font-middle mb-3" for="">商流への関与<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                        class="custom-grey-text d-block mb-3">それぞれの選択肢の意味は<a
                                            id="display_involvement_description_btn" class="default-main-color"
                                            data-toggle="modal" data-target="#involvement_description_modal"
                                            href="javascript:void(0)">解説図（商流に入る・仲介のみ）</a>からも確認できます。</span>
                                    <div class="mb-3">
                                        <div class="row mb-3">
                                            <div class="col-xl-2 col-md-3 col-12"><input class="form-check-input"
                                                    v-model="involvements" id="involvement_field0" type="radio"
                                                    value="enter_sales_channels" name="opportunity[involvement]"
                                                    @click="SelectedInvolvement(1)"><label class="form-check-label"
                                                    for="involvement_field0">商流に入る</label>
                                            </div>
                                            <div class="custom-grey-6-text col-xl-10 col-md-9 col-12">
                                                自社が商流に入り、契約主体となる案件
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-xl-2 col-md-3 col-12"><input class="form-check-input"
                                                    v-model="involvements" id="involvement_field1" type="radio"
                                                    value="agent_only" name="opportunity[involvement]"
                                                    @click="SelectedInvolvement(2)"><label class="form-check-label"
                                                    for="involvement_field1">仲介のみ</label>
                                            </div>
                                            <div class="custom-grey-6-text col-xl-10 col-md-9 col-12">
                                                自社が仲介のみ行い、仲介費用や営業支援費を​受け取る案件
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-danger text-left" v-if="hasAttemptedSubmit && !check_involvements">
                                        商流への関与を選択してください。</div>
                                </div>

                                <div class="col-12 mb-5" v-show="involvements !== 'enter_sales_channels'"><label
                                        class="font-middle mb-3" for="">案件の商流<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block"
                                            :style="{ display: showRequiredBadge ? 'inline-block' : 'none !important' }">必須</span></label>
                                    <button
                                        class="mdb-modal-form btn btn-outline-default btn-sm btn-action ml-3 my-0 waves-effect waves-light"
                                        data-target="#select_trading_flow_modal" data-toggle="modal" href=""
                                        type="button">選択
                                    </button>
                                    <p class="mb-3" id="type-id-selected">{{ selectedLabel3 || "選択されていません" }}</p><input
                                        type="hidden" name="opportunity[opp_type_id]" id="opp_type_id"
                                        autocomplete="off">
                                    <div class="text-danger text-left"
                                        v-if="hasAttemptedSubmit && !check_opp_type_id && involvements !== 'enter_sales_channels'">
                                        案件の商流を選択してください。</div>
                                    <div aria-labelledby="select_trading_flow_modal" class="modal"
                                        id="select_trading_flow_modal" tabindex="-1" style="display: none;"
                                        aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h4 class="modal-title w-100">
                                                        案件の商流を選択</h4>
                                                    <button aria-label="Close" class="close" data-dismiss="modal"
                                                        type="button"><span aria-hidden="true"><i
                                                                class="material-icons md-dark mb-36">clear</i></span>
                                                    </button>
                                                </div>
                                                <div class="modal-body flex-column">
                                                    <div class="alert alert-warning w-100" id="not-selected"
                                                        :style="{ display: isSelected ? 'none' : 'block' }">
                                                        商流への関与を選択してください。
                                                    </div>
                                                    <div class="" id="selected" v-show="isSelected">
                                                        <div class="row mb-3 justify-content-center">
                                                            <div class="col-12 mb-4 custom-grey-5-text">
                                                                登録する案件の商流の深さを選択してください。<br><span
                                                                    id="trading-notification"></span></div>
                                                            <div class="col-12 mb-2">
                                                                <div class="row">
                                                                    <div class="col-12 col-md-6 mb-3">
                                                                        <div class="row">
                                                                            <div class="col-xl-4 col-12 pr-xl-0"><input
                                                                                    class="form-check-input"
                                                                                    v-model="opp_type_id"
                                                                                    id="opp_type_id_field0" type="radio"
                                                                                    value="clnt"
                                                                                    name="opportunity[opp_type_id]"><label
                                                                                    class="form-check-label"
                                                                                    for="opp_type_id_field0">エンド</label>
                                                                            </div>
                                                                            <div
                                                                                class="custom-grey-6-text col-xl-8 col-md-9 col-12 pl-xl-0">
                                                                                自社プロジェクトの案件
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-6 mb-3">
                                                                        <div class="row">
                                                                            <div class="col-xl-4 col-12 pr-xl-0"><input
                                                                                    class="form-check-input"
                                                                                    v-model="opp_type_id"
                                                                                    id="opp_type_id_field1" type="radio"
                                                                                    value="prim"
                                                                                    name="opportunity[opp_type_id]"><label
                                                                                    class="form-check-label"
                                                                                    for="opp_type_id_field1">元請</label>
                                                                            </div>
                                                                            <div
                                                                                class="custom-grey-6-text col-xl-8 col-md-9 col-12 pl-xl-0">
                                                                                エンド企業から直接依頼を受けた場合
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-6 mb-3">
                                                                        <div class="row">
                                                                            <div class="col-xl-4 col-12 pr-xl-0"><input
                                                                                    class="form-check-input"
                                                                                    v-model="opp_type_id"
                                                                                    id="opp_type_id_field2" type="radio"
                                                                                    value="subc"
                                                                                    name="opportunity[opp_type_id]"><label
                                                                                    class="form-check-label"
                                                                                    for="opp_type_id_field2">一次請</label>
                                                                            </div>
                                                                            <div
                                                                                class="custom-grey-6-text col-xl-8 col-md-9 col-12 pl-xl-0">
                                                                                元請企業から依頼を受けた場合
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-12 col-md-6 mb-3">
                                                                        <div class="row">
                                                                            <div class="col-xl-4 col-12 pr-xl-0"><input
                                                                                    class="form-check-input"
                                                                                    v-model="opp_type_id"
                                                                                    id="opp_type_id_field3" type="radio"
                                                                                    value="msubc"
                                                                                    name="opportunity[opp_type_id]"><label
                                                                                    class="form-check-label"
                                                                                    for="opp_type_id_field3">二次請以降</label>
                                                                            </div>
                                                                            <div
                                                                                class="custom-grey-6-text col-xl-8 col-md-9 col-12 pl-xl-0">
                                                                                一次請以降の企業から依頼を受けた場合
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-12 mb-2 d-flex justify-content-center">
                                                                <picture id="flow-picture-0">
                                                                    <source media="(max-width: 575px)"
                                                                        srcset="/assets/trading_flow_involved_sp-baef012fbd6726635b588c280acee788429d5c805ab209550649df52dbefbde5.png">
                                                                </picture>
                                                                <picture class="d-none" id="flow-picture-1">
                                                                    <source media="(max-width: 575px)"
                                                                        srcset="/assets/trading_flow_agent_sp-94fc5b33df7f1108a8c0db07cab8657252edbf22d16adc30f27f1a8f043b847a.png">
                                                                    <img
                                                                        src="/assets/trading_flow_agent-cf2f3327a916cf3de3135728eac34e95cb8fa10495176562b7fb6c91f2367830.png">
                                                                </picture>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="max-width-200">
                                                            <button aria-label="Close"
                                                                class="btn btn-blue-grey mx-auto btn-modal-close waves-effect waves-light"
                                                                data-dismiss="modal">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mb-5"><label class="active font-middle mb-3" for="_field">契約形態<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <div class="custom-grey-text d-block mb-3" id="dispatch_license_change_message">
                                        派遣契約を選択したい場合、会社情報の労働者派遣事業許可の有無を「有」に設定してください。<a id="dispatch_license_update_btn"
                                            href="/companies/manage/edit">設定を変更する</a></div>
                                    <div class="mx-auto mt-0 mb-3">
                                        <div class="selecting-form row px-3"><input type="hidden"
                                                name="opportunity[contract_types][]" value="">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-sm-3"><input
                                                    class="custom-control-input" id="contract_types_field_opportunity_0"
                                                    required="required" type="checkbox" value="quas"
                                                    v-model="contract_types" name="opportunity[contract_types][]"><label
                                                    id="contract_types_field_label_0"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="contract_types_field_opportunity_0"
                                                    required="required">業務委託（準委任）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-sm-3"><input
                                                    class="custom-control-input" id="contract_types_field_opportunity_1"
                                                    required="required" type="checkbox" value="subc"
                                                    v-model="contract_types" name="opportunity[contract_types][]"><label
                                                    id="contract_types_field_label_1"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="contract_types_field_opportunity_1"
                                                    required="required">業務委託（請負）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-sm-3"><input
                                                    class="custom-control-input" id="contract_types_field_opportunity_2"
                                                    required="required" type="checkbox" value="temp"
                                                    v-model="contract_types" name="opportunity[contract_types][]"><label
                                                    id="contract_types_field_label_2"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="contract_types_field_opportunity_2"
                                                    required="required">派遣契約</label></div>
                                        </div>
                                        <div class="text-danger text-left" v-if="!check_contract_types">契約形態を選択してください。
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mb-5">
                                    <div><label class="font-middle mb-0">面談回数<span
                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2">必須</span></label>
                                        <div class="form-inline my-3">
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="interview_count_id" id="interview_count_id_field0"
                                                    required="required" type="radio" value="once"
                                                    name="opportunity[interview_count_id]"><label
                                                    id="interview_count_id_field_label_0" class="form-check-label"
                                                    for="interview_count_id_field0" required="required">1回</label>
                                            </div>
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="interview_count_id" id="interview_count_id_field1"
                                                    required="required" type="radio" value="twice"
                                                    name="opportunity[interview_count_id]"><label
                                                    id="interview_count_id_field_label_1" class="form-check-label"
                                                    for="interview_count_id_field1" required="required">2回</label>
                                            </div>
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="interview_count_id" id="interview_count_id_field2"
                                                    required="required" type="radio" value="other"
                                                    name="opportunity[interview_count_id]"><label
                                                    id="interview_count_id_field_label_2" class="form-check-label"
                                                    for="interview_count_id_field2" required="required">その他</label>
                                            </div>
                                        </div>
                                        <div class="text-danger text-left" v-if="!check_interview_count_id">
                                            面談回数を選択してください。</div>
                                    </div>
                                </div>

                                <div class="col-12 mb-3">
                                    <div class="row">
                                        <div class="col-10 col-md-5 max-iphone-p-0">
                                            <div class="mx-auto mb-2">
                                                <label class="font-middle mb-3" for="">面談可能時間</label>
                                                <select v-model="start_time" class="custom-select-time dropdown-below"
                                                    style="position: relative; z-index: 100;" required>
                                                    <option disabled value="">-- 選択 --</option>
                                                    <option v-for="h in 15" :key="h"
                                                        :value="(h+7).toString().padStart(2, '0') + ':00'">
                                                        {{ (h+7).toString().padStart(2, '0') }}:00
                                                    </option>
                                                </select>
                                                <div class="text-danger text-left"
                                                    v-if="hasAttemptedSubmit && !check_start_time">
                                                    {{ error_start_time }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-2 col-md-1 px-0 pt-5 d-flex align-items-center">
                                            <span>から</span>
                                        </div>
                                        <div class="col-10 col-md-5">
                                            <div class="mx-auto mb-2">
                                                <div class="font-middle mb-3" style="height: 1.5rem;">&nbsp;</div>
                                                <select v-model="end_time" class="custom-select-time dropdown-below"
                                                    style="position: relative; z-index: 100;" required>
                                                    <option disabled value="">-- 選択 --</option>
                                                    <option v-for="h in 15" :key="h"
                                                        :value="(h+7).toString().padStart(2, '0') + ':00'">
                                                        {{ (h+7).toString().padStart(2, '0') }}:00
                                                    </option>
                                                </select>
                                                <div class="text-danger text-left"
                                                    v-if="hasAttemptedSubmit && !check_end_time">
                                                    {{ error_end_time }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-2 col-md-1 px-0 pt-5 d-flex align-items-center">
                                            <span>まで</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Weekly Availability Section -->
                                <div class="col-12 mb-3">
                                    <label class="font-middle mb-3">面談可能曜日</label>
                                    <div class="row">
                                        <div class="col-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    id="available_monday" v-model="available_monday">
                                                <label class="custom-control-label" for="available_monday">月</label>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    id="available_tuesday" v-model="available_tuesday">
                                                <label class="custom-control-label" for="available_tuesday">火</label>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    id="available_wednesday" v-model="available_wednesday">
                                                <label class="custom-control-label" for="available_wednesday">水</label>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    id="available_thursday" v-model="available_thursday">
                                                <label class="custom-control-label" for="available_thursday">木</label>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    id="available_friday" v-model="available_friday">
                                                <label class="custom-control-label" for="available_friday">金</label>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input"
                                                    id="available_saturday" v-model="available_saturday">
                                                <label class="custom-control-label" for="available_saturday">土</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 mb-5"><label class="font-middle mb-2" for="">案件内容<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                        class="custom-grey-text d-block mb-3">作業内容 / 開発環境（開発プロジェクトの場合） /
                                        契約条件などを、できるだけ具体的に記載しましょう。<br>出社頻度 / 稼働率が相談可能な場合はその旨を記載してください。</span>
                                    <div class="mx-auto mb-1">
                                        <div class=""></div>
                                        <textarea rows="10" class="form-control" autocomplete="off"
                                            v-model="requirements" id="requirement_field"
                                            name="opportunity[requirement]"></textarea>
                                    </div>
                                    <div class="text-danger text-left" v-if="hasAttemptedSubmit && !check_requirements">
                                        案件内容を入力してください。</div>
                                    <div class="d-flex justify-content-between align-items-start">
                                        <CharacterCounter targetId="requirement_field" :max-length="2000" />
                                        <a class="mdb-modal-form requirement-sample-insertion default-main-color"
                                            data-target="#requirement-sample-modal" data-toggle="modal"
                                            href="">サンプルを使用</a>
                                    </div>
                                </div>
                                <div class="col-12 mb-2"><label class="font-middle mb-2" for="">人財要件<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <div class="mx-auto mb-1">
                                        <div class=""></div>
                                        <textarea rows="10" class="form-control" autocomplete="off"
                                            v-model="skill_requirements" id="skill_requirements_field"
                                            name="opportunity[skill_requirements]"></textarea>
                                    </div>
                                    <div class="text-danger text-left"
                                        v-if="hasAttemptedSubmit && !check_skill_requirements">人財要件を入力してください。</div>
                                    <div class="d-flex justify-content-between align-items-start">
                                        <CharacterCounter targetId="skill_requirements_field" :max-length="2000" />
                                        <a class="mdb-modal-form skill_requirements-sample-insertion default-main-color"
                                            data-target="#skill_requirements-sample-modal" data-toggle="modal"
                                            href="">サンプルを使用</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="pl-0 pl-sm-2">
                            <div class="row px-3">
                                <div class="col-12 mb-2">
                                    <div class="mb-5"><label class="font-middle mb-0">案件の公開設定<span
                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2">必須</span></label>
                                        <div class="form-inline my-3">
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="public_status_id" id="public_status_id_field0"
                                                    required="required" type="radio" value="public"
                                                    name="opportunity[public_status_id]"><label
                                                    id="public_status_id_field_label_0" class="form-check-label"
                                                    for="public_status_id_field0" required="required">公開</label></div>
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="public_status_id" id="public_status_id_field1"
                                                    required="required" type="radio" value="limited"
                                                    name="opportunity[public_status_id]"><label
                                                    id="public_status_id_field_label_1" class="form-check-label"
                                                    for="public_status_id_field1"
                                                    required="required">ブックマーク先のみに公開</label></div>
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="public_status_id" id="public_status_id_field2"
                                                    required="required" type="radio" value="private"
                                                    name="opportunity[public_status_id]"><label
                                                    id="public_status_id_field_label_2" class="form-check-label"
                                                    for="public_status_id_field2" required="required">非公開</label>
                                            </div>
                                        </div>
                                        <div class="text-danger text-left" v-if="!check_public_status_id">
                                            案件の公開設定を選択してください。</div>
                                    </div>
                                    <div class="row pl-3 accessible-bookmark" v-show="public_status_id === 'limited'">
                                        <div class="col-12 col-md-6">
                                            <div class="mx-auto mb-5"><label class="font-middle mb-3">公開する会社<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                <div
                                                    v-dropdown="{ modelValue: default_user, listType: 'accessible_bookmark_user_groups_field'}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-6">
                                            <div class="mx-auto mb-5"><label class="font-middle mb-3">公開する人財<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                <div
                                                    v-dropdown="{ modelValue: default_resume, listType: 'accessible_bookmark_resume_groups_field' }">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row px-3">
                                <div class="col-12"><label class="font-middle mb-3"
                                        for="publish_company_name_status_id_field">会社情報の公開設定<span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <div class="mb-5">
                                        <div class="form-inline my-3">
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="publish_company_name_status_id"
                                                    id="publish_company_name_status_id_field0" type="radio"
                                                    value="public"
                                                    name="opportunity[publish_company_name_status_id]"><label
                                                    id="publish_company_name_status_id_field_label_0"
                                                    class="form-check-label"
                                                    for="publish_company_name_status_id_field0">公開</label></div>
                                            <div class="form-check "><input class="form-check-input"
                                                    v-model="publish_company_name_status_id"
                                                    id="publish_company_name_status_id_field1" type="radio"
                                                    value="private"
                                                    name="opportunity[publish_company_name_status_id]"><label
                                                    id="publish_company_name_status_id_field_label_1"
                                                    class="form-check-label"
                                                    for="publish_company_name_status_id_field1">非公開</label></div>
                                        </div>
                                        <div class="text-danger text-left" v-if="!check_publish_company_name_status_id">
                                            会社情報の公開設定を選択してください。</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-md-center pb-3">
                                <div class="col-12">
                                    <div class="row justify-content-md-center save_bottum_area">
                                        <div class="col-12 col-md-4 mt-2">
                                            <button name="save_button" type="button" value="use_ticket_button"
                                                class="btn btn-default btn-block btn-lg font-extralarge confirm-open waves-effect waves-light"
                                                id="create-modal-button" @click="opp_new">登録
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div aria-hidden="true" aria-labelledby="requirement-sample-modal" class="modal" id="requirement-sample-modal"
            role="dialog" tabindex="-1">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title w-100">サンプルを使用</h4>
                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                            <span aria-hidden="true">
                                <i class="material-icons md-dark mb-36">
                                    clear
                                </i>
                            </span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-4">既に入力されている内容は上書きされてしまいますが、よろしいですか？</p>
                    </div>
                    <div class="modal-footer">
                        <div class="col-6">
                            <button class="btn btn-outline-blue-grey btn-block px-0 waves-effect waves-light"
                                data-dismiss="modal" type="button">キャンセル
                            </button>
                        </div>

                        <div class="col-6">
                            <button
                                class="btn btn-default btn-block waves-effect px-0 waves-light insert-requirement-sample"
                                @click="setTextareaValue" data-dismiss="modal" style="padding: .7em;" type="button">上書き
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="remove_tag_id" id="remove_tag_id" value="contract_types_field2"
            autocomplete="off"><input type="hidden" name="remove_message_id" id="remove_message_id"
            value="dispatch_license_change_message" autocomplete="off">
        <div aria-hidden="true" aria-labelledby="dispatch_license_update_modal" class="modal"
            id="dispatch_license_update" role="dialog" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header pb-0">
                        <h4 class="modal-title w-100">会社データ変更</h4>
                        <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span></button>
                    </div>
                    <div class="modal-body px-5 confirm-disable-content-modal-body resumes-modal-body">
                        <p class="mb-4"></p>
                        <div class="mb-3">会社情報の労働者派遣事業許可を「有」に変更しますか？</div>
                        労働者派遣事業許可を取得している場合のみ、「有」に設定してください。<br>「会社データ変更」ページの労働者派遣事業許可の項目でも設定を変更することが可能です。
                        <p></p>
                        <div class="row">
                            <div class="col-6">
                                <p class="btn btn-outline-blue-grey btn-block px-0 waves-effect waves-light"
                                    data-dismiss="modal">更新しない</p>
                            </div>
                            <div class="col-6">
                                <button name="button" type="submit"
                                    class="btn btn-default btn-block px-0 waves-effect waves-light"
                                    id="dispatch_license_update_update_btn">更新する
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div aria-hidden="true" aria-labelledby="skill_requirements-sample-modal" class="modal"
            id="skill_requirements-sample-modal" role="dialog" tabindex="-1">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title w-100">サンプルを使用</h4>
                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                            <span aria-hidden="true">
                                <i class="material-icons md-dark mb-36">
                                    clear
                                </i>
                            </span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-4">既に入力されている内容は上書きされてしまいますが、よろしいですか？</p>
                    </div>
                    <div class="modal-footer">
                        <div class="col-6">
                            <button class="btn btn-outline-blue-grey btn-block px-0 waves-effect waves-light"
                                data-dismiss="modal" type="button">キャンセル
                            </button>
                        </div>

                        <div class="col-6">
                            <button
                                class="btn btn-default btn-block waves-effect px-0 waves-light insert-skill_requirements-sample"
                                @click="setSkillRequirementsText" data-dismiss="modal" style="padding: .7em;"
                                type="button">上書き
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div aria-hidden="true" aria-labelledby="involvement_description_modal" class="modal"
            id="involvement_description_modal" role="dialog" tabindex="-1">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title w-100"></h4>
                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                            <span aria-hidden="true">
                                <i class="material-icons md-dark mb-36">
                                    clear
                                </i>
                            </span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div>
                            <picture>
                                <source
                                    srcset="/assets/commercial_distribution_sp-caa3aa49aba64fbc509eca9baf3730dcbedb2d461f01c458815fefb55ed6802c.png"
                                    media="(max-width: 767px)">
                                <img src="/custom_frontend/static/img/commercial_distribution.png">
                            </picture>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <div class="col-6">
                            <button class="btn btn-default btn-block waves-effect px-0 waves-light btn-blue-grey"
                                data-dismiss="modal" type="button">閉じる
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/opportunities/manage/new.css">
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css">
    `,
    data() {
        return {
            error_expired_at: "",
            isSelected: false,
            isChecked: false,
            showRequiredBadge: true,
            selectedRestrictions: [],
            isOpen: false,
            isOpen1: false,
            isOpen2: false,
            isOpen3: false,
            hasAttemptedSubmit: false,
            label2: {
                own_employee: "自社社員",
                one_subcontract_employee: "協力会社社員（一社先）",
                more_subcontract_employee: "協力会社社員（二社先以降）",
                freelance_person: "フリーランス（本人）",
                subcontract_freelance: "フリーランス（一社先）",
                more_subcontract_freelance: "フリーランス（二社先以降）"
            },
            opt_ids: {
                clnt: "エンド　　自社プロジェクトの案件",
                prim: "元請　　エンド企業から直接依頼を受けた場合",
                subc: "一次請　　元請企業から依頼を受けた場合",
                msubc: "二次請以降　　一次請以降の企業から依頼を受けた場合",
            },
            categories: [
                { value: "design_pmo", label: "PMO" },
                { value: "design_pmpl", label: "PM・PL" },
                { label: "DX", value: "design_DX" },
                { label: "クラウド", value: "design_cloud" },
                { label: "モダナイゼション", value: "design_strategy" },
                { label: "セキュリティ", value: "design_work" },
                { label: "ITインフラ", value: "design_it" },
                { label: "AI", value: "design_ai" },
            ],
            categoriesDev: [
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_embedded", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
            ],
            categoriesInfra: [
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
            ],
            categories3: [
                { value: "operation_pmo", label: "業務システム" },
                { value: "operation_pmpl", label: "オープン" },
                { label: "クラウド", value: "operation_DX" },
                { label: "メインフレーム", value: "operation_mainframe" },
                { label: "ヘルプデスク", value: "operation_strategy" },
            ],
            isCheckboxDisabled: true,
            unitPriceMin: "",
            unitPriceMax: "",
            // Add DB fields
            errorMessage: "",
            subject: "",
            categories_design: [],
            categories_development: [],
            categories_infrastructure: [],
            categories_operation_maintenance: [],
            utilization_rate: 100,
            unit_price_min: 0,
            unit_price_max: 300,
            skill_matching_flg: "",
            work_frequencies: "5days",
            specifies_workplaces: [],
            contract_startdate_at: null,
            contract_enddate_at: null,
            possible_continue_flg: "",
            requirements: "",
            skill_requirements: "",
            order_accuracy_ids: "",
            involvements: "",
            opp_type_id: "",
            contract_types: [],
            expired_at: null,
            participants: "",
            interview_count_id: "",
            trading_restriction: [],
            opp_qualities: [],
            public_status_id: "",
            publish_company_name_status_id: "",
            business_field: "",
            default_user: "すべてのブックマーク先に公開",
            default_resume: "",
            company_id: "",
            isSubmitted: false,
            margin_bot:'',
            selectedCountry: "japan",
            Nationality: "",
            isResidential: null,
            check_subject: true,
            check_categories: true,
            check_unit_price_min: true,
            check_unit_price_max: true,
            check_utilization_rate: true,
            check_work_frequencies: true,
            check_specifies_workplaces: true,
            check_contract_startdate_at: true,
            check_contract_enddate_at: true,
            check_requirements: true,
            check_skill_requirements: true,
            check_order_accuracy_ids: true,
            check_involvements: true,
            check_opp_type_id: true,
            check_contract_types: true,
            check_expired_at: true,
            check_participants: true,
            check_interview_count_id: true,
            check_trading_restriction: true,
            check_public_status_id: true,
            check_publish_company_name_status_id: true,
            start_time: "",
            end_time: "",
            check_start_time: true,
            check_end_time: true,
            error_start_time: "",
            error_end_time: "",
            available_monday: false,
            available_tuesday: false,
            available_wednesday: false,
            available_thursday: false,
            available_friday: false,
            available_saturday: false,
        }
    },
    mounted() {
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        this.check_session();
        this.company_id = userInfo ? userInfo.user_company_id : null;
        if (this.$route.params.id != null || this.$route.params.id != "") {
            this.get_Dup();
        }

        // Initialize badge visibility based on current selection
        if (this.involvements === "enter_sales_channels") {
            this.showRequiredBadge = false;
            this.check_opp_type_id = true; // Không cần kiểm tra opp_type_id khi chọn "商流に入る"
        } else {
            this.showRequiredBadge = true;
            // Nếu đã chọn opp_type_id, đánh dấu là đã kiểm tra
            this.check_opp_type_id = (this.opp_type_id && this.opp_type_id.length > 0);
        }

        // Fix dropdown direction
        this.$nextTick(() => {
            // Ensure dropdowns open downward
            const fixDropdownDirection = () => {
                const selects = document.querySelectorAll('.dropdown-below');
                selects.forEach(select => {
                    // Add event listener to force dropdown to open downward
                    select.addEventListener('mousedown', function() {
                        // Get the position of the select element
                        const rect = this.getBoundingClientRect();
                        // Calculate available space below
                        const spaceBelow = window.innerHeight - rect.bottom;
                        // If there's not enough space below, we still force it to open downward
                        // by adjusting the position
                        if (spaceBelow < 200) {
                            // Temporarily adjust the position to ensure dropdown opens downward
                            this.style.position = 'relative';
                            this.style.zIndex = '1000';
                        }
                    });
                });
            };

            // Call immediately and also after a short delay to ensure DOM is fully loaded
            fixDropdownDirection();
            setTimeout(fixDropdownDirection, 500);
        });
    },
    watch: {
        business_field: {
            handler(newVal) {
                if (newVal && newVal.length > 0) {
                    this.check_business_field = true;
                }
            },
            immediate: true
        },
    },
    methods: {

        tabChange(tabNumber) {
            // Cập nhật trạng thái active tab
    this.activeTab = tabNumber;

    // Chỉ chuyển trang khi tab thay đổi
    if (tabNumber === 1) {
        // Nếu đang ở trang khác trang opportunities/manage/new
        if (location.pathname !== '/opportunities/manage/new') {
            // Dùng Vue Router để chuyển trang không load lại
            window.location.href = '/opportunities/manage/new';
        }
    } else if (tabNumber === 2) {
        // Nếu đang ở trang khác trang resumes/manage/new
        if (location.pathname !== '/resumes/manage/new') {
            // Dùng Vue Router để chuyển trang không load lại
            window.location.href ='/resumes/manage/new';
        }
    }
        },

        async check_session() {
            const token = localStorage.getItem("authToken");
            const response = await fetch("/api/check_session", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ token }),
            });

            const data = await response.json();
            if (!data.loginEmail) {
                localStorage.removeItem("authToken");
                window.location.href = '/login';
            }
        },
        convertDateToJapanese(date) {
            if (!date) return null;
            const year = date.getFullYear();
            const month = ('0' + (date.getMonth() + 1)).slice(-2);  // Đảm bảo tháng luôn 2 chữ số
            const day = ('0' + date.getDate()).slice(-2);  // Đảm bảo ngày luôn 2 chữ số
            return `${year}年${month}月${day}日`;
        },
        
        async get_Dup() {
            const id = this.$route.params.id;

            let response = await fetch(`/api/opp_edit?id=${id}`);

            if (!response.ok) {
                console.error("Error fetching opportunity:", response.statusText);
                return;
            }

            let result = await response.json();
            //console.log("result: ",result.data);
            if (result.success) {
                // Lấy dữ liệu từ API trước
                this.subject = result.data.subject;
                this.categories_design = result.data.categories_design.split(",");
                this.categories_development = result.data.categories_development.split(",");
                this.categories_infrastructure = result.data.categories_infrastructure.split(",");
                this.categories_operation_maintenance = result.data.categories_operation_maintenance.split(",");
                // Handle utilization_rate - convert from array to single value
                const utilizationRateArray = result.data.utilization_rate.split(",");
                this.utilization_rate = utilizationRateArray.length > 0 ? parseInt(utilizationRateArray[0]) : 100;

                this.unit_price_min = String(result.data.unit_price_min ?? 0);
                this.unit_price_max = String(result.data.unit_price_max ?? 0);
                this.skill_matching_flg = result.data.skill_matching_flg;

                // Handle work_frequencies - convert from array to single value
                const workFrequenciesArray = result.data.work_frequencies.split(",");
                this.work_frequencies = workFrequenciesArray.length > 0 ? workFrequenciesArray[0] : "5days";
                this.specifies_workplaces = result.data.specifies_workplaces.split("/").map(item => item.trim());
                this.possible_continue_flg = result.data.possible_continue_flg;
                this.requirements = result.data.requirements;
                this.skill_requirements = result.data.skill_requirements;
                this.order_accuracy_ids = result.data.order_accuracy_ids;
                this.involvements = result.data.involvements;
                this.opp_type_id = result.data.opp_type_id;
                this.contract_types = result.data.contract_types.split(",");
                this.expired_at = result.data.expired_at ? this.convertDateToJapanese(new Date(result.data.expired_at)) : null;
                this.participants = String(result.data.participants ?? "");
                this.interview_count_id = result.data.interview_count_id;
                this.selectedRestrictions = result.data.trading_restriction.split(",");
                this.opp_qualities = result.data.opp_qualities.split(",");
                this.public_status_id = result.data.public_status_id;
                this.publish_company_name_status_id = result.data.publish_company_name_status_id;
                this.business_field = result.data.business_field || "";
                this.Nationality = result.data.nationality;
                this.isResidential = result.data.resident;
                this.start_time = result.data.interview_start_time;
                this.end_time = result.data.interview_end_time;

                if (this.Nationality != "Japan") {
                    this.selectedCountry = "foreign";
                } else {
                    this.selectedCountry = "japan";
                }

                this.contract_startdate_at = result.data.contract_startdate_at ? this.convertDateToJapanese(new Date(result.data.contract_startdate_at)) : '';
                this.contract_enddate_at = result.data.contract_enddate_at ? this.convertDateToJapanese(new Date(result.data.contract_enddate_at)) : '';
                document.getElementById('possible_continue_field').checked = result.data.possible_continue_flg === 'True';
                this.expired_at = result.data.expired_at ? this.convertDateToJapanese(new Date(result.data.expired_at)) : '';

                // Sau khi lấy dữ liệu, cập nhật dropdown business_field
                // Thêm timeout để đảm bảo DOM đã được cập nhật
                setTimeout(() => {
                    this.updateBusinessFieldDropdown();
                }, 500);
            }
        },
        async opp_new() {
            this.hasAttemptedSubmit = true;

            // Debug các giá trị trước khi validate
            console.log("Before validation - unit_price_min:", this.unit_price_min, "unit_price_max:", this.unit_price_max);
            console.log("Before validation - utilization_rate:", this.utilization_rate);
            console.log("Before validation - work_frequencies:", this.work_frequencies);

            if(!this.validateInput()){
                window.toastr.warning("データが不正です。");
                console.log("Validation failed");
                return;
            }

            // Debug các giá trị sau khi validate thành công
            console.log("After validation - unit_price_min:", this.unit_price_min, "unit_price_max:", this.unit_price_max);
            console.log("After validation - utilization_rate:", this.utilization_rate);
            console.log("After validation - work_frequencies:", this.work_frequencies);
            if (this.isSubmitted) return;
            this.isSubmitted = true;

            try {

                let formattedText = this.specifies_workplaces.join(',');
                const response = await fetch('/api/opp_new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        subject: this.subject,
                        categories_design: this.categories_design.join(","),
                        categories_development: this.categories_development.join(","),
                        categories_infrastructure: this.categories_infrastructure.join(","),
                        categories_operation_maintenance: this.categories_operation_maintenance.join(","),
                        utilization_rate: String(this.utilization_rate),
                        unit_price_min: this.skill_matching_flg ? 0 : this.unit_price_min,
                        unit_price_max: this.skill_matching_flg ? 0 : this.unit_price_max,
                        skill_matching_flg: this.skill_matching_flg,
                        work_frequencies: String(this.work_frequencies),
                        specifies_workplaces: formattedText,
                        contract_startdate_at: this.convertJapaneseDate($("#contract_startdate_at_field").val()), //this.contract_startdate_at,
                        contract_enddate_at: this.convertJapaneseDate($("#contract_enddate_at_field").val()), //this.contract_enddate_at,
                        possible_continue_flg: this.possible_continue_flg,
                        requirements: this.requirements,
                        skill_requirements: this.skill_requirements,
                        order_accuracy_ids: this.order_accuracy_ids,
                        involvements: this.involvements,
                        opp_type_id: this.opp_type_id,
                        contract_types: this.contract_types.join(","),
                        expired_at: this.convertJapaneseDate($("#expired_at_field").val()), //this.expired_at,
                        participants: this.participants,
                        interview_count_id: this.interview_count_id,
                        trading_restriction: this.selectedRestrictions.join(","),
                        opp_qualities: this.opp_qualities.join(","),
                        public_status_id: this.public_status_id,
                        publish_company_name_status_id: this.publish_company_name_status_id,
                        business_field: this.business_field,
                        nationality: this.selectedCountry === 'japan' ? 'Japan' : this.Nationality,
                        resident: this.selectedCountry === 'japan' ? '' : this.isResidential,
                        company_id: this.company_id,
                        interview_start_time: this.start_time,
                        interview_end_time: this.end_time,
                        available_monday: this.available_monday,
                        available_tuesday: this.available_tuesday,
                        available_wednesday: this.available_wednesday,
                        available_thursday: this.available_thursday,
                        available_friday: this.available_friday,
                        available_saturday: this.available_saturday
                    })
                });

                const data = await response.json();
                console.log('Ruturned result:', data);

                if (data.result.success) {
                    console.log('Opp create new successful:', data.result.message);
                    window.location.href = '/opportunities/manage/index';
                } else {
                    console.log('Opp create new failed:', data.result.message);
                    this.errorMessage = data.result.message;
                    window.toastr.warning(data.result.message);
                    this.isSubmitted = false;
                }
            } catch (error) {
                console.log('Error API:', error.message);
                this.errorMessage = error.message || 'Đã xảy ra lỗi khi gửi dữ liệu';
                this.isSubmitted = false;
                window.toastr.error(this.errorMessage);
            }
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        toggleAccordion() {
            this.isOpen = !this.isOpen; // Đảo trạng thái mở/đóng
        },
        toggleAccordion1() {
            this.isOpen1 = !this.isOpen1; // Đảo trạng thái mở/đóng
        },
        toggleAccordion2() {
            this.isOpen2 = !this.isOpen2; // Đảo trạng thái mở/đóng
        },
        toggleAccordion3() {
            this.isOpen3 = !this.isOpen3; // Đảo trạng thái mở/đóng
        },

        handleBusinessFieldSelected(event) {
            // Kiểm tra xem event có phải là CustomEvent với detail không
            if (event && event.detail) {
                this.business_field = event.detail;
            } else {
                this.business_field = event;
            }
            // Đảm bảo check_business_field được cập nhật chỉ khi giá trị không rỗng và không phải là "選択してください。"
            this.check_business_field = this.business_field && this.business_field.length > 0 && this.business_field !== "選択してください。";
        },

        handleCountryChange(event) {
            this.selectedCountry = event.target.value;

            if (this.selectedCountry === "japan") {
                this.Nationality = ""; // Xóa nội dung ô nhập quốc gia

                // Bỏ chọn tất cả radio button trong nhóm "reside"
                document.querySelectorAll('input[name="opportunity[reside]"]').forEach(radio => {
                    radio.checked = false;
                });
                this.isResidential = null;
            }
        },

        updateBusinessFieldDropdown() {
            // Chuyển đổi giá trị business_field từ tiếng Anh sang tiếng Nhật
            if (this.business_field) {
                // Tìm tên tiếng Nhật tương ứng với giá trị tiếng Anh
                // Tạo mapping ngược từ giá trị tiếng Anh sang tiếng Nhật
                const reverseMapping = {
                    "it_telecom_internet": "情報通信・インターネット",
                    "automobile_machinery": "自動車・機械",
                    "electronics": "エレクトロニクス機器",
                    "resources_energy_materials": "資源・エネルギー・素材",
                    "finance_corporate_services": "金融・法人サービス",
                    "food_agriculture": "食品・農業",
                    "consumer_goods_pharmaceuticals": "生活用品・嗜好品・薬",
                    "entertainment_media": "娯楽・エンタメ・メディア",
                    "construction_real_estate": "建設・不動産",
                    "logistics_transport": "運輸・物流",
                    "retail_dining": "流通・外食",
                    "public_services": "生活・公共サービス"
                };

                const japaneseName = reverseMapping[this.business_field];
                console.log("Business field value:", this.business_field);
                console.log("Japanese name:", japaneseName);

                if (japaneseName) {
                    // Tạo một sự kiện để cập nhật dropdown
                    document.dispatchEvent(new CustomEvent('restoreDropdown', {
                        detail: {
                            key: 'business_field',
                            value: japaneseName
                        }
                    }));
                }
            }
        },
        SelectedInvolvement(option) {
            this.hasAttemptedSubmit = false;
            if (option == 2) {
                this.isSelected = true;
                this.showRequiredBadge = true; // Show the badge when "仲介のみ" is selected
                this.check_opp_type_id = false; // Yêu cầu người dùng chọn opp_type_id
            } else {
                this.isSelected = false;
                $("#type-id-selected").text("選択されていません");
                this.opp_type_id = "";
                this.showRequiredBadge = false; // Hide the badge when "商流に入る" is selected
                this.check_opp_type_id = true; // Tự động đánh dấu là đã kiểm tra, không cần chọn opp_type_id
            }
        },
        convertJapaneseDate(jpDate) {
            // Tách các thành phần từ chuỗi ngày tiếng Nhật
            let parts = jpDate.match(/(\d{4})年(\d{2})月(\d{2})日/);
            if (!parts) return null; // Nếu không khớp định dạng, trả về null

            let year = parts[1];
            let month = parts[2];
            let day = parts[3];

            // Trả về định dạng YYYY-MM-DD
            return `${year}-${month}-${day}`;
        },

        setTextareaValue() {
            const content = [
                "【作業内容】",
                "Ruby on RailsでのWebサービス開発のプロジェクトで、バックエンドエンジニアを募集しています。",
                "主に新機能の実装と、リリース後の不具合対応を行って頂く予定です。",
                "",
                "【開発環境】",
                "言語 フレームワーク：Ruby on Rails",
                "OS：CentOS 7",
                "DB：mysql",
                "運用環境：AWS",
                "作業PC：mac",
                "",
                "【契約条件】",
                "就業場所：新宿＋リモート併用（最低週3日以上、東京都新宿区まで出社できる方）",
                "　　　　※初月最初の2週間は新宿区へ出社",
                "稼働率：80％以上（100％が望ましい）",
                "期間：4月 ～ 長期となる予定です",
                "勤務時間：10:00～19:00（フレックスのため出社時間は相談可）",
                "精算条件：100％稼働の場合　140-180時間/月（上下割）",
                "　　　　　 80％稼働の場合　112-144時間/月（上下割）",
                "",
                "【備考】",
                "・アジャイル開発を積極的に取り入れているチームで、最新のシステム開発を学ぶ意欲のある方におすすめです。",
                "・PM1名、PL1名、メンバー6名の、計8名体制のチームです。",
                "・ビジネスサイドで決めた新機能に関する要件がいくつかあり、それを実装していきます。",
                "　アジャイル開発のためきちんとしたドキュメントがない場合もありますが、​弊社からも社員が1名参画中のためサポートを受けられる体制です。"
            ].join("\n");

            this.requirements = content;
            $('#requirement-sample-modal').modal('hide');
        },
        setSkillRequirementsText() {
            const content = [
                "【必須スキル】",
                "・Ruby on Railsを使った設計開発のご経験（2年以上の方が目安です）",
                "・分からないことがあった時などに、周囲とのコミュニケーションが積極的にとれる方",
                "",
                "【尚可スキル】",
                "・アジャイル開発のご経験",
                "・自然言語処理に詳しい方、あるいは勉強中の方",
                "",
                "【その他補足事項】",
                "・外国籍可ですが、エンジニア同士、またはビジネスサイドとのコミュニケーションが多々発生しますので、",
                "ビジネスレベルの日本語力がある方をご提案ください。（面談時、日本語力の確認もします）",
                "・20～30代の方が中心の現場です。"
            ].join("\n");

            this.skill_requirements = content;
            $('#skill-requirements-sample-modal').modal('hide');
        },

        validateInput(){
            // Reset all check flags
            this.check_subject = false;
            this.check_categories = false;
            this.check_unit_price_min = false;
            this.check_unit_price_max = false;
            this.check_utilization_rate = false;
            this.check_work_frequencies = false;
            this.check_specifies_workplaces = false;
            this.check_contract_startdate_at = false;
            this.check_contract_enddate_at = false;
            this.check_requirements = false;
            this.check_skill_requirements = false;
            this.check_order_accuracy_ids = false;
            this.check_involvements = false;
            this.check_opp_type_id = false;
            this.check_contract_types = false;
            this.check_expired_at = false;
            this.check_participants = false;
            this.check_interview_count_id = false;
            this.check_trading_restriction = false;
            this.check_public_status_id = false;
            this.check_publish_company_name_status_id = false;
            this.check_business_field = false;
            this.check_start_time = false;
            this.check_end_time = false;

            // Validate each field
            if (this.subject && this.subject.trim() !== '') {
                this.check_subject = true;
            }
            if (this.categories_design.length > 0 || this.categories_development.length > 0 || this.categories_infrastructure.length > 0 || this.categories_operation_maintenance.length > 0) {
                this.check_categories = true;
            }

            // Slider luôn có giá trị mặc định cho unit_price_min
            this.check_unit_price_min = true;

            // Kiểm tra unit_price_max > unit_price_min
            if (this.unit_price_max > this.unit_price_min) {
                this.check_unit_price_max = true;
            } else {
                this.check_unit_price_max = false;
            }

            // Check utilization_rate (single value)
            if (this.utilization_rate && this.utilization_rate >= 25 && this.utilization_rate <= 100) {
                this.check_utilization_rate = true;
            } else {
                this.check_utilization_rate = false;
            }

            // Check work_frequencies (single value)
            if (this.work_frequencies && this.work_frequencies.length > 0) {
                this.check_work_frequencies = true;
            } else {
                this.check_work_frequencies = false;
            }
            if (this.specifies_workplaces.length > 0) {
                this.check_specifies_workplaces = true;
            }

            const startInput = this.contract_startdate_at?.trim();
            const endInput = this.contract_enddate_at?.trim();

            if (startInput) {
                // Parse Japanese date using utility function (fixes Safari iPhone issue)
                const startDate = parseJapaneseDate(startInput);
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Xoá giờ để so sánh ngày

                if (!startDate) {
                    this.error_contract_startdate = "契約開始日の形式が正しくありません。";
                    this.check_contract_startdate_at = false;
                } else if (startDate < today) {
                    this.error_contract_startdate = "契約開始日は将来日を選択してください。";
                    this.check_contract_startdate_at = false;
                } else {
                    this.check_contract_startdate_at = true;
                    this.error_contract_startdate = "";
                }

                if (endInput) {
                    // Parse Japanese date using utility function (fixes Safari iPhone issue)
                    const endDate = parseJapaneseDate(endInput);

                    if (!endDate) {
                        this.check_contract_enddate_at = false;
                        this.error_contract_enddate = "契約終了日の形式が正しくありません。";
                    } else if (endDate > startDate) {
                        this.check_contract_enddate_at = true;
                        this.error_contract_enddate = "";
                    } else {
                        this.check_contract_enddate_at = false;
                        this.error_contract_enddate = "終了日は開始日より後でなければなりません。";
                    }
                } else {
                    this.check_contract_enddate_at = false;
                    this.error_contract_enddate = "終了日を選択してください。";
                }
            } else {
                this.check_contract_startdate_at = false;
                this.error_contract_startdate = "開始日を選択してください。";

                if (endInput) {
                    this.check_contract_enddate_at = true;
                } else {
                    this.check_contract_enddate_at = false;
                    this.error_contract_enddate = "終了日を選択してください。";
                }
            }

            if (this.requirements && this.requirements.trim() !== '') {
                this.check_requirements = true;
            }
            if (this.skill_requirements && this.skill_requirements.trim() !== '') {
                this.check_skill_requirements = true;
            }
            if (this.order_accuracy_ids !== '' && this.order_accuracy_ids.length > 0) {
                this.check_order_accuracy_ids = true;
            }
            if (this.involvements && this.involvements.length > 0) {
                this.check_involvements = true;

                // Nếu người dùng chọn "商流に入る" (enter_sales_channels), không cần kiểm tra opp_type_id
                if (this.involvements === "enter_sales_channels") {
                    this.check_opp_type_id = true; // Tự động đánh dấu là đã kiểm tra
                } else if (this.opp_type_id && this.opp_type_id.length > 0) {
                    this.check_opp_type_id = true;
                }
            } else {
                // Nếu chưa chọn involvements, vẫn kiểm tra opp_type_id như bình thường
                if (this.opp_type_id && this.opp_type_id.length > 0) {
                    this.check_opp_type_id = true;
                }
            }
            if (this.contract_types && this.contract_types.length > 0) {
                this.check_contract_types = true;
            }
            const expiredDate = $("#expired_at_field").val();
            if (!expiredDate) {
                this.check_expired_at = false;
                this.error_expired_at = "応募期限を選択してください。";
            } else {
                const convertedDate = this.convertJapaneseDate(expiredDate);
                if (!this.checkExpiredDate(convertedDate)) {
                    this.check_expired_at = false;
                    this.error_expired_at = "応募期限は30日以内に設定してください。";
                } else {
                    this.check_expired_at = true;
                    this.error_expired_at = "";
                }
            }
            if (this.participants !== null && this.participants !== undefined && this.participants !== "" && parseInt(this.participants) <= 20) {
                this.check_participants = true;
            }
            if (this.interview_count_id && this.interview_count_id.length > 0) {
                this.check_interview_count_id = true;
            }
            if (this.selectedRestrictions && this.selectedRestrictions.length > 0) {
                this.check_trading_restriction = true;
            }

            if (this.public_status_id && this.public_status_id.length > 0) {
                this.check_public_status_id = true;
            }
            if (this.publish_company_name_status_id && this.publish_company_name_status_id.length > 0) {
                this.check_publish_company_name_status_id = true;
            }

            // Kiểm tra business_field không phải là chuỗi rỗng và không phải là "選択してください。"
            if (this.business_field && this.business_field.length > 0 && this.business_field !== "選択してください。") {
                this.check_business_field = true;
            } else {
                this.check_business_field = false;
            }

            // 面談開始時間と終了時間は必須ではなくなりました
            this.check_start_time = true;
            this.check_end_time = true;

            // Auto-fill logic: Nếu chỉ nhập 1 trong 2 thì tự động điền cái còn lại
            if (this.start_time && !this.end_time) {
                this.end_time = "22:00";
            }
            if (!this.start_time && this.end_time) {
                this.start_time = "08:00";
            }

            // Validation: start_time >= end_time thì không cho submit
            if (this.start_time && this.end_time && this.start_time >= this.end_time) {
                this.check_end_time = false;
                this.error_end_time = "面談終了時間は面談開始時間より後でなければなりません。";
            } else {
                this.check_end_time = true;
                this.error_end_time = "";
            }

            // Log validation status for debugging
            console.log("Validation status:", {
                subject: this.check_subject,
                categories: this.check_categories,
                unit_price_min: this.check_unit_price_min,
                unit_price_max: this.check_unit_price_max,
                work_frequencies: this.check_work_frequencies,
                specifies_workplaces: this.check_specifies_workplaces,
                contract_startdate_at: this.check_contract_startdate_at,
                contract_enddate_at: this.check_contract_enddate_at,
                requirements: this.check_requirements,
                skill_requirements: this.check_skill_requirements,
                order_accuracy_ids: this.check_order_accuracy_ids,
                involvements: this.check_involvements,
                opp_type_id: this.check_opp_type_id,
                contract_types: this.check_contract_types,
                participants: this.check_participants,
                interview_count_id: this.check_interview_count_id,
                trading_restriction: this.check_trading_restriction,
                public_status_id: this.check_public_status_id,
                publish_company_name_status_id: this.check_publish_company_name_status_id,
                business_field: this.check_business_field
            });

            // Log actual values for debugging
            console.log("Field values:", {
                unit_price_min: this.unit_price_min,
                unit_price_max: this.unit_price_max,
                utilization_rate: this.utilization_rate,
                work_frequencies: this.work_frequencies,
                business_field: this.business_field
            });

            if (!this.check_subject || !this.check_categories ||
                !this.check_unit_price_min || !this.check_unit_price_max || !this.check_utilization_rate || !this.check_work_frequencies ||
                !this.check_specifies_workplaces || !this.check_contract_startdate_at || !this.check_contract_enddate_at ||
                !this.check_requirements || !this.check_skill_requirements || !this.check_order_accuracy_ids ||
                !this.check_involvements || !this.check_opp_type_id || !this.check_contract_types ||
                !this.check_participants || !this.check_interview_count_id ||
                !this.check_trading_restriction ||
                !this.check_public_status_id || !this.check_publish_company_name_status_id ||
                !this.check_business_field || !this.check_expired_at) {
                return false;
            }
            return true;
        },
        handleDateInput(event) {
            const rawDate = event.target.value;
            this.expired_at = this.convertJapaneseDate(rawDate);
            console.log("expired: ", this.expired_at)
        },
        checkExpiredDate(date) {
            if (!date) return false;

            const currentDate = new Date();
            currentDate.setHours(0, 0, 0, 0);

            const maxDate = new Date();
            maxDate.setHours(0, 0, 0, 0);
            maxDate.setDate(currentDate.getDate() + 30);

            const inputDate = new Date(date);
            inputDate.setHours(0, 0, 0, 0);

            return inputDate >= currentDate && inputDate <= maxDate;
        }
    },
    computed: {
        selectedLabel2() {
            return this.selectedRestrictions.map(value => this.label2[value]).join(" / ");
        },
        selectedLabel3() {
            // Chuyển đổi opp_type_id thành mảng nếu nó không phải là mảng
            let oppTypeArray = Array.isArray(this.opp_type_id) ? this.opp_type_id : [this.opp_type_id];

            return oppTypeArray
                .map(value => this.opt_ids[value]) // Lấy giá trị từ opt_ids
                .filter(Boolean) // Lọc ra những giá trị không hợp lệ (undefined/null)
                .join(" / "); // Nối chuỗi lại
        }
    },

    created() {
        const fields = [
            'subject', 'utilization_rate', 'unit_price_min', 'unit_price_max',
            'work_frequencies', 'specifies_workplaces', 'expired_at', 'contract_startdate_at', 'contract_enddate_at',
            'requirements', 'skill_requirements', 'order_accuracy_ids', 'involvements', 'opp_type_id',
            'contract_types', 'participants', 'interview_count_id',
            'opp_qualities', 'public_status_id', 'publish_company_name_status_id',
            'business_field'
        ];


        fields.forEach(field => {
            this.$watch(field, () => {
                this[`check_${field}`] = true;
            });
        });

        // Add specific watchers for the new single value fields
        this.$watch('utilization_rate', () => {
            this.check_utilization_rate = true;
        });

        this.$watch('work_frequencies', () => {
            this.check_work_frequencies = true;
        });



        ['categories_design', 'categories_development', 'categories_infrastructure', 'categories_operation_maintenance']
        .forEach(key => {
            this.$watch(key, () => {
                this.check_categories = true;
            });
        });

        this.$watch(('selectedLabel2'), () => {
            this['check_trading_restriction'] = true
        })

        this.$watch('specifies_workplaces', val => {
            this.check_specifies_workplaces = val.length > 0;
        }, { deep: true });

    },

    watch: {
        work_frequencies: {
            handler(newValue) {
                this.work_frequencies = newValue;
                console.log("work_frequencies: ", this.work_frequencies)
            },
            immediate: true,
            deep: true
        }
    }
}
export default New;