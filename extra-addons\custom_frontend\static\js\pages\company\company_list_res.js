import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const companyListRes = {
	template: `
        <main class="margin-header" id="vue-app" data-v-app="" style="background-color: #eeeeee; margin-top: 80px" >
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: '案件・人財を探す', link: '/opportunities/active' },
                { text: '会社詳細', link: null },
                { text: '会社詳細/人財一覧', link: null, current: true }
            ], 'container-fluid', '')}
             <div class="container-fluid pt-3" style="background-color: #fff;">
                <div class="w-100">
                    <div class="row">
                        <div class="col-12">
							<!-- Hiển thị loading khi đang tải dữ liệu -->
							<div v-if="!dataReady" class="text-center py-5">
								<div class="spinner-border text-primary" role="status">
									<span class="sr-only">読み込み中...</span>
								</div>
								<p class="mt-2">データを読み込んでいます...</p>
							</div>
                            <!-- Header của bảng - luôn hiển thị -->
                            <div class="resume-table-header">
                                <div class="header-cell status-header" style="width: 150px;">ステータス</div>
                                <div class="header-cell" style="width: 12%;">名前</div>
                                <div class="header-cell" style="width: 20%;">得意領域</div>
                                <div class="header-cell" style="width: 18%;">就業場所</div>
                                <div class="header-cell" style="width: 15%;">単価（万円）</div>
                                <div class="header-cell" style="width: 12%;">稼働状況</div>
                                <div class="header-cell" style="width: 8%;">性別</div>
                                <div class="header-cell" style="width: 8%;">年齢</div>
                                <div class="header-cell" style="width: 7%;">評価</div>
                            </div>

                            <!-- Hiển thị dữ liệu khi đã sẵn sàng -->
                            <div v-else>
                                <!-- Dòng dữ liệu -->
                                <a v-for="resume in resumes" :key="resume.id" class="resume-table-row" :href="'/resumes/' + resume.id + '/detail?prev_next_display=display'" @click="add_viewer(resume.id, this.isloggedin)">
                                    <!-- Status và bookmark -->
                                    <div class="status-cell" style="width: 150px;">
                                        <div v-if="viewedStatuses[resume.id] === true" class="viewed-flag"><span class="font-small">既読</span></div>
                                        <div v-else-if="check_new(resume.created_at)" class="new-flag"><span class="font-small">新着</span></div>
                                        <div v-else class="unread-flag"><span class="font-small">未読</span></div>

                                        <div class="bookmark-area" data-toggle="tooltip" title="ブックマーク">
                                            <span @click.stop.prevent="toggleBookmark(resume.id)">
                                                <i class="material-icons" style="color: #1072e9;">
                                                    {{ resume.is_react ? 'bookmark' : 'bookmark_border' }}
                                                </i>
                                            </span>
                                            <span class="pl-1 font-small" style="color: black !important;">{{ resume.react_number }}</span>
                                        </div>
                                    </div>

                                    <!-- 名前 -->
                                    <div class="cell cell-name" style="width: 12%;">
                                        <span class="default-main-color">{{ resume.initial_name }}</span>
                                    </div>

                                    <!-- 得意領域 -->
                                    <div class="cell" style="width: 20%;">
                                        <span class="default-main-color">{{ getExpCategoryName(resume) }}</span>
                                    </div>

                                    <!-- 就業場所 -->
                                    <div class="cell" style="width: 18%;">
                                        <span class="default-main-color">{{ resume.working_location }}</span>
                                    </div>

                                    <!-- 単価 -->
                                    <div class="cell" style="width: 15%; text-align: center;">
                                        <span class="default-main-color">{{resume.unit_price_min}} 〜 {{resume.unit_price_max}}</span>
                                    </div>

                                    <!-- 稼働状況 -->
                                    <div class="cell" style="width: 12%; text-align: center;">
                                        <span class="default-main-color">{{ mapping_work_status[resume.status] || '' }}</span>
                                    </div>

                                    <!-- 性別 -->
                                    <div class="cell" style="width: 8%; text-align: center;">
                                        <span class="default-main-color">{{getGenderLabel(resume.gender)}}</span>
                                    </div>

                                    <!-- 年齢 -->
                                    <div class="cell" style="width: 8%; text-align: center;">
                                        <span class="default-main-color">{{ calculateAge(resume.birthday) }}歳</span>
                                    </div>

                                    <!-- 評価 -->
                                    <div class="cell" style="width: 7%; text-align: center;">
                                        <span v-if="resume.rating_flag" class="evaluation-yes">有</span>
                                        <span v-else class="evaluation-no">無</span>
                                    </div>
                                </a>

                                <!-- Modal for each resume -->
                                <div v-for="resume in resumes" :key="'modal-'+resume.id" aria-labelledby="display_text_format_modal" class="modal" :id="'display_text_format_' + resume.id" tabindex="-1" aria-modal="true" role="dialog">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title w-100">人財詳細</h4>
                                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-4">**********************************************************<br>◆人財ID: {{resume.id}}<br>◆氏名: {{
                                                    resume.initial_name }} {{getGenderLabel(resume.gender)}} {{ calculateAge(resume.birthday) }}歳<br>◆参画可能時期:
                                                    {{resume.status}}<br>◆単価: {{resume.unit_price_min}} 〜 {{resume.unit_price_max}} / 月 <br>◆稼働率: {{
                                                    (resume?.utilization_rate || '').split(',').map(rate => mapping_utilization[rate] || '').join('／') || '' }}<br>◆就業場所:
                                                    {{ (resume.working_location ?? "").split(',').join('／') }}<br>◆所属: 自社社員<br>◆経験PR:<br>◆人物サマリー<br>--------------------------------------------<br><br><br>◆スキルサマリー<br>{{resume.experience_pr}}<br><br>◆備考<br>・<br><br>-------------------------------------------------<br><br><br>◆希望詳細:<br>・{{resume.working_hope}}<br><br>**********************************************************
                                                </div>
                                                <div class="text-center"><a aria-label="Close" class="btn btn-blue-grey waves-effect waves-light" data-dismiss="modal">閉じる</a></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <nav role="navigation" class="pagination" v-if="resumes.length > 0">
                        <ul class="pagination">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)"></a>
                            </li>
                            <li v-for="page in visiblePages" :key="page" class="page-item" :class="{ active: page === currentPage }">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item disabled" v-if="totalPages > 5 && currentPage < totalPages - 2">
                                <a class="page-link waves-effect" href="#">…</a>
                            </li>
                            <li class="page-item" v-if="totalPages > 5">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(totalPages)">{{ totalPages }}</a>
                            </li>
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)"></a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/resumes/active.css"/>
    `,
	props: ["id"],
	data() {
		return {
			resumes: [],
			currentPage: 1,
			totalPages: 1,
			totalRecord: 0,
			count_resumes: 0,
			viewedStatuses: {},
			mapping_work_status: {
				'work_available': "即日可",
				'will_be_available': "今後可",
				'depends_on_opportunities': "要相談",
				'not_corresponding': "対応不可",
			},
			mapping_working_frequency: {
				'5days': '週5日出社',
				'4days': '週4日出社',
				'3days': '週3日出社',
				'2days': '週2日出社',
				'2to4days': '週4 〜 2日出社',
				'less_than_1day': '週1日未満出社',
				'full_remote': 'フルリモート',
				'1day': '週1日出社'
			},
			mapping_utilization: {
				'100': '100%（フル稼働）',
				'75': '75%',
				'50': '50%',
				'25': '25%'
			},
			mapping_resume_visibility: {
				'private': '非公開',
				'limited': 'ログイン後に表示',
			},
			categories: [
				{ label: "PMO", value: "consul_pmo" },
				{ label: "PM・PL", value: "consul_pmpl" },
				{ label: "DX", value: "consul_DX" },
				{ label: "クラウド", value: "consul_cloud" },
				{ label: "モダナイゼション", value: "consul_strategy" },
				{ label: "セキュリティ", value: "consul_work" },
				{ label: "ITインフラ", value: "consul_it" },
				{ label: "AI", value: "consul_ai" },
			],
			categoriesDev: [
				{ label: "PMO", value: "dev_pmo" },
				{ label: "PM・PL", value: "dev_pmpl" },
				{ label: "クラウド", value: "dev_cloud" },
				{ label: "サーバー", value: "dev_server" },
				{ label: "データベース", value: "dev_db" },
				{ label: "ネットワーク", value: "dev_network" },
				{ label: "メインフレーム", value: "dev_mainfream" },
			],
			categoriesInfra: [
				{ label: "PMO", value: "infra_pmo" },
				{ label: "PM・PL", value: "infra_pmpl" },
				{ label: "サーバー", value: "infra_server" },
				{ label: "ネットワーク", value: "infra_network" },
				{ label: "データベース", value: "infra_db" },
				{ label: "クラウド", value: "infra_cloud" },
				{ label: "仮想化", value: "infra_virtualized" },
				{ label: "メインフレーム", value: "infra_mainframe" },
				{ label: "運用・保守", value: "infra_operation" },
			],
			categories3: [
				{ value: "design_business", label: "業務システム" },
				{ value: "design_open", label: "オープン" },
				{ label: "クラウド", value: "desgin_cloud" },
				{ label: "メインフレーム", value: "design_mainfream" },
				{ label: "ヘルプデスク", value: "design_helpdesk" },
			],
			dataReady: false,
			company_id: this.id,
		}
	},
	async mounted() {
		$(function () {
			$('[data-toggle="tooltip"]').tooltip()
		}),
			document.addEventListener("click", this.closeDropdown);
		const params = new URLSearchParams(window.location.search);
		this.userId = userInfo ? userInfo.user_id : null;
		const page = parseInt(params.get('page')) || 1;
		try{
			await this.fetchResumes(page, this.sortType);
			if (this.resumes.length > 0) {
				await this.checkAllViewStatuses();
			} else {
				console.log('No resumes data available.');
			}

			this.dataReady = true;
		} catch(error){
			console.log('Error fetching resumes:', error);
			this.dataReady = true;
		}
	},
	methods: {
		changePage(page) {
			if (page >= 1 && page <= this.totalPages) {
				window.location.href = `?page=${page}`; // Cập nhật URL và reload trang
			}
		},

		getGenderLabel(gender) {
			if (gender === "male") return "男性";
			if (gender === "female") return "女性";
		},
		calculateAge(birthday) {
			if (!birthday) return "N/A"; // Nếu không có ngày sinh, trả về "N/A"

			// Xử lý chuỗi có dạng "YYYY-MM-DD HH:MM:SS"
			if (typeof birthday === "string") {
				birthday = birthday.split(" ")[0]; // Lấy phần "YYYY-MM-DD", bỏ giờ
			}

			const birthDate = new Date(birthday);
			if (isNaN(birthDate.getTime())) return "N/A"; // Kiểm tra nếu ngày không hợp lệ

			const today = new Date();
			let age = today.getFullYear() - birthDate.getFullYear() + 1;

			// Kiểm tra nếu chưa tới sinh nhật trong năm nay thì trừ đi 1 tuổi
			const monthDiff = today.getMonth() - birthDate.getMonth();
			if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
				age--;
			}

			return age;
		},

		closeDropdown(e) {
			if (!this.$refs.dropdown.contains(e.target)) this.isOpen6 = false;
		},
		openModal(resumeId, event) {
			// Ngừng hành động mặc định của thẻ <a> khi nhấn nút mở modal
			const modalId = '#display_text_format_' + resumeId;

			// Dùng jQuery để mở modal
			$(modalId).modal('show');
		},

		check_new(date) {
			var today = new Date();
			var expired = new Date(date);

			// Tính số ngày còn lại
			var diffTime = expired - today;
			var diffDays = diffTime / (1000 * 60 * 60 * 24);

			return diffDays <= 7;
		},

		async fetchResumes(page = 1, sort_type = 'created_at') {
			try {
				const response = await fetch(`/api/resumes_active?company_id=${this.id}&page=${page}&sort_type=${sort_type}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'X-CSRFToken': odoo.csrf_token,
						'X-User-ID': this.userId
					},
				});
				const data = await response.json();
				if (data.success === true) {
					this.resumes = data.data;
					this.currentPage = data.current_page;
					this.totalPages = data.total_pages;
					this.totalRecord = data.total_records;
				} else {
					console.warn("API data is not in the correct format:", data);
				}
			} catch (error) {
				console.log('Error API:', error.message);
				this.errorMessage = error.message;
			}
		},
		async add_viewer(partner_id, user_id) {
			try {
				// Tạo URL với user_id nếu có
				let url = `/api/view_partner?partner_id=${encodeURIComponent(partner_id)}`;
				if (user_id) {
					url += `&user_id=${encodeURIComponent(user_id)}`;
				}

				// Gửi yêu cầu GET đến API
				const response = await fetch(url, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
				});

				// Kiểm tra nếu có lỗi từ phía server
				if (!response.ok) {
					throw new Error('Error: ' + response.statusText);
				}

				// Xử lý kết quả
				const data = await response.json();
				console.log('Response:', data);

			} catch (error) {
				console.error('Fetch error:', error);
				this.errorMessage = error.message;
			}
		},

		async isViewed(partnerId) {
			if (this.viewedStatuses[partnerId] === undefined) {
				await this.fetchViewedStatus(partnerId); // Chỉ gọi API nếu chưa có trong viewedStatuses
			}
			return this.viewedStatuses[partnerId] || false; // Trả về trạng thái đã xem nếu có, false nếu chưa xem
		},

		// Lấy trạng thái đã xem của partner từ API
		async fetchViewedStatus(partnerId) {
			try {
				const response = await fetch(`/api/check_view?user_id=${this.userId}&partner_id=${partnerId}`);
				const data = await response.json();
				this.viewedStatuses[partnerId] = data.success ? data.viewed : false; // Lưu kết quả vào viewedStatuses
			} catch (error) {
				console.error('Error fetching viewed status:', error);
				this.viewedStatuses[partnerId] = false; // Mặc định là chưa xem nếu có lỗi
			}
		},

		// Kiểm tra tất cả các partner đã được xem hay chưa (chỉ gọi API 1 lần cho mỗi partner)
		async checkAllViewStatuses() {
			const partnerIds = this.resumes.map(resume => resume.id);
			await Promise.all(partnerIds.map(id => this.isViewed(id))); // Kiểm tra tất cả partnerId trong danh sách resumes
		},

		async toggleBookmark(id) {
			try {
				const response = await fetch('/api/react_res', {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						user_id: this.userId,
						resumes_id: id,
					}),
				});

				const result = await response.json();
				console.log(result);
				if (result.result.success) {
					this.fetchResumes();
				}
			} catch (error) {
				console.error("Bookmark failed:", error);
			}
		},

		getExpCategoryName(resume) {
			// Mapping prefix to Japanese names
			const prefixMapping = {
				'consul': '設計',
				'dev': '開発',
				'infra': 'インフラ',
				'design': '運用保守'
			};

			// Tìm category từ các field categories
			const allCategories = [
				...this.categories,
				...this.categoriesDev,
				...this.categoriesInfra,
				...this.categories3
			];

			// Lấy tất cả categories fields từ API response với field mapping
			const categoryFields = [
				{ field: resume.categories_consultation, prefix: 'consul' },
				{ field: resume.categories_development, prefix: 'dev' },
				{ field: resume.categories_infrastructure, prefix: 'infra' },
				{ field: resume.categories_design, prefix: 'design' }
			];

			const results = [];

			// Xử lý từng field category
			categoryFields.forEach(({ field, prefix }) => {
				if (field) {
					// Split các categories trong field (có thể có nhiều categories cách nhau bởi dấu phẩy)
					const categories = field.split(',').map(cat => cat.trim()).filter(cat => cat);

					categories.forEach(categoryValue => {
						const category = allCategories.find(cat => cat.value === categoryValue);
						if (category) {
							const prefixJapanese = prefixMapping[prefix] || prefix;
							results.push(`${prefixJapanese}_${category.label}`);
						}
					});
				}
			});

			// Trả về tất cả categories, ngăn cách bởi dấu phẩy
			return results.length > 0 ? results.join(', ') : '';
		},
	},

	computed: {
		visiblePages() {
			let pages = [];
			if (this.totalPages <= 5) {
				pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
			} else if (this.currentPage <= 3) {
				pages = [1, 2, 3, 4, 5];
			} else if (this.currentPage >= this.totalPages - 2) {
				pages = [this.totalPages - 4, this.totalPages - 3, this.totalPages - 2, this.totalPages - 1, this.totalPages];
			} else {
				pages = [this.currentPage - 2, this.currentPage - 1, this.currentPage, this.currentPage + 1, this.currentPage + 2];
			}
			return pages;
		},
		displayedRecords() {
			return this.totalRecord < 24 ? this.totalRecord : 24;
		},
	}
}

export default companyListRes;
