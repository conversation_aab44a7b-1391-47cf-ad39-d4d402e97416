const wait_active = {
    'template': `
    <main class="pb-3 margin-header" id="vue-app">
        <div class="container-fluid"></div>
        <div class="container-fluid grabient pt-2">
            <div class="row">
                <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                    <div class="card px-0 px-md-4 py-3 mb-4 wow fadeIn animated" data-wow-delay="0.3s" style="visibility: visible; animation-name: fadeIn; animation-iteration-count: 1; animation-delay: 0.3s;">
                        <div class="card-body">
                            <div class="mx-auto py-4 px-md-4">
                                <div class="col-12 col-md-10 mx-auto">
                                    <ul>
                                        <li>ご登録いただいたメールアドレス宛にご確認メールを送信いたしました。</li>
                                        <li>メールに記載のURLへアクセス後登録完了となります。</li>
                                        <li>登録メールアドレス：{{this.mask_email}}</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-12 px-0">
                                <div class="col-12 col-md-6 mx-auto text-center px-0"><button name="button" onclick="location.href='/home'" class="btn btn-default btn-block btn-lg font-middle px-0 waves-effect waves-light">トップページへ</button></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/users/account/active.css"/>
    `,
    data(){
        return {
            mask_email: localStorage.getItem('mask_email'),
        };
    },
}
export default wait_active;