import { createSingleBreadcrumb } from "../utils/breadcrumbHelper.js";

const SignUp = {
    'template': `
<div aria-hidden="true" aria-labelledby="trouble_confirm_modal" class="modal" id="trouble_confirm" role="dialog"
     tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header"><h4 class="modal-title w-100" id="trouble_confirm_modal">トラブル確認</h4>
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">×</span></button>
            </div>
            <div class="modal-body">
                <form class="create_trouble_contact" action="/trouble_contacts" accept-charset="UTF-8"
                      data-remote="true" method="post"><input name="utf8" type="hidden" value="✓" autocomplete="off">
                    <p class="mb-3">特定会員の過去のトラブル有無を確認されたい方は下記フォームよりご連絡下さい。<br>Mi52事務局より電話にてご連絡差し上げます。
                    </p><input value="confirm" autocomplete="off" type="hidden" name="trouble_contact[contact_type]"
                               id="trouble_contact_contact_type">
                    <div class="mx-auto mb-2">
                        <div class=""></div>
                        <textarea pick_target_class="trouble-textarea" rows="5"
                                  class="form-control trouble-textarea" autocomplete="off" id="contact_body_field"
                                  name="trouble_contact[contact_body]"></textarea></div>
                    <span class="custom-grey-text d-block mb-3 small">最大1000文字まで</span>
                    <div class="message-validator mb-3" id="confirm-validator"></div>
                    <div class="row">
                        <div class="col-6"><p
                                class="btn btn-outline-blue-grey btn-block px-0 trouble-cancel waves-effect waves-light"
                                data-dismiss="modal">キャンセル</p></div>
                        <div class="col-6">
                            <button name="button" type="submit"
                                    class="btn btn-default btn-block px-0 waves-effect waves-light"
                                    data-disable-with="送信中">送信
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div aria-hidden="true" aria-labelledby="trouble_report_modal" class="modal" id="trouble_report" role="dialog"
     tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header"><h4 class="modal-title w-100" id="trouble_confirm_modal">トラブル連絡</h4>
                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                        aria-hidden="true">×</span></button>
            </div>
            <div class="modal-body">
                <form class="create_trouble_contact" action="/trouble_contacts" accept-charset="UTF-8"
                      data-remote="true" method="post"><input name="utf8" type="hidden" value="✓" autocomplete="off">
                    <p class="mb-3">会員様間でトラブルが発生した場合は、下記フォームよりご連絡下さい。</p>
                    <p></p>
                    <div class="d-block">※Mi52の運営改善に活用させて頂きます。</div>
                    <div class="d-block mb-3">※個別に調査・回答・警告等を行うものではございません。</div>
                    <p></p><input value="report" autocomplete="off" type="hidden" name="trouble_contact[contact_type]"
                                  id="trouble_contact_contact_type">
                    <div class="mx-auto mb-2">
                        <div class=""></div>
                        <textarea pick_target_class="trouble-textarea" rows="5"
                                  class="form-control trouble-textarea" autocomplete="off" id="contact_body_field"
                                  name="trouble_contact[contact_body]"></textarea></div>
                    <span class="custom-grey-text d-block mb-3 small">最大1000文字まで</span>
                    <div class="message-validator mb-3" id="report-validator"></div>
                    <div class="row">
                        <div class="col-6"><p
                                class="btn btn-outline-blue-grey btn-block px-0 trouble-cancel waves-effect waves-light"
                                data-dismiss="modal">キャンセル</p></div>
                        <div class="col-6">
                            <button name="button" type="submit"
                                    class="btn btn-default btn-block px-0 waves-effect waves-light"
                                    data-disable-with="送信中">送信
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div><!--main contents start-->
<main class="margin-header sp_fluid" id="vue-app">
    ${createSingleBreadcrumb('会員登録', 'container-fluid', '')}
    <div class="container-fluid grabient pt-2">
        <div class="row mb-7">
            <div class="col-12 col-md-10 col-lg-8 mx-auto">
                <form class="new_user" id="new_user" novalidate="novalidate" autocomplete="off" @submit.prevent="signup"
                    accept-charset="UTF-8" method="post">
                    <div class="card px-0 px-md-4 py-3 mb-4 sp_sides_uniter">
                        <div class="card-body px-3">
                            <input name="utf8" type="hidden" value="✓"
                                    autocomplete="off"><input type="hidden"
                                    name="authenticity_token"
                                    value="KqpeVHYvfmije09aXLJYNna2rFdj9E+WdLCv0F06GCDqZ2WjoXMub49c2ulkGJLLhtRwhNd+Uhj9b0hfwJhftw=="
                                    autocomplete="off">
                            <div class="mt-1 mb-5 ml-md-3 bold"><p class="text-md-center font-extralarge">
                                        会員登録後すぐにご利用いただけます​</p
                            </div>
                            <div class="container-sm">
                                <div class="post-sec">
                                    <div class="progressbar mb-30">
                                        <div class="item" :class="{'active': step === 1}"><span class="step">STEP1</span></div>
                                        <div class="item" :class="{'active': step === 2}"><span class="step">STEP2</span></div>
                                        <div class="item" :class="{'active': step === 3}"><span class="step">STEP3</span></div>
                                        <div class="item" :class="{'active': step === 4}"><span class="step">STEP4</span></div>
                                    </div>

                                    <!-- Step 1 -->
                                    <div v-show="step === 1">
                                        <div class="d-flex justify-content-center align-items-center gap-10 flex-wrap-md fs-18 fs-16-md lh-3">
                                            <span class="bg-sub color-white bold p-1">STEP1</span>
                                            <span>名前をご入力ください。</span>
                                        </div>
                                        <div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="rowtt">
                                                        <div class="col-6tt">
                                                            <label class="font-middle mb-3" for="last_name_field">姓<span
                                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                            <div class="mb-0">
                                                                <div class="mx-auto mb-5">
                                                                    <input v-model="last_name" class="form-control" autocomplete="off" id="last_name_field" type="text" name="user[last_name]">
                                                                    <div class="text-danger text-left mt-3" v-if="!check_last_name">姓を入力してください。</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6tt">
                                                            <label class="font-middle mb-3" for="first_name_field">名<span
                                                                class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                            <div class="mb-0">
                                                                <div class="mx-auto mb-5">
                                                                    <input v-model="first_name" class="form-control" autocomplete="off" id="first_name_field" type="text" name="user[first_name]">
                                                                    <div class="text-danger text-left mt-3" v-if="!check_first_name">名を入力してください。</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 2 -->
                                    <div v-show="step === 2">
                                        <div class="d-flex justify-content-center align-items-center gap-10 flex-wrap-md fs-18 fs-16-md lh-3">
                                            <span class="bg-sub color-white bold p-1">STEP2</span>
                                            <span>メールアドレス、パスワードをご入力ください。</span>
                                        </div>
                                        <div>
                                            <div class="row">
                                                <div class="col-12 col-md-6">
                                                    <div class="mx-auto mb-5"><label class="mb-3 font-middle mt-3"
                                                                                    for="email_field">メールアドレス<span
                                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input
                                                            v-model="email"
                                                            class="form-control" autocomplete="off" id="email_field" type="email" value=""
                                                            name="user[email]">
                                                                <div v-if="!check_email" class="text-danger text-left mt-3">正しい形式のメールアドレスを入力してください。</div>
                                                            <div v-if="email_exists" class="text-danger text-left mt-3">このメールアドレスは既に登録されています。</div>
                                                            </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12"><label class="font-middle mb-2"
                                                                        for="password_field">パスワード<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                    <p class="text-danger text-left mt-3" style="color: #000 !important;">パスワードは8文字以上30文字以内で、小文字・大文字・数字・記号(!@#$%^&*)を含む必要があります。</p>
                                                    <div class="pl-0 mb-5 col-md-6 password-field-container">
                                                        <input v-model="password" class="form-control" autocomplete="off"
                                                                                        id="password_field" :type="passwordFieldType"
                                                                                        name="user[password]">
                                                        <i class="material-icons password-toggle-icon" @click="togglePasswordVisibility">{{ passwordFieldIcon }}</i>
                                                        <div v-if="!check_password" class="text-danger text-left mt-3">パスワードは8文字以上30文字以内で、小文字・大文字・数字・記号(!@#$%^&*)を含む必要があります。</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 3 -->
                                    <div v-show="step === 3">
                                        <div class="d-flex justify-content-center align-items-center gap-10 flex-wrap-md fs-18 fs-16-md lh-3">
                                            <span class="bg-sub color-white bold p-1">STEP3</span>
                                            <span>会社名、URL、電話番号、設立年、従業員数、資本金、労働者派遣許可の有無をご入力ください。</span>
                                        </div>
                                        <div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="mx-auto mb-5"><label class="font-middle mb-3 mt-3" for="">会社名<span
                                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input
                                                            v-model="company_name" class="form-control" autocomplete="off" id="name_field" type="text"
                                                            name="user[company_attributes][name]">
                                                            <div v-if="!check_company_name" class="text-danger text-left mt-3">会社名を入力してください。</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">ホームぺージのURL<span
                                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input
                                                            v-model="company_site_url" class="form-control"
                                                            autocomplete="off" id="site_url_field" type="text"
                                                            name="user[company_attributes][site_url]">
                                                            <div v-if="!check_company_site_url" class="text-danger text-left mt-3">正しい形式のURLを入力してください。</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12"><label class="font-middle mb-2" for="tel_field">電話番号<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span
                                                        class="custom-grey-6-text d-block mb-3">ハイフンなしでご入力ください。</span>
                                                </div>
                                                <div class="col-12 col-md-4">
                                                    <div class="mx-auto mb-5 text-md-nowrap">
                                                        <input v-model="tel" class="form-control" autocomplete="off" id="tel_field" type="text" name="user[tel]">
                                                        <div v-if="!check_tel" class="text-danger text-left mt-3">正しい形式の電話番号を入力してください。</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12"><label class="font-middle mb-3" for="establishment_field">設立年（西暦）<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-11 col-md-4">
                                                    <div class="mx-auto mb-5 text-md-nowrap">
                                                        <input v-model="company_establishment" class="form-control" autocomplete="off" id="establishment_field" type="number" name="user[company_attributes][establishment]">
                                                        <div v-if="!check_company_establishment" class="text-danger text-left mt-3">正しい形式の設立年を入力してください。</div>
                                                    </div>
                                                </div>
                                                <div class="col pl-0"><span class="custom-grey-6-text inline-unit-label">年</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12"><label class="font-middle mb-3" for="number_of_employees_field">従業員数（人）<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-11 col-md-4">
                                                    <div class="mx-auto mb-5 text-md-nowrap">
                                                        <input v-model="company_number_of_employees" class="form-control" autocomplete="off" id="number_of_employees_field" type="number" name="user[company_attributes][number_of_employees]">
                                                        <div v-if="!check_company_number_of_employees" class="text-danger text-left mt-3">正しい形式の従業員数を入力してください。</div>
                                                    </div>
                                                </div>
                                                <div class="col pl-0"><span class="custom-grey-6-text inline-unit-label">人</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12"><label class="font-middle mb-3" for="capital_field">資本金（万円）<span
                                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-11 col-md-4">
                                                    <div class="mx-auto mb-5 text-md-nowrap">
                                                        <input v-model="company_capital" class="form-control" autocomplete="off" id="capital_field" type="number" name="user[company_attributes][capital]">
                                                        <div v-if="!check_company_capital" class="text-danger text-left mt-3">正しい形式の資本金を入力してください。</div>
                                                    </div>
                                                </div>
                                                <div class="col pl-0"><span class="custom-grey-6-text inline-unit-label">万円</span>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="mb-5"><label class="font-middle mb-0">労働者派遣事業許可の有無<span
                                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2">必須</span></label>
                                                        <div class="form-inline my-3">
                                                            <div class="form-check mr-sm-8"><input v-model="company_dispatch_license" class="form-check-input"
                                                                                                id="dispatch_license_field0"
                                                                                                required="required" type="radio"
                                                                                                value="true"
                                                                                                name="user[company_attributes][dispatch_license]"><label
                                                                    id="dispatch_license_field_label_0" class="form-check-label"
                                                                    for="dispatch_license_field0" required="required">有</label>
                                                            </div>
                                                            <div class="form-check mr-sm-8"><input v-model="company_dispatch_license" class="form-check-input"
                                                                                                id="dispatch_license_field1"
                                                                                                required="required" type="radio"
                                                                                                value="false"
                                                                                                name="user[company_attributes][dispatch_license]"><label
                                                                    id="dispatch_license_field_label_1" class="form-check-label"
                                                                    for="dispatch_license_field1" required="required">無</label>
                                                            </div>
                                                        </div>
                                                        <div v-if="!check_company_dispatch_license" class="text-danger text-left mt-3">労働者派遣事業許可の有無を選択してください。</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Step 4 -->
                                    <div v-show="step === 4">
                                        <div class="d-flex justify-content-center align-items-center gap-10 flex-wrap-md fs-18 fs-16-md lh-3">
                                            <span class="bg-sub color-white bold p-1">STEP4</span>
                                            <span>利用目的、このサイトを知ったきっかけをご入力ください。</span>
                                        </div>
                                        <div>
                                            <div class="mx-auto mt-0 mb-3"><label class="font-middle mb-3 mt-3" for="">主な利用目的（複数可）<span
                                        class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                                <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                                    name="user[purpose_of_use][]"
                                                                                                    value="">
                                                    <div class="custom-control custom-checkbox pl-4 col-12 col-md-5 col-lg-3 pr-md-3 purpose-of-use">
                                                        <input v-model="purpose_of_use" class="custom-control-input" id="purpose_of_use_field_user_0"
                                                            required="required" type="checkbox" value="find_opportunities"
                                                            name="user[purpose_of_use][]"><label
                                                            id="purpose_of_use_field_label_0"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="purpose_of_use_field_user_0"
                                                            required="required">案件を見つける</label></div>
                                                    <div class="custom-control custom-checkbox pl-4 col-12 col-md-5 col-lg-3 pr-md-3 purpose-of-use">
                                                        <input v-model="purpose_of_use" class="custom-control-input" id="purpose_of_use_field_user_1"
                                                            required="required" type="checkbox" value="find_project_staffs"
                                                            name="user[purpose_of_use][]"><label
                                                            id="purpose_of_use_field_label_1"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="purpose_of_use_field_user_1"
                                                            required="required">人財を探したい</label></div>
                                                    <div class="custom-control custom-checkbox pl-4 col-12 col-md-5 col-lg-3 pr-md-3 purpose-of-use">
                                                        <input v-model="purpose_of_use" class="custom-control-input" id="purpose_of_use_field_user_3"
                                                            required="required" type="checkbox" value="other_purposes"
                                                            name="user[purpose_of_use][]"><label
                                                            id="purpose_of_use_field_label_3"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="purpose_of_use_field_user_3" required="required">その他</label>
                                                    </div>
                                                </div>
                                                <div v-if="!check_purpose_of_use" class="text-danger text-left mt-3">利用目的を選択してください。</div>
                                            </div>
                                            <div v-show="purpose_of_use.includes('other_purposes')" class="mx-auto mb-5 purpose_of_use_note">
                                                <label class="font-middle mb-3" for="">
                                                    その他を選択した理由
                                                    <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">
                                                        必須
                                                    </span>
                                                </label>
                                                <input class="form-control" autocomplete="off" id="purpose_of_use_note_field" v-model="purpose_of_use_note" type="text" name="user[purpose_of_use_note]">
                                                <div v-if="!check_purpose_of_use_note" class="text-danger text-left mt-3">その他を選択した理由を入力してください。</div>
                                            </div>
                                            <div class="mx-auto mb-5"><label class="font-middle">このサイトを知ったきっかけ<span
                                                    class="badge-pill badge-danger pink lighten-2 font-small ml-2">必須</span></label>
                                                <div class="form-inline my-3">
                                                    <div class="form-check "><input v-model="learn_about_this_site" class="form-check-input"
                                                                                    id="learn_about_this_site_field0"
                                                                                    required="required" type="radio"
                                                                                    value="search_internet"
                                                                                    name="user[learn_about_this_site]"><label
                                                            id="learn_about_this_site_field_label_0" class="form-check-label"
                                                            for="learn_about_this_site_field0"
                                                            required="required">インターネットでの検索</label></div>
                                                    <div class="form-check "><input v-model="learn_about_this_site" class="form-check-input"
                                                                                    id="learn_about_this_site_field1"
                                                                                    required="required" type="radio"
                                                                                    value="introduction"
                                                                                    name="user[learn_about_this_site]"><label
                                                            id="learn_about_this_site_field_label_1" class="form-check-label"
                                                            for="learn_about_this_site_field1"
                                                            required="required">知人の紹介</label></div>
                                                    <div class="form-check "><input v-model="learn_about_this_site" class="form-check-input"
                                                                                    id="learn_about_this_site_field2"
                                                                                    required="required" type="radio"
                                                                                    value="sales_from_us"
                                                                                    name="user[learn_about_this_site]"><label
                                                            id="learn_about_this_site_field_label_2" class="form-check-label"
                                                            for="learn_about_this_site_field2"
                                                            required="required">当社からの営業</label></div>
                                                    <div class="form-check "><input v-model="learn_about_this_site" @change="console.log(learn_about_this_site)" class="form-check-input"
                                                                                    id="learn_about_this_site_field3"
                                                                                    required="required" type="radio" value="others"
                                                                                    name="user[learn_about_this_site]"><label
                                                            id="learn_about_this_site_field_label_3" class="form-check-label"
                                                            for="learn_about_this_site_field3" required="required">その他</label>
                                                    </div>
                                                </div>
                                                <div v-if="!check_learn_about_this_site" class="text-danger text-left mt-3">その他を選択した理由を入力してください。</div>
                                            </div>
                                            <div v-show="learn_about_this_site === 'others'" class="mx-auto mb-5 learn_about_this_site_note">
                                                <label class="mb-1 font-middle" for="">
                                                    その他を選択した理由
                                                    <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">
                                                        必須
                                                    </span>
                                                </label>
                                                <input class="form-control" autocomplete="off" id="learn_about_this_site_note_field" v-model="learn_about_this_site_note" type="text" name="user[learn_about_this_site_note]">
                                                <div v-if="!check_learn_about_this_site_note" class="text-danger text-left mt-3">その他を選択した理由を入力してください。</div>
                                            </div>
                                            <div class="mx-auto mb-5 font-middle text-md-center">
                                                <div class="selecting-form custom-control custom-checkbox z-2"><input value="false"
                                                                                                                    autocomplete="off"
                                                                                                                    type="hidden"
                                                                                                                    name="user[terms_of_service]"
                                                                                                                    id="user_terms_of_service"><input
                                                        id="terms_of_service_field" class="custom-control-input" type="checkbox" v-model="terms_of_service"
                                                        value="利用規約" name="user[terms_of_service]"><label
                                                        id="terms_of_service_field_label" class="custom-control-label"
                                                        for="terms_of_service_field"><a href="/our_services/agreement"
                                                                                        target="_blank">利用規約<i
                                                        class="material-icons font-middle vertical-sub">open_in_new</i></a> <a
                                                        href="https://verticallimit.co.jp/personal/" target="_blank">個人情報保護方針<i
                                                        class="material-icons font-middle vertical-sub">open_in_new</i></a>および<a
                                                        href="/our_services/userguide" target="_blank">ご利用ガイドライン<i
                                                        class="material-icons font-middle vertical-sub">open_in_new</i></a>に同意する。</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="consultation__btn">
                                        <button class="btn btn-prev" type="button" v-if="step !== 1" @click="step = step - 1"><　戻る</button>
                                        <button class="btn btn-next" type="button" v-if="step !== 4" @click="nextStep">次へ　></button>
                                        <button class="btn btn-default font-extralarge waves-effect waves-light btn-submit" type="submit" v-if="step === 4">登録</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main><!--main contents end-->
<link rel="stylesheet" href="/custom_frontend/static/css/signup.css"/>
<link rel="stylesheet" href="/custom_frontend/static/css/password-toggle.css"/>
    `,

    data() {
        return {
            step: 1,
            last_name: '',
            first_name: '',
            display_name: '', // Field removed from UI but kept for API compatibility
            email: '',
            password: '',
            company_name: '',
            company_site_url: '',
            tel: '',
            company_establishment: '',
            company_number_of_employees: '',
            company_capital: '',
            company_dispatch_license: '',
            purpose_of_use: [],
            purpose_of_use_note: '',
            learn_about_this_site: '',
            learn_about_this_site_note: '',
            terms_of_service: false,
            errorMessage: '',
            isSubmitted: false,
            email_exists: false, // Thêm biến để kiểm tra email đã tồn tại hay chưa
            check_last_name: true,
            check_first_name: true,
            check_email: true,
            check_password: true,
            check_company_name: true,
            check_company_site_url: true,
            check_tel: true,
            check_company_establishment: true,
            check_company_number_of_employees: true,
            check_company_capital: true,
            check_company_dispatch_license: true,
            check_purpose_of_use: true,
            check_purpose_of_use_note: true,
            check_learn_about_this_site: true,
            check_learn_about_this_site_note: true,
            check_terms_of_service: true,
            passwordFieldType: 'password', // Thêm biến để theo dõi loại trường mật khẩu
            passwordFieldIcon: 'visibility_off', // Biểu tượng mặc định là ẩn mật khẩu
        };
    },

    mounted() {
        this.loadExternalScript("/custom_frontend/static/js/pages/signup-extra.js");
    },

    methods: {
        // Phương thức để chuyển đổi hiển thị/ẩn mật khẩu
        togglePasswordVisibility() {
            this.passwordFieldType = this.passwordFieldType === 'password' ? 'text' : 'password';
            this.passwordFieldIcon = this.passwordFieldType === 'password' ? 'visibility_off' : 'visibility';
        },

        async signup() {
            // Check if already submitted
            if (this.isSubmitted) {
                window.toastr.warning('クリックを減らす');
                return;
            }

            // Validate final step before submitting
            if (!this.validateInput(4)) {
                return; // Do not continue if validation fails
            }

            // Check terms of service
            if (!this.terms_of_service) {
                window.toastr.warning('利用規約をお読みいただき、同意してください。');
                return;
            }

            // Kiểm tra email một lần nữa trước khi đăng ký
            await this.checkEmailExists();
            if (this.email_exists) {
                window.toastr.warning('このメールアドレスは既に登録されています。別のメールアドレスを入力してください。');
                this.step = 2; // Quay lại bước nhập email
                return;
            }

            this.isSubmitted = true;

            try {
                const response = await fetch('/api/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        last_name: this.last_name,
                        first_name: this.first_name,
                        display_name: "", // Field removed from UI
                        email: this.email,
                        password: this.password,
                        company_name: this.company_name,
                        company_site_url: this.company_site_url,
                        tel: this.tel,
                        company_establishment: this.company_establishment,
                        company_number_of_employees: this.company_number_of_employees,
                        company_capital: this.company_capital,
                        company_dispatch_license: this.company_dispatch_license,
                        purpose_of_use: this.purpose_of_use.join(','),
                        purpose_of_use_note: this.purpose_of_use_note,
                        learn_about_this_site: this.learn_about_this_site,
                        learn_about_this_site_note: this.learn_about_this_site_note,
                        terms_of_service: this.terms_of_service,
                    })
                });

                const data = await response.json();
                console.log('Returned result:', data);

                if (data.result.success) {
                    console.log('Signup successful:', data.result.message);
                    localStorage.setItem('mask_email', this.email); // Lưu email thật thay vì mã hóa
                    this.active_account();
                } else {
                    console.log('Signup failed:', data.result.message);
                    this.errorMessage = data.result.message;
                    window.toastr.warning(data.result.message);
                    this.isSubmitted = false;
                }
            } catch (error) {
                console.log('Error API:', data.result.message);
                this.errorMessage = data.result.message;
                this.isSubmitted = false;
            }
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },

        maskEmail(email) {
            const [_, domain] = email.split('@');
            if (!domain) return '<EMAIL>';

            if (domain === 'gmail.com') {
                return `<EMAIL>`;
            }

            // If not gmail → mask part before dot
            const domainParts = domain.split('.');
            if (domainParts.length < 2) return `xxx@xxx`;

            domainParts[0] = 'xxx'; // replace first part of domain
            return `xxx@${domainParts.join('.')}`;
        },
        async active_account(){
            try {
                const response = await fetch('/api/email_active', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: this.email }),
                });
                const data = await response.json();
                console.log('Returned result:', data);
                if (data.result.success) {
                    window.location.href = '/account/wait_active';
                } else {
                    console.log('Active account failed:', data.result.message);
                }
            } catch (error) {
                console.log('Error API:', error);
            }
        },

        validateInput(step){
            switch(step){
                case 1:
                    // Reset check flags về false
                    this.check_last_name = false;
                    this.check_first_name = false;

                    if(this.last_name && this.last_name.trim() !== ''){
                        this.check_last_name = true;
                    }
                    if(this.first_name && this.first_name.trim() !== ''){
                        this.check_first_name = true;
                    }
                    return this.check_last_name && this.check_first_name;

                case 2:
                    // Reset check flags về false
                    this.check_password = false;

                    // Chỉ kiểm tra password trong quá trình validate thông thường
                    // Email sẽ được kiểm tra riêng trong hàm nextStep
                    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[a-zA-Z\d!@#$%^&*]{8,30}$/;

                    // Đặt check_email = true để không hiển thị lỗi khi người dùng đang nhập
                    this.check_email = true;

                    if(this.password && this.password.trim() !== '' && passwordRegex.test(this.password.trim())){
                        this.check_password = true;
                    }

                    return this.check_password;

                case 3:
                    // Reset check flags về false
                    this.check_company_name = false;
                    this.check_company_site_url = false;
                    this.check_tel = false;
                    this.check_company_establishment = false;
                    this.check_company_number_of_employees = false;
                    this.check_company_capital = false;
                    this.check_company_dispatch_license = false;

                    if(this.company_name && this.company_name.trim() !== ''){
                        this.check_company_name = true;
                    }

                    const urlRegex = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
                    if(this.company_site_url && this.company_site_url.trim() !== '' && urlRegex.test(this.company_site_url.trim())){
                        this.check_company_site_url = true;
                    }

                    const telRegex = /^0\d{9,10}$/;
                    if(this.tel && this.tel.trim() !== '' && telRegex.test(this.tel.trim())){
                        this.check_tel = true;
                    }

                    const yearRegex = /^\d{4}$/;
                    const currentYear = new Date().getFullYear();
                    if(this.company_establishment &&
                       yearRegex.test(this.company_establishment) &&
                       parseInt(this.company_establishment) <= currentYear){
                        this.check_company_establishment = true;
                    }

                    if(this.company_number_of_employees && parseInt(this.company_number_of_employees) > 0){
                        this.check_company_number_of_employees = true;
                    }

                    if(this.company_capital && parseInt(this.company_capital) > 0){
                        this.check_company_capital = true;
                    }

                    if(this.company_dispatch_license && this.company_dispatch_license.trim() !== ''){
                        this.check_company_dispatch_license = true;
                    }

                    return this.check_company_name && this.check_company_site_url && this.check_tel &&
                           this.check_company_establishment && this.check_company_number_of_employees &&
                           this.check_company_capital && this.check_company_dispatch_license;

                case 4:
                    // Reset check flags về false
                    this.check_purpose_of_use = false;
                    this.check_purpose_of_use_note = false;
                    this.check_learn_about_this_site = false;
                    this.check_learn_about_this_site_note = false;

                    if(this.purpose_of_use.length > 0) {
                        this.check_purpose_of_use = true;
                    }

                    if(!this.purpose_of_use.includes('other_purposes') ||
                       (this.purpose_of_use.includes('other_purposes') && this.purpose_of_use_note && this.purpose_of_use_note.trim() !== '')) {
                        this.check_purpose_of_use_note = true;
                    }

                    if(this.learn_about_this_site && this.learn_about_this_site.trim() !== '') {
                        this.check_learn_about_this_site = true;
                    }

                    if(this.learn_about_this_site !== 'others' ||
                       (this.learn_about_this_site === 'others' && this.learn_about_this_site_note && this.learn_about_this_site_note.trim() !== '')) {
                        this.check_learn_about_this_site_note = true;
                    }

                    return this.check_purpose_of_use && this.check_purpose_of_use_note &&
                           this.check_learn_about_this_site && this.check_learn_about_this_site_note;
            }
        },

        // Hàm kiểm tra email đã tồn tại hay chưa
        async checkEmailExists() {
            // Kiểm tra định dạng email trước
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!this.email || !emailRegex.test(this.email.trim())) {
                this.check_email = false;
                this.email_exists = false;
                return;
            }

            this.check_email = true;

            try {
                const response = await fetch('/api/check_email_exists', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: this.email }),
                });

                const data = await response.json();
                if (data.result && data.result.exists) {
                    this.email_exists = true;
                    window.toastr.warning('このメールアドレスは既に登録されています。');
                } else {
                    this.email_exists = false;
                }
            } catch (error) {
                console.log('Error checking email:', error);
            }
        },

        async nextStep() {
            // Kiểm tra dữ liệu nhập vào của bước hiện tại
            if (this.validateInput(this.step)) {
                // Nếu đang ở bước 2, kiểm tra email trước khi chuyển bước
                if (this.step === 2) {
                    // Kiểm tra định dạng email
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!this.email || !emailRegex.test(this.email.trim())) {
                        this.check_email = false;
                        window.toastr.warning('正しい形式のメールアドレスを入力してください。');
                        return;
                    }

                    // Kiểm tra email đã tồn tại hay chưa
                    try {
                        const response = await fetch('/api/check_email_exists', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ email: this.email }),
                        });

                        const data = await response.json();
                        if (data.result && data.result.exists) {
                            this.email_exists = true;
                            window.toastr.warning('このメールアドレスは既に登録されています。別のメールアドレスを入力してください。');
                            return;
                        } else {
                            this.email_exists = false;
                        }
                    } catch (error) {
                        console.log('Error checking email:', error);
                    }
                }

                // Nếu mọi kiểm tra đều thành công, chuyển sang bước tiếp theo
                this.step += 1;
            }
        },
    },
    // watch: {
    //     last_name: {
    //         handler(){
    //             this.check_last_name = true
    //         }
    //     },
    //     first_name: { handler(){ this.check_first_name = true} },
    //     email: {
    //         handler() {
    //             this.check_email = true;  // Chỉ cần set về true khi email thay đổi
    //         }
    //     },
    //     password: { handler(){ this.check_password = true} },
    //     company_name: { handler(){ this.check_company_name = true} },
    //     company_site_url: { handler(){ this.check_company_site_url = true} },
    //     tel: { handler(){ this.check_tel = true} },
    //     company_establishment: { handler(){ this.check_company_establishment = true} },
    //     company_number_of_employees: { handler(){ this.check_company_number_of_employees = true} },
    //     company_capital: { handler(){ this.check_company_capital = true} },
    //     company_dispatch_license: { handler(){ this.check_company_dispatch_license = true} },
    //     purpose_of_use: { handler(){ this.check_purpose_of_use = true} },
    //     purpose_of_use_note: { handler(){ this.check_purpose_of_use_note = true} },
    //     learn_about_this_site: { handler(){ this.check_learn_about_this_site = true} },
    //     learn_about_this_site_note: { handler(){ this.check_learn_about_this_site_note = true} },
    //     terms_of_service: { handler(){ this.check_terms_of_service = true} },
    // }

    created() {
        const fields = [
          'last_name', 'first_name', 'email', 'password',
          'company_name', 'company_site_url', 'tel',
          'company_establishment', 'company_number_of_employees',
          'company_capital', 'company_dispatch_license',
          'purpose_of_use', 'purpose_of_use_note',
          'learn_about_this_site', 'learn_about_this_site_note',
          'terms_of_service'
        ];

        fields.forEach(field => {
          this.$watch(field, () => {
            this[`check_${field}`] = true;
          });
        });
    }
}
export default SignUp