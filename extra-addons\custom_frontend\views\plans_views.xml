<?xml version="1.0"?>
<odoo>
    <record id="vit_plans_view_form" model="ir.ui.view">
        <field name="name">vit.plans.form</field>
        <field name="model">vit.plans</field>
        <field name="arch" type="xml">
            <form string="Plan">
                <sheet>
                    <group>
                        <group>
                            <field name="title"/>
                            <field name="price"/>
                        </group>
                        <group>
                            <field name="type"/>
                            <field name="unit"/>
                        </group>
                        <group>
                            <field name="created_at"/>
                            <field name="created_by"/>
                            <field name="updated_at"/>
                            <field name="updated_by"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="vit_plans_view_for_tree" model="ir.ui.view">
        <field name="name">vit.plans.tree</field>
        <field name="model">vit.plans</field>
        <field name="arch" type="xml">
            <list string="Plans">
                <field name="title"/>
                <field name="price"/>
                <field name="type"/>
                <field name="unit"/>
                <field name="created_at"/>
                <field name="created_by"/>
                <field name="updated_at"/>
                <field name="updated_by"/>
            </list>
        </field>
    </record>

    <record id="vit_plans_action" model="ir.actions.act_window">
        <field name="name">VIT Plans</field>
        <field name="res_model">vit.plans</field>
        <field name="view_mode">list,form</field>
    </record>
    <menuitem id="vit_plans_menu_root" name="VIT Plans"/>
    <menuitem id="vit_plans_menu" name="Plans" parent="vit_plans_menu_root" action="vit_plans_action"/>
</odoo>