/* <PERSON><PERSON>y chỉnh radio button khi được chọn */
input[type="radio"].form-check-input:checked {
    background-color: #00b0f0; /* <PERSON><PERSON><PERSON> x<PERSON>h dươ<PERSON> */
    border-color: #00b0f0;
}

/* <PERSON><PERSON><PERSON> bảo hình dạng tròn và bo viền */
input[type="radio"].form-check-input {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #ccc;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}

.container-fluid {
    padding-left: 0px !important;
    padding-right: 0px !important;
}

input[type="radio"].form-check-input:focus {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: none !important;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    position: relative;
}


/* Inner dot khi được chọn */
input[type="radio"].form-check-input:checked::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 10px;
    height: 10px;
    background-color: #00b0f0;
    border-radius: 50%;
}
