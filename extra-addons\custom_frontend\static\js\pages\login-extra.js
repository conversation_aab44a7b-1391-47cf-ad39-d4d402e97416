// $(document).ready(function() {
//     function disableScroll(event) {
//         event.preventDefault();
//     }
//     // FIXME: header整理の時の方針に従って修正する
//     // サイドメニュー開く
//     $('.menu').on("click", function() {
//         $('.custom-side-nav').addClass('d-block')
//         document.body.classList.add('overflow-hidden');
//     });
//     // サイドメニューを閉じる
//     function closeSideBar() {
//         $('#pop_up').fadeOut();
//         $("#sidenav-overlay").remove();
//         $('.custom-side-nav').removeClass('d-block');
//         document.body.classList.remove('overflow-hidden');
//     }
//     // ×ボタンを押下時にサイドメニューを閉じる
//     $(document).on('click', '.slide-close', function() {
//         closeSideBar();
//     });
//     // サイドメニュー以外の要素を押下時にサイドメニューを閉じる
//     $(document).on('click', function(e) {
//         if (!$(e.target).closest('.custom-side-nav').length && !$(e.target).closest('.menu').length) {
//             closeSideBar();
//         }
//     });

//     // headerのkeyboard_arrow_downを制御
//     $('.header-search-menu').on('mouseenter', function() {
//         $(this).find('i').text('keyboard_arrow_up');
//     });
//     $('.header-search-menu').on('mouseleave', function(e) {
//         $(this).find('i').text('keyboard_arrow_down');
//     });
// });

// $(document).on('click', '#confirm_trouble_list, #confirm_trouble_list_second', function() {
//     $("#trouble_confirm").modal();
// });
// $("#trouble_confirm").on('show.bs.modal', function() {
//     $(this).addClass('show');
// });
// // modal-backdropはモーダル表示する時に作られる要素なので、「表示が完了したら」処理する
// $("#trouble_confirm").on('shown.bs.modal', function() {
//     $('.modal-backdrop').addClass('show');
// });

// $(document).on('click', '#report_trouble_list, #report_trouble_list_second', function() {
//     $("#trouble_report").modal();
// });
// $("#trouble_report").on('show.bs.modal', function() {
//     $(this).addClass('show');
// });
// // modal-backdropはモーダル表示する時に作られる要素なので、「表示が完了したら」処理する
// $("#trouble_report").on('shown.bs.modal', function() {
//     $('.modal-backdrop').addClass('show');
// });

// $(document).ready(function() {
//     setModalText();
//     create_trouble_contract_handler();
// });

// // トラブルモーダルの初期値をセット
// function setModalText() {
//     $('#confirm_trouble_list, #report_trouble_list, #confirm_trouble_list_second, #report_trouble_list_second').on('click', function() {
//         const confirm_trouble_list_array = ['confirm_trouble_list', 'confirm_trouble_list_second']
//         const report_trouble_list_array = ['report_trouble_list', 'report_trouble_list_second']
//         var modal_id = $(this).attr('id');
//         var add_info = `\nお相手の名前/会社名：
// トラブル内容：`
//         if ($('#opp_uuid_text').length > 0) {
//             // 案件詳細ページ上の場合、案件uuidをセット。トラブル連絡モーダルの場合はさらに追加テキストをセット
//             var opp_text = $('.trouble-textarea').val(`案件` + $('#opp_uuid_text').text());
//             if (report_trouble_list_array.includes(modal_id))
//                 $('.trouble-textarea').val(opp_text.val() + add_info);
//         } else if ($('#res_uuid_text').length > 0) {
//             // 人財詳細ページ上の場合、人財uuidをセット。トラブル連絡モーダルの場合はさらに追加テキストをセット
//             var res_text = $('.trouble-textarea').val(`人財` + $('#res_uuid_text').text());
//             if (report_trouble_list_array.includes(modal_id))
//                 $('.trouble-textarea').val(res_text.val() + add_info);
//         } else {
//             // 案件・人財詳細ページ以外の場合の初期値をトラブル連絡・トラブル確認モーダルそれぞれセット
//             if (report_trouble_list_array.includes(modal_id)) {
//                 $('.trouble-textarea').val(`お相手の名前/会社名：
// トラブル内容：`)
//             } else if (confirm_trouble_list_array.includes(modal_id)) {
//                 $('.trouble-textarea').val(`案件/人財ID：`)
//             }
//         }
//     })
// }

// function create_trouble_contract_handler() {
//     $('.create_trouble_contact').bind("ajax:complete", function(xhr, response, status) {
//         var result = JSON.parse(response.responseText)
//         if (status == "success") {
//             toastr.success(result['flash_message']);
//             $('.trouble-cancel').click();
//             $('.trouble-textarea').val('');
//             $('.message-validator').html('');
//         } else {
//             var validate_id = result['contact_type'] + '-validator'
//             $('#' + validate_id).html(result['flash_message']);
//         }
//     })
// }

// $(document).ready(function() {
//     var pagetop = $('.pagetop');
//     pagetop.click(function() {
//         $('body, html').animate({
//             scrollTop: 0
//         }, 500);
//         return false;
//     });
// });

toastr.options.extendedTimeOut = '0';
toastr.options.showDuration = '300';
toastr.options.hideDuration = '1000';
toastr.options.positionClass = 'toast-top-full-width';
toastr.options.showEasing = 'swing';
toastr.options.hideEasing = 'linear';
toastr.options.progressBar = true;
toastr.options.onclick = null;
toastr.options.escapeHtml = false;
toastr.options.closeButton = true;
toastr.options.closeHtml = '<button>&times;</button>';
toastr.options.preventDuplicates = true;

// toastr.options = {
//     closeButton: true,
//     progressBar: true,
//     positionClass: "toast-top-right",
//     timeOut: 3000,
//     showMethod: "fadeIn",
//     hideMethod: "fadeOut"
// };