# Breadcrumb Helper Documentation

## 📋 **Tổng quan**

Breadcrumb Helper là một utility library được tạo để thay thế tất cả các breadcrumb implementations riêng lẻ trong codebase. Helper này cung cấp:

- ✅ **Consistent styling** across tất cả pages
- ✅ **Responsive design** cho mobile, tablet, desktop
- ✅ **Easy to use** với helper functions
- ✅ **Maintainable** - chỉ cần update 1 chỗ
- ✅ **No Vue component complexity** - simple HTML string generation

## 🚀 **Cách sử dụng**

### 1. Import helper functions

```javascript
import { createSingleBreadcrumb, createManagementBreadcrumb } from "../utils/breadcrumbHelper.js";
```

### 2. Sử dụng trong template

```javascript
const YourPage = {
    template: `
        <main class="margin-header">
            ${createSingleBreadcrumb('ページ名')}
            <!-- rest of your template -->
        </main>
    `
}
```

### 3. <PERSON><PERSON><PERSON> helper functions có sẵn

```javascript
// Single page breadcrumb
createSingleBreadcrumb('ご利用ガイドライン')

// Service menu pattern
createServiceMenuBreadcrumb('案件を探す')

// Management pattern
createManagementBreadcrumb('人財管理一覧')

// Search pattern
createSearchBreadcrumb('案件検索')

// Usage pattern
createUsageBreadcrumb('お問合せ')
```

## 📝 **Props**

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `items` | Array | Yes | `[]` | Array of breadcrumb items |
| `containerClass` | String | No | `'container-fluid'` | CSS class cho container |

## 🔧 **Breadcrumb Item Structure**

```javascript
{
    text: 'Display Text',     // Text hiển thị
    link: '/path/to/page',    // URL link (null nếu không có link)
    current: true             // true nếu là current page
}
```

## 📱 **Examples**

### Basic Usage
```javascript
breadcrumbItems: [
    { text: 'ご利用ガイドライン', link: null, current: true }
]
```

### With Links
```javascript
breadcrumbItems: [
    { text: 'サービスメニュー', link: '/' },
    { text: '探す', link: '/search' },
    { text: '案件を探す', link: null, current: true }
]
```

### Management Pages
```javascript
breadcrumbItems: [
    { text: 'サービスメニュー', link: null },
    { text: '登録・管理', link: null },
    { text: '登録データ管理', link: null },
    { text: '人財管理一覧', link: null, current: true }
]
```

## 🎨 **Styling**

Component sử dụng CSS từ `/css/components/breadcrumb.css` với:

- ✅ **Responsive spacing** cho mobile/tablet/desktop
- ✅ **Arrow separators** giữa các items
- ✅ **Hover effects** cho links
- ✅ **Current page styling**

## 🔄 **Migration từ old breadcrumbs**

### Before (Old way):
```html
<div class="container-fluid">
    <div class="breadcrumbs">
        <span>サービスメニュー</span>
        <span class="breadcrumb-arrow"><span></span></span>
        <span class="current">案件を探す</span>
    </div>
</div>
```

### After (New way):
```javascript
import { createServiceMenuBreadcrumb } from "../utils/breadcrumbHelper.js";

const YourPage = {
    template: `
        <main class="margin-header">
            ${createServiceMenuBreadcrumb('案件を探す')}
            <!-- rest of template -->
        </main>
    `
}
```

## ✅ **Đã refactor các files:**

### **Phase 1 - Initial refactor:**
- ✅ `contact_new.js` - sử dụng `createUsageBreadcrumb()`
- ✅ `userguide.js` - sử dụng `createSingleBreadcrumb()`
- ✅ `resumes.js` - sử dụng `createManagementBreadcrumb()`

### **Phase 2 - Mass refactor:**
- ✅ `bookmark_list.js` - sử dụng `createBreadcrumb()`
- ✅ `opportunities_scout.js` - sử dụng `createBreadcrumb()`
- ✅ `resumes/active.js` - sử dụng `createSearchBreadcrumb()`
- ✅ `IT.js` - sử dụng `createBreadcrumb()`
- ✅ `FAQ.js` - sử dụng `createUsageBreadcrumb()`
- ✅ `opportunities/Active.js` - sử dụng `createSearchBreadcrumb()`
- ✅ `ERP.js` - sử dụng `createBreadcrumb()`
- ✅ `schedules.js` - sử dụng `createBreadcrumb()`
- ✅ `database/IT.js` - sử dụng `createBreadcrumb()`
- ✅ `opportunities/Search.js` - sử dụng `createBreadcrumb()`
- ✅ `messages/resumes_apply.js` - sử dụng `createBreadcrumb()`
- ✅ `registration_data.js` - sử dụng `createServiceMenuBreadcrumb()`
- ✅ `resumes/manage/file_upload.js` - sử dụng `createBreadcrumb()`
- ✅ `contact_success.js` - sử dụng `createBreadcrumb()`
- ✅ `users/profile/change_mail.js` - sử dụng `createBreadcrumb()`
- ✅ `users/mypage.js` - sử dụng `createBreadcrumb()`
- ✅ `preview_project.js` - sử dụng `createBreadcrumb()`
- ✅ `company/company_detail.js` - sử dụng `createBreadcrumb()`
- ✅ `guideline.js` - sử dụng `createSingleBreadcrumb()`

### **Total: 20 files refactored! 🎉**

## 📂 **Files liên quan:**

- `/js/utils/breadcrumbHelper.js` - Main helper functions
- `/css/layout.css` - Consolidated breadcrumb CSS (ALL breadcrumb styles)
- `/js/components/Breadcrumb.js` - Vue component (deprecated)

## 🧹 **Cleaned up duplicate CSS từ các files:**

### **CSS Files cleaned:**
- ✅ `userguild.css` - Removed duplicate breadcrumb CSS
- ✅ `bookmark_list.css` - Removed duplicate breadcrumb CSS
- ✅ `business_system.css` - Removed duplicate breadcrumb CSS
- ✅ `IT.css` - Removed duplicate breadcrumb CSS
- ✅ `PMO_talent.css` - Removed duplicate breadcrumb CSS
- ✅ `contact_new.css` - Removed duplicate breadcrumb CSS
- ✅ `resumes.css` - Removed duplicate breadcrumb CSS (2 blocks)
- ✅ `company_list_opp.css` - Removed duplicate breadcrumb CSS (2 blocks)
- ✅ `company_list_res.css` - Removed duplicate breadcrumb CSS
- ✅ `design_server.css` - Removed duplicate breadcrumb CSS
- ✅ `password_new.css` - Removed duplicate breadcrumb CSS
- ✅ `schedules.css` - Removed duplicate breadcrumb CSS
- ✅ `database/business_system.css` - Removed duplicate breadcrumb CSS
- ✅ `database/design_server.css` - Removed duplicate breadcrumb CSS
- ✅ `PM_PL.css` - Removed duplicate breadcrumb CSS
- ✅ `components/breadcrumb.css` - File deleted (no longer needed)

### **Total: 16 CSS files cleaned! 🧹**

## 🎯 **Benefits của Helper Approach:**

- ✅ **No import issues** - không cần Vue component imports
- ✅ **Simple HTML generation** - chỉ tạo HTML strings
- ✅ **Template string integration** - dễ dàng embed vào templates
- ✅ **Consistent patterns** - predefined patterns cho common cases
- ✅ **Maintainable** - tất cả logic ở 1 file
