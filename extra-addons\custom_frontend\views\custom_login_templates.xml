<odoo>
    <template id="auth_signup.login" inherit_id="web.login" name="Custom Login">
        <xpath expr="//form" position="replace">
            <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/login.css"/>
            <div class="container-fluid grabient pt-5">
                <div class="row">
                    <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                        <div class="row">
                            <div class="col-12">
                                <h1 class="font-extralarge text-center my-2">ログイン</h1>
                            </div>
                        </div>
                        <div class="row justify-content-center mt-3 mt-md-5">
                            <div class="col-12 col-xl-6 col-md-7">
                                <div class="card">
                                    <div class="card-body px-3 py-5 p-md-5">


                                        <form id="loginForm" method="POST" action="/web/login">
                                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                                            <div class="mx-auto mb-3">
                                                <input placeholder="メールアドレス" autocomplete="off" id="email_field" type="email" class="form-control" name="login" required="true" maxlength="64"/>
                                            </div>

                                            <div class="mx-auto mb-4">
                                                <input placeholder="パスワード" class="form-control" autocomplete="off" id="password_field" type="password" name="password" required="true" maxlength="64"/>
                                            </div>

                                            <div class="mx-auto text-center">
                                                <div class="selecting-form custom-control custom-checkbox z-2">
                                                    <input id="remember_me_field" class="custom-control-input" type="checkbox" value="true" name="remember"/>
                                                    <label id="remember_me_field_label" class="custom-control-label" for="remember_me_field">次回から入力を省略する</label>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12">
                                                    <button name="button" type="submit" id="mail_login_btn" class="btn btn-default btn-block btn-lg font-middle mt-3 mb-5 waves-effect waves-light">ログイン</button>
                                                </div>
                                            </div>
                                        </form>
                                        <div class="row justify-content-center">
                                            <div class="col-12 mb-4">
                                                <div class="border-top"></div>
                                            </div>
                                            <div class="col-12">
                                                <p class="text-center custom-grey-6-text">SNSアカウントで会員登録されている方は下記よりログインしてください。</p>
                                            </div>
                                            <div class="col-12">
                                                <a class="btn btn-block btn-lg font-middle mb-3 waves-effect waves-light" href="/users/auth/google_oauth2">
                                                    <span class="mr-3 sns-icon google-icon"></span>Googleでログイン
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center mt-5 mx-3">
                            <div class="col-12 text-center mt-2">
                                <a id="pass_forget_link" class="green-text font-middle" href="/users/password/new">パスワードをお忘れの方はこちら</a>
                            </div>
                            <div class="col-12 mt-3 mb-4 text-center">
                                <a id="email_forget_link" class="green-text font-middle" href="/contacts/new">メールアドレスが不明な方はこちら</a>
                            </div>
                            <div class="col-12 col-md-6 px-0 px-md-5">
                                <a class="btn btn-white btn-outline-default btn-lg btn-block waves-effect waves-light" href="/users/registrations">
                                    <span class="overflow-visible">会員登録はこちら</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <script>
                async function fetchData() {
                            try {
                                const response = await fetch('/api/clear', {
                                    method: 'GET',
                                });

                                if (response.ok) {
                                    console.log('Request successful!');
                                } else {
                                    console.error('Request failed with status:', response.status);
                                }
                            } catch (error) {
                                console.error('An error occurred:', error);
                            }
                        }
                        fetchData()
            </script>

        </xpath>
    </template>
</odoo>
