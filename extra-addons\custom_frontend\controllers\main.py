from odoo import http
from odoo.http import request

class CustomSignup(http.Controller):

    @http.route('/web/signup/submit', type='http', auth="public", methods=['POST'], website=True)
    def signup_submit(self, **kwargs):
        first_name = kwargs.get('name')
        last_name = kwargs.get('lastname')
        email = kwargs.get('email')
        phone = kwargs.get('phone')
        password = kwargs.get('password')
        password_confirm = kwargs.get('password_confirmation')
        terms1 = kwargs.get('terms1')
        terms2 = kwargs.get('terms2')

        # Kiểm tra thông tin nhập
        if not first_name or not last_name or not email or not password or not password_confirm:
            request.session['signup_error'] = "All fields are required!"
            return request.redirect('/web/signup')
        if password != password_confirm:
            request.session['signup_error'] = "Passwords do not match!"
            return request.redirect('/web/signup')
        if not terms1 or not terms2:
            request.session['signup_error'] = "You must agree to the terms and conditions."
            return request.redirect('/web/signup')

        # L<PERSON>y nhóm quyền Public User
        public_group = request.env.ref('base.group_public', raise_if_not_found=False)

        # Kiểm tra nếu nhóm quyền không tồn tại
        if not public_group:
            request.session['signup_error'] = "Public User group not found!"
            return request.redirect('/web/signup')

        # Tạo partner
        partner = request.env['res.partner'].sudo().create({
            'name': f"{first_name} {last_name}",
            'email': email,
            'phone': phone,
        })

        # Tạo user với nhóm quyền cụ thể
        user = request.env['res.users'].sudo().create({
            'name': f"{first_name} {last_name}",
            'login': email,
            'partner_id': partner.id,
            'email': email,
            'phone': phone,
            'password': password,
            'company_id': request.env.company.id,  # Đảm bảo công ty được gán chính xác
        })

        # Thêm nhóm quyền Public User vào user
        user.write({'groups_id': [(6, 0, [public_group.id])]})

        if user:
            request.session['signup_success'] = "Your account has been created successfully!"
            return request.redirect('/web/login')
        else:
            request.session['signup_error'] = "An error occurred while creating your account. Please try again."
            return request.redirect('/web/signup')
    @http.route('/api/clear', type='http', auth="public", methods=['GET', 'POST'])
    def api_clear(self, **kwargs):
        request.session['signup_error'] = ''
        request.session['signup_success'] = ''
        return request.make_response('Session cleared successfully', [('Content-Type', 'text/plain')])

    @http.route('/', type='http', auth="public", website=True)
    def redirect_home(self, **kwargs):
        return request.redirect('/home')

    @http.route('/users/sign_out', type='http', auth="public", website=True)
    def sign_out(self, **kwargs):
        request.session.pop('loginEmail', None)
        return """
        <script>
            localStorage.removeItem('isloggedin'); // Xóa token trong localStorage
            localStorage.removeItem('authToken'); // Xóa token trong localStorage
            window.location.href = '/login'; // Chuyển hướng về trang đăng nhập
        </script>
        """

    @http.route('/<path:any>', type='http', auth="public", website=True)
    def custom_page(self, **kwargs):
        return request.render('custom_frontend.layout_custom', {})