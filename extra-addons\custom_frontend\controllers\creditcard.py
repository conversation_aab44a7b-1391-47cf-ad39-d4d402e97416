from odoo import http
from odoo.http import request
from datetime import datetime
import requests
import xml.etree.ElementTree as ET
import logging
import random
import string
from . import common
import os

_logger = logging.getLogger(__name__)

selected_plan = None
transaction_id = None

class CreditCardController(http.Controller):

    def generate_sendid(self, length=20):
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    def create_zeus_payment_xml(self, token_key, amount, send_id, transaction_id, telno, email):
        env = request.env
        # Tạo root element
        root = ET.Element("request")
        root.set("service", "secure_link_3d")
        root.set("action", "enroll")
        
        # Authentication
        auth = ET.SubElement(root, "authentication")
        clientip = ET.SubElement(auth, "clientip")
        # Lấy IP từ environment hoặc request
        client_ip = os.getenv('ZEUS_CLIENT_IP') or request.httprequest.remote_addr
        clientip.text = client_ip
        
        key = ET.SubElement(auth, "key")
        # Lấy key từ environment
        zeus_key = os.getenv('ZEUS_API_KEY')
        if not zeus_key:
            _logger.error("ZEUS_API_KEY not found in environment variables")
            raise ValueError("ZEUS_API_KEY not configured")
        key.text = zeus_key
        
        # Token key
        token = ET.SubElement(root, "token_key")
        token.text = token_key
        
        # Payment info
        payment = ET.SubElement(root, "payment")
        amount_elem = ET.SubElement(payment, "amount")
        amount_elem.text = str(amount)
        count = ET.SubElement(payment, "count")
        count.text = "01"
        
        # User info
        user = ET.SubElement(root, "user")
        telno = ET.SubElement(user, "telno")
        telno.set("validation", "strict")
        telno.text = telno
        email = ET.SubElement(user, "email")
        email.set("language", "japanese")
        email.text = email
        
        # Unique keys
        uniq = ET.SubElement(root, "uniq_key")
        sendid = ET.SubElement(uniq, "sendid")
        sendid.text = send_id
        sendpoint = ET.SubElement(uniq, "sendpoint")
        sendpoint.text = transaction_id
        
        # 3DS flag
        flag_3ds = ET.SubElement(root, "use_3ds2_flag")
        flag_3ds.text = "1"
        
        # termUrl
        term_url = ET.SubElement(root, "termUrl")
        term_url.text = request.httprequest.host_url.replace('http://', 'https://', 1) + "zeus/3ds/callback"
        _logger.info(f"term_url: {term_url.text}")
        
        # Convert to string
        return ET.tostring(root, encoding='utf-8', method='xml')
    
    def create_zeus_authreq_xml(self, xid, pares):
        root = ET.Element("request")
        root.set("service", "secure_link_3d")
        root.set("action", "authentication")

        xid_elem = ET.SubElement(root, "xid")
        xid_elem.text = xid

        pares_elem = ET.SubElement(root, "PaRes")
        pares_elem.text = pares

        return ET.tostring(root, encoding='utf-8', method='xml')
    
    def create_zeus_payreq_xml(self, xid):
        root = ET.Element("request")
        root.set("service", "secure_link_3d")
        root.set("action", "payment")

        xid_elem = ET.SubElement(root, "xid")
        xid_elem.text = xid

        print_am = ET.SubElement(root, "print_am")
        print_am.text = "yes"

        print_addition_value = ET.SubElement(root, "print_addition_value")
        print_addition_value.text = "yes"

        return ET.tostring(root, encoding='utf-8', method='xml')
    
    def send_zeus_request(self, xml_data, url):
        headers = {'Content-Type': 'application/xml; charset=utf-8'}
        response = requests.post(url, data=xml_data, headers=headers)
        root = ET.fromstring(response.text)
        status = root.find('.//status').text
        return status, response.text

    @http.route('/api/creditcard', type='json', auth='public', methods=['POST'], csrf=False)
    def payment_first_time(self):
        data = request.httprequest.get_json(silent=True)
        required_fields = ['user_id', 'token_key', 'masked_card_number', 'card_expiration', 'selected_plan']
        missing = [f for f in required_fields if not data.get(f)]
        if missing:
            return {'success': False, 'message': f'Missing required fields: {", ".join(missing)}'}
        user_id = data['user_id']
        token_key = data['token_key']
        masked_card_number = data['masked_card_number']
        card_expiration = data['card_expiration']
        selected_plan = data['selected_plan']
        auto_renew = data['auto_renew']

        self.selected_plan = selected_plan
        amount = request.env['vit.plans'].sudo().search([('id', '=', selected_plan)], limit=1).price
        _logger.info(f"amount: {amount}")
        if not amount:
            return {'success': False, 'message': f'Invalid plan: {selected_plan}'}
        try:
            send_id = self.generate_sendid()
            telno = request.env['vit.users'].sudo().search([('id', '=', user_id)], limit=1).phone
            credit_card = request.env['vit.user_credit_card'].sudo().create({
                'user_id': user_id,
                'send_id': send_id,
                'masked_card_number': masked_card_number,
                'card_expiration': card_expiration,
                'is_active': True,
                'is_default': True,
                'last_used': datetime.now(),
                'auto_renew': auto_renew,
                'telno': telno,
            })
            url = "https://secure2-sandbox.cardservice.co.jp/cgi-bin/secure/api.cgi"
            transaction_id = self.generate_sendid()
            self.transaction_id = transaction_id
            payment_history = request.env['vit.payment_history'].sudo().create({
                'user_id': user_id,
                'transaction_id': transaction_id,
                'status': 'pending',
            })

            user = request.env['vit.users'].sudo().search([('id', '=', user_id)], limit=1)
            telno = user.phone
            email = user.email
            xml_data = self.create_zeus_payment_xml(token_key, int(amount), send_id, transaction_id, telno, email)
            headers = {'Content-Type': 'application/xml; charset=utf-8'}
            try:
                response = requests.post(url, data=xml_data, headers=headers)
                return {'success': True, 'response': response.text, 'transaction_id': transaction_id}
            except Exception as e:
                return {'success': False, 'message': str(e)}
        except Exception as e:
            return {'success': False, 'message': str(e)}

    @http.route('/zeus/3ds/callback', type='json', auth='public', methods=['POST'], csrf=False)
    def zeus_3ds_callback(self):
        data = request.httprequest.get_json(silent=True)
        status = data.get('status')
        md = data.get('MD')
        pares = data.get('PaRes')
        url = "https://secure2-sandbox.cardservice.co.jp/cgi-bin/secure/api.cgi"
        payment_history = None
        transaction_id = None
        order_id = None
        masked_card_number = None
        card_expiration = None
        if status == 'success' and md:
            xml_auth = self.create_zeus_authreq_xml(md, pares)
            try:
                auth_status, auth_response = self.send_zeus_request(xml_auth, url)
                payment_history = request.env['vit.payment_history'].sudo().search([('transaction_id', '=', self.transaction_id)], limit=1)
                if auth_status == 'success':
                    xml_pay = self.create_zeus_payreq_xml(md)
                    pay_status, pay_response = self.send_zeus_request(xml_pay, url)
                    if pay_status == 'success':
                        pay_response_xml = ET.fromstring(pay_response)
                        status = pay_response_xml.find('.//status').text if pay_response_xml.find('.//status') is not None else None
                        order_id = pay_response_xml.find('.//order_number').text if pay_response_xml.find('.//order_number') is not None else None
                        prefix = pay_response_xml.find('.//prefix').text if pay_response_xml.find('.//prefix') is not None else ''
                        suffix = pay_response_xml.find('.//suffix').text if pay_response_xml.find('.//suffix') is not None else ''
                        masked_card_number = prefix + '********' + suffix
                        month = pay_response_xml.find('.//month').text if pay_response_xml.find('.//month') is not None else ''
                        year = pay_response_xml.find('.//year').text if pay_response_xml.find('.//year') is not None else ''
                        card_expiration = month + '/' + year
                        transaction_id = pay_response_xml.find('.//sendpoint').text if pay_response_xml.find('.//sendpoint') is not None else None
                        if payment_history:
                            payment_history.write({
                                'status': status,
                                'order_id': order_id,
                                'masked_card_number': masked_card_number,
                                'card_expiration': card_expiration,
                                'payment_date': datetime.now(),
                                'payment_method': 'credit_card',
                            })

                            user = request.env['vit.users'].sudo().search([('id', '=', payment_history.user_id.id)], limit=1)
                            user.write({
                                'plan_id': self.selected_plan,
                                'payment_status': 'paid' if status == 'success' else 'failed',
                                'plan_updated_at': datetime.now(),
                            })
                            
                            type_plan = request.env['vit.plans'].sudo().search([('id', '=', self.selected_plan)], limit=1).type

                            if type_plan == 'standard':
                                common.sendMailStandardPlan(user.id)
                            elif type_plan == 'premium':
                                common.sendMailPremiumPlan(user.id)

                        return {
                            'success': True,
                            'message': '3D Secure OK, AuthReq and PayReq sent',
                            'MD': md,
                            'PaRes': pares,
                            'authreq_response': auth_response,
                            'payreq_response': pay_response
                        }
                    else:
                        if payment_history:
                            auth_response_xml = ET.fromstring(auth_response)
                            error_code = auth_response_xml.find('.//code').text if auth_response_xml.find('.//code') is not None else ''
                            payment_history.write({'status': 'failed', 'error_code': error_code})
                        return {
                            'success': False,
                            'message': f'PayReq failed: {pay_status}',
                            'MD': md,
                            'PaRes': pares,
                            'authreq_response': auth_response,
                            'payreq_response': pay_response
                        }
                else:
                    if payment_history:
                        auth_response_xml = ET.fromstring(auth_response)
                        error_code = auth_response_xml.find('.//code').text if auth_response_xml.find('.//code') is not None else ''
                        payment_history.write({'status': 'failed', 'error_code': error_code})
                    return {
                        'success': False,
                        'message': f'AuthReq failed: {auth_status}',
                        'MD': md,
                        'PaRes': pares,
                        'authreq_response': auth_response
                    }
            except Exception as e:
                if payment_history:
                    auth_response_xml = ET.fromstring(auth_response)
                    error_code = auth_response_xml.find('.//code').text if auth_response_xml.find('.//code') is not None else ''
                    payment_history.write({'status': 'failed', 'error_code': error_code})
                return {
                    'success': False,
                    'message': f'Error sending AuthReq/PayReq: {str(e)}',
                    'MD': md,
                    'PaRes': pares
                }
        else:
            if payment_history:
                auth_response_xml = ET.fromstring(auth_response)
                error_code = auth_response_xml.find('.//code').text if auth_response_xml.find('.//code') is not None else ''
                payment_history.write({'status': 'failed', 'error_code': error_code})
            return {
                'success': False,
                'message': f'3D Secure failed: {status}',
                'MD': md,
                'PaRes': pares
            }

    @http.route('/api/payment_status', type='json', auth='public', methods=['POST'], csrf=False)
    def payment_status(self):
        data = request.httprequest.get_json(silent=True)
        transaction_id = data.get('transaction_id')
        if not transaction_id:
            return {'success': False, 'message': 'Missing transaction_id'}
        payment_history = request.env['vit.payment_history'].sudo().search([('transaction_id', '=', transaction_id)], limit=1)
        if not payment_history:
            return {'success': False, 'message': 'Not found'}
        return {
            'success': True,
            'status': payment_history.status,
            'order_id': payment_history.order_id,
            'payment_method': payment_history.payment_method,
            'payment_date': payment_history.payment_date,
            'masked_card_number': payment_history.masked_card_number,
            'card_expiration': payment_history.card_expiration,
            'message': payment_history.error_message,
            'error_code': payment_history.error_code,
        }
        
    
