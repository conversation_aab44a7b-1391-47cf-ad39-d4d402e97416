# Hướng dẫn tích hợp thanh toán ZEUS

## Giới thiệu

ZEUS là hệ thống thanh toán phổ biến tại <PERSON>, cung cấp dịch vụ xử lý thanh toán trực tuyến an toàn. Tài liệu này hướng dẫn cách tích hợp ZEUS vào hai trang thanh toán của Mi52:

1. **入会 (Nyukai)** - Trang đăng ký plan mới (đường dẫn: `/nyukai/plan`)
2. **プラン変更 (Plan Change)** - Trang thay đổi plan (đường dẫn: `/mypage/plan`)

## Các bước tích hợp

### 1. Thêm thư viện JavaScript của ZEUS

Thêm script của ZEUS vào trang thanh toán:

```html
<script src="https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token.js"></script>
```

### 2. <PERSON><PERSON><PERSON> nghĩa hàm generateToken

Hàm `generateToken` được gọi khi người dùng nhấn nút "申し込み" (Đăng ký). Hàm này sẽ:
- Thu thập thông tin thẻ tín dụng từ form
- Gửi thông tin đến ZEUS để tạo token
- Nhận token từ ZEUS và lưu vào form
- Gửi form đến server của chúng ta để xử lý

```javascript
function generateToken(button) {
    // Vô hiệu hóa nút để tránh nhấn nhiều lần
    button.disabled = true;

    // Lấy thông tin từ form
    const cardNumber = document.getElementById('credit_card_number_field').value;
    const cardHolderName = document.getElementById('card_holder_name_field').value;
    const expMonth = document.getElementById('credit_card_expire_month_field').value;
    const expYear = document.getElementById('credit_card_expire_year_field').value;
    const securityCode = document.getElementById('security_code_field').value;

    // Kiểm tra dữ liệu đầu vào
    if (!cardNumber || !cardHolderName || !expMonth || !expYear || !securityCode) {
        alert('すべての必須フィールドに入力してください。');
        button.disabled = false;
        return;
    }

    // Cấu hình ZEUS
    const zeusTokenRequestPayload = {
        merchant_id: document.getElementById('credit_payment_merchant_id').value,
        service_id: document.getElementById('credit_payment_service_id').value,
        card_number: cardNumber,
        card_holder_name: cardHolderName,
        card_exp_month: expMonth,
        card_exp_year: expYear,
        security_code: securityCode
    };

    // Gọi API của ZEUS để tạo token
    Zeustap.getToken(zeusTokenRequestPayload, function(response) {
        if (response.status === 'success') {
            // Lưu token vào form
            document.getElementById('credit_payment_token').value = response.token;
            document.getElementById('credit_payment_token_key').value = response.token_key;
            document.getElementById('credit_payment_masked_cc_number').value = response.masked_card_number;
            document.getElementById('credit_payment_card_brand_code').value = response.card_brand;
            document.getElementById('credit_payment_cc_expiration').value = expMonth + '/' + expYear;

            // Gửi form
            document.getElementById('credit_payments_regist_cc_form').submit();
        } else {
            // Xử lý lỗi
            alert('カード情報の処理中にエラーが発生しました: ' + response.error_message);
            button.disabled = false;
        }
    });
}
```

### 3. Xử lý backend

Khi form được gửi đến server, cần thực hiện các bước sau:

1. Nhận token từ form
2. Gọi API của ZEUS để xử lý thanh toán với token
3. Cập nhật trạng thái thanh toán trong database
4. Thông báo kết quả cho người dùng

Chúng ta sử dụng một controller chung để xử lý cả hai loại thanh toán:

```python
class PaymentController(http.Controller):

    @http.route('/mypage/plan', type='http', auth='public', methods=['POST'], website=True)
    def process_payment(self, **post):
        """
        Xử lý thanh toán từ form プラン変更 (thay đổi gói)
        """
        return self._process_payment(post, is_nyukai=False)

    @http.route('/nyukai/plan', type='http', auth='public', methods=['POST'], website=True)
    def process_nyukai_payment(self, **post):
        """
        Xử lý thanh toán từ form 入会 (tham gia)
        """
        return self._process_payment(post, is_nyukai=True)

    def _process_payment(self, post, is_nyukai=False):
        """
        Xử lý thanh toán từ form
        """
        try:
            # Lấy thông tin từ form
            token = post.get('credit_payment[token]')
            token_key = post.get('credit_payment[token_key]')
            plan = post.get('credit_payment[plan]')

            # Xác định trang redirect dựa vào loại form
            redirect_base = '/nyukai/plan' if is_nyukai else '/mypage/plan'

            if not token or not token_key or not plan or plan == '未選択':
                return request.redirect(f'{redirect_base}?error=missing_data')

            # Gọi API của ZEUS để xử lý thanh toán
            payment_result = self._call_zeus_payment_api(
                token=token,
                token_key=token_key,
                merchant_id=merchant_id,
                service_id=service_id,
                secret_key=settings.ZEUS_SECRET_KEY,  # Lấy từ cấu hình bảo mật
                amount=plan_amount,
                order_id=f"{'NYUKAI' if is_nyukai else 'PLAN'}_{user_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            )

            if payment_result.get('success'):
                # Cập nhật plan cho user
                update_values = {
                    'plan': plan,
                    'plan_updated_at': fields.Datetime.now(),
                    'payment_status': 'paid'
                }

                # Nếu là đăng ký mới (nyukai), kích hoạt tài khoản
                if is_nyukai:
                    update_values.update({
                        'active': True,
                        'activation_date': fields.Datetime.now()
                    })

                user.write(update_values)

                # Lưu thông tin thanh toán
                payment_type = 'registration' if is_nyukai else 'plan_change'

                return request.redirect(f'/payment/result?success=true&plan={plan}')
            else:
                return request.redirect(f'/payment/result?error={payment_result.get("error_code", "unknown")}')

        except Exception as e:
            return request.redirect('/payment/result?error=system_error')
```

## Ý nghĩa các trường ẩn

Form thanh toán có các trường ẩn quan trọng:

- `credit_payment[merchant_id]`: ID của merchant trong hệ thống ZEUS (72327)
- `credit_payment[service_id]`: ID dịch vụ trong hệ thống ZEUS (001)
- `credit_payment[token]`: Token được tạo bởi ZEUS, đại diện cho thông tin thẻ
- `credit_payment[token_key]`: Khóa token để xác thực
- `credit_payment[masked_cc_number]`: Số thẻ được che một phần (ví dụ: 4111-xxxx-xxxx-1111)
- `credit_payment[card_brand_code]`: Mã nhận diện loại thẻ (VISA, Mastercard, JCB, AMEX)
- `credit_payment[cc_expiration]`: Ngày hết hạn thẻ

## Cấu trúc file

Dự án sử dụng các file sau để tích hợp ZEUS:

1. **zeus_payment.js** - File JavaScript chung cho cả hai trang thanh toán, chứa hàm `generateToken` và các hàm hỗ trợ
2. **new_payment.js** - Trang thay đổi plan (プラン変更)
3. **nyukai_payment.js** - Trang đăng ký plan mới (入会)
4. **payment.py** - Controller xử lý backend cho cả hai loại thanh toán
5. **payment_models.py** - Model lưu trữ lịch sử thanh toán
6. **payment_result.js** - Trang hiển thị kết quả thanh toán

## Trang kết quả thanh toán

Sau khi xử lý thanh toán, người dùng sẽ được chuyển hướng đến trang kết quả thanh toán (`/payment/result`) với các tham số truy vấn khác nhau tùy thuộc vào kết quả:

### Thanh toán thành công

URL: `/payment/result?success=true&plan=プレミアム会員&type=nyukai&transaction_id=12345`

Trang hiển thị:
- Biểu tượng thành công (dấu tích xanh)
- Thông báo thành công phù hợp với loại thanh toán (入会 hoặc プラン変更)
- Thông tin về gói đã chọn
- Ngày và giờ thanh toán
- Nút để chuyển đến trang マイページ

### Thanh toán thất bại

URL: `/payment/result?error=card_error&type=nyukai`

Trang hiển thị:
- Biểu tượng lỗi (dấu chấm than đỏ)
- Thông báo lỗi
- Mã lỗi và giải thích về nguyên nhân có thể gây ra lỗi
- Hướng dẫn khắc phục lỗi
- Nút để thử lại (chuyển đến trang thanh toán tương ứng)

### Các mã lỗi phổ biến

- `card_error`: Thông tin thẻ không chính xác
- `expired_card`: Thẻ đã hết hạn
- `insufficient_funds`: Số dư thẻ không đủ
- `processing_error`: Lỗi xử lý thanh toán
- `system_error`: Lỗi hệ thống

### Xử lý lỗi thanh toán

Hệ thống xử lý các lỗi thanh toán như sau:

1. **Nguồn gốc của mã lỗi**:
   - Trong môi trường thực tế: Mã lỗi được trả về từ API của ZEUS
   - Trong môi trường test: Mã lỗi được giả lập trong hàm `_get_mock_payment_response`
   - Để test lỗi cụ thể, thêm tên lỗi vào order_id (ví dụ: `PLAN_123_card_error`)

2. **Quy trình xử lý lỗi**:
   - Controller `payment.py` nhận mã lỗi từ ZEUS hoặc từ hàm giả lập
   - Lưu thông tin lỗi vào bảng `vit.payment_history` với trạng thái "failed"
   - Chuyển hướng người dùng đến trang kết quả thanh toán với tham số `error`
   - Trang kết quả hiển thị thông báo lỗi tương ứng và hướng dẫn khắc phục

3. **Hiển thị lỗi**:
   - Trang kết quả thanh toán (`payment_result.js`) đọc mã lỗi từ URL
   - Hiển thị thông báo lỗi tương ứng với mã lỗi
   - Đề xuất các bước khắc phục cho người dùng

4. **Validation ở client-side**:
   - Validation cơ bản được thực hiện trước khi gửi thông tin thẻ
   - Hiển thị lỗi inline khi người dùng nhập sai định dạng
   - Ẩn thông báo lỗi khi người dùng bắt đầu nhập lại

Lưu ý: Mặc dù có validation ở client-side, vẫn có thể xảy ra lỗi từ ZEUS vì họ thực hiện kiểm tra chi tiết hơn (tính hợp lệ của thẻ, trạng thái thẻ, số dư, v.v.).

## Môi trường test

ZEUS cung cấp môi trường test để phát triển và kiểm thử:

- URL: https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token_test.js
- Merchant ID test: TEST_MERCHANT
- Service ID test: TEST_SERVICE
- Secret Key test: TEST_SECRET_KEY
- Thẻ test: 4111-1111-1111-1111 (VISA)
- Ngày hết hạn: Bất kỳ ngày nào trong tương lai
- CVV: Bất kỳ số 3 chữ số

**Lưu ý quan trọng**: Secret Key là thông tin bảo mật dùng để xác thực giao dịch với ZEUS. Trong môi trường thực tế, Secret Key sẽ được cung cấp bởi ZEUS khi đăng ký tài khoản merchant. Secret Key không nên được lưu trữ trong mã nguồn phía client mà nên được lưu trữ an toàn trên server và chỉ được sử dụng trong các API calls từ backend.

### Lấy thông tin test từ ZEUS

Để có được thông tin test chính xác và đầy đủ, bạn cần liên hệ trực tiếp với ZEUS. Các thông tin test như Merchant ID, Service ID và Secret Key không được công khai trên trang web của ZEUS và thường được cung cấp riêng cho từng đối tác sau khi đăng ký.

**Cách liên hệ với ZEUS để lấy thông tin test**:

1. **Liên hệ qua trang chủ**: https://www.cardservice.co.jp/contact/
2. **Gọi điện thoại**: Số điện thoại hỗ trợ có thể tìm thấy trên trang web của ZEUS
3. **Email**: Địa chỉ email hỗ trợ có thể tìm thấy trên trang web của ZEUS

Khi liên hệ, bạn nên cung cấp các thông tin sau:
- Tên công ty của bạn
- Mục đích sử dụng (phát triển và kiểm thử)
- Loại tích hợp bạn đang thực hiện (thanh toán thẻ, thanh toán định kỳ, v.v.)

ZEUS sẽ cung cấp cho bạn:
- Merchant ID test
- Service ID test
- Secret Key test
- Tài liệu API chi tiết
- Các thẻ test và trường hợp test

**Lưu ý**: Trong khi chờ đợi thông tin test từ ZEUS, bạn có thể tiếp tục phát triển dựa trên tài liệu API công khai và các giá trị test giả định. Tuy nhiên, bạn sẽ cần thông tin test thực tế từ ZEUS để kiểm thử đầy đủ trước khi triển khai.

## Hướng dẫn triển khai cho developers

### 1. Chuẩn bị môi trường phát triển

1. **Cài đặt các file cần thiết**:
   - Đảm bảo các file `zeus_payment.js`, `new_payment.js`, `nyukai_payment.js`, `payment.py`, `payment_models.py` và `payment_result.js` đã được tạo và đặt đúng vị trí
   - Kiểm tra các đường dẫn trong router đã được cấu hình đúng

2. **Cấu hình model `payment_models.py`**:
   - Đảm bảo model `PaymentHistory` có các trường cần thiết như `auto_renew`, `order_id`, `error_code`
   - Đảm bảo model `ZeusConfiguration` đã được tạo để lưu trữ cấu hình ZEUS

3. **Cấu hình môi trường test**:
   - Liên hệ với ZEUS để lấy thông tin test (Merchant ID, Service ID, Secret Key)
   - Sử dụng hàm `initialize_test_configuration` để khởi tạo cấu hình test mặc định

### 2. Triển khai phần còn lại

1. **Sử dụng model `ZeusConfiguration` để lưu trữ cấu hình ZEUS**:
   ```python
   class ZeusConfiguration(models.Model):
       _name = 'vit.zeus_configuration'
       _description = 'ZEUS Payment Configuration'

       name = fields.Char(string='Configuration Name', required=True)
       environment = fields.Selection([
           ('test', 'Test Environment'),
           ('production', 'Production Environment')
       ], string='Environment', default='test', required=True)
       merchant_id = fields.Char(string='Merchant ID', required=True)
       service_id = fields.Char(string='Service ID', required=True)
       secret_key = fields.Char(string='Secret Key', required=True)
       api_url = fields.Char(string='API URL', required=True)
       token_url = fields.Char(string='Token URL', required=True)
       active = fields.Boolean(string='Active', default=True)

       @api.model
       def get_active_configuration(self):
           """Get the active configuration"""
           return self.search([('active', '=', True)], limit=1)

       @api.model
       def initialize_test_configuration(self):
           """Initialize test configuration with default values"""
           if not self.search_count([('environment', '=', 'test')]):
               self.create({
                   'name': 'ZEUS Test Configuration',
                   'environment': 'test',
                   'merchant_id': 'TEST_MERCHANT',
                   'service_id': 'TEST_SERVICE',
                   'secret_key': 'TEST_SECRET_KEY',
                   'api_url': 'https://linkpt.cardservice.co.jp/cgi-bin/token/charge.cgi',
                   'token_url': 'https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token_test.js',
                   'active': True
               })
   ```

2. **Hoàn thiện hàm `_call_zeus_payment_api` trong `payment.py`**:
   ```python
   def _call_zeus_payment_api(self, token, token_key, merchant_id, service_id, secret_key, amount, order_id, api_url):
       try:
           # Tạo chữ ký xác thực
           signature = self._generate_zeus_signature(merchant_id, service_id, amount, order_id, secret_key)

           # Dữ liệu gửi đến ZEUS
           payload = {
               "merchant_id": merchant_id,
               "service_id": service_id,
               "token": token,
               "token_key": token_key,
               "amount": amount,
               "order_id": order_id,
               "signature": signature,
               "currency": "JPY"
           }

           # Gọi API
           _logger.info(f"Calling ZEUS API for order {order_id}")

           # Trong môi trường phát triển, giả lập phản hồi thành công
           if merchant_id == 'TEST_MERCHANT' or service_id == 'TEST_SERVICE':
               _logger.info("Using mock response for test environment")
               return self._get_mock_payment_response(order_id)

           # Gọi API thực tế
           response = requests.post(api_url, data=payload)

           # Xử lý phản hồi
           if response.status_code == 200:
               result = response.json()
               if result.get('status') == 'success':
                   return {
                       'success': True,
                       'transaction_id': result.get('transaction_id', ''),
                       'approval_code': result.get('approval_code', '')
                   }
               else:
                   return {
                       'success': False,
                       'error_code': result.get('error_code', 'unknown'),
                       'error_message': result.get('error_message', 'Unknown error')
                   }
           else:
               _logger.error(f"ZEUS API returned status code {response.status_code}")
               return {
                   'success': False,
                   'error_code': 'api_error',
                   'error_message': f"API error: {response.status_code}"
               }

       except Exception as e:
           _logger.exception(f"Exception calling ZEUS API: {str(e)}")
           return {
               'success': False,
               'error_code': 'system_error',
               'error_message': str(e)
           }
   ```

2. **Triển khai hàm tạo chữ ký xác thực (nếu ZEUS yêu cầu)**:
   ```python
   def _generate_zeus_signature(self, merchant_id, service_id, amount, order_id, secret_key):
       # Tạo chuỗi dữ liệu cần ký
       data_to_sign = f"{merchant_id}{service_id}{amount}{order_id}{secret_key}"

       # Tạo chữ ký bằng HMAC-SHA256
       import hmac
       import hashlib
       import base64

       signature = hmac.new(
           secret_key.encode('utf-8'),
           data_to_sign.encode('utf-8'),
           hashlib.sha256
       ).digest()

       # Mã hóa Base64
       return base64.b64encode(signature).decode('utf-8')
   ```

3. **Thêm option tự động gia hạn**:
   - Thêm checkbox "自動更新する" (Tự động gia hạn) vào form thanh toán:
     ```html
     <div class="row">
         <div class="col-12">
             <div class="mx-auto mb-5">
                 <div class="custom-control custom-checkbox">
                     <input type="checkbox" class="custom-control-input" id="auto_renew" name="credit_payment[auto_renew]">
                     <label class="custom-control-label font-middle" for="auto_renew">自動更新する</label>
                 </div>
             </div>
         </div>
     </div>
     ```
   - Thêm trường `auto_renew`, `token` và `token_key` vào model `vit.payment_history`:
     ```python
     auto_renew = fields.Boolean(string='Auto Renew', default=False)
     next_payment_date = fields.Date(string='Next Payment Date')
     token = fields.Char(string='Payment Token', help='Token used for auto-renewal payments')
     token_key = fields.Char(string='Token Key', help='Token key used for auto-renewal payments')
     ```
   - Cập nhật controller để lưu trữ thông tin này:
     ```python
     # Lấy giá trị auto_renew từ form
     auto_renew = post.get('credit_payment[auto_renew]') == 'on'

     # Lưu thông tin thanh toán với auto_renew và token nếu cần
     request.env['vit.payment_history'].sudo().create({
         # Các trường khác...
         'auto_renew': auto_renew,
         'token': token if auto_renew else False,  # Lưu token nếu auto_renew = True
         'token_key': token_key if auto_renew else False  # Lưu token_key nếu auto_renew = True
     })
     ```
   - Thêm cron job để tự động xử lý thanh toán gia hạn:
     ```xml
     <record id="ir_cron_process_auto_renewals" model="ir.cron">
         <field name="name">Process Auto Renewal Payments</field>
         <field name="model_id" ref="database.model_vit_payment_history"/>
         <field name="state">code</field>
         <field name="code">model.process_auto_renewals()</field>
         <field name="interval_number">1</field>
         <field name="interval_type">days</field>
         <field name="numbercall">-1</field>
         <field name="doall" eval="False"/>
         <field name="active" eval="True"/>
     </record>
     ```
   - Triển khai phương thức `process_auto_renewals()` trong model `PaymentHistory`:
     ```python
     def process_auto_renewals(self):
         """Process auto renewals for payments that are due"""
         today = fields.Date.today()
         due_payments = self.search([
             ('auto_renew', '=', True),
             ('next_payment_date', '<=', today),
             ('status', '=', 'success')
         ])

         for payment in due_payments:
             try:
                 # Gọi phương thức xử lý thanh toán gia hạn
                 self.env['payment.controller'].process_renewal_payment(payment)

                 # Cập nhật ngày thanh toán tiếp theo
                 if '月' in payment.plan:  # Gói tháng
                     payment.next_payment_date = (datetime.now() + timedelta(days=30)).date()
                 elif '年' in payment.plan:  # Gói năm
                     payment.next_payment_date = (datetime.now() + timedelta(days=365)).date()

                 _logger.info(f"Auto renewal processed for payment ID {payment.id}")
             except Exception as e:
                 _logger.error(f"Error processing auto renewal: {str(e)}")
     ```
   - Triển khai phương thức `process_renewal_payment()` trong controller:
     ```python
     def process_renewal_payment(self, payment):
         """Xử lý thanh toán tự động gia hạn"""
         try:
             # Lấy cấu hình ZEUS
             zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()

             # Tạo order ID
             order_id = f"RENEWAL_{payment.user_id.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

             # Gọi API của ZEUS để xử lý thanh toán với token đã lưu
             payment_result = self._call_zeus_payment_api(
                 token=payment.token,
                 token_key=payment.token_key,
                 merchant_id=zeus_config.merchant_id,
                 service_id=zeus_config.service_id,
                 secret_key=zeus_config.secret_key,
                 amount=payment.amount,
                 order_id=order_id,
                 api_url=zeus_config.api_url
             )

             if payment_result.get('success'):
                 # Lưu thông tin thanh toán mới
                 request.env['vit.payment_history'].sudo().create({
                     'user_id': payment.user_id.id,
                     'plan': payment.plan,
                     'amount': payment.amount,
                     'payment_date': datetime.now(),
                     'payment_method': 'credit_card',
                     'card_brand': payment.card_brand,
                     'masked_card_number': payment.masked_card_number,
                     'card_expiration': payment.card_expiration,
                     'transaction_id': payment_result.get('transaction_id', ''),
                     'order_id': order_id,
                     'status': 'success',
                     'payment_type': 'renewal',
                     'auto_renew': payment.auto_renew,
                     'token': payment.token,
                     'token_key': payment.token_key
                 })
                 return True
             else:
                 # Lưu thông tin lỗi thanh toán
                 request.env['vit.payment_history'].sudo().create({
                     'user_id': payment.user_id.id,
                     'plan': payment.plan,
                     'amount': payment.amount,
                     'payment_date': datetime.now(),
                     'payment_method': 'credit_card',
                     'card_brand': payment.card_brand,
                     'masked_card_number': payment.masked_card_number,
                     'card_expiration': payment.card_expiration,
                     'status': 'failed',
                     'error_message': payment_result.get('error_message', 'Unknown error'),
                     'error_code': payment_result.get('error_code', 'unknown'),
                     'payment_type': 'renewal',
                     'order_id': order_id,
                     'auto_renew': payment.auto_renew,
                     'token': payment.token,
                     'token_key': payment.token_key
                 })
                 return False
         except Exception as e:
             _logger.exception(f"Exception during renewal payment: {str(e)}")
             return False
     ```

4. **Thêm hàm giả lập phản hồi cho môi trường test**:
   ```python
   def _get_mock_payment_response(self, order_id):
       """
       Tạo phản hồi giả lập cho môi trường test
       """
       # Giả lập phản hồi thành công
       if 'error' not in order_id.lower():
           return {
               'success': True,
               'transaction_id': f"MOCK_TX_{order_id}",
               'approval_code': f"MOCK_APPROVAL_{datetime.now().strftime('%Y%m%d%H%M%S')}"
           }
       # Giả lập phản hồi thất bại
       else:
           error_codes = {
               'card_error': 'Invalid card information',
               'expired_card': 'Card has expired',
               'insufficient_funds': 'Insufficient funds',
               'processing_error': 'Error processing payment',
               'system_error': 'System error'
           }
           error_code = next((code for code in error_codes.keys() if code in order_id.lower()), 'unknown')
           return {
               'success': False,
               'error_code': error_code,
               'error_message': error_codes.get(error_code, 'Unknown error')
           }
   ```

5. **Xử lý webhook (nếu ZEUS hỗ trợ)**:
   ```python
   @http.route('/zeus/webhook', type='json', auth='public', csrf=False)
   def zeus_webhook(self, **post):
       # Lấy dữ liệu webhook
       payload = request.httprequest.get_data()
       signature = request.httprequest.headers.get('X-Zeus-Signature')

       # Xác thực webhook
       zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()
       if not zeus_config:
           return {'status': 'error', 'message': 'No active ZEUS configuration found'}

       if not self._verify_zeus_webhook(payload, signature, zeus_config.secret_key):
           _logger.error("Invalid webhook signature")
           return {'status': 'error', 'message': 'Invalid signature'}

       # Xử lý dữ liệu webhook
       data = json.loads(payload)
       transaction_id = data.get('transaction_id')
       status = data.get('status')

       _logger.info(f"Received webhook for transaction {transaction_id} with status {status}")

       # Cập nhật trạng thái thanh toán
       payment = request.env['vit.payment_history'].sudo().search([('transaction_id', '=', transaction_id)], limit=1)
       if payment:
           payment.write({
               'status': 'success' if status == 'completed' else 'failed',
               'updated_at': datetime.now()
           })
           _logger.info(f"Updated payment status for transaction {transaction_id} to {status}")
       else:
           _logger.warning(f"Payment not found for transaction {transaction_id}")

       return {'status': 'success'}
   ```

### 3. Kiểm thử

1. **Kiểm thử tạo token**:
   - Điền thông tin thẻ test vào form
   - Kiểm tra xem token có được tạo thành công không
   - Kiểm tra xem các trường ẩn có được điền đúng không

2. **Kiểm thử thanh toán**:
   - Thực hiện thanh toán với thẻ test
   - Kiểm tra xem thanh toán có thành công không
   - Kiểm tra xem thông tin thanh toán có được lưu vào database không

3. **Kiểm thử xử lý lỗi**:
   - Thử thanh toán với thẻ không hợp lệ
   - Kiểm tra xem lỗi có được xử lý đúng không
   - Kiểm tra xem trang kết quả thanh toán có hiển thị thông báo lỗi đúng không

### 4. Triển khai lên môi trường production

1. **Thay đổi cấu hình**:
   - Thay thế URL test bằng URL production
   - Thay thế thông tin test bằng thông tin thực từ tài khoản ZEUS của khách hàng

2. **Kiểm thử cuối cùng**:
   - Thực hiện kiểm thử đầy đủ trên môi trường production
   - Đảm bảo mọi thứ hoạt động đúng trước khi ra mắt

## Sự khác biệt giữa hai trang thanh toán

| Tính năng | 入会 (Nyukai) | プラン変更 (Plan Change) |
|-----------|--------------|------------------------|
| Đường dẫn | /nyukai/plan | /mypage/plan |
| Tiêu đề | 入会 | プラン変更 |
| Text hướng dẫn | 入会するプランを選択してください。 | 変更するプランを選択してください。 |
| Menu bên trái | Không hiển thị | Hiển thị menu 会社データ管理 |
| Xử lý backend | Kích hoạt tài khoản mới | Chỉ cập nhật plan |

## Cấu hình ZEUS

ZEUS cần được cấu hình đúng cách để đảm bảo tích hợp thành công. Chúng ta sử dụng model `ZeusConfiguration` để lưu trữ và quản lý cấu hình:

1. **Khởi tạo cấu hình test mặc định**:
   ```python
   # Trong controller hoặc khi khởi động ứng dụng
   env['vit.zeus_configuration'].sudo().initialize_test_configuration()
   ```

2. **Lấy cấu hình hiện tại**:
   ```python
   zeus_config = env['vit.zeus_configuration'].sudo().get_active_configuration()
   ```

3. **Sử dụng cấu hình trong API calls**:
   ```python
   payment_result = self._call_zeus_payment_api(
       token=token,
       token_key=token_key,
       merchant_id=zeus_config.merchant_id,
       service_id=zeus_config.service_id,
       secret_key=zeus_config.secret_key,
       amount=plan_amount,
       order_id=order_id,
       api_url=zeus_config.api_url
   )
   ```

4. **Chuyển từ môi trường test sang production**:
   ```python
   # Tạo cấu hình production
   env['vit.zeus_configuration'].sudo().create({
       'name': 'ZEUS Production Configuration',
       'environment': 'production',
       'merchant_id': 'REAL_MERCHANT_ID',  # ID merchant thực tế từ ZEUS
       'service_id': 'REAL_SERVICE_ID',    # ID dịch vụ thực tế từ ZEUS
       'secret_key': 'REAL_SECRET_KEY',    # Secret Key thực tế từ ZEUS
       'api_url': 'https://linkpt.cardservice.co.jp/cgi-bin/token/charge.cgi',
       'token_url': 'https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token.js',
       'active': True
   })

   # Vô hiệu hóa cấu hình test
   test_config = env['vit.zeus_configuration'].sudo().search([('environment', '=', 'test')], limit=1)
   if test_config:
       test_config.write({'active': False})
   ```

## Quản lý thanh toán trong trang Admin

Hệ thống cung cấp giao diện quản lý thanh toán trong trang Admin với các chức năng sau:

### 1. Thông tin plan trong danh sách Users

Trong trang quản lý Users (`VIT Users > Users`), các thông tin về plan đã được thêm vào:
- **Plan**: Gói dịch vụ hiện tại của user
- **Payment Status**: Trạng thái thanh toán (Paid, Unpaid, Expired)
- **Plan Updated At**: Thời điểm cập nhật plan gần nhất

### 2. Tab Payment History trong form User

Khi xem chi tiết một user, tab "Payment History" hiển thị lịch sử thanh toán của user đó, bao gồm:
- Plan đã thanh toán
- Số tiền
- Ngày thanh toán
- Phương thức thanh toán
- Loại thanh toán (đăng ký mới, thay đổi plan)
- Trạng thái
- Mã giao dịch
- Tự động gia hạn
- Ngày thanh toán tiếp theo

### 3. Menu Payment Management

Menu `Payment Management` cung cấp các chức năng quản lý thanh toán:

#### 3.1. Payment History
Hiển thị tất cả lịch sử thanh toán trong hệ thống, với các chức năng:
- Lọc theo user, plan, trạng thái, loại thanh toán
- Xem chi tiết từng giao dịch
- Thống kê theo nhiều tiêu chí

#### 3.2. ZEUS Configuration
Quản lý cấu hình kết nối với ZEUS:
- Merchant ID
- Service ID
- Secret Key
- URL API
- Môi trường (test/production)

#### 3.3. Plan Statistics
Thống kê số lượng user theo plan, hiển thị dưới dạng biểu đồ.

#### 3.4. Auto Renewal Management
Quản lý các thanh toán tự động gia hạn:
- Xem danh sách các thanh toán có tự động gia hạn
- Bật/tắt tự động gia hạn cho từng thanh toán
- Xem lịch sử gia hạn tự động
- Xem ngày thanh toán tiếp theo

## Lưu ý bảo mật

1. Không bao giờ lưu trữ thông tin thẻ tín dụng trong database
2. Luôn sử dụng HTTPS cho trang thanh toán
3. Chỉ lưu token và thông tin được che một phần
4. Xác thực người dùng trước khi cho phép thanh toán
5. Bảo vệ Secret Key và không bao giờ tiết lộ nó trong mã nguồn phía client
6. Sử dụng chữ ký xác thực (signature) cho các giao dịch quan trọng
7. Lưu trữ đầy đủ thông tin giao dịch để đối chiếu và giải quyết tranh chấp nếu cần
8. Đối với tự động gia hạn:
   - Thông báo rõ ràng cho người dùng về việc bật tự động gia hạn
   - Cho phép người dùng dễ dàng tắt tự động gia hạn trong trang cài đặt
   - Gửi email thông báo trước khi tự động gia hạn (ít nhất 3-7 ngày)
   - Lưu trữ lịch sử đầy đủ của các lần gia hạn tự động

## Tài liệu tham khảo

- [Tài liệu API chính thức của ZEUS](https://www.cardservice.co.jp/api/)
- [Hướng dẫn tích hợp token](https://www.cardservice.co.jp/api/token/)
