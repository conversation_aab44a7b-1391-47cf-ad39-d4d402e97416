<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--
        Cron job để xử lý thanh toán tự động gia hạn

        Mục đích:
        - Ch<PERSON><PERSON> hàng ngày để kiểm tra các thanh toán cần gia hạn tự động
        - Tìm các bản ghi thanh toán có auto_renew=True và next_payment_date <= today
        - Gọi phương thức process_auto_renewals() của model vit.payment_history

        Lưu ý quan trọng:
        1. Đảm bảo model vit.payment_history đã được đăng ký đúng cách
        2. Phương thức process_auto_renewals() phải tồn tại trong model
        3. Phương thức này không thể gọi trực tiếp controller (http.Controller)
           mà phải gọi một service hoặc xử lý trực tiếp trong model
        4. Nếu gặp lỗi, hãy kiểm tra log và điều chỉnh phương thức process_auto_renewals()

        Cài đặt:
        - Mặc định cron job này bị vô hiệu hóa (active=False)
        - Sau khi đã kiểm tra và đảm bảo code hoạt động đúng, hãy đặt active=True
        -->
        <record id="ir_cron_process_auto_renewals" model="ir.cron">
            <field name="name">Process Auto Renewal Payments</field>
            <field name="model_id" ref="database.model_vit_payment_history"/>
            <field name="state">code</field>
            <field name="code">model.process_auto_renewals()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="False"/>
        </record>
    </data>
</odoo>
