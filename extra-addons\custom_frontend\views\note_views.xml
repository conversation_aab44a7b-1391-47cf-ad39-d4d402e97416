<?xml version="1.0"?>
<odoo>
    <record id="view_notes_form" model="ir.ui.view">
        <field name="name">vit.note.form</field>
        <field name="model">vit.note</field>
        <field name="arch" type="xml">
            <form string="Notes">
                <sheet>
                    <group>
                        <field name="content" widget="html"/>
                    </group>
                    <group>
                        <field name="created_at" readonly="1"/>
                    </group>
                    <group>
                        <field name="updated_at" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_notes_tree" model="ir.ui.view">
        <field name="name">vit.note.tree</field>
        <field name="model">vit.note</field>
        <field name="arch" type="xml">
            <list string="Channel">
                <field name="content"/>
                <field name="created_at"/>
                <field name="updated_at"/>
            </list>
        </field>
    </record>

    <record id="vit_notes_action" model="ir.actions.act_window">
        <field name="name">VIT Notes</field>
        <field name="res_model">vit.note</field>
        <field name="view_mode">list,form</field>
    </record>
    <menuitem id="vit_notes_menu_root" name="VIT Notes"/>
    <menuitem id="vit_notes_menu" name="Notes" parent="vit_notes_menu_root" action="vit_notes_action"/>
</odoo>
