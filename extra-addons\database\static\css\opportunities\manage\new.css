/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    main {
        display: block;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h1,
    h4 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    img {
        vertical-align: middle;
        border-style: none;
    }

    table {
        border-collapse: collapse;
    }

    th {
        text-align: inherit;
        text-align: -webkit-match-parent;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button,
    input,
    select,
    textarea {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button,
    input {
        overflow: visible;
    }

    button,
    select {
        text-transform: none;
    }

    [role=button] {
        cursor: pointer;
    }

    select {
        word-wrap: normal;
    }

    [type=button],
    [type=submit],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    [type=submit]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    input[type=checkbox],
    input[type=radio] {
        box-sizing: border-box;
        padding: 0;
    }

    textarea {
        overflow: auto;
        resize: vertical;
    }

    h1,
    h4 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-10,
    .col-12,
    .col-2,
    .col-4,
    .col-6,
    .col-8,
    .col-lg-8,
    .col-md-1,
    .col-md-10,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-9,
    .col-sm-2,
    .col-sm-3,
    .col-sm-6,
    .col-xl-10,
    .col-xl-2,
    .col-xl-4,
    .col-xl-8 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-2 {
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }

    .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-10 {
        flex: 0 0 83.333333%;
        max-width: 83.333333%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 576px) {
        .col-sm-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }

        .col-sm-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    @media (min-width: 768px) {
        .col-md-1 {
            flex: 0 0 8.333333%;
            max-width: 8.333333%;
        }

        .col-md-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }

        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-5 {
            flex: 0 0 41.666667%;
            max-width: 41.666667%;
        }

        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-md-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }

        .col-md-10 {
            flex: 0 0 83.333333%;
            max-width: 83.333333%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }
    }

    @media (min-width: 1200px) {
        .col-xl-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }

        .col-xl-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-xl-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }

        .col-xl-10 {
            flex: 0 0 83.333333%;
            max-width: 83.333333%;
        }
    }

    .form-control {
        display: block;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        padding: .375rem .75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-control {
            transition: none;
        }
    }

    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .form-control:disabled,
    .form-control[readonly] {
        background-color: #e9ecef;
        opacity: 1;
    }

    textarea.form-control {
        height: auto;
    }

    .form-check {
        position: relative;
        display: block;
        padding-left: 1.25rem;
    }

    .form-check-input {
        position: absolute;
        margin-top: .3rem;
        margin-left: -1.25rem;
    }

    .form-check-input:disabled~.form-check-label {
        color: #6c757d;
    }

    .form-check-label {
        margin-bottom: 0;
    }

    .form-inline {
        display: flex;
        flex-flow: row wrap;
        align-items: center;
    }

    .form-inline .form-check {
        width: 100%;
    }

    @media (min-width: 576px) {
        .form-inline label {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
        }

        .form-inline .form-check {
            display: flex;
            align-items: center;
            justify-content: center;
            width: auto;
            padding-left: 0;
        }

        .form-inline .form-check-input {
            position: relative;
            flex-shrink: 0;
            margin-top: 0;
            margin-right: .25rem;
            margin-left: 0;
        }
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn.disabled,
    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-lg {
        padding: .5rem 1rem;
        font-size: 1.25rem;
        line-height: 1.5;
        border-radius: .3rem;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .custom-control {
        position: relative;
        z-index: 1;
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5rem;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .custom-control-input {
        position: absolute;
        left: 0;
        z-index: -1;
        width: 1rem;
        height: 1.25rem;
        opacity: 0;
    }

    .custom-control-input:checked~.custom-control-label::before {
        color: #fff;
        border-color: #007bff;
        background-color: #007bff;
    }

    .custom-control-input:focus~.custom-control-label::before {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #80bdff;
    }

    .custom-control-input:not(:disabled):active~.custom-control-label::before {
        color: #fff;
        background-color: #b3d7ff;
        border-color: #b3d7ff;
    }

    .custom-control-input:disabled~.custom-control-label,
    .custom-control-input[disabled]~.custom-control-label {
        color: #6c757d;
    }

    .custom-control-input:disabled~.custom-control-label::before,
    .custom-control-input[disabled]~.custom-control-label::before {
        background-color: #e9ecef;
    }

    .custom-control-label {
        position: relative;
        margin-bottom: 0;
        vertical-align: top;
    }

    .custom-control-label::before {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        pointer-events: none;
        content: "";
        background-color: #fff;
        border: #adb5bd solid 1px;
    }

    .custom-control-label::after {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        content: "";
        background: 50%/50% 50% no-repeat;
    }

    .custom-checkbox .custom-control-label::before {
        border-radius: .25rem;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
    }

    .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
        background-color: rgba(0, 123, 255, 0.5);
    }

    .custom-control-label::before {
        transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .custom-control-label::before {
            transition: none;
        }
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .badge-pill {
        padding-right: .6em;
        padding-left: .6em;
        border-radius: 10rem;
    }

    .badge-danger {
        color: #fff;
        background-color: #dc3545;
    }

    .alert {
        position: relative;
        padding: .75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: .25rem;
    }

    .alert-warning {
        color: #856404;
        background-color: #fff3cd;
        border-color: #ffeeba;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-dialog-centered {
        display: flex;
        align-items: center;
        min-height: calc(100% - 1rem);
    }

    .modal-dialog-centered::before {
        display: block;
        height: calc(100vh - 1rem);
        height: -webkit-min-content;
        height: -moz-min-content;
        height: min-content;
        content: "";
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    .modal-footer {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;
        padding: .75rem;
        border-top: 1px solid #dee2e6;
        border-bottom-right-radius: calc(.3rem - 1px);
        border-bottom-left-radius: calc(.3rem - 1px);
    }

    .modal-footer>* {
        margin: .25rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        .modal-dialog-centered {
            min-height: calc(100% - 3.5rem);
        }

        .modal-dialog-centered::before {
            height: calc(100vh - 3.5rem);
            height: -webkit-min-content;
            height: -moz-min-content;
            height: min-content;
        }
    }

    @media (min-width: 992px) {
        .modal-lg {
            max-width: 800px;
        }
    }

    .align-baseline {
        vertical-align: baseline !important;
    }

    .align-middle {
        vertical-align: middle !important;
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-inline-block {
            display: inline-block !important;
        }

        .d-md-block {
            display: block !important;
        }

        .d-md-flex {
            display: flex !important;
        }
    }

    .flex-column {
        flex-direction: column !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-start {
        align-items: flex-start !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    @media (min-width: 768px) {
        .justify-content-md-center {
            justify-content: center !important;
        }
    }

    .w-100 {
        width: 100% !important;
    }

    .h-100 {
        height: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0,
    .my-0 {
        margin-top: 0 !important;
    }

    .mr-0 {
        margin-right: 0 !important;
    }

    .mb-0,
    .my-0 {
        margin-bottom: 0 !important;
    }

    .ml-0 {
        margin-left: 0 !important;
    }

    .mt-1 {
        margin-top: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .mt-2 {
        margin-top: 0.5rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .ml-2 {
        margin-left: 0.5rem !important;
    }

    .mt-3,
    .my-3 {
        margin-top: 1rem !important;
    }

    .mb-3,
    .my-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3 {
        margin-left: 1rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .p-1 {
        padding: 0.25rem !important;
    }

    .pt-1,
    .py-1 {
        padding-top: 0.25rem !important;
    }

    .py-1 {
        padding-bottom: 0.25rem !important;
    }

    .pl-1 {
        padding-left: 0.25rem !important;
    }

    .pt-2,
    .py-2 {
        padding-top: 0.5rem !important;
    }

    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .px-5 {
        padding-right: 3rem !important;
    }

    .px-5 {
        padding-left: 3rem !important;
    }

    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 576px) {
        .pl-sm-2 {
            padding-left: 0.5rem !important;
        }
    }

    @media (min-width: 768px) {
        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }

        .px-md-5 {
            padding-right: 3rem !important;
        }

        .px-md-5 {
            padding-left: 3rem !important;
        }
    }

    @media (min-width: 1200px) {
        .pr-xl-0 {
            padding-right: 0 !important;
        }

        .pl-xl-0 {
            padding-left: 0 !important;
        }
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        thead {
            display: table-header-group;
        }

        img,
        tr {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }
    }

    .pink.lighten-2 {
        background-color: #ff6388 !important;
    }

    .pink {
        background-color: #ff285b !important;
    }

    .disabled,
    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    a:not([href]):not([tabindex]),
    a:not([href]):not([tabindex]):focus,
    a:not([href]):not([tabindex]):hover {
        color: inherit;
        text-decoration: none;
    }

    h1,
    h4 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn.btn-lg {
        padding: 1rem 2.4rem;
        font-size: .94rem;
    }

    .btn.btn-sm {
        padding: .5rem 1.6rem;
        font-size: .64rem;
    }

    .btn.disabled:hover,
    .btn.disabled:focus,
    .btn.disabled:active,
    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn.btn-sm[class*=btn-outline-] {
        padding-top: .38rem;
        padding-bottom: .38rem;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-default {
        color: #fff;
        background-color: #1072e9 !important;
    }

    .btn-default:hover {
        color: #fff;
        background-color: #00d6c6;
    }

    .btn-default:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:focus,
    .btn-default:active {
        background-color: #005650;
    }

    .btn-default:not([disabled]):not(.disabled):active {
        background-color: #005650 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-outline-default {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #1072e9 !important;
    }

    .btn-outline-default:hover,
    .btn-outline-default:focus,
    .btn-outline-default:active,
    .btn-outline-default:active:focus {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
    }

    .btn-outline-default:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-grey {
        color: #fff;
        background-color: #616161 !important;
    }

    .btn-grey:hover {
        color: #fff;
        background-color: #6e6e6e;
    }

    .btn-grey:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-grey:focus,
    .btn-grey:active {
        background-color: #2e2e2e;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-blue-grey {
        color: #fff;
        background-color: #78909c !important;
    }

    .btn-blue-grey:hover {
        color: #fff;
        background-color: #879ca7;
    }

    .btn-blue-grey:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:focus,
    .btn-blue-grey:active {
        background-color: #4a5b64;
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active {
        background-color: #4a5b64 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-outline-blue-grey {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #78909c !important;
    }

    .btn-outline-blue-grey:hover,
    .btn-outline-blue-grey:focus,
    .btn-outline-blue-grey:active,
    .btn-outline-blue-grey:active:focus {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #000;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #000;
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .badge-pill {
        padding-right: .5rem;
        padding-left: .5rem;
        border-radius: 10rem;
    }

    .badge-danger {
        color: #fff !important;
        background-color: #ff285b !important;
    }

    .picker__box .picker__header .picker__date-display,
    .picker__box .picker__table .picker__day--outfocus,
    .picker__date-display {
        color: #fff !important;
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    table th {
        font-size: .9rem;
        font-weight: 400;
    }

    table td {
        font-size: .9rem;
        font-weight: 300;
    }

    .btn.btn-flat {
        font-weight: 500;
        color: inherit;
        background-color: rgba(0, 0, 0, 0);
        box-shadow: none;
    }

    .btn.btn-flat:not([disabled]):not(.disabled):active {
        box-shadow: none;
    }

    button,
    html [type=button],
    [type=submit] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .card .btn-action {
        margin-top: -1.44rem;
        margin-bottom: -1.44rem;
    }

    .form-check-input:not(:checked),
    .form-check-input:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    .form-check-input[type=radio]:not(:checked)+label,
    .form-check-input[type=radio]:checked+label {
        position: relative;
        display: inline-block;
        height: 1.5625rem;
        padding-left: 28px;
        line-height: 1.5625rem;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        transition: .28s ease;
    }

    .form-check-input[type=radio]+label:before,
    .form-check-input[type=radio]+label:after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 18px;
        height: 18px;
        margin: 4px;
        content: "";
        transition: .28s ease;
    }

    .form-check-input[type=radio]:not(:checked)+label:before,
    .form-check-input[type=radio]:not(:checked)+label:after,
    .form-check-input[type=radio]:checked+label:before,
    .form-check-input[type=radio]:checked+label:after {
        border-radius: 50%;
    }

    .form-check-input[type=radio]:not(:checked)+label:before,
    .form-check-input[type=radio]:not(:checked)+label:after {
        border: 2px solid #d5d9db;
    }

    .form-check-input[type=radio]:not(:checked)+label:after {
        transform: scale(0);
    }

    .form-check-input[type=radio]:checked+label:before {
        border: 2px solid rgba(0, 0, 0, 0);
    }

    .form-check-input[type=radio]:checked+label:after {
        border: 2px solid #1072e9;
    }

    .form-check-input[type=radio]:checked+label:after {
        background-color: #1072e9;
    }

    .form-check-input[type=radio]:checked+label:after {
        transform: scale(1.02);
    }

    .form-check-input[type=radio]:disabled:not(:checked)+label:before,
    .form-check-input[type=radio]:disabled:checked+label:before {
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0.46);
    }

    .form-check-input[type=radio]:focus+label:before {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    [type=checkbox]:not(:checked),
    [type=checkbox]:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    .form-check-input[type=checkbox]+label {
        position: relative;
        display: inline-block;
        height: 1.5625rem;
        padding-left: 35px;
        line-height: 1.5625rem;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .form-check-input[type=checkbox]+label:before,
    .form-check-input[type=checkbox]:not(.filled-in)+label:after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 18px;
        height: 18px;
        margin-top: 3px;
        content: "";
        border: 2px solid #8a8a8a;
        border-radius: 1px;
        transition: .2s;
    }

    .form-check-input[type=checkbox]:not(.filled-in)+label:after {
        border: 0;
        transform: scale(0);
    }

    .form-check-input[type=checkbox]:not(:checked):disabled+label:before {
        background-color: #bdbdbd;
        border: none;
    }

    .form-check-input[type=checkbox]:checked+label:before {
        top: -4px;
        left: -5px;
        width: 12px;
        height: 1.375rem;
        border-top: 2px solid rgba(0, 0, 0, 0);
        border-right: 2px solid #1072e9;
        border-bottom: 2px solid #1072e9;
        border-left: 2px solid rgba(0, 0, 0, 0);
        transform: rotate(40deg);
        transform-origin: 100% 100%;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }

    .form-check-input[type=checkbox]:checked:disabled+label:before {
        border-right: 2px solid #bdbdbd;
        border-bottom: 2px solid #bdbdbd;
    }

    .form-check-input[type=checkbox]:disabled:not(:checked)+label:before {
        background-color: #bdbdbd;
        border-color: #bdbdbd;
    }

    .form-check-input[type=checkbox]:disabled:not(:checked)+label:after {
        background-color: #bdbdbd;
        border-color: #bdbdbd;
    }

    .form-check-input[type=checkbox]:disabled:checked+label:before {
        background-color: rgba(0, 0, 0, 0);
    }

    .form-check-input[type=checkbox]:disabled:checked+label:after {
        background-color: #bdbdbd;
        border-color: #bdbdbd;
    }

    .form-check-input[type=checkbox]:focus:not(:checked)+label::before {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-check-input[type=checkbox]:focus:checked+label::before {
        border-color: #007bff;
        box-shadow: 3px 2px 0 0 rgba(0, 123, 255, 0.25);
        border-top: 2px solid rgba(0, 0, 0, 0);
        border-left: 2px solid rgba(0, 0, 0, 0);
    }

    .select-wrapper .select-dropdown {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .select-wrapper {
        position: relative;
    }

    .select-wrapper:not(.md-outline) .select-dropdown:focus {
        border-bottom: 1px solid #4285f4;
        box-shadow: 0 1px 0 0 #4285f4;
    }

    .select-wrapper input.select-dropdown {
        position: relative;
        z-index: 2;
        display: block;
        width: 100%;
        height: 40px;
        padding: 0;
        margin: 0 0 .94rem 0;
        font-size: 1rem;
        line-height: 2.9rem;
        text-overflow: ellipsis;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0);
        border: none;
        border-bottom: 1px solid #ced4da;
        outline: none;
    }

    .select-wrapper input.select-dropdown:disabled {
        color: rgba(0, 0, 0, 0.3);
        cursor: default;
        border-bottom-color: rgba(0, 0, 0, 0.2);
    }

    .select-wrapper span.caret {
        position: absolute;
        top: .8rem;
        right: 0;
        font-size: .63rem;
        color: #495057;
    }

    .select-wrapper ul {
        padding-left: 0;
        list-style-type: none;
    }

    select {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    }

    select.mdb-select.initialized {
        display: none !important;
    }

    select:disabled {
        color: rgba(0, 0, 0, 0.3);
    }

    .dropdown-content {
        position: absolute;
        z-index: 1021;
        display: none;
        min-width: 6.25rem;
        max-height: 40.625rem;
        margin: 0;
        overflow-y: auto;
        background-color: #fff;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        opacity: 0;
        will-change: width, height;
    }

    .dropdown-content li {
        width: 100%;
        clear: both;
        line-height: 1.3rem;
        color: #000;
        text-align: left;
        text-transform: none;
        cursor: pointer;
    }

    .dropdown-content li:hover,
    .dropdown-content li.active {
        background-color: #eee;
    }

    .dropdown-content li>span {
        display: block;
        padding: .5rem;
        font-size: .9rem;
        color: #4285f4;
    }

    button:focus {
        outline: 0 !important;
    }

    .picker__input {
        cursor: default;
    }

    .picker {
        position: absolute;
        z-index: 10000;
        font-size: 1rem;
        line-height: 1.2;
        color: #000;
        text-align: center;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .picker .picker__holder {
        position: fixed;
        width: 100%;
        overflow-y: auto;
        overflow-scrolling: touch;
        transition: background .15s ease-out, top 0s .15s;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }

    .picker .picker__holder,
    .picker .picker__frame {
        top: 100%;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .picker .picker__frame {
        position: absolute;
        width: 18.75rem;
        min-width: 16rem;
        max-width: 20.3125rem;
        max-height: 21.875rem;
        margin: 0 auto;
        filter: alpha(opacity=0);
        opacity: 0;
        transition: all .15s ease-out;
    }

    @media (min-height: 28.875em) {
        .picker .picker__frame {
            top: auto;
            bottom: -100%;
            max-height: 80%;
            overflow: visible;
        }
    }

    @media (min-height: 40.125em) {
        .picker .picker__frame {
            margin-bottom: 7.5%;
        }
    }

    .picker .picker__frame .picker__wrap {
        display: table;
        width: 100%;
        height: 100%;
    }

    @media (min-height: 28.875em) {
        .picker .picker__frame .picker__wrap {
            display: block;
        }
    }

    .picker .picker__box {
        display: table-cell;
        vertical-align: middle;
        background: #fff;
    }

    @media (min-height: 28.875em) {
        .picker .picker__box {
            display: block;
            border: 1px solid #777;
            border-top-color: #898989;
            border-bottom-width: 0;
            border-radius: 5px 5px 0 0;
            box-shadow: 0 0.75rem 2.25rem 1rem rgba(0, 0, 0, 0.24);
        }
    }

    .picker__date-display {
        padding-bottom: .9375rem;
        margin-bottom: 1rem;
        font-weight: 300;
        text-align: center;
        background-color: #00d2c3;
    }

    .picker__footer {
        width: 100%;
    }

    .picker__box {
        padding: 0;
        overflow: hidden;
        border-radius: .125rem;
    }

    .picker__box .picker__header {
        position: relative;
        margin-bottom: 1.25rem;
        text-align: center;
    }

    .picker__box .picker__header select {
        display: inline-block !important;
    }

    .picker__box .picker__header .picker__date-display {
        display: flex;
        justify-content: center;
        padding-bottom: .3125rem;
        font-weight: 400;
        background-color: #00d2c3;
    }

    .picker__box .picker__header .picker__date-display .picker__weekday-display {
        padding: .875rem .4375rem .3125rem .5rem;
        margin-top: 1.25rem;
        font-size: 2.1rem;
        letter-spacing: .5;
    }

    .picker__box .picker__header .picker__date-display .picker__month-display {
        padding: .875rem .3125rem .25rem;
        margin-top: 1.25rem;
        font-size: 2.1rem;
    }

    .picker__box .picker__header .picker__date-display .picker__day-display {
        padding: .875rem .3125rem .25rem;
        margin-top: 1.25rem;
        font-size: 2.1rem;
    }

    .picker__box .picker__header .picker__date-display .picker__year-display {
        position: absolute;
        top: .625rem;
        left: 45%;
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.4);
    }

    .picker__box .picker__header .picker__select--month,
    .picker__box .picker__header .picker__select--year {
        display: inline-block;
        height: 2em;
        padding: 0;
        margin-right: .25em;
        margin-left: .25em;
        background: rgba(0, 0, 0, 0);
        border: none;
        border-bottom: 1px solid #ced4da;
        outline: 0;
    }

    .picker__box .picker__header .picker__select--month:focus,
    .picker__box .picker__header .picker__select--year:focus {
        border-color: rgba(0, 0, 0, 0.05);
    }

    .picker__box .picker__header .picker__select--year {
        width: 30%;
    }

    .picker__box .picker__header .picker__nav--prev,
    .picker__box .picker__header .picker__nav--next {
        position: absolute;
        box-sizing: content-box;
        padding: .1875rem .625rem;
    }

    .picker__box .picker__header .picker__nav--prev:hover,
    .picker__box .picker__header .picker__nav--next:hover {
        color: #000;
        cursor: pointer;
    }

    .picker__box .picker__header .picker__nav--prev:before,
    .picker__box .picker__header .picker__nav--next:before {
        display: block;
        font-family: "Font Awesome 5 Free", sans-serif;
        font-weight: 900;
    }

    .picker__box .picker__header .picker__nav--prev {
        left: -0.5em;
        padding-right: 1.25em;
    }

    .picker__box .picker__header .picker__nav--next {
        right: -0.2em;
        padding-left: 1.25em;
    }

    .picker__box .picker__table {
        width: 100%;
        margin-top: .75em;
        margin-bottom: .5em;
        font-size: 1rem;
        text-align: center;
        table-layout: fixed;
        border-spacing: 0;
        border-collapse: collapse;
    }

    .picker__box .picker__table th,
    .picker__box .picker__table td {
        text-align: center;
    }

    .picker__box .picker__table td {
        padding: 0;
        margin: 0;
    }

    .picker__box .picker__table .picker__weekday {
        width: 14%;
        padding-bottom: .25em;
        font-size: .9em;
        font-weight: 500;
        color: #999;
    }

    @media (min-height: 33.875em) {
        .picker__box .picker__table .picker__weekday {
            padding-bottom: .25em;
        }
    }

    .picker__box .picker__table .picker__day--today {
        position: relative;
        padding: .75rem 0;
        font-weight: 400;
        letter-spacing: -0.3;
        border: 1px solid rgba(0, 0, 0, 0);
    }

    .picker__box .picker__table .picker__day.picker__day--today {
        color: #1072e9;
    }

    .picker__box .picker__table .picker__day--infocus {
        padding: .75rem 0;
        font-weight: 400;
        color: #595959;
        letter-spacing: -0.3;
        border: #595959 rgba(0, 0, 0, 0);
    }

    .picker__box .picker__table .picker__day--infocus:hover {
        font-weight: 500;
        color: #000;
        cursor: pointer;
    }

    .picker__box .picker__table .picker__day--outfocus {
        display: none;
        padding: .75rem 0;
    }

    .picker__box .picker__table .picker__day--outfocus:hover {
        font-weight: 500;
        color: #ddd;
        cursor: pointer;
    }

    .picker__box .picker__table .picker__day--highlighted:hover {
        cursor: pointer;
    }

    .picker__box .picker__footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: .3125rem .625rem;
        text-align: right;
    }

    .picker__box .picker__footer .picker__button--today,
    .picker__box .picker__footer .picker__button--clear,
    .picker__box .picker__footer .picker__button--close {
        display: inline-block;
        width: 33%;
        padding: 1rem 0 .7rem 0;
        font-size: .8em;
        font-weight: 700;
        text-transform: uppercase;
        vertical-align: bottom;
        background: #fff;
        border: 1px solid #fff;
    }

    .picker__box .picker__footer .picker__button--today:hover,
    .picker__box .picker__footer .picker__button--clear:hover,
    .picker__box .picker__footer .picker__button--close:hover {
        color: #000;
        cursor: pointer;
        background: #b1dcfb;
        border-bottom-color: #b1dcfb;
    }

    .picker__box .picker__footer .picker__button--today:focus,
    .picker__box .picker__footer .picker__button--clear:focus,
    .picker__box .picker__footer .picker__button--close:focus {
        background: #b1dcfb;
        border-color: rgba(0, 0, 0, 0.05);
        outline: none;
    }

    .picker__box .picker__footer .picker__button--today:before,
    .picker__box .picker__footer .picker__button--clear:before,
    .picker__box .picker__footer .picker__button--close:before {
        position: relative;
        display: inline-block;
        height: 0;
    }

    .picker__box .picker__footer .picker__button--today:before,
    .picker__box .picker__footer .picker__button--clear:before {
        margin-right: .45em;
        content: " ";
    }

    .picker__box .picker__footer .picker__button--today:before {
        top: -0.05em;
        width: 0;
        border-top: .66em solid #0059bc;
        border-left: 0.66em solid rgba(0, 0, 0, 0);
    }

    .picker__box .picker__footer .picker__button--clear:before {
        top: -0.25em;
        width: .66em;
        border-top: 3px solid #e20;
    }

    .picker__box .picker__footer .picker__button--close:before {
        top: -0.1em;
        margin-right: .35em;
        font-size: 1.1em;
        color: #777;
        vertical-align: top;
        content: "×";
    }

    .picker__box .picker__footer .picker__button--today[disabled],
    .picker__box .picker__footer .picker__button--today[disabled]:hover {
        color: #ddd;
        cursor: default;
        background: #f5f5f5;
        border-color: #f5f5f5;
    }

    .picker__box .picker__footer .picker__button--today[disabled]:before {
        border-top-color: #aaa;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-extralarge {
        font-size: 1.25rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-5-text {
        color: #738a97;
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0,
    .my-0 {
        margin-top: 0 !important;
    }

    .mr-0 {
        margin-right: 0 !important;
    }

    .mb-0,
    .my-0 {
        margin-bottom: 0 !important;
    }

    .ml-0 {
        margin-left: 0 !important;
    }

    .p-1 {
        padding: .25rem !important;
    }

    .pt-1,
    .py-1 {
        padding-top: .25rem !important;
    }

    .py-1 {
        padding-bottom: .25rem !important;
    }

    .pl-1 {
        padding-left: .25rem !important;
    }

    .mt-1 {
        margin-top: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .pt-2,
    .py-2 {
        padding-top: .5rem !important;
    }

    .py-2 {
        padding-bottom: .5rem !important;
    }

    .mt-2 {
        margin-top: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .ml-2 {
        margin-left: .5rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .mt-3,
    .my-3 {
        margin-top: 1rem !important;
    }

    .mb-3,
    .my-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3 {
        margin-left: 1rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .px-5 {
        padding-right: 2rem !important;
    }

    .px-5 {
        padding-left: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    @media (min-width: 576px) {
        .pl-sm-2 {
            padding-left: .5rem !important;
        }
    }

    @media (min-width: 768px) {
        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }

        .px-md-5 {
            padding-right: 2rem !important;
        }

        .px-md-5 {
            padding-left: 2rem !important;
        }
    }

    @media (min-width: 1200px) {
        .pr-xl-0 {
            padding-right: 0 !important;
        }

        .pl-xl-0 {
            padding-left: 0 !important;
        }
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    body a:not([href]):not([tabindex]):focus,
    body a:not([href]):not([tabindex]):hover {
        color: #1072e9;
    }

    .bg-grey-1 {
        background-color: #f4f4f4;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-dark {
        color: rgba(0, 0, 0, 0.54);
    }

    .z-2 {
        z-index: 2;
    }

    .clear:after {
        clear: both;
        content: "";
        display: block;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .btn.btn-sm {
        padding: .5rem 1rem;
        font-size: .875rem;
    }

    .badge-pill {
        padding: .1rem .5rem;
        color: #fff;
    }

    .dropdown-content li>span {
        color: #595959 !important;
    }

    .dropdown-content li.active,
    .dropdown-content li:hover {
        background-color: #1072e9;
    }

    .dropdown-content li.active>span,
    .dropdown-content li:hover>span {
        color: #fff;
    }

    .error-text {
        color: rgba(255, 0, 0, 0.87);
        font-size: .875rem;
        margin-top: .5rem;
        text-indent: .5rem;
    }







    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar) {
        position: relative;
        line-height: 1.8;
        font-weight: 700 !important;
    }

    main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
        content: "";
        background-color: #455965;
        display: block;
        height: 100%;
        width: .428rem;
        position: absolute;
        left: -1.285rem;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }

        main .sp_sides_uniter {
            margin: 0 -15px;
        }

        main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
            left: -0.8125rem !important;
        }
    }

    .title {
        background-color: #1072e9 !important;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    .fixed_banner {
        bottom: 14px;
        display: flex;
        left: 14px;
        opacity: 1;
        position: fixed;
        transition: opacity 400ms ease-in-out, transform 600ms cubic-bezier(0.165, 0.84, 0.44, 1);
        width: 350px;
        z-index: 99;
    }

    .fixed_banner.show {
        opacity: 1;
    }

    @media screen and (max-width: 767px) {
        .fixed_banner {
            display: none;
        }
    }

    :focus {
        outline: 0;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #009688;
    }

    input.form-control[type=text],
    input.form-control[type=number] {
        border: none;
        border-bottom: 1px solid #1072e9;
        border-radius: 0;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        height: inherit;
        padding: .75rem .75rem;
        cursor: text;
    }

    input.form-control.picker__input {
        padding: .75rem .5rem;
        letter-spacing: -0.045rem;
        color: inherit !important;
    }

    textarea.form-control {
        border-color: #009688;
    }

    .custom-checkbox label[class*=custom-control-label] {
        cursor: pointer;
    }

    .custom-control-label::before {
        width: 1.125rem;
        height: 1.125rem;
        background-color: rgba(0, 0, 0, 0);
        border: 2px solid #d5d9db;
        top: 3px;
        box-shadow: none !important;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
        background-color: #1072e9;
        border-color: #1072e9;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background: url(https://assign-navi.jp/assets/img/common/check_white.png);
        background-size: contain;
        left: -1.4rem;
        top: 3px;
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #d5d9db;
    }

    .select-wrapper.anavi-select {
        background-color: #f4f4f4;
        border-radius: 3px 3px 0 0;
    }

    .select-wrapper.anavi-select input.select-dropdown {
        padding-right: 0;
        height: 3rem;
        border-color: #009688;
        background-color: rgba(0, 0, 0, 0);
        font-size: 1rem;
        text-indent: 1rem;
    }

    .select-wrapper.anavi-select span.caret {
        top: 1rem;
        right: 1rem;
    }

    .select-wrapper.anavi-select span.caret.material-icons {
        font-size: 16px;
    }

    .picker__nav--prev:before {
        content: "";
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 0 8px 12px;
        border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: rotate(180deg);
    }

    .picker__nav--next:before {
        content: "";
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 0 8px 12px;
        border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767;
        position: absolute;
        top: 50%;
        right: 40%;
    }

    .modal {
        opacity: .5;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    .modal-footer {
        border-top: none;
        padding: 0 2rem 2rem;
        margin-top: -2rem;
        flex-wrap: initial;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul {
        list-style: none;
        padding: 0;
    }

    .accordion_close {
        cursor: pointer;
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    @media (max-width: 767px) {
        .btn-outline-default:hover {
            border-color: #1072e9 !important;
            background-color: inherit !important;
            color: #1072e9 !important;
        }
    }

    @media (max-width: 767px) {
        .btn-outline-blue-grey:hover {
            border-color: #78909c !important;
            background-color: inherit !important;
            color: #78909c !important;
        }
    }

    .font-size-middle {
        font-size: custom-(1rem) !important;
        line-height: 28.8px !important;
    }

    .form-check-input[type=checkbox]:focus:not(:checked)+label::before {
        border-color: #8a8a8a;
    }

    .form-check-input[type=checkbox]:focus:checked+label::before {
        border-right-color: #1072e9;
        border-bottom-color: #1072e9;
    }

    .form-check-input[type=radio]:focus+label:before {
        border-color: custom-(#d5d9db);
        box-shadow: none;
    }

    .form-check-input[type=radio]:not(:checked)+label,
    .form-check-input[type=radio]:checked+label {
        height: initial;
    }

    #involvement_description_modal .modal-dialog {
        max-width: 532px;
    }

    #involvement_description_modal .modal-dialog .modal-header {
        padding-bottom: 1rem;
    }

    @media (max-width: 767px) {
        #involvement_description_modal .modal-dialog .modal-body {
            display: flex;
            justify-content: center;
        }
    }

    #involvement_description_modal .modal-dialog img {
        width: 100%;
    }

    @media (max-width: 767px) {
        #involvement_description_modal .modal-dialog img {
            max-width: 340px;
        }
    }

    #involvement_description_modal .modal-dialog .modal-footer {
        margin-top: 0;
        justify-content: center;
    }

    #select_trading_restriction_modal,
    #select_trading_flow_modal {
        padding-left: 2rem !important;
        padding-right: 2rem !important;
    }

    #select_trading_restriction_modal .modal-dialog,
    #select_trading_flow_modal .modal-dialog {
        max-width: 900px !important;
    }

    #select_trading_restriction_modal .modal-dialog .modal-header,
    #select_trading_flow_modal .modal-dialog .modal-header {
        padding-bottom: 1rem;
    }

    @media (max-width: 767px) {

        #select_trading_restriction_modal .modal-dialog .modal-body,
        #select_trading_flow_modal .modal-dialog .modal-body {
            display: flex;
            justify-content: center;
            flex-flow: column;
        }
    }

    #select_trading_restriction_modal .modal-dialog picture,
    #select_trading_flow_modal .modal-dialog picture {
        max-width: 100%;
    }

    #select_trading_restriction_modal .modal-dialog picture img,
    #select_trading_flow_modal .modal-dialog picture img {
        max-width: 100%;
        height: auto;
    }

    @media (max-width: 575px) {
        #select_trading_restriction_modal .modal-dialog .trading_restriction_checkboxes {
            margin: auto;
            margin-left: 0;
        }
    }

    @media (max-width: 575px) {
        #select_trading_restriction_modal .modal-dialog .trading_restriction_checkboxes .selecting-form {
            flex-flow: column;
        }
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used from: Embedded */
@media (min-width:576px) {

    label[for=order_accuracy_id_field1],
    label[for=interview_count_id_field1],
    label[for=interview_count_id_field2],
    label[for=interview_count_id_field3],
    label[for=public_status_id_field1],
    label[for=public_status_id_field2],
    label[for=publish_company_name_status_id_field1] {
        margin-left: 2.5rem;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}