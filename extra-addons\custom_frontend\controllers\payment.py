from odoo import http
from odoo.http import request
import requests
import logging
import json
import hmac
import hashlib
import base64
from datetime import datetime
import time

_logger = logging.getLogger(__name__)

# Constants
ERROR_MAPPING = {
    'card_error': 'カード情報が無効です',
    'expired_card': 'カードの有効期限が切れています',
    'insufficient_funds': '残高が不足しています',
    'processing_error': '処理中にエラーが発生しました',
    'system_error': 'システムエラーが発生しました'
}

SUPPORTED_PLANS = ['スタンダード会員', 'プレミアム会員']

class PaymentController(http.Controller):

    def _validate_token(self, token, token_key):
        """Validate payment token"""
        if not token or not token_key:
            return False
        # Thêm validation theo yêu cầu của ZEUS
        return True

    def _get_plan_amount(self, plan):
        """Get plan amount from plan string"""
        try:
            if ':' in plan:
                plan_parts = plan.split(':')
                if len(plan_parts) == 2:
                    plan_price_str = plan_parts[1].replace(',', '').replace('円', '')
                    return float(plan_price_str)
            # Mapping cho các plan cố định
            plan_mapping = {
                'スタンダード会員': 5000,
                'プレミアム会員': 10000
            }
            return plan_mapping.get(plan, 0)
        except Exception as e:
            _logger.error(f"Error parsing plan amount: {str(e)}")
            return 0

    def _save_credit_card(self, user_id, token, token_key, masked_cc_number, card_brand, cc_expiration):
        """Save credit card information"""
        try:
            card_vals = {
                'user_id': user_id,
                'token': token,
                'token_key': token_key,
                'masked_card_number': masked_cc_number,
                'card_brand': card_brand,
                'card_expiration': cc_expiration,
                'is_default': True  # Nếu là thẻ đầu tiên
            }
            return request.env['vit.user_credit_card'].sudo().create(card_vals)
        except Exception as e:
            _logger.error(f"Error saving credit card: {str(e)}")
            return False

    def _validate_auto_renew(self, plan, auto_renew):
        """Validate auto renew option"""
        if auto_renew:
            return plan in SUPPORTED_PLANS
        return True

    def _get_error_message(self, error_code):
        """Get localized error message"""
        return ERROR_MAPPING.get(error_code, '不明なエラーが発生しました')

    def _log_payment_attempt(self, user_id, plan, amount, payment_type):
        """Log payment attempt with structured data"""
        _logger.info({
            'event': 'payment_attempt',
            'user_id': user_id,
            'plan': plan,
            'amount': amount,
            'payment_type': payment_type,
            'timestamp': datetime.now().isoformat()
        })

    def _check_rate_limit(self, user_id):
        """Check if user has exceeded rate limit"""
        # Implement rate limiting logic here
        return True

    def _validate_payment_input(self, post):
        """Validate payment input data"""
        required_fields = ['credit_payment[token]', 'credit_payment[plan]']
        for field in required_fields:
            if not post.get(field):
                return False, f"Missing required field: {field}"
        return True, None

    @http.route('/mypage/plan', type='http', auth='public', methods=['POST'], website=True)
    def process_payment(self, **post):
        """Handle plan change payment"""
        try:
            # ✅ ZEUS SECURITY: Validate payment IP
            from ..utils.ip_middleware import validate_payment_ip
            is_valid, reason = validate_payment_ip()
            if not is_valid:
                _logger.warning(f"Payment blocked: {reason}")
                return request.redirect(f'/mypage/plan?error=payment_location_restricted')

            # Check rate limit
            if not self._check_rate_limit(request.session.get('loginId')):
                return request.redirect('/payment/rate_limit_exceeded')

            # Validate input
            is_valid, error_msg = self._validate_payment_input(post)
            if not is_valid:
                return request.redirect(f'/mypage/plan?error={error_msg}')

            return self._process_payment(post, is_nyukai=False)
        except Exception as e:
            request.env.cr.rollback()
            _logger.exception("Payment processing failed")
            return request.redirect('/payment/error')

    @http.route('/nyukai/plan', type='http', auth='public', methods=['POST'], website=True)
    def process_nyukai_payment(self, **post):
        """Handle registration payment"""
        try:
            # Check rate limit
            if not self._check_rate_limit(request.session.get('loginId')):
                return request.redirect('/payment/rate_limit_exceeded')

            # Validate input
            is_valid, error_msg = self._validate_payment_input(post)
            if not is_valid:
                return request.redirect(f'/nyukai/plan?error={error_msg}')

            return self._process_payment(post, is_nyukai=True)
        except Exception as e:
            request.env.cr.rollback()
            _logger.exception("Payment processing failed")
            return request.redirect('/payment/error')

    def _process_payment(self, post, is_nyukai=False):
        """Process payment from form"""
        try:
            # Get form data
            token = post.get('credit_payment[token]')
            token_key = post.get('credit_payment[token_key]')
            plan = post.get('credit_payment[plan]')
            masked_cc_number = post.get('credit_payment[masked_cc_number]')
            card_brand_code = post.get('credit_payment[card_brand_code]')
            cc_expiration = post.get('credit_payment[cc_expiration]')
            auto_renew = post.get('credit_payment[auto_renew]') == 'on'

            # Determine redirect page
            redirect_base = '/nyukai/plan' if is_nyukai else '/mypage/plan'

            # Log payment attempt
            self._log_payment_attempt(request.session.get('loginId'), plan, 0, 'registration' if is_nyukai else 'plan_change')

            # Validate token
            if not self._validate_token(token, token_key):
                return request.redirect(f'{redirect_base}?error=invalid_token')

            # Validate auto renew
            if not self._validate_auto_renew(plan, auto_renew):
                return request.redirect(f'{redirect_base}?error=invalid_auto_renew')

            # Get current user
            user_id = request.session.get('loginId')
            if not user_id:
                return request.redirect('/login')

            user = request.env['vit.users'].sudo().browse(int(user_id))
            if not user:
                return request.redirect('/login')

            # Get plan amount
            plan_amount = self._get_plan_amount(plan)
            if plan_amount <= 0:
                return request.redirect(f'{redirect_base}?error=invalid_plan')

            # Get ZEUS configuration
            zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()
            if not zeus_config:
                request.env['vit.zeus_configuration'].sudo().initialize_test_configuration()
                zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()

            # Create order ID
            order_id = f"{'NYUKAI' if is_nyukai else 'PLAN'}_{user_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Call ZEUS API
            payment_result = self._call_zeus_payment_api(
                token=token,
                token_key=token_key,
                merchant_id=zeus_config.merchant_id,
                service_id=zeus_config.service_id,
                secret_key=zeus_config.secret_key,
                amount=plan_amount,
                order_id=order_id,
                api_url=zeus_config.api_url
            )

            if payment_result.get('success'):
                # Update user plan
                update_values = {
                    'plan': plan,
                    'plan_updated_at': datetime.now(),
                    'payment_status': 'paid'
                }

                if is_nyukai:
                    update_values.update({
                        'active': True,
                        'activation_date': datetime.now()
                    })

                user.write(update_values)

                # Save credit card if auto renew
                if auto_renew:
                    self._save_credit_card(
                        user_id=user_id,
                        token=token,
                        token_key=token_key,
                        masked_cc_number=masked_cc_number,
                        card_brand=card_brand_code,
                        cc_expiration=cc_expiration
                    )

                # Save payment history
                payment_type = 'registration' if is_nyukai else 'plan_change'
                request.env['vit.payment_history'].sudo().create({
                    'user_id': user_id,
                    'plan': plan,
                    'amount': plan_amount,
                    'payment_date': datetime.now(),
                    'payment_method': 'credit_card',
                    'card_brand': card_brand_code,
                    'masked_card_number': masked_cc_number,
                    'card_expiration': cc_expiration,
                    'transaction_id': payment_result.get('transaction_id', ''),
                    'order_id': order_id,
                    'status': 'success',
                    'payment_type': payment_type,
                    'auto_renew': auto_renew,
                    'token': token if auto_renew else False,
                    'token_key': token_key if auto_renew else False
                })

                _logger.info(f"Payment successful for user {user_id}, plan {plan}, type: {payment_type}")
                type_param = 'nyukai' if is_nyukai else 'plan_change'
                transaction_id = payment_result.get('transaction_id', '')
                return request.redirect(f'/payment/result?success=true&plan={plan}&type={type_param}&transaction_id={transaction_id}')
            else:
                # Save failed payment
                payment_type = 'registration' if is_nyukai else 'plan_change'
                request.env['vit.payment_history'].sudo().create({
                    'user_id': user_id,
                    'plan': plan,
                    'amount': plan_amount,
                    'payment_date': datetime.now(),
                    'payment_method': 'credit_card',
                    'card_brand': card_brand_code,
                    'masked_card_number': masked_cc_number,
                    'card_expiration': cc_expiration,
                    'status': 'failed',
                    'error_message': self._get_error_message(payment_result.get('error_code', 'unknown')),
                    'error_code': payment_result.get('error_code', 'unknown'),
                    'payment_type': payment_type,
                    'order_id': order_id,
                    'auto_renew': auto_renew,
                    'token': token if auto_renew else False,
                    'token_key': token_key if auto_renew else False
                })

                _logger.error(f"Payment failed for user {user_id}: {payment_result.get('error_message')}")
                type_param = 'nyukai' if is_nyukai else 'plan_change'
                return request.redirect(f'/payment/result?error={payment_result.get("error_code", "unknown")}&type={type_param}')

        except Exception as e:
            _logger.exception(f"Exception during payment processing: {str(e)}")
            type_param = 'nyukai' if is_nyukai else 'plan_change'
            return request.redirect(f'/payment/result?error=system_error&type={type_param}')

    def _call_zeus_payment_api(self, token, token_key, merchant_id, service_id, secret_key, amount, order_id, api_url):
        """Call ZEUS payment API"""
        try:
            # Generate signature
            signature = self._generate_zeus_signature(merchant_id, service_id, amount, order_id, secret_key)

            # Prepare payload
            payload = {
                "merchant_id": merchant_id,
                "service_id": service_id,
                "token": token,
                "token_key": token_key,
                "amount": amount,
                "order_id": order_id,
                "signature": signature,
                "currency": "JPY"
            }

            _logger.info(f"Calling ZEUS API for order {order_id}")

            # Use mock response for test environment
            if merchant_id == 'TEST_MERCHANT' or service_id == 'TEST_SERVICE':
                _logger.info("Using mock response for test environment")
                return self._get_mock_payment_response(order_id)

            # Call actual API
            response = requests.post(api_url, data=payload, timeout=30)  # Add timeout

            # Handle response
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    return {
                        'success': True,
                        'transaction_id': result.get('transaction_id', ''),
                        'approval_code': result.get('approval_code', '')
                    }
                else:
                    return {
                        'success': False,
                        'error_code': result.get('error_code', 'unknown'),
                        'error_message': result.get('error_message', 'Unknown error')
                    }
            else:
                _logger.error(f"ZEUS API returned status code {response.status_code}")
                return {
                    'success': False,
                    'error_code': 'api_error',
                    'error_message': f"API error: {response.status_code}"
                }

        except requests.Timeout:
            _logger.error("ZEUS API request timed out")
            return {
                'success': False,
                'error_code': 'timeout',
                'error_message': 'Request timed out'
            }
        except Exception as e:
            _logger.exception(f"Exception calling ZEUS API: {str(e)}")
            return {
                'success': False,
                'error_code': 'system_error',
                'error_message': str(e)
            }

    def _generate_zeus_signature(self, merchant_id, service_id, amount, order_id, secret_key):
        """Generate ZEUS API signature"""
        data_to_sign = f"{merchant_id}{service_id}{amount}{order_id}{secret_key}"
        signature = hmac.new(
            secret_key.encode('utf-8'),
            data_to_sign.encode('utf-8'),
            hashlib.sha256
        ).digest()
        return base64.b64encode(signature).decode('utf-8')

    def _get_mock_payment_response(self, order_id):
        """Get mock payment response for test environment"""
        if 'error' not in order_id.lower():
            return {
                'success': True,
                'transaction_id': f"MOCK_TX_{order_id}",
                'approval_code': f"MOCK_APPROVAL_{datetime.now().strftime('%Y%m%d%H%M%S')}"
            }
        else:
            error_codes = {
                'card_error': 'Invalid card information',
                'expired_card': 'Card has expired',
                'insufficient_funds': 'Insufficient funds',
                'processing_error': 'Error processing payment',
                'system_error': 'System error'
            }
            error_code = next((code for code in error_codes.keys() if code in order_id.lower()), 'unknown')
            return {
                'success': False,
                'error_code': error_code,
                'error_message': error_codes.get(error_code, 'Unknown error')
            }

    @http.route('/zeus/webhook', type='json', auth='public', csrf=False)
    def zeus_webhook(self, **post):
        """Handle ZEUS webhook"""
        try:
            # Get webhook data
            payload = request.httprequest.get_data()
            signature = request.httprequest.headers.get('X-Zeus-Signature')

            # Verify webhook
            zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()
            if not zeus_config:
                return {'status': 'error', 'message': 'No active ZEUS configuration found'}

            if not self._verify_zeus_webhook(payload, signature, zeus_config.secret_key):
                _logger.error("Invalid webhook signature")
                return {'status': 'error', 'message': 'Invalid signature'}

            # Process webhook data
            data = json.loads(payload)
            transaction_id = data.get('transaction_id')
            status = data.get('status')

            _logger.info(f"Received webhook for transaction {transaction_id} with status {status}")

            # Update payment status with retry
            if not self._process_webhook_with_retry(transaction_id, status):
                return {'status': 'error', 'message': 'Failed to process webhook after retries'}

            return {'status': 'success'}

        except Exception as e:
            _logger.exception(f"Exception processing webhook: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    def _verify_zeus_webhook(self, payload, signature, secret_key):
        """Verify ZEUS webhook signature"""
        if not signature:
            return False

        expected_signature = hmac.new(
            secret_key.encode('utf-8'),
            payload,
            hashlib.sha256
        ).digest()
        expected_signature = base64.b64encode(expected_signature).decode('utf-8')

        return hmac.compare_digest(signature, expected_signature)

    def _process_webhook_with_retry(self, transaction_id, status, max_retries=3):
        """Process webhook with retry mechanism"""
        for attempt in range(max_retries):
            try:
                payment = request.env['vit.payment_history'].sudo().search([
                    ('transaction_id', '=', transaction_id)
                ], limit=1)

                if payment:
                    payment.write({
                        'status': 'success' if status == 'completed' else 'failed',
                        'updated_at': datetime.now()
                    })
                    _logger.info(f"Updated payment status for transaction {transaction_id} to {status}")
                    return True
                else:
                    _logger.warning(f"Payment not found for transaction {transaction_id}")
                    return False

            except Exception as e:
                if attempt == max_retries - 1:
                    _logger.error(f"Webhook processing failed after {max_retries} attempts: {str(e)}")
                    return False
                time.sleep(1)  # Wait before retry

    def process_renewal_payment(self, payment):
        """Process automatic renewal payment"""
        try:
            _logger.info(f"Processing renewal payment for user {payment.user_id.id}, plan {payment.plan}")

            # Get ZEUS configuration
            zeus_config = request.env['vit.zeus_configuration'].sudo().get_active_configuration()
            if not zeus_config:
                raise Exception("No active ZEUS configuration found")

            # Create order ID
            order_id = f"RENEWAL_{payment.user_id.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # Call ZEUS API
            payment_result = self._call_zeus_payment_api(
                token=payment.token,
                token_key=payment.token_key,
                merchant_id=zeus_config.merchant_id,
                service_id=zeus_config.service_id,
                secret_key=zeus_config.secret_key,
                amount=payment.amount,
                order_id=order_id,
                api_url=zeus_config.api_url
            )

            if payment_result.get('success'):
                # Save new payment
                request.env['vit.payment_history'].sudo().create({
                    'user_id': payment.user_id.id,
                    'plan': payment.plan,
                    'amount': payment.amount,
                    'payment_date': datetime.now(),
                    'payment_method': 'credit_card',
                    'card_brand': payment.card_brand,
                    'masked_card_number': payment.masked_card_number,
                    'card_expiration': payment.card_expiration,
                    'transaction_id': payment_result.get('transaction_id', ''),
                    'order_id': order_id,
                    'status': 'success',
                    'payment_type': 'renewal',
                    'auto_renew': payment.auto_renew,
                    'token': payment.token,
                    'token_key': payment.token_key
                })

                _logger.info(f"Renewal payment successful for user {payment.user_id.id}, plan {payment.plan}")
                return True
            else:
                # Save failed payment
                request.env['vit.payment_history'].sudo().create({
                    'user_id': payment.user_id.id,
                    'plan': payment.plan,
                    'amount': payment.amount,
                    'payment_date': datetime.now(),
                    'payment_method': 'credit_card',
                    'card_brand': payment.card_brand,
                    'masked_card_number': payment.masked_card_number,
                    'card_expiration': payment.card_expiration,
                    'status': 'failed',
                    'error_message': self._get_error_message(payment_result.get('error_code', 'unknown')),
                    'error_code': payment_result.get('error_code', 'unknown'),
                    'payment_type': 'renewal',
                    'order_id': order_id,
                    'auto_renew': payment.auto_renew,
                    'token': payment.token,
                    'token_key': payment.token_key
                })

                _logger.error(f"Renewal payment failed for user {payment.user_id.id}: {payment_result.get('error_message')}")
                return False

        except Exception as e:
            _logger.exception(f"Exception during renewal payment processing: {str(e)}")
            return False
