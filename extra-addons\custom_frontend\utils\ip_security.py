import logging
import ipaddress
import json
from datetime import datetime, timedelta
from collections import defaultdict
import requests

_logger = logging.getLogger(__name__)

class IPSecurityManager:
    def __init__(self):
        # IP blacklist (có thể load từ database hoặc config)
        self.blacklisted_ips = set()
        
        # Failed login attempts tracking
        self.failed_attempts = defaultdict(list)
        
        # Admin IP whitelist
        self.admin_whitelist = {
            '127.0.0.1',  # localhost
            '::1',        # localhost IPv6
            # Thêm IP office/admin ở đây
        }
        
        # High-risk countries (ISO country codes)
        self.blocked_countries = {
            'CN',  # China
            'RU',  # Russia
            'KP',  # North Korea
            'IR',  # Iran
            # Thêm các nước high-risk khác
        }
        
        # Allowed countries (nếu muốn whitelist approach)
        self.allowed_countries = {
            'JP',  # Japan (primary market)
            'US',  # United States
            'GB',  # United Kingdom
            'DE',  # Germany
            'FR',  # France
            'AU',  # Australia
            'CA',  # Canada
            'SG',  # Singapore
            'KR',  # South Korea
        }
    
    def get_client_ip(self, request):
        """Lấy IP thực của client (xử lý proxy/load balancer)"""
        # Kiểm tra các headers phổ biến
        ip_headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CF_CONNECTING_IP',  # Cloudflare
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ]
        
        for header in ip_headers:
            ip = request.httprequest.environ.get(header)
            if ip:
                # X-Forwarded-For có thể chứa nhiều IP, lấy IP đầu tiên
                ip = ip.split(',')[0].strip()
                if self._is_valid_ip(ip):
                    return ip
        
        # Fallback
        return request.httprequest.remote_addr or '127.0.0.1'
    
    def _is_valid_ip(self, ip):
        """Kiểm tra IP có hợp lệ không"""
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    def is_ip_blacklisted(self, ip):
        """Kiểm tra IP có trong blacklist không"""
        return ip in self.blacklisted_ips
    
    def is_admin_ip_allowed(self, ip):
        """Kiểm tra IP có được phép truy cập admin không"""
        return ip in self.admin_whitelist
    
    def add_failed_login_attempt(self, ip):
        """Thêm failed login attempt cho IP"""
        now = datetime.now()
        # Xóa attempts cũ hơn 1 giờ
        cutoff_time = now - timedelta(hours=1)
        self.failed_attempts[ip] = [
            attempt for attempt in self.failed_attempts[ip] 
            if attempt > cutoff_time
        ]
        
        # Thêm attempt mới
        self.failed_attempts[ip].append(now)
        
        # Auto-blacklist nếu quá nhiều attempts
        if len(self.failed_attempts[ip]) >= 5:  # 5 attempts trong 1 giờ
            self.blacklist_ip(ip, reason="Too many failed login attempts")
            return True
        
        return False
    
    def blacklist_ip(self, ip, reason="Manual blacklist"):
        """Thêm IP vào blacklist"""
        self.blacklisted_ips.add(ip)
        _logger.warning(f"IP {ip} blacklisted: {reason}")
    
    def whitelist_admin_ip(self, ip):
        """Thêm IP vào admin whitelist"""
        self.admin_whitelist.add(ip)
        _logger.info(f"IP {ip} added to admin whitelist")
    
    def get_country_from_ip(self, ip):
        """Lấy country code từ IP (sử dụng free service)"""
        try:
            # Sử dụng ipapi.co (free tier: 1000 requests/day)
            response = requests.get(f'https://ipapi.co/{ip}/country/', timeout=5)
            if response.status_code == 200:
                country = response.text.strip()
                return country if len(country) == 2 else None
        except Exception as e:
            _logger.error(f"Error getting country for IP {ip}: {str(e)}")
        
        return None
    
    def is_country_blocked(self, country_code):
        """Kiểm tra country có bị block không"""
        if not country_code:
            return False
        
        # Approach 1: Blacklist approach
        return country_code in self.blocked_countries
        
        # Approach 2: Whitelist approach (uncomment nếu muốn strict hơn)
        # return country_code not in self.allowed_countries
    
    def validate_ip_access(self, request, require_admin=False):
        """
        Validate IP access với các rules
        Returns: (is_allowed: bool, reason: str, country: str)
        """
        client_ip = self.get_client_ip(request)
        
        # 1. Kiểm tra blacklist
        if self.is_ip_blacklisted(client_ip):
            return False, "IP is blacklisted", None
        
        # 2. Kiểm tra admin access
        if require_admin and not self.is_admin_ip_allowed(client_ip):
            return False, "IP not in admin whitelist", None
        
        # 3. Kiểm tra geo-blocking (chỉ cho non-admin requests)
        if not require_admin:
            country = self.get_country_from_ip(client_ip)
            if country and self.is_country_blocked(country):
                return False, f"Country {country} is blocked", country
            
            return True, "Access allowed", country
        
        return True, "Admin access allowed", None
    
    def log_ip_access(self, request, endpoint, success=True, reason=None):
        """Log IP access attempts"""
        from .security_logger import security_logger
        
        client_ip = self.get_client_ip(request)
        country = self.get_country_from_ip(client_ip) if not success else None
        
        log_data = {
            'event': 'ip_access',
            'ip': client_ip,
            'endpoint': endpoint,
            'success': success,
            'reason': reason,
            'country': country,
            'user_agent': request.httprequest.headers.get('User-Agent', ''),
            'timestamp': datetime.now().isoformat()
        }
        
        if success:
            security_logger.security_logger.info(json.dumps(log_data))
        else:
            security_logger.security_logger.warning(json.dumps(log_data))
    
    def get_ip_stats(self):
        """Lấy thống kê IP để monitoring"""
        return {
            'blacklisted_count': len(self.blacklisted_ips),
            'admin_whitelist_count': len(self.admin_whitelist),
            'failed_attempts_ips': len(self.failed_attempts),
            'blocked_countries': list(self.blocked_countries),
            'allowed_countries': list(self.allowed_countries)
        }

# Singleton instance
ip_security = IPSecurityManager()
