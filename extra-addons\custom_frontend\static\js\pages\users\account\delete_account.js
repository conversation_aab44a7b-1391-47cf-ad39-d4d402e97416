import { userInfo } from "../../../router/router.js";
import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const deleteAccount = {
    'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: '登録・管理', link: null },
        { text: '会社データ管理', link: null },
        { text: '退会', link: null, current: true }
    ])}
    <div class="container-fluid" style="padding: 0 !important;">
        <div class="mail-settings-container">
            <div class="d-md-none">
                <button class="mobile-menu-btn" @click="toggleMobileMenu" style="position: absolute; top: 10px; right: 20px; z-index: 1000;">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a href="/companies/manage/edit">会社データ</a></li>
                        <li><a href="/users/edit">プロフィール</a></li>
                        <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a href="/users/profile/edit_password">パスワード</a></li>
                        <li><a href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a href="/mypage/plan">プラン</a></li>
                        <li><a class="active" href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
            <!-- Settings form -->
            <div class="container-fluid grabient pt-2 position-relative">
                <div class="row mb-4 mb-md-0">
                    <div class="d-md-block col-md-4 col-lg-3 side-menu-contents" style="padding-left: 30px !important;">
                        <div class="card px-3 pb-3 side-card collapsible">
                            <ul class="collapsible mb-0">
                                <div class="d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                                <li class="my-md-1" hidden><a class="d-block py-1 px-3" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3 active" aria-current="page" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-12 col-md-8 col-lg-9 mail-settings-form p-4">
                        <div class="setting-row">
                            <div class="d-flex flex-column align-items-center">
                                <div class="alert alert-warning mb-4" role="alert">
                                    <h4 class="alert-heading">退会の注意事項</h4>
                                    <p>アカウントを削除すると、以下のデータが完全に削除され、復元できなくなります：</p>
                                    <ul>
                                        <li>プロフィール情報</li>
                                        <li>登録した案件情報</li>
                                        <li>ブックマークした案件</li>
                                        <li>その他すべてのアカウント関連データ</li>
                                    </ul>
                                    <hr>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-4">
                                <button class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light" @click="deleteAccount">退会</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css" />

<!-- Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true" v-if="showConfirmModal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">退会確認</h5>
                <button type="button" class="close" @click="closeConfirmModal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>本当に退会しますか。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @click="closeConfirmModal">キャンセル</button>
                <button type="button" class="btn btn-danger" @click="confirmDeleteAccount" :disabled="isLoading">
                    <span v-if="isLoading" class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                    削除する
                </button>
            </div>
        </div>
    </div>
</div>
    `,
    data() {
        return {
            user_id: userInfo ? userInfo.user_id : null,
            isMobileMenuOpen: false,
            showConfirmModal: false,
            isLoading: false
        }
    },
    mounted() {

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });

    },
    methods:{
        deleteAccount(){
            // Set the flag first
            this.showConfirmModal = true;
            // Use $nextTick to ensure DOM is updated before showing modal
            this.$nextTick(() => {
                $('#deleteConfirmModal').modal({
                    backdrop: true,
                    keyboard: true,
                    show: true
                });
            });
        },
        closeConfirmModal() {
            // Hide the modal first
            $('#deleteConfirmModal').modal('hide');
            // After the modal is hidden, set the flag to false
            $('#deleteConfirmModal').on('hidden.bs.modal', () => {
                this.showConfirmModal = false;
            });
        },
        async confirmDeleteAccount(){
            if (this.isLoading) return; // Return early if already loading
            this.isLoading = true;
            try {
                const response = await fetch('/api/delete_account', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: this.user_id })
                });
                const data = await response.json();
                console.log(data);

                if(data.result.success){
                    localStorage.clear();
                    sessionStorage.clear();
                    window.location.href = '/gratitude';
                } else {
                    console.log('Error:', data.result.message);
                }
            } catch (error) {
                console.error('Error:', error);
            } finally {
                this.isLoading = false;
                // Close modal after operation
                this.closeConfirmModal();
            }
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        }
    }
}

export default deleteAccount