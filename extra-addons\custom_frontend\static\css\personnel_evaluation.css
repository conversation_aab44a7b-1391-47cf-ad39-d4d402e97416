.interview-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.interview-header {
  margin-bottom: 30px;
}

.interview-options {
  margin-top: 20px;
}

.checkbox-group {
  display: flex;
  gap: 30px;
  margin-bottom: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-label input {
  margin-right: 8px;
}

.info-fields {
  display: flex;
  gap: 30px;
  margin-top: 15px;
}

.info-field {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  margin-right: 5px;
}

.result-section {
  margin: 30px 0;
}

.result-options {
  display: flex;
  gap: 50px;
  margin-bottom: 15px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-label input {
  margin-right: 8px;
}

.reason-section {
  margin-top: 10px;
}

.reason-label {
  margin-bottom: 10px;
  color: #333;
}

.reason-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  color: #000;
}

.lock-section {
  margin: 30px 0;
}

.lock-label {
  margin-bottom: 10px;
  color: #333;
}

.lock-input {
  width: 250px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  color: #000;
}

.sample-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.sample-button {
  background: none;
  border: none;
  color: #00BFA5;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: 14px;
}

.submit-section {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.submit-button {
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 40px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background-color: #3367D6;
}

.evaluation-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
}

.title {
  background-color: #1072e9 !important;
  background-size: cover;
}

.title h1 {
  color: #fff;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  font-weight: 500;
  color: #555;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.info-row a {
  color: #1890ff !important;
}

.info-row a:hover {
  color: #1072e9 !important;
  text-decoration: underline !important;
}

.form-header {
  display: grid;
  grid-template-columns: 200px 150px 1fr;
  gap: 20px;
  align-items: center;
  padding: 0 10px 10px;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.evaluation-item {
  display: grid;
  grid-template-columns: 200px 150px 1fr;
  gap: 20px;
  align-items: center;
  padding: 15px 10px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.evaluation-item:hover {
  background-color: #f5f5f5;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 150px;
  font-weight: 500;
  color: #333;
}

.actions {
  display: flex;
  gap: 5px;
  margin-left: 10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.evaluation-item:hover .actions {
  opacity: 1;
}

.icon-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.icon-button:hover {
  opacity: 1;
}

.delete-button {
  color: #ff0000;
  font-size: 16px;
}

.edit-input {
  padding: 4px 0;
  border: none;
  border-bottom: 1px solid #00BFA5;
  font-size: 14px;
  width: 120px;
  outline: none;
  background: transparent;
  color: #000;
}

.edit-input:focus {
  border-bottom: 2px solid #00BFA5;
}

.rating {
  display: flex;
  gap: 4px;
}

.star {
  color: #e0e0e0;
  cursor: pointer;
  user-select: none;
  font-size: 20px;
  transition: color 0.2s;
}

.star:hover {
  color: #00BFA5;
}

.star.filled {
  color: #00BFA5;
}

.reason-input {
  width: 100%;
  padding: 4px 0;
  border: none;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  outline: none;
  background: transparent;
  transition: border-color 0.2s;
  color: #000;
}

.reason-input:focus {
  border-bottom: 2px solid #00BFA5 !important;
}

.actions-row {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
}

.button {
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.button:focus {
  outline: none !important;
}

.add-button {
  background-color: #fff;
  color: #00BFA5;
  border: 1px solid #00BFA5;
}

.add-button:hover {
  background-color: rgba(0, 191, 165, 0.1);
}

/* Personnel Information Styles */
.personnel-info {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 30px;
  border-left: 4px solid #1890ff;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  font-weight: 500;
  color: #555;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.form-header {
  display: grid;
  grid-template-columns: 200px 150px 1fr;
  gap: 20px;
  align-items: center;
  padding: 0 10px 10px;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.evaluation-item {
  display: grid;
  grid-template-columns: 200px 150px 1fr;
  gap: 20px;
  align-items: center;
  padding: 15px 10px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.evaluation-item:hover {
  background-color: #f5f5f5;
}

.category-name {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 150px;
  font-weight: 500;
  color: #333;
}

.actions {
  display: flex;
  gap: 5px;
  margin-left: 10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.evaluation-item:hover .actions {
  opacity: 1;
}

.icon-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.icon-button:hover {
  opacity: 1;
}

.delete-button {
  color: #ff0000;
  font-size: 16px;
}

.edit-input {
  padding: 4px 0;
  border: none;
  border-bottom: 1px solid #00BFA5;
  font-size: 14px;
  width: 120px;
  outline: none;
  background: transparent;
  color: #000;
}

.edit-input:focus {
  border-bottom: 2px solid #00BFA5;
}

.rating {
  display: flex;
  gap: 4px;
}

.star {
  color: #e0e0e0;
  cursor: pointer;
  user-select: none;
  font-size: 20px;
  transition: color 0.2s;
}

.star:hover {
  color: #00BFA5;
}

.star.filled {
  color: #00BFA5;
}

.reason-input {
  width: 100%;
  padding: 4px 0;
  border: none;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  outline: none;
  background: transparent;
  transition: border-color 0.2s;
  color: #000;
}

.reason-input:focus {
  border-bottom: 2px solid #00BFA5 !important;
}

.actions-row {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
}

.button {
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.button:focus {
  outline: none !important;
}

.add-button {
  background-color: #fff;
  color: #00BFA5;
  border: 1px solid #00BFA5;
}

.add-button:hover {
  background-color: rgba(0, 191, 165, 0.1);
}
