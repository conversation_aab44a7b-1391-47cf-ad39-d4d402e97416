import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const planOut = {
    'template' : `
${createBreadcrumb([
    { text: 'プラン変更', link: '/mypage' },
    { text: '退会', link: null, current: true }
])}
<div class="container">
    <p>退会する場合は下記の注意事項を確認後下のボタンをクリックしてください。</p>

    <div class="note">
        <ul>
            <li class="note-content" v-for="note in notes" :key="note.id">- {{note.content}}</li>
        </ul>
    </div>

    <div class="row justify-content-center">
        <div class="col-12 col-md-4">
            <button class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light">
                退会
            </button>
        </div>
    </div>
</div>
<link rel="stylesheet" href="/custom_frontend/static/css/plan/plan_out.css" />
    `,
    data() {
        return {
            notes: [],
        }
    },
    mounted() {
        this.getNote();
    },
    methods: {
        async getNote(){
            try{
                const res = await fetch('/api/get_note', {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({}),
                });

                const result = await res.json();
                console.log("Note: ", result);

                if(result.result.success){
                    this.notes = result.result.data;
                } else{
                    console.log("Fail to get note");
                }
            } catch(error){
                console.log("Error API: ", error);
            }
        }
    },
}

export default planOut;