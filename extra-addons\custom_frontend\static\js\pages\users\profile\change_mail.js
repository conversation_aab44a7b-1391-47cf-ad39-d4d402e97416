import { userInfo } from "../../../router/router.js";
import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const change_mail = {
    'template': `
    <main class="pb-3 margin-header" id="vue-app">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '登録・管理', link: null },
            { text: '会社データ管理', link: null },
            { text: 'メールアドレス', link: null, current: true }
        ])}
        <div class="container-fluid">
            <div class="d-flex justify-content-end align-items-center">
                <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a href="/companies/manage/edit">会社データ</a></li>
                        <li><a href="/users/edit">プロフィール</a></li>
                        <li><a class="active" href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a href="/users/profile/edit_password">パスワード</a></li>
                        <li><a href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a href="/mypage/plan">プラン</a></li>
                        <li><a href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="container-fluid grabient pt-5 position-relative">
            <div class="row mb-4 mb-md-0">
                <div class="d-md-block col-md-4 col-lg-3 side-menu-contents">
                    <div class="card px-3 pb-3 side-card collapsible">
                        <ul class="collapsible mb-0">
                            <div class="d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3 active" aria-current="page" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                            <li class="my-md-1" hidden><a class="d-block py-1 px-3" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                    <form class="edit_user" @submit.prevent="request_change_mail">
                        <div class="card mb-5 px-md-3">
                            <div class="card-body py-5">
                                <div class="mb-5">
                                    <label class="font-middle mb-3" for="email_field">現在のメールアドレス</label>
                                    <p>{{this.email}}</p>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3" for="new_email_field">新しいメールアドレス<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="email_field" type="text" v-model="new_email"></div>
                                <div v-if="!check_new_email" class="text-danger text-left">新しいメールアドレスを入力してください。</div>
                                <p class="custom-grey-text">会社のメールアドレスでご登録をお願いします。</p>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-4"><button name="button" type="submit" class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light" :disabled="isSendmail">変更</button></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css"/>
    <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css">
    <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/change_mail.css"/>
    `,
    data(){
        return {
            email: userInfo ? userInfo.user_email : null,
            new_email: '',
            errMessage: '',
            isSendmail: false,
            check_new_email: true,
            isMobileMenuOpen: false
        }
    },
    methods:{
        async request_change_mail(){
            if (!this.validateInput()) {
                return;
            }

            this.isSendmail = true;
            this.errMessage = '';
            await fetch('/api/request_change_mail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: this.email, new_email: this.new_email }),

            }).then(async (response) => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const data = await response.json();

                if (data.result.success) {
                    console.log(data.result);

                    // Hiển thị toast message ngay lập tức
                    if (window.toastr) {
                        window.toastr.success("メールアドレスリンクが送信されました。新しいメールアドレスをご確認ください。", "", {
                            showDuration: 300,
                            hideDuration: 2000,
                            extendedTimeOut: 0,
                            positionClass: 'toast-top-full-width',
                            showEasing: 'swing',
                            hideEasing: 'linear',
                            progressBar: true,
                            closeButton: true,
                            closeHtml: '<button>&times;</button>',
                            preventDuplicates: true,
                            toastClass: 'toast-success custom-toastr'
                        });
                    }

                    // Đợi 3 giây để người dùng đọc thông báo, sau đó đăng xuất và chuyển hướng
                    setTimeout(() => {
                        // Đăng xuất người dùng bằng cách xóa tất cả thông tin đăng nhập
                        localStorage.removeItem('isloggedin');
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('userId');
                        localStorage.removeItem('user_name');
                        localStorage.removeItem('company_id');
                        localStorage.removeItem('email');

                        // Chuyển hướng về trang home
                        window.location.replace("/");
                    }, 3000);
                } else {
                    if (window.toastr) {
                        window.toastr.error(data.result.message);
                    } else {
                        this.errMessage = data.result.message;
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                window.toastr.warning(data.result.message);
            })
            .finally(() => {
                this.new_email = '';
                this.isSendmail = false;
            });
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function() {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function() {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function() {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function() {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },

        validateInput() {
            this.check_new_email = false;

            // Kiểm tra email trống
            if (this.new_email.trim() === '') {
                if (window.toastr) {
                    window.toastr.error("新しいメールアドレスを入力してください。");
                }
                return false;
            }

            // Kiểm tra định dạng email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(this.new_email.trim())) {
                if (window.toastr) {
                    window.toastr.error("有効なメールアドレス形式で入力してください。");
                }
                return false;
            }

            // Kiểm tra email mới không trùng với email hiện tại
            if (this.new_email.trim().toLowerCase() === this.email.toLowerCase()) {
                if (window.toastr) {
                    window.toastr.error("新しいメールアドレスは現在のメールアドレスと異なる必要があります。");
                }
                return false;
            }

            this.check_new_email = true;
            return true;
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        }

    },
    mounted(){
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        const toastrCSS = document.createElement("link");
        toastrCSS.rel = "stylesheet";
        toastrCSS.href = "/custom_frontend/static/css/Toastr.css";
        toastrCSS.onload = function() {
            console.log("Toastr CSS loaded successfully!");
        };
        document.head.appendChild(toastrCSS);

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });
    },

    watch: {
        new_email: {
            handler() {
                this.check_new_email = true;
            },
        },
    },
}
export default change_mail