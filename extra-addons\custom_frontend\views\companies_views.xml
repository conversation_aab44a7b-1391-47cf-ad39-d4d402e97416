<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Tree View -->
    <record id="vit_companies_tree_view" model="ir.ui.view">
        <field name="name">vit.companies.tree</field>
        <field name="model">vit.companies</field>
        <field name="arch" type="xml">
            <tree string="Channel">
                <field name="name"/>
                <field name="phone_number"/>
                <field name="email"/>
                <field name="representative"/>
                <field name="charter_capital"/>
                <field name="employee_count"/>
                <field name="industry"/>
                <field name="specialty_industry"/>
                <field name="public_status"/>
                <field name="description"/>
                <field name="options_bussiness"/>
                <field name="head_office_location"/>
                <field name="branch_location"/>
                <field name="established_year"/>
                <field name="has_dispatch_license"/>
                <field name="response_rate"/>
                <field name="interview_count"/>
                <field name="successful_contracts"/>
                <field name="high_ratings_count"/>
                <field name="created_at"/>
                <field name="updated_at"/>
            </tree>
        </field>
    </record>

    <!-- Form View -->
    <record id="vit_companies_form_view" model="ir.ui.view">
        <field name="name">vit.companies.form</field>
        <field name="model">vit.companies</field>
        <field name="arch" type="xml">
            <form string="Lost Reason">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="email"/>
                            <field name="phone_number"/>
                            <field name="representative"/>
                            <field name="established_year"/>
                        </group>
                        <group>
                            <field name="charter_capital"/>
                            <field name="employee_count"/>
                            <field name="industry"/>
                            <field name="specialty_industry"/>
                            <field name="public_status"/>
                            <field name="description"/>
                            <field name="options_bussiness"/>
                            <field name="head_office_location"/>
                            <field name="branch_location"/>
                            <field name="has_dispatch_license"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Statistics">
                            <group>
                                <field name="response_rate"/>
                                <field name="interview_count"/>
                                <field name="successful_contracts"/>
                                <field name="high_ratings_count"/>
                            </group>
                        </page>
                        <page string="Addresses">
                            <!-- <field name="addresses" nolabel="1"/> -->
                        </page>
                    </notebook>
                    <group>
                        <field name="created_at" readonly="1"/>
                        <field name="created_by" readonly="1"/>
                        <field name="updated_at" readonly="1"/>
                        <field name="updated_by" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="vit_companies_action" model="ir.actions.act_window">
        <field name="name">Companies</field>
        <field name="res_model">vit.companies</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="vit_companies_tree_view"/>
    </record>

    <!-- Menu -->
    <menuitem id="vit_companies_menu_root" name="VIT Companies"/>
    <menuitem id="vit_companies_menu" name="Manage Companies" parent="vit_companies_menu_root" action="vit_companies_action"/>
</odoo>
