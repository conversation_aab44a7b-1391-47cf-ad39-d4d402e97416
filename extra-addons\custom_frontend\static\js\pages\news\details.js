const detail = {
    props:['id'],
    'template': `
        <main class="pb-3 margin-header" id="vue-app" data-v-app>
            <div class="container-fluid"></div>
            <div class="container-fluid title py-2 py-md-4">
                <h1 class="mb-0">運営からのお知らせ詳細</h1>
            </div>
            <div class="container-fluid grabient pt-5">
                <div class="row">
                    <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                        <div v-if="news" class="detail-card-wrapper">
                            <div  class="d-flex align-items-start bg-light-green p-4">
                                <div class="icon flex-shrink-0 mr-md-3 mr-2 other_news"></div>
                                <div>
                                    <div class="font-extralarge">{{news.title}}</div>
                                    <div class="flex-shrink-0 font-default custom-grey-5-text align-self-end">{{ formatDate(news.created_at) }}</div>
                                </div>
                            </div>
                            <div class="body p-4 bg-white font-middle" v-html="news.content"></div>
                        </div>
                        <a class="btn btn-blue-grey btn-md mt-6 mx-0 mb-0 d-inline-block font-default waves-effect waves-light" href="/mypage">マッチング状況画面に戻る</a>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/news/details.css" type="text/css"/>
    `,
    data() {
        return {
            news: null,
        }
    },
    mounted() {
        this.getById(this.id);
        console.log('id',this.id);
    },
    methods: {
        async getById(id) {
            try {
                const res = await fetch(`/api/new_detail?id=${id}`);
                const json = await res.json();
                if (json.success) {
                    this.news = json.data;
                } else {
                    console.error("Không tìm thấy bài viết:", json.message);
                }
            } catch (err) {
                console.error("Lỗi khi gọi API:", err);
            }
        },
        formatDate(datetime) {
            if (!datetime) return '';
            const date = new Date(datetime);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}年${month}月${day}日`;
        }        
    },
}
export default detail