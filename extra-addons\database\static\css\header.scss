@font-face {
    font-family: 'Roboto';
    src: url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
    font-weight: normal;
    font-style: normal;
}

body {
    font-family: 'Roboto', sans-serif;
}

*{
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
}
$blue: #007bff;
$indigo: #6610f2;
$purple: #6f42c1;
$pink: #e83e8c;
$red: #dc3545;
$orange: #fd7e14;
$yellow: #ffc107;
$green: #28a745;
$teal: #20c997;
$cyan: #17a2b8;
$white: #fff;
$gray: #6c757d;
$gray-dark: #343a40;
$primary: #007bff;
$secondary: #6c757d;
$success: #28a745;
$info: #17a2b8;
$warning: #ffc107;
$danger: #dc3545;
$light: #f8f9fa;
$dark: #343a40;

.header-custom{
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 100000000;
    box-shadow: #6c757d 0px 0px 10px 0px;
    background-color: $white;
    height: 80px;
    display: flex;
    ul, p{
        margin-bottom: 0;
    }
    .navbar-custom{
        width: 60%;
        display: flex;
        justify-items: flex-start;
        align-items: center;
        padding: 2rem;
        .logo{
            width: 8.5rem;
            margin-right: 1.5rem;
            margin-bottom: 0;
        }
        .nav-links{
            display: flex;
            justify-content: space-between;
            align-items: center;
            li{
                margin: 0 1rem;
                a{
                    color: $dark;
                    line-height: 20px;
                    transition: all ease-in-out 0.3s;
                    &:hover{
                        color: $secondary;
                        &::before{
                            transform: rotate(135deg);
                            color: $secondary;
                        }
                    }
                }
                .nav-menu{
                    position: relative;
                    &::before{
                        content: "";
                        position: absolute;
                        width: 7px;
                        height: 7px;
                        border-left: $dark solid 2px;
                        border-bottom: $dark solid 2px;
                        right: -15px;
                        top: 3px;
                        transition: all ease-in-out 0.3s;
                        transform: rotate(315deg);
                    }
                }
                
            }
            .dropdown{
                position: relative;
                .dropdown-menu{
                    display: none;
                    position: absolute;
                    right: -25px;
                    top: 12px;
                    background-color: $white;
                    z-index: 1000;
                    
                    
                        margin: 0.5rem 0;
                        a{
                            padding: 10px 20px;
                            display: block;
                            color: $dark;
                            &:hover{
                                color: $secondary;
                            }
                        }
                }
                &:hover{
                    .dropdown-menu{
                        display: block;
                    }
                }
            }
        }
    }
    .contact-info-cus{
        width: 40%;
        margin-top: 3px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 2rem;
        li{
            margin: 0 1rem;
            a, button{
                background-color: rgba(0,0,0,0);
                border: none;
                i{
                    color: $dark;
                font-size: 32px;
                }
            }
        }
        .phone{
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 18px;
            font-family: 'Roboto', sans-serif;
            a{
                display: block;
                color: $primary;
                i{
                    font-size: 18px;
                    color: $primary;
                }
            }
            p{
                display: block;
                font-size: 12px;
                font-weight: bold;
                letter-spacing: 0;
            }
        }
    }
}

.footer-custom{
    background-color: $dark;
    padding: 4rem;
    color: $white;
    a{
        text-decoration: none;
        font-size: 14px;
        transition: all ease-in-out 0.3s;
        color: $white;
        font-weight: 300;
        &:hover{
            color: $cyan;
            text-decoration: none;
        }
    }
}
body{
    position: relative;
    #list-route{
        background-color: #17a2b8;
        border: none;
        position: fixed;
        width: 70px;
        display: flex;
        height: 70px;
        bottom: 70px;
        right: 30px;
        color: #fff;
        font-size: 20px;
        align-content: center;
        justify-content: center;
        align-items: center;
        border-radius: 100%;
        z-index: 100000000;
        transition: all ease-in-out 0.3s  ;
        &:hover{
            background-color: #1900ff;
        }
        &:focus-visible{
            outline: none;
        }
        &:focus{
            outline: none;
        }

    }
}

