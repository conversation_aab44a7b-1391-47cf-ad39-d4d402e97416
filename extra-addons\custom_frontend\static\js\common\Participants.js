const ParticipantsSlider = {
    props: {
        min: { type: Number, default: 1 },
        max: { type: Number, default: 20 },
        step: { type: Number, default: 1 },
        startValue: {
            type: Array,
            default: () => [null, null] // null = không giới hạn
        },
    },
    data() {
        return {
            selectedRange: [
                this.startValue[0] !== null ? parseInt(this.startValue[0]) : this.min,
                this.startValue[1] !== null ? parseInt(this.startValue[1]) : this.max
            ],
            slider: null,
        };
    },
    watch: {
        startValue: {
            handler(newValues) {
                if (!Array.isArray(newValues)) return;

                const newMin = newValues[0] !== null ? parseInt(newValues[0]) : this.min;
                const newMax = newValues[1] !== null ? parseInt(newValues[1]) : this.max;

                if (newMin !== this.selectedRange[0] || newMax !== this.selectedRange[1]) {
                    this.selectedRange = [newMin, newMax];
                    if (this.slider) {
                        this.slider.set(this.selectedRange);
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) {
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedRange,
                    connect: true,
                    range: { min: this.min, max: this.max },
                    step: this.step,
                    margin: this.step,
                });

                this.slider.on("update", (values, handle) => {
                    let newValue = Math.round(parseFloat(values[handle]));

                    if (handle === 0) {
                        this.selectedRange[0] = newValue;
                    } else {
                        this.selectedRange[1] = newValue;
                    }

                    // Kiểm tra nếu là giá trị tối thiểu/tối đa thì đặt thành null
                    const participantsMin = this.selectedRange[0] === this.min ? null : this.selectedRange[0];
                    const participantsMax = this.selectedRange[1] === this.max ? null : this.selectedRange[1];

                    this.$emit("update:participants", [participantsMin, participantsMax]);
                });
            }
        });
    },
    computed: {
        displayMin() {
            return this.selectedRange[0] === this.min ? "下限なし" : this.selectedRange[0] + "人";
        },
        displayMax() {
            return this.selectedRange[1] === this.max ? "上限なし" : this.selectedRange[1] + "人";
        },
        displayRange() {
            return `${this.displayMin} 〜 ${this.displayMax}`;
        },
    },
    template: `
      <div class="mb-2">
        <p class="d-flex justify-content-center" id="participants-range">
          {{ displayRange }}
        </p>
        <div class="slider-styled mx-4 noUi-target noUi-ltr noUi-horizontal noUi-txt-dir-ltr" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>下限なし</span><span>上限なし</span>
        </div>
      </div>
    `,
};
export default ParticipantsSlider;
