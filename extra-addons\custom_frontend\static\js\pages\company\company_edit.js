import { checkToastrMessage } from "/custom_frontend/static/js/common/Toastr.js";
import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const company_edit = {
    'template':`
    <main class="pb-3 margin-header" id="vue-app" data-v-app="">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '登録・管理', link: null },
            { text: '会社データ管理', link: null },
            { text: '会社データ', link: null, current: true }
        ])}
        <div class="container-fluid">
            <div class="d-flex justify-content-end align-items-center">
                <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a class="active" href="/companies/manage/edit">会社データ</a></li>
                        <li><a  href="/users/edit">プロフィール</a></li>
                        <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a href="/users/profile/edit_password">パスワード</a></li>
                        <li><a href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a href="/mypage/plan">プラン</a></li>
                        <li><a href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="container-fluid grabient pt-5 position-relative">
            <div class="row mb-4 mb-md-0">
                <div class="d-md-block col-md-4 col-lg-3 side-menu-contents">
                    <div class="card px-3 pb-3 side-card collapsible">
                        <ul class="collapsible mb-0">
                            <div class="d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                            <li class="my-md-1"><a class="d-block py-1 px-3 active" aria-current="page" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                            <li class="my-md-1" hidden><a class="d-block py-1 px-3" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                    <div class="card mb-4">
                        <div class="card-body p-5">
                            <div class="mb-5">
                                <p class="font-extralarge mb-3">基本情報</p>
                                <span class="custom-grey-text d-block mb-1">案件・人財登録の際「会社情報非公開」にした場合は「基本情報」の各項目は他ユーザーに公開されません。</span>
                            </div>
                            <form class="edit_company" id="edit_company" @submit.prevent="updateCompany">
                                <div class="row pl-3 mb-3">
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">会社番号</label><input class="form-control" autocomplete="off" id="name_field" type="text" name="company[referral_code]" v-model="referral_code" disabled>
                                            <div v-if="errors.referral_code" class="text-danger text-left mt-3">{{ errors.referral_code }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">会社名<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="name_field" type="text" name="company[name]" v-model="company_name">
                                            <div v-if="errors.company_name" class="text-danger text-left mt-3">{{ errors.company_name }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <div class="mx-auto mb-5">
                                            <label class="font-middle mb-3">業種</label>
                                            <div v-dropdown="{modelValue: '', listType: 'options_business_field'}" @selected="select_options_business"><input type="hidden" v-model="options_business_field" name="options_business_field"></div>
                                            <div v-if="errors.options_business_field" class="text-danger text-left mt-3">{{ errors.options_business_field }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">ホームぺージのURL<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="site_url_field" type="text" name="company[site_url]" v-model="company_site_url">
                                            <div v-if="errors.company_site_url" class="text-danger text-left mt-3">{{ errors.company_site_url }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12"><label class="font-middle mb-3" for="">本社所在地（都道府県）</label></div>
                                    <div class="col-12 col-md-6">
                                        <div class="mx-auto mb-5">
                                            <div v-dropdown="{modelValue: '', listType: 'workplaces'}" @selected="select_head_office_location"><input type="hidden" v-model="head_office_location" name="head_office_location"></div>
                                            <div v-if="errors.head_office_location" class="text-danger text-left">{{ errors.head_office_location }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12"><label class="font-middle mb-3" for="address_field">本社所在地</label></div>
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><input class="form-control" autocomplete="off" id="address_field" type="text" name="company[address]" v-model="address">
                                            <div v-if="errors.address" class="text-danger text-left mt-3">{{ errors.address }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mx-auto">
                                            <div class="row ml-0 mr-0">
                                                <workplace-selector :required-label="false" lable="支店所在地（都道府県）" title="支店所在地（都道府県）を選択" bold="" col_12="" :mb_5="margin_bot" :inputName="'brand_location[]'" v-model="brand_location"></workplace-selector>
                                            </div>
                                            <div v-if="errors.brand_location" class="text-danger text-left mb-5">{{ errors.brand_location }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12"><label class="font-middle" for="tel_field">電話番号<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><span class="custom-grey-text d-block mb-1">ハイフンなしでご入力ください。</span><span class="custom-grey-text d-block mb-3">Mi52からのご連絡に利用します。この項目は他ユーザーに公開されることはありません。</span></div>
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><input class="form-control" autocomplete="off" id="tel_field" type="text" name="company[tel]" v-model="phone">
                                            <div v-if="errors.phone" class="text-danger text-left mt-3">{{ errors.phone }}</div>
                                        </div>
                                    </div>
                                </div>
                                <p class="font-extralarge">会社概要</p>
                                <span class="custom-grey-text d-block mb-3">設立年、従業員数、資本金、派遣免許有無は、会社情報非公開の案件・人財を含め全ユーザーが閲覧可能です。</span>
                                <div class="row pl-3 mb-3">
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">設立年（西暦）<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="establishment_field" type="text" name="company[establishment]" v-model="established_year">
                                            <div v-if="errors.established_year" class="text-danger text-left mt-3">{{ errors.established_year }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">従業員数（人）<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="number_of_employees_field" type="text" name="company[number_of_employees]" v-model="employee_count">
                                            <div v-if="errors.employee_count" class="text-danger text-left mt-3">{{ errors.employee_count }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mx-auto mb-5"><label class="font-middle mb-3 active" for="">資本金（万円）<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label><input class="form-control" autocomplete="off" id="capital_field" type="text" name="company[capital]" v-model="charter_capital">
                                            <div v-if="errors.charter_capital" class="text-danger text-left mt-3">{{ errors.charter_capital }}</div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-5">
                                            <label class="font-middle mb-0">労働者派遣事業許可の有無<span class="badge-pill badge-danger pink lighten-2 font-small ml-2">必須</span></label>
                                            <div class="form-inline my-3">
                                                <div class="form-check mr-sm-8"><input class="form-check-input" id="dispatch_license_field0" type="radio" name="company[dispatch_license]" :value="true" v-model="has_dispatch_license"><label id="dispatch_license_field_label_0" class="form-check-label" for="dispatch_license_field0">有</label></div>
                                                <div class="form-check mr-sm-8"><input class="form-check-input" id="dispatch_license_field1" type="radio" name="company[dispatch_license]" :value="false" v-model="has_dispatch_license"><label id="dispatch_license_field_label_1" class="form-check-label" for="dispatch_license_field1">無</label></div>
                                            </div>
                                            <div v-if="errors.has_dispatch_license" class="text-danger text-left mt-3">{{ errors.has_dispatch_license }}</div>
                                        </div>
                                    </div>
                                </div>
                                <p class="font-extralarge mb-3">会社紹介</p>
                                <span class="custom-grey-text d-block mb-1">案件・人財登録の際「会社情報非公開」にした場合は「会社紹介」の各項目は他ユーザーに公開されません。</span>
                                <div class="row px-2 px-md-3 mb-4 mb-4">
                                    <div class="col-12">
                                        <label id="" class="font-middle pl-1 pl-md-0 mb-3">得意領域</label>
                                        <div class="selecting-form row px-3">
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field0" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="pmo"><label id="specialty_field_field_label_0" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field0">PMO</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field1" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="pmpl"><label id="specialty_field_field_label_1" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field1">PM・PL</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field2" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="dx"><label id="specialty_field_field_label_2" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field2">DX</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field3" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="cloud"><label id="specialty_field_field_label_3" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field3">クラウド</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field4" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="modernization"><label id="specialty_field_field_label_4" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field4">モダナイゼション</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field5" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="safety_features"><label id="specialty_field_field_label_5" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field5">セキュリティ</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field6" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="it_infrastructure"><label id="specialty_field_field_label_6" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field6">ITインフラ</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field7" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="ai_dl_ml"><label id="specialty_field_field_label_7" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field7">AI・DL・ML</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field8" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="web_system"><label id="specialty_field_field_label_8" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field8">Webシステム</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field9" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="ios"><label id="specialty_field_field_label_9" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field9">IOS</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field10" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="android"><label id="specialty_field_field_label_10" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field10">Android</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field11" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="control"><label id="specialty_field_field_label_11" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field11">制御</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field12" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="embedded"><label id="specialty_field_field_label_12" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field12">組込</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field13" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="test"><label id="specialty_field_field_label_13" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field13">テスト</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field14" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="server"><label id="specialty_field_field_label_14" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field14">サーバー</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field15" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="database"><label id="specialty_field_field_label_15" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field15">データベース</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field16" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="network"><label id="specialty_field_field_label_16" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field16">ネットワーク</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field17" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="mainframe"><label id="specialty_field_field_label_17" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field17">メインフレーム</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field18" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="virtualization"><label id="specialty_field_field_label_18" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field18">仮想化</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field19" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="business_systems"><label id="specialty_field_field_label_19" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field19">業務システム</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field20" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="open"><label id="specialty_field_field_label_20" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field20">オープン</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_field_field21" class="custom-control-input" type="checkbox" name="company[specialty_field][]" v-model="specialty" value="help_desk"><label id="specialty_field_field_label_21" class="custom-control-label anavi-select-label mb-3" for="specialty_field_field21">ヘルプデスク</label></div>
                                        </div>
                                        <!--v-if-->
                                    </div>
                                </div>
                                <div class="row px-2 px-md-3 mb-4 mb-4">
                                    <div class="col-12">
                                        <label id="" class="font-middle pl-1 pl-md-0 mb-3">得意業界</label><!--v-if--><!--v-if--><!-- subTextIdは人財の「可能な契約形態」にて使用 --><!--v-if--><!--v-if-->
                                        <div class="selecting-form row px-3">
                                            <!-- チェックボックスが選択されていない場合、nilを送信する。（nilを送信しないと、チェックされたまま返ってきてしまうため。 --><input type="hidden" name="company[specialty_industry][]">
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field0" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="forestry_fisheries"><label id="specialty_industry_field_label_0" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field0">農林水産業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field1" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="mining"><label id="specialty_industry_field_label_1" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field1">鉱業、採石業、砂利採取業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field2" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="construction"><label id="specialty_industry_field_label_2" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field2">建設業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field3" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="manufacturing"><label id="specialty_industry_field_label_3" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field3">製造業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field4" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="utility"><label id="specialty_industry_field_label_4" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field4">電気・ガス・熱供給・水道業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field5" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="telecommunications"><label id="specialty_industry_field_label_5" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field5">情報通信業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field6" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="transportation"><label id="specialty_industry_field_label_6" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field6">運輸業、郵便業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field7" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="wholesale"><label id="specialty_industry_field_label_7" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field7">卸売業、小売業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field8" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="finance"><label id="specialty_industry_field_label_8" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field8">金融、保険業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field9" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="real_estate"><label id="specialty_industry_field_label_9" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field9">不動産業、物品賃貸業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field10" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="technology"><label id="specialty_industry_field_label_10" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field10">学術研究、専門・技術サービス業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field11" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="lodging"><label id="specialty_industry_field_label_11" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field11">宿泊業、飲食サービス業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field12" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="life_related"><label id="specialty_industry_field_label_12" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field12">生活関連サービス業、娯楽業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field13" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="education"><label id="specialty_industry_field_label_13" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field13">教育、学習支援業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field14" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="government_office"><label id="specialty_industry_field_label_14" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field14">官公庁</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field15" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="medical"><label id="specialty_industry_field_label_15" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field15">医療、福祉業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field16" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="complex_service"><label id="specialty_industry_field_label_16" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field16">複合サービス事業</label></div>
                                            <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4"><input id="specialty_industry_field17" class="custom-control-input" type="checkbox" name="company[specialty_industry][]" v-model="specialty_industry" value="service"><label id="specialty_industry_field_label_17" class="custom-control-label anavi-select-label mb-3" for="specialty_industry_field17">サービス業</label></div>
                                        </div>
                                        <!--v-if-->
                                    </div>
                                </div>
                                <div class="row pl-3 mb-3">
                                    <div class="col-12">
                                        <div class="mx-auto mb-1">
                                            <div class=""><label class="font-middle mb-3" for="">紹介文</label></div>
                                            <textarea rows="10" class="form-control" autocomplete="off" id="description_field" name="company[description]" v-model="description"></textarea>
                                        </div>
                                        <CharacterCounter targetId="description_field" :max-length="5000" />
                                    </div>

                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-12 col-md-4 mt-2"><button name="button" type="submit" class="btn btn-default btn-block btn-lg font-middle submit-btn waves-effect waves-light" form="edit_company">変更</button></div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css">
    <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css">
    <link rel="stylesheet" href="/custom_frontend/static/css/company/company_edit.css">
    `,
    data(){
        return{
            options_business_field:'',
            head_office_location:'',
            brand_location:[],
            specialty:[],
            specialty_industry:[],
            public_status:'',
            company_name: '',
            established_year: '',
            employee_count: '',
            charter_capital: '',
            company_site_url: '',
            has_dispatch_license: null,
            address: '',
            phone: '',
            description: '',
            company_id: userInfo ? userInfo.user_company_id : null,
            user_id: userInfo ? userInfo.user_id : null,
            errors: {},
            margin_bot:'mb-5',
            referral_code:'',
            isMobileMenuOpen: false
        }
    },
    methods:{
        validateForm() {
            this.errors = {};

            if (!this.company_name) {
              this.errors.company_name = '会社名は必須です。';
            }

            if (!this.phone) {
              this.errors.phone = '電話番号は必須です。';
            } else if (!/^0\d{9,10}$/.test(this.phone)) {
              this.errors.phone = '正しい電話番号を入力してください。';
            }

            // BỎ VALIDATION CHO 4 TRƯỜNG: 業種, 本社所在地（都道府県）, 本社所在地, 支店所在地（都道府県）
            // if (this.options_business_field === '選択してください。') {
            //   this.errors.options_business_field = '業種を選択してください。';
            // }

            // if (this.head_office_location === '選択してください。') {
            //   this.errors.head_office_location = '本社所在地を選択してください。';
            // }

            // if (!this.brand_location.length) {
            //   this.errors.brand_location = '拠点を選択してください。';
            //   this.margin_bot = '';
            // }

            if (!this.public_status) {
              this.errors.public_status = '公開状況を選択してください。';
            }

            if (!this.established_year) {
              this.errors.established_year = '設立年を入力してください。';
            } else if (!/^\d{4}$/.test(this.established_year)) {
              this.errors.established_year = '正しい年（例: 2020）を入力してください。';
            }

            if (!this.employee_count) {
              this.errors.employee_count = '従業員数を入力してください。';
            }

            if (!this.charter_capital) {
              this.errors.charter_capital = '資本金を入力してください。';
            }

            if (this.company_site_url && !/^https?:\/\/.+/.test(this.company_site_url)) {
              this.errors.company_site_url = '有効なURLを入力してください（例：https://...）。';
            }

            if (this.has_dispatch_license === null || this.has_dispatch_license === '') {
              this.errors.has_dispatch_license = '派遣許可の有無を選択してください。';
            }

            // BỎ VALIDATION CHO 本社所在地 (address)
            // if (!this.address) {
            //   this.errors.address = '住所を入力してください。';
            // }

            return Object.keys(this.errors).length === 0;
        },
        select_options_business(event) {
            // Lưu giá trị từ dropdown (đây là giá trị tiếng Anh)
            this.options_business_field = event.detail;
            console.log("Selected options_business_field:", this.options_business_field);
        },
        select_head_office_location(event) {
            this.head_office_location = event.detail;
        },
        select_public_status(event) {
            this.public_status = event.detail;
        },
        async getUserInfo() {
            // Kiểm tra xem user_id có tồn tại không
            if (!this.user_id) {
                console.error('User ID not found in localStorage');
                return;
            }

            try {
                const response = await fetch('/api/get_profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: this.user_id,
                    }),
                });

                const data = await response.json();
                if (data.result.success) {
                    // Lấy số điện thoại từ người dùng
                    this.user_phone = data.result.phone || '';
                    this.phone = this.user_phone;
                } else {
                    console.error('Error fetching user profile:', data.result.message);
                }
            } catch (error) {
                console.error('Error fetching user profile:', error);
            }
        },
        async getCompany() {
            try {
                const response = await fetch('/api/get_company', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: this.company_id,
                    }),
                });

                const com = await response.json();
                if (com.result.success) {
                    this.referral_code = com.result.data.referral_code ? com.result.data.referral_code : "";
                    this.established_year = com.result.data.established_year ? com.result.data.established_year : "";
                    this.employee_count = com.result.data.employee_count ? com.result.data.employee_count : "";
                    this.charter_capital = com.result.data.charter_capital ? com.result.data.charter_capital : "";
                    this.company_site_url = com.result.data.company_site_url ? (com.result.data.company_site_url.startsWith("http://") || com.result.data.company_site_url.startsWith("https://")
                        ? com.result.data.company_site_url
                        : ("https://" + com.result.data.company_site_url)) : "";
                    this.has_dispatch_license = com.result.data.has_dispatch_license !== undefined ? com.result.data.has_dispatch_license : null;
                    this.company_name = com.result.data.name ? com.result.data.name : "";
                    this.options_business_field = com.result.data.options_bussiness ? com.result.data.options_bussiness : "";
                    this.specialty_industry = com.result.data.specialty_industry ? com.result.data.specialty_industry.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                    this.specialty = com.result.data.industry ? com.result.data.industry.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                    this.address = com.result.data.addresses ? com.result.data.addresses : "";
                    // Không lấy số điện thoại từ công ty nữa, sẽ lấy từ người dùng
                    this.brand_location = com.result.data.brand_location ? com.result.data.brand_location.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                    this.head_office_location = com.result.data.head_office_location ? com.result.data.head_office_location : "";
                    this.public_status = com.result.data.public_status ? com.result.data.public_status : "";
                    this.description = com.result.data.description ? com.result.data.description : "";
                } else {
                    console.log(com.result.message);
                }

                // Sau khi lấy thông tin công ty, lấy thông tin người dùng
                await this.getUserInfo();
            } catch (error) {
                console.error('Có lỗi xảy ra khi lấy dữ liệu:', error);
            }
        },
        async updateCompany() {
            if (!this.validateForm()) {
                return;
            }
            try {
                // Log thông tin trước khi gửi
                console.log("Updating company with data:", {
                    user_id: this.user_id,
                    phone: this.phone,
                    options_business_field: this.options_business_field
                });

                const response = await fetch('/api/company_edit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: this.company_id,
                        user_id: this.user_id,
                        name: this.company_name,
                        established_year: this.established_year,
                        options_bussiness: this.options_business_field, // Giá trị đã là tiếng Anh
                        employee_count: this.employee_count,
                        charter_capital: this.charter_capital,
                        company_site_url: this.company_site_url,
                        has_dispatch_license: this.has_dispatch_license,
                        addresses: this.address,
                        phone_number: this.phone,
                        description: this.description,
                        brand_location: this.brand_location.join(','),
                        head_office_location: this.head_office_location,
                        public_status: this.public_status,
                        specialty_industry: this.specialty_industry.join(','),
                        specialty: this.specialty.join(','),
                        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                    }),
                });

                const result = await response.json();
                if (result.result.success) {
                    // Hiển thị toast message trực tiếp thay vì reload trang
                    if (window.toastr) {
                        window.toastr.success("会社データが正常に更新されました。", "", {
                            showDuration: 300,
                            hideDuration: 2000,
                            extendedTimeOut: 0,
                            positionClass: 'toast-top-full-width',
                            showEasing: 'swing',
                            hideEasing: 'linear',
                            progressBar: true,
                            closeButton: true,
                            closeHtml: '<button>&times;</button>',
                            preventDuplicates: true,
                            toastClass: 'toast-success custom-toastr'
                        });
                    } else {
                        // Fallback nếu toastr chưa được load
                        sessionStorage.setItem('toastrMessage', "会社データが正常に更新されました。");
                        sessionStorage.setItem('toastrType', "success");
                        window.location.reload();
                    }
                } else {
                    if (window.toastr) {
                        window.toastr.error(result.result.message);
                    } else {
                        sessionStorage.setItem('toastrMessage', result.result.message);
                        sessionStorage.setItem('toastrType', "error");
                    }
                }
            } catch (error) {
                console.error('Có lỗi xảy ra khi cập nhật dữ liệu:', error);
            }
        },
        restoreSelection1() {
            // Lấy giá trị tiếng Anh từ this.options_business_field
            const options_businessEN = this.options_business_field;

            // Log để debug
            console.log("Restoring options_business_field (before):", {
                value: options_businessEN
            });

            // Nếu giá trị tiếng Anh không rỗng, tìm giá trị tiếng Nhật tương ứng để hiển thị
            let displayValue = "選択してください。"; // Giá trị mặc định

            if (options_businessEN) {
                // Tìm key (tiếng Nhật) tương ứng với value (tiếng Anh)
                const japaneseValue = Object.keys(optionsMappings.options_business_field).find(
                    key => optionsMappings.options_business_field[key] === options_businessEN
                );

                if (japaneseValue) {
                    displayValue = japaneseValue;
                }
            }

            // Log để debug
            console.log("Restoring options_business_field (after):", {
                original: options_businessEN,
                displayValue: displayValue
            });

            // Gửi sự kiện để cập nhật dropdown (hiển thị giá trị tiếng Nhật)
            document.dispatchEvent(new CustomEvent("restoreDropdown", {
                detail: {
                    key: "options_business_field",
                    value: displayValue
                }
            }));
        },
        restoreSelection2() {
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "workplaces", value: this.head_office_location } }));
        },
        restoreSelection3() {
            const public_statusEN = this.public_status;
            this.public_status = Object.keys(optionsMappings.public_status_field).find(
                key => optionsMappings.public_status_field[key] === public_statusEN
            ) || "登録会社一覧へ掲載する";
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "public_status_field", value: this.public_status } }));
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function() {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function() {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function() {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function() {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        }
    },
    async mounted() {
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        const toastrCSS = document.createElement("link");
        toastrCSS.rel = "stylesheet";
        toastrCSS.href = "/custom_frontend/static/css/Toastr.css";
        toastrCSS.onload = function() {
            console.log("Toastr CSS loaded successfully!");
        };
        document.head.appendChild(toastrCSS);
        checkToastrMessage()
        await this.getCompany();
        this.restoreSelection1();
        this.restoreSelection2();
        this.restoreSelection3();

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });
    },
}
export default company_edit