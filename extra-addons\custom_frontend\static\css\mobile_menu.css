/* CSS cho menu di động */
.mobile-menu ul li a {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.mobile-menu ul li a.active {
    background-color: #1072e9;
    color: #fff !important;
}

.mobile-menu ul li a.active i,
.mobile-menu ul li a.active span {
    color: #fff !important;
}

.mobile-menu ul li a:hover {
    background-color: #1072e9;
    color: #fff !important;
}

.mobile-menu ul li a:hover i,
.mobile-menu ul li a:hover span {
    color: #fff !important;
}

.mobile-menu ul li a:active {
    background-color: #1072e9;
    color: #fff !important;
}

.mobile-menu ul li a:active i,
.mobile-menu ul li a:active span {
    color: #fff !important;
}

/* Thêm CSS cho mobile menu content */
.mobile-menu-content a.active {
    background-color: #1072e9;
    color: #fff !important;
}

.mobile-menu-content a:hover {
    background-color: #1072e9;
    color: #fff !important;
}
