const WorkplaceSelector = {
  props: {
    modelValue: {
      type: Array,
      default: [],
    },
    requiredLabel: {
      type: <PERSON>olean,
      default: true,
    },
    bold:{
      type: String,
      default: 'ex-bold',
    },
    col_12:{
      type: String,
      default: 'col-12',
    },
    mb_5:{
      type: String,
      default: 'mb-5',
    },
    lable: {
      type: String,
      default: '就業場所'
    },
    title: {
      type: String,
      default: '就業場所を選択'
    },
    inputName: {
      type: String,
      default: "opportunity[specifies_workplaces][]", // Mặc định là giá trị cũ
    }
  },
  template: `
    <div class="mb-2" :class="col_12" id="work_place">
      <div class="mx-auto 3yBFr76" :class="mb_5">
        <div class="row ml-0 mr-0">
          <label v-if="lable" class="font-middle mb-3 no-label-bar custom-label" :class="bold" for="">
            {{ lable }}
            <span v-if="requiredLabel" class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
          </label>
          <button class="mdb-modal-form btn btn-outline-default btn-sm mb-3 mt-0 ml-3 waves-effect waves-light"
                  type="button" data-toggle="modal"
                  data-target="#specifies_workplaces_base">
            選択
          </button>
          <p class="pref-select p-0 m-0 w-100 notranslate" id="check_item">
            {{ displaySelectedPrefectures }}
          </p>
          <input v-if="selectedPrefectures.length > 0" id="specifies_workplaces" type="hidden"
                 :value="selectedPrefectures" :name="inputName">
          <div class="modal" id="specifies_workplaces_base" tabindex="-1"
               aria-labelledby="specifies_workplaces_modal" style="display: none;"
               aria-hidden="true">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <div class="modal-header">
                  <h4 v-if="title" class="modal-title w-100">{{title}}</h4>
                  <button class="close btn-modal-close" aria-label="Close"
                          type="button" data-dismiss="modal">
                    <i class="material-icons md-dark mb-36" aria-hidden="true">clear</i>
                  </button>
                </div>
                <div class="modal-body">
                  <div class="row multiple-prefecture-select">
                    <div v-for="(prefecture, index) in workplaces" :key="index"
                         class="col-4 col-sm-2 p-1">
                      <button type="button" class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light notranslate"
                              :class="{ 'selected-btn': selectedPrefectures.includes(prefecture) }"
                              @click="toggleSelection(prefecture); $event.target.blur();">
                        {{ prefecture }}
                      </button>
                    </div>
                  </div>
                  <div class="row pt-5">
                    <button class="btn btn-blue-grey mx-auto btn-modal-close waves-effect waves-light"
                            aria-label="Close" data-dismiss="modal">
                      閉じる
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      // Danh sách nơi làm việc cố định bằng tiếng Nhật
      workplaces: ["北海道", "青森県", "岩手県", "宮城県", "秋田県", "山形県", "福島県",
      "茨城県", "栃木県", "群馬県", "埼玉県", "千葉県", "東京都", "神奈川県",
      "新潟県", "富山県", "石川県", "福井県", "山梨県", "長野県", "岐阜県",
      "静岡県", "愛知県", "三重県", "滋賀県", "京都府", "大阪府", "兵庫県",
      "奈良県", "和歌山県", "鳥取県", "島根県", "岡山県", "広島県", "山口県",
      "徳島県", "香川県", "愛媛県", "高知県", "福岡県", "佐賀県", "長崎県",
      "熊本県", "大分県", "宮崎県", "鹿児島県", "沖縄県", "海外"],

      // Ánh xạ từ tiếng Anh sang tiếng Nhật
      workplaceMap: {
        "Hokkaido": "北海道",
        "Aomori": "青森県",
        "Iwate": "岩手県",
        "Miyagi": "宮城県",
        "Akita": "秋田県",
        "Yamagata": "山形県",
        "Fukushima": "福島県",
        "Ibaraki": "茨城県",
        "Tochigi": "栃木県",
        "Gunma": "群馬県",
        "Saitama": "埼玉県",
        "Chiba": "千葉県",
        "Tokyo": "東京都",
        "Kanagawa": "神奈川県",
        "Niigata": "新潟県",
        "Toyama": "富山県",
        "Ishikawa": "石川県",
        "Fukui": "福井県",
        "Yamanashi": "山梨県",
        "Nagano": "長野県",
        "Gifu": "岐阜県",
        "Shizuoka": "静岡県",
        "Aichi": "愛知県",
        "Mie": "三重県",
        "Shiga": "滋賀県",
        "Kyoto": "京都府",
        "Osaka": "大阪府",
        "Hyogo": "兵庫県",
        "Nara": "奈良県",
        "Wakayama": "和歌山県",
        "Tottori": "鳥取県",
        "Shimane": "島根県",
        "Okayama": "岡山県",
        "Hiroshima": "広島県",
        "Yamaguchi": "山口県",
        "Tokushima": "徳島県",
        "Kagawa": "香川県",
        "Ehime": "愛媛県",
        "Kochi": "高知県",
        "Fukuoka": "福岡県",
        "Saga": "佐賀県",
        "Nagasaki": "長崎県",
        "Kumamoto": "熊本県",
        "Oita": "大分県",
        "Miyazaki": "宮崎県",
        "Kagoshima": "鹿児島県",
        "Okinawa": "沖縄県",
        "Overseas": "海外"
      }
    };
  },
  mounted() {
    // Khi component được tạo, chuẩn hóa dữ liệu đầu vào
    this.normalizeInputData();
    this.addCustomLabelStyle();
  },
  methods: {
    // Chuẩn hóa dữ liệu đầu vào để đảm bảo luôn là tiếng Nhật
    normalizeInputData() {
      if (this.modelValue && this.modelValue.length > 0) {
        const normalizedValues = this.modelValue.map(value => this.normalizeWorkplace(value));
        if (JSON.stringify(normalizedValues) !== JSON.stringify(this.modelValue)) {
          this.$emit('update:modelValue', normalizedValues);
        }
      }
    },

    // Chuẩn hóa một giá trị workplace từ tiếng Anh sang tiếng Nhật nếu cần
    normalizeWorkplace(workplace) {
      // Nếu đã là tiếng Nhật, giữ nguyên
      if (this.isJapanese(workplace)) {
        return workplace;
      }

      // Nếu là tiếng Anh, chuyển đổi sang tiếng Nhật
      return this.workplaceMap[workplace] || workplace;
    },

    // Kiểm tra xem một chuỗi có phải là tiếng Nhật không
    isJapanese(text) {
      return text && (
        text.includes('県') ||
        text.includes('都') ||
        text.includes('府') ||
        text.includes('北海道') ||
        text.includes('海外')
      );
    },

    toggleSelection(prefecture) {
      const index = this.selectedPrefectures.indexOf(prefecture);
      if (index === -1) {
        this.selectedPrefectures.push(prefecture);
      } else {
        this.selectedPrefectures.splice(index, 1);
      }

      this.$nextTick(() => {
        const buttons = document.querySelectorAll(".pref");
        buttons.forEach((btn) => {
          btn.style.pointerEvents = "none";
        });

        setTimeout(() => {
          buttons.forEach((btn) => {
            btn.style.pointerEvents = "auto";
          });
        }, 50);
      });
    },

    addCustomLabelStyle() {
      const style = document.createElement('style');
      style.textContent = `
        .custom-label {
          position: relative;
        }
        .custom-label::before {
          content: "";
          background-color: #455965;
          display: block;
          height: 75%;
          width: .428rem;
          position: absolute;
          left: -1.285rem;
        }
      `;
      document.head.appendChild(style);
    }
  },
  computed: {
    selectedPrefectures: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue); // Cập nhật modelValue
      }
    },

    // Hiển thị các giá trị đã chọn
    displaySelectedPrefectures() {
      return this.selectedPrefectures.length ? this.selectedPrefectures.join('/ ') : '選択されていません';
    }
  },
};

export default WorkplaceSelector;
