/**
 * ZEUS Payment Integration
 *
 * File này chứa các hàm cần thiết để tích hợp với hệ thống thanh toán ZEUS
 */

/**
 * Hiển thị thông báo lỗi inline
 * @param {string} fieldId - ID của trường input
 * @param {string} message - Thông báo lỗi
 */
function showError(fieldId, message) {
    // Xóa thông báo lỗi cũ nếu có
    removeError(fieldId);

    const field = document.getElementById(fieldId);
    if (!field) return;

    // Tạo phần tử hiển thị lỗi
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = message;
    errorDiv.style.color = '#F44336';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.marginTop = '5px';
    errorDiv.style.marginBottom = '10px';

    // Thêm phần tử lỗi vào sau trường input
    field.parentNode.appendChild(errorDiv);

    // Thêm viền đỏ cho trường input
    field.style.borderColor = '#F44336';
}

/**
 * Xóa thông báo lỗi
 * @param {string} fieldId - ID của trường input
 */
function removeError(fieldId) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    // Xóa thông báo lỗi nếu có
    const errorDiv = field.parentNode.querySelector('.error-message');
    if (errorDiv) {
        errorDiv.remove();
    }

    // Khôi phục viền cho trường input
    field.style.borderColor = '';
}

/**
 * Xóa tất cả thông báo lỗi
 */
function removeAllErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    errorMessages.forEach(error => error.remove());

    const fields = document.querySelectorAll('.form-control');
    fields.forEach(field => {
        field.style.borderColor = '';
    });
}

/**
 * Hàm tạo token thanh toán từ ZEUS
 * Được gọi khi người dùng nhấn nút thanh toán
 * @param {HTMLElement} button - Nút thanh toán được nhấn
 */
function generateToken(button) {
    // Xóa tất cả thông báo lỗi
    removeAllErrors();

    // Vô hiệu hóa nút để tránh nhấn nhiều lần
    button.disabled = true;
    button.innerHTML = '処理中...'; // "Đang xử lý..."

    // Lấy thông tin từ form
    const cardNumber = document.getElementById('credit_card_number_field').value;
    const cardHolderName = document.getElementById('card_holder_name_field').value;
    const expMonth = document.getElementById('credit_card_expire_month_field').value;
    const expYear = document.getElementById('credit_card_expire_year_field').value;
    const securityCode = document.getElementById('security_code_field').value;
    const selectedPlan = document.querySelector('input[name="credit_payment[plan]"]').value;

    // Biến kiểm tra lỗi
    let hasError = false;

    // Kiểm tra plan đã được chọn chưa
    if (selectedPlan === '未選択') {
        // Hiển thị lỗi ở dropdown plan
        const planField = document.querySelector('input[name="credit_payment[plan]"]');
        if (planField) {
            const planContainer = planField.closest('div');
            if (planContainer) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.innerHTML = 'プランを選択してください。'; // "Vui lòng chọn gói"
                errorDiv.style.color = '#F44336';
                errorDiv.style.fontSize = '12px';
                errorDiv.style.marginTop = '5px';
                errorDiv.style.marginBottom = '10px';
                planContainer.appendChild(errorDiv);
            }
        }
        hasError = true;
    }

    // Kiểm tra dữ liệu đầu vào
    if (!cardNumber) {
        showError('credit_card_number_field', '必須項目です。'); // "Trường bắt buộc"
        hasError = true;
    }

    if (!cardHolderName) {
        showError('card_holder_name_field', '必須項目です。'); // "Trường bắt buộc"
        hasError = true;
    }

    if (!expMonth) {
        showError('credit_card_expire_month_field', '必須項目です。'); // "Trường bắt buộc"
        hasError = true;
    }

    if (!expYear) {
        showError('credit_card_expire_year_field', '必須項目です。'); // "Trường bắt buộc"
        hasError = true;
    }

    if (!securityCode) {
        showError('security_code_field', '必須項目です。'); // "Trường bắt buộc"
        hasError = true;
    }

    // Kiểm tra định dạng
    if (cardNumber && !/^\d{13,16}$/.test(cardNumber.replace(/\s/g, ''))) {
        showError('credit_card_number_field', '有効なカード番号を入力してください。'); // "Vui lòng nhập số thẻ hợp lệ"
        hasError = true;
    }

    if (expMonth && (!/^\d{1,2}$/.test(expMonth) || parseInt(expMonth) < 1 || parseInt(expMonth) > 12)) {
        showError('credit_card_expire_month_field', '有効な有効期限（月）を入力してください。'); // "Vui lòng nhập tháng hết hạn hợp lệ"
        hasError = true;
    }

    if (expYear && !/^\d{2}$/.test(expYear)) {
        showError('credit_card_expire_year_field', '有効な有効期限（年）を入力してください。'); // "Vui lòng nhập năm hết hạn hợp lệ"
        hasError = true;
    }

    if (securityCode && !/^\d{3,4}$/.test(securityCode)) {
        showError('security_code_field', '有効なセキュリティコードを入力してください。'); // "Vui lòng nhập mã bảo mật hợp lệ"
        hasError = true;
    }

    // Nếu có lỗi, dừng xử lý
    if (hasError) {
        button.disabled = false;
        button.innerHTML = '申し込み'; // Khôi phục nút
        return;
    }

    // Cấu hình ZEUS
    const zeusTokenRequestPayload = {
        merchant_id: document.getElementById('credit_payment_merchant_id').value,
        service_id: document.getElementById('credit_payment_service_id').value,
        card_number: cardNumber.replace(/\s/g, ''), // Loại bỏ khoảng trắng
        card_holder_name: cardHolderName,
        card_exp_month: expMonth,
        card_exp_year: expYear,
        security_code: securityCode
    };

    // Kiểm tra xem Zeustap đã được định nghĩa chưa
    if (typeof Zeustap === 'undefined') {
        console.error('ZEUS API chưa được tải. Vui lòng kiểm tra kết nối mạng và thử lại.');

        // Hiển thị thông báo lỗi ở đầu form
        const form = document.getElementById('credit_payments_regist_cc_form');
        if (form) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message system-error';
            errorDiv.innerHTML = '決済システムの読み込みに失敗しました。ネットワーク接続を確認して、再度お試しください。';
            errorDiv.style.color = '#F44336';
            errorDiv.style.fontSize = '14px';
            errorDiv.style.padding = '10px';
            errorDiv.style.marginBottom = '20px';
            errorDiv.style.backgroundColor = '#FFEBEE';
            errorDiv.style.borderRadius = '4px';
            errorDiv.style.textAlign = 'center';

            form.insertBefore(errorDiv, form.firstChild);
        }

        button.disabled = false;
        button.innerHTML = '申し込み';
        return;
    }

    // Gọi API của ZEUS để tạo token
    Zeustap.getToken(zeusTokenRequestPayload, function(response) {
        if (response.status === 'success') {
            console.log('Token được tạo thành công');

            // Lưu token vào form
            document.getElementById('credit_payment_token').value = response.token;
            document.getElementById('credit_payment_token_key').value = response.token_key;
            document.getElementById('credit_payment_masked_cc_number').value = response.masked_card_number;
            document.getElementById('credit_payment_card_brand_code').value = response.card_brand;
            document.getElementById('credit_payment_cc_expiration').value = expMonth + '/' + expYear;

            // Gửi form
            document.getElementById('credit_payments_regist_cc_form').submit();
        } else {
            // Xử lý lỗi
            console.error('Lỗi khi tạo token:', response.error_message);

            // Hiển thị thông báo lỗi ở đầu form
            const form = document.getElementById('credit_payments_regist_cc_form');
            if (form) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message system-error';
                errorDiv.innerHTML = 'カード情報の処理中にエラーが発生しました: ' + response.error_message;
                errorDiv.style.color = '#F44336';
                errorDiv.style.fontSize = '14px';
                errorDiv.style.padding = '10px';
                errorDiv.style.marginBottom = '20px';
                errorDiv.style.backgroundColor = '#FFEBEE';
                errorDiv.style.borderRadius = '4px';
                errorDiv.style.textAlign = 'center';

                // Xóa thông báo lỗi cũ nếu có
                const oldError = form.querySelector('.system-error');
                if (oldError) {
                    oldError.remove();
                }

                form.insertBefore(errorDiv, form.firstChild);
            }

            button.disabled = false;
            button.innerHTML = '申し込み';
        }
    });
}

/**
 * Hàm định dạng số thẻ tín dụng khi người dùng nhập
 * Thêm khoảng trắng sau mỗi 4 chữ số
 */
function formatCreditCardNumber() {
    const input = document.getElementById('credit_card_number_field');
    if (!input) return;

    input.addEventListener('input', function() {
        // Loại bỏ tất cả ký tự không phải số
        let value = this.value.replace(/\D/g, '');

        // Thêm khoảng trắng sau mỗi 4 chữ số
        if (value.length > 0) {
            value = value.match(new RegExp('.{1,4}', 'g')).join(' ');
        }

        // Cập nhật giá trị
        this.value = value;

        // Xóa thông báo lỗi khi người dùng bắt đầu nhập lại
        removeError('credit_card_number_field');
    });
}

/**
 * Thêm sự kiện để ẩn thông báo lỗi khi người dùng bắt đầu nhập lại
 */
function setupErrorClearingEvents() {
    // Danh sách các trường input
    const fields = [
        'credit_card_number_field',
        'card_holder_name_field',
        'credit_card_expire_month_field',
        'credit_card_expire_year_field',
        'security_code_field'
    ];

    // Thêm sự kiện cho từng trường
    fields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                removeError(fieldId);
            });

            field.addEventListener('focus', function() {
                removeError(fieldId);
            });
        }
    });

    // Thêm sự kiện cho dropdown plan
    const planField = document.querySelector('input[name="credit_payment[plan]"]');
    if (planField) {
        // Sử dụng MutationObserver để theo dõi thay đổi giá trị
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    // Xóa thông báo lỗi khi giá trị thay đổi
                    const errorDiv = planField.parentNode.querySelector('.error-message');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                }
            });
        });

        observer.observe(planField, { attributes: true });
    }
}

/**
 * Khởi tạo các sự kiện khi trang được tải
 */
document.addEventListener('DOMContentLoaded', function() {
    // Thêm script ZEUS
    const zeusScript = document.createElement('script');
    zeusScript.src = 'https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token.js';
    zeusScript.async = true;
    document.head.appendChild(zeusScript);

    // Định dạng số thẻ tín dụng
    formatCreditCardNumber();

    // Giới hạn đầu vào cho các trường
    const expMonthField = document.getElementById('credit_card_expire_month_field');
    const expYearField = document.getElementById('credit_card_expire_year_field');
    const securityCodeField = document.getElementById('security_code_field');

    // Chỉ cho phép nhập số
    if (expMonthField && expYearField && securityCodeField) {
        [expMonthField, expYearField, securityCodeField].forEach(field => {
            field.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '');
            });
        });
    }

    // Thiết lập sự kiện để ẩn thông báo lỗi
    setupErrorClearingEvents();

    // Thêm CSS cho thông báo lỗi
    const style = document.createElement('style');
    style.textContent = `
        .error-message {
            color: #F44336;
            font-size: 12px;
            margin-top: 5px;
            margin-bottom: 10px;
        }

        .system-error {
            color: #F44336;
            font-size: 14px;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #FFEBEE;
            border-radius: 4px;
            text-align: center;
        }

        .form-control.error {
            border-color: #F44336;
        }
    `;
    document.head.appendChild(style);
});
