@startuml

entity "categories" as categories {
  id: int <<PK>>
  name: varchar
  parent_id: int
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "plans" as plans {
  id: int <<PK>>
  title: varchar
  description: text
  ticket_count: int
  duration_months: int
  initial_fee: int
  total_cost: int
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "res_users" as res_users {
  id: int <<PK>>
  username: varchar
  email: varchar
  password: varchar
  old_password: varchar
  role: varchar
  plan_id: int
  company_id: varchar
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "res_partner" as res_partner {
  id: int <<PK>>
  user_id: int
  cv_id: int
  project_id: int
  resume_visibility: varchar
  public_companies_setting: varchar
  public_projects_setting: varchar
  gender: varchar
  birthday: timestamp
  contract_type: varchar
  work_type: varchar
  work_region: varchar
  monthly_salary_min: int
  monthly_salary_max: int
  work_schedule: varchar
  skills_and_preferences: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "curriculum_vitae" as curriculum_vitae {
  cv_id: int <<PK>>
  cv_contents: text
  cv_stored_path: varchar
  avatar_image_url: varchar
  refer_url: varchar
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "resumes_score" as resumes_score {
  id: int <<PK>>
  user_id: int
  resumes_id: int
  criteria1: varchar
  criteria2: varchar
  criteria3: varchar
  criteria4: varchar
  criteria5: varchar
  final_score: int
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "expertise_fields" as expertise_fields {
  id: int <<PK>>
  category: varchar
  subcategory: varchar
  experience: float
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "resume_expertise" as resume_expertise {
  id: int <<PK>>
  resume_id: varchar
  expertise_field_id: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "skills_and_preferences" as skills_and_preferences {
  id: int <<PK>>
  resume_id: int
  skill_type: varchar
  preference_details: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "desired_work_conditions" as desired_work_conditions {
  id: int <<PK>>
  resume_id: int
  work_type: varchar
  work_region: varchar
  monthly_salary_min: int
  monthly_salary_max: int
  work_schedule: varchar
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "comments" as comments {
  id: int <<PK>>
  resume_id: int
  comment: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "projects" as projects {
  id: int <<PK>>
  title: varchar
  work_ratio: varchar
  office_frequency: varchar
  work_location: varchar
  contract_duration: varchar
  project_category: varchar
  project_content: text
  project_requirements: text
  detailed_info: text
  content_confirmation_status: varchar
  contract_form: varchar
  commercial_involvement_level: varchar
  commercial_flow: text
  application_deadline: datetime
  recruitment_count: int
  recruitment_target: text
  project_features: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "application_settings" as application_settings {
  id: int <<PK>>
  project_id: int
  contract_type: varchar
  work_ratio: varchar
  salary_min: int
  salary_max: int
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "project_expertise" as project_expertise {
  id: int <<PK>>
  project_id: varchar
  expertise_field_id: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "companies" as companies {
  id: int <<PK>>
  name: varchar
  addresses: text
  phone_number: varchar
  email: varchar
  representative: varchar
  charter_capital: bigint
  employee_count: int
  business_content: text
  established_year: int
  has_dispatch_license: boolean
  response_rate: float
  interview_count: int
  successful_contracts: int
  high_ratings_count: int
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "notification" as notification {
  id: int <<PK>>
  user_id: int
  message: text
  is_read: boolean
  read_at: timestamp
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "invouce_tracking" as invouce_tracking {
  id: int <<PK>>
  issue_date: timestamp
  invoice_number: varchar
  amount: decimal(15,2)
  payment_status: varchar
  paid_amount: decimal(15,2)
  payment_date: timestamp
  notes: text
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}

entity "cc_infor" as cc_infor {
  id: int <<PK>>
  cc_number: varchar
  cc_name: varchar
  cc_expired: timestamp
  cc_code: int
  created_at: timestamp
  created_by: varchar
  updated_at: timestamp
  updated_by: varchar
}


entity "cart" as cart {
  id: int <<PK>>
  user_id: int
  created_at: timestamp
  updated_at: timestamp
  updated_at timestamp
  updated_by varchar
}

entity "cart_items" as cart_items {
  id: int <<PK>>
  cart_id: int
  product_id: int
  quantity: int
  price: decimal
  created_at: timestamp
  updated_at: timestamp
}

' Define relationships (refs)
res_partner --> res_users : user_id
resume_expertise --> res_partner : resume_id
resume_expertise --> expertise_fields : expertise_field_id
skills_and_preferences --> res_partner : resume_id
desired_work_conditions --> res_partner : resume_id
comments --> res_partner : resume_id
application_settings --> projects : project_id
project_expertise --> projects : project_id
project_expertise --> expertise_fields : expertise_field_id
res_users --> companies : company_id
notification --> res_users : user_id
resumes_score --> res_users : user_id
resumes_score --> res_partner : resumes_id
cart --> res_users : user_id
cart_items --> cart: cart_id
cart_items --> plans: plan_id

@enduml