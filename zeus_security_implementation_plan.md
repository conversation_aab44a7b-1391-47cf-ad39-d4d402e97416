# K<PERSON> hoạch Triển k<PERSON> mật ZEUS - Must Have
## <PERSON><PERSON>nh cho Production Server (mi52.jp)

### 🎯 Mục tiêu: Triển khai các yêu cầu BẮT BUỘC của ZEUS mà KHÔNG ảnh hưởng logic hiện tại

---

## 1. 🔥 KHẨN CẤP: ClamAV Antivirus (BẮT BUỘC)

### Bước 1: Cài đặt ClamAV trên Server
```bash
# SSH vào server production
ssh <EMAIL>

# Cài đặt ClamAV
sudo apt update
sudo apt install clamav clamav-daemon clamav-freshclam

# Khởi động services
sudo systemctl enable clamav-freshclam
sudo systemctl start clamav-freshclam
sudo systemctl enable clamav-daemon
sudo systemctl start clamav-daemon

# Cập nhật virus definitions
sudo freshclam
```

### Bước 2: Tạo Python Module cho ClamAV Integration
**File: `extra-addons/custom_frontend/utils/antivirus.py`**
```python
import subprocess
import logging
import os
import tempfile

_logger = logging.getLogger(__name__)

class AntivirusScanner:
    @staticmethod
    def scan_file(file_content, filename):
        """
        Scan file content using ClamAV
        Returns: (is_clean: bool, scan_result: str)
        """
        try:
            # Tạo file tạm để scan
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}") as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            # Chạy ClamAV scan
            result = subprocess.run(
                ['clamscan', '--no-summary', temp_file_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            # Xóa file tạm
            os.unlink(temp_file_path)
            
            # Kiểm tra kết quả
            if result.returncode == 0:
                _logger.info(f"File {filename} is clean")
                return True, "Clean"
            elif result.returncode == 1:
                _logger.warning(f"Virus detected in {filename}: {result.stdout}")
                return False, f"Virus detected: {result.stdout}"
            else:
                _logger.error(f"ClamAV scan error for {filename}: {result.stderr}")
                return False, f"Scan error: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            _logger.error(f"ClamAV scan timeout for {filename}")
            return False, "Scan timeout"
        except Exception as e:
            _logger.error(f"ClamAV scan exception for {filename}: {str(e)}")
            return False, f"Scan exception: {str(e)}"
    
    @staticmethod
    def is_allowed_file_type(filename):
        """Kiểm tra file type được phép"""
        allowed_extensions = ['.pdf', '.doc', '.docx', '.txt']
        return any(filename.lower().endswith(ext) for ext in allowed_extensions)
    
    @staticmethod
    def is_allowed_file_size(file_size, max_size_mb=10):
        """Kiểm tra file size (default max 10MB)"""
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
```

### Bước 3: Cập nhật File Upload Controller (KHÔNG ảnh hưởng logic cũ)
**File: `extra-addons/custom_frontend/controllers/resumes_upload_file.py`**

Thêm import và sử dụng antivirus scanner:
```python
# Thêm vào đầu file
from ..utils.antivirus import AntivirusScanner

# Cập nhật method upload_cv (chỉ thêm validation, không thay đổi logic cũ)
@http.route('/api/resume/upload_cv', type='http', auth='public', methods=['POST'], csrf=False)
def upload_cv(self, **kwargs):
    file = request.httprequest.files.get('file')
    resume_id = kwargs.get('resume_id')

    if not file or not resume_id:
        return json.dumps({"success": False, "error": "Required fields or id are missing."})

    # ✅ THÊM MỚI: Antivirus scanning (không ảnh hưởng logic cũ)
    try:
        # Kiểm tra file type
        if not AntivirusScanner.is_allowed_file_type(file.filename):
            _logger.warning(f"Rejected file type: {file.filename}")
            return json.dumps({
                "success": False, 
                "error": "File type not allowed. Only PDF, DOC, DOCX files are permitted."
            })
        
        # Kiểm tra file size
        file_content = file.read()
        if not AntivirusScanner.is_allowed_file_size(len(file_content)):
            _logger.warning(f"File too large: {file.filename}, size: {len(file_content)} bytes")
            return json.dumps({
                "success": False, 
                "error": "File size too large. Maximum 10MB allowed."
            })
        
        # Scan virus
        is_clean, scan_result = AntivirusScanner.scan_file(file_content, file.filename)
        if not is_clean:
            _logger.error(f"Virus detected in uploaded file: {file.filename}, result: {scan_result}")
            return json.dumps({
                "success": False, 
                "error": "File rejected due to security scan failure."
            })
        
        _logger.info(f"File passed security scan: {file.filename}")
        
        # Reset file pointer để logic cũ hoạt động bình thường
        file.seek(0)
        
    except Exception as e:
        _logger.error(f"Security scan error: {str(e)}")
        return json.dumps({
            "success": False, 
            "error": "File security scan failed."
        })
    
    # ✅ LOGIC CŨ KHÔNG THAY ĐỔI - tiếp tục từ đây
    try:
        # Encode file content to base64
        file_content_b64 = base64.b64encode(file_content).decode('utf-8')
        
        # Create CV record
        cv = request.env['vit.curriculum_vitae'].sudo().create({
            'name': file.filename,
            'file_content': file_content_b64,
            'file_type': file.content_type,
            'file_size': len(file_content),
        })

        # Update CV ID to Partner
        if cv:
            partner = request.env["vit.partner"].sudo().search([("id", "=", resume_id)], limit=1)
            if partner:
                partner.sudo().write({"cv_id": cv.id})

            return json.dumps({"success": True, "cv_id": cv.id})
    except Exception as e:
        return json.dumps({"success": False, "error": str(e)})
```

---

## 2. 🛡️ Security Headers cho Nginx (Dễ triển khai)

### Cập nhật Nginx Config
**File: `/etc/nginx/sites-available/mi52.jp`**
```nginx
server {
    listen 80;
    server_name mi52.jp www.mi52.jp;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://linkpt.cardservice.co.jp https://secure2-sandbox.cardservice.co.jp; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://linkpt.cardservice.co.jp https://secure2-sandbox.cardservice.co.jp;" always;
    
    # Disable directory listing
    autoindex off;
    
    # Hide nginx version
    server_tokens off;
    
    # Existing proxy configuration...
    location / {
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Restart Nginx
```bash
sudo nginx -t  # Test config
sudo systemctl reload nginx
```

---

## 3. 📊 Logging và Monitoring (Compliance cho ZEUS)

### Tạo Security Log Module
**File: `extra-addons/custom_frontend/utils/security_logger.py`**
```python
import logging
import json
from datetime import datetime

# Tạo dedicated logger cho security events
security_logger = logging.getLogger('security')
security_handler = logging.FileHandler('/var/log/odoo/security.log')
security_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
security_handler.setFormatter(security_formatter)
security_logger.addHandler(security_handler)
security_logger.setLevel(logging.INFO)

class SecurityLogger:
    @staticmethod
    def log_antivirus_scan(filename, result, user_id=None):
        """Log antivirus scan results"""
        log_data = {
            'event': 'antivirus_scan',
            'filename': filename,
            'result': result,
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        }
        security_logger.info(json.dumps(log_data))
    
    @staticmethod
    def log_file_upload(filename, size, user_id=None, success=True):
        """Log file upload attempts"""
        log_data = {
            'event': 'file_upload',
            'filename': filename,
            'size': size,
            'user_id': user_id,
            'success': success,
            'timestamp': datetime.now().isoformat()
        }
        security_logger.info(json.dumps(log_data))
```

### Cập nhật Upload Controller để Log
```python
# Thêm vào upload_cv method
from ..utils.security_logger import SecurityLogger

# Sau khi scan virus
SecurityLogger.log_antivirus_scan(file.filename, scan_result, resume_id)
SecurityLogger.log_file_upload(file.filename, len(file_content), resume_id, is_clean)
```

---

## 4. 🔄 Scheduled Tasks cho Maintenance

### Tạo Cron Jobs
**File: `/etc/cron.d/zeus-security`**
```bash
# Update ClamAV definitions daily at 2 AM
0 2 * * * root /usr/bin/freshclam --quiet

# Weekly full system scan at 3 AM Sunday
0 3 * * 0 root /usr/bin/clamscan -r /var/lib/odoo --log=/var/log/clamav/weekly-scan.log

# Daily log rotation
0 1 * * * root /usr/sbin/logrotate /etc/logrotate.d/zeus-security
```

### Log Rotation Config
**File: `/etc/logrotate.d/zeus-security`**
```
/var/log/odoo/security.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 odoo odoo
}
```

---

## 5. ✅ Verification Script

### Tạo Script Kiểm tra
**File: `zeus_security_check.sh`**
```bash
#!/bin/bash
echo "=== ZEUS Security Compliance Check ==="

# Check ClamAV status
echo "1. ClamAV Status:"
systemctl is-active clamav-daemon
systemctl is-active clamav-freshclam

# Check virus definitions date
echo "2. Virus Definitions:"
/usr/bin/freshclam --version

# Check security logs
echo "3. Security Logs:"
tail -5 /var/log/odoo/security.log

# Check nginx security headers
echo "4. Security Headers:"
curl -I http://mi52.jp | grep -E "(X-Frame-Options|X-XSS-Protection|Content-Security-Policy)"

echo "=== Check Complete ==="
```

---

## 📋 CHECKLIST TRIỂN KHAI

- [ ] Cài đặt ClamAV trên server
- [ ] Tạo antivirus utils module
- [ ] Cập nhật file upload controller
- [ ] Cấu hình nginx security headers
- [ ] Thiết lập security logging
- [ ] Tạo cron jobs maintenance
- [ ] Test file upload với virus scan
- [ ] Chạy verification script
- [ ] Tạo báo cáo compliance cho ZEUS

**⚠️ LƯU Ý**: Tất cả thay đổi chỉ THÊM MỚI, không sửa logic cũ để đảm bảo hệ thống hoạt động ổn định.
