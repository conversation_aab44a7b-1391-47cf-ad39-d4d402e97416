/* CSS for password toggle eye icon */
.password-field-container {
    position: relative;
}

.password-toggle-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #546e7a;
    z-index: 10;
}

.password-toggle-icon:hover {
    color: #1072e9;
}

/* Adjust input padding to prevent text from going under the icon */
.password-field-container input[type="password"],
.password-field-container input[type="text"] {
    padding-right: 40px;
}

@media screen and (min-width: 375px) and (max-width: 390px) {
    .position-relative {
        margin-top: -1rem !important;
    }
}
