
const temp_change_mail = {
    data(){
        return {
            email :'',
            token: '',
        }
    },
    methods:{
        async change_mail(){
            const urlParams = new URLSearchParams(window.location.search);
            this.token = urlParams.get('token');
            this.email = urlParams.get('email');
            if (!this.token) {
                window.location.replace("/404");
            }
            try {
                const response = await fetch("/api/change_mail", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ token: this.token, email: this.email }),
                });

                const data = await response.json();
                if (data.result.success) {
                    console.log('Email change successful');

                    // Lưu thông báo để hiển thị sau khi chuyển trang
                    sessionStorage.setItem('toastrMessage', 'メールアドレスが正常に変更されました。新しいメールアドレスでログインしてください。');
                    sessionStorage.setItem('toastrType', "success");

                    // Đăng xuất người dùng bằng cách xóa tất cả thông tin đăng nhập
                    localStorage.removeItem('isloggedin');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userId');
                    localStorage.removeItem('user_name');
                    localStorage.removeItem('company_id');

                    // Thêm log để debug
                    console.log('Redirecting to login page...');

                    // Đảm bảo tất cả các thao tác đã hoàn thành trước khi chuyển hướng
                    setTimeout(() => {
                        // Chuyển hướng trực tiếp đến trang đăng nhập
                        window.location.replace("/login");
                    }, 500);
                } else {
                    window.location.replace("/404");
                }
            } catch (error) {
                console.error('Error changing email:', error);
                // Trong trường hợp có lỗi, vẫn chuyển hướng đến trang đăng nhập
                sessionStorage.setItem('toastrMessage', 'エラーが発生しました。もう一度お試しください。');
                sessionStorage.setItem('toastrType', "error");
                window.location.replace("/login");
            }
        }
    },
    mounted(){
        this.change_mail();
    },
}
export default temp_change_mail