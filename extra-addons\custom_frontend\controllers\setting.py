from odoo import http
from odoo.http import request

class Setting<PERSON><PERSON>roller(http.Controller):
    @http.route('/api/setting_email', type='json', auth='public', methods=['POST'])
    def setting_email(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get("user_id")
        receive_mail = data.get("receive_mail")
        
        if not user_id or receive_mail not in [True, False]:
            return {'success': False, 'message': 'Invalid parameters'}

        setting = request.env['vit.setting'].sudo().search([('user_id', '=', user_id)], limit=1)
        if not setting:
            return {'success': False, 'message': 'Setting not found'}

        try:
            setting.sudo().write({'receive_mail': receive_mail})
            return {'success': True, 'message': 'Email setting updated successfully'}
        except Exception as e:
            return {'success': False, 'message': 'Failed to update email setting', 'details': str(e)}
        
    @http.route('/api/get_setting', type='json', auth='public', methods=['POST'])
    def get_setting(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get("user_id")
        
        if not user_id:
            return {'success': False, 'message': 'User ID is required'}

        setting = request.env['vit.setting'].sudo().search([('user_id', '=', user_id)], limit=1)
        if not setting:
            return {'success': False, 'message': 'Setting not found'}

        return {
            'success': True,
            'message': 'Setting retrieved successfully',
            'user_id': setting.user_id.id,
            'receive_mail': setting.receive_mail,
            'receive_opportunities': setting.receive_opportunities,
            'receive_resumes': setting.receive_resumes,
        }
        
