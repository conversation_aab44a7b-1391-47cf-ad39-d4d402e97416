import subprocess
import logging
import os
import tempfile
import platform
import re
import time
import hashlib
from datetime import datetime

_logger = logging.getLogger(__name__)

class AntivirusScanner:
    # Class variables for rate limiting
    _scan_count = 0
    _last_reset = time.time()
    _max_scans_per_hour = 1000

    @staticmethod
    def is_clamav_available():
        """Kiểm tra ClamAV có sẵn không"""
        try:
            result = subprocess.run(['clamscan', '--version'],
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    @staticmethod
    def scan_file(file_content, filename):
        """
        Scan file content using ClamAV
        Returns: (is_clean: bool, scan_result: str)
        """
        try:
            # Validate input
            if not isinstance(file_content, bytes):
                return False, "Invalid file content type"

            if not isinstance(filename, str):
                return False, "Invalid filename type"

            # Rate limiting check
            if time.time() - AntivirusScanner._last_reset > 3600:
                AntivirusScanner._scan_count = 0
                AntivirusScanner._last_reset = time.time()

            if AntivirusScanner._scan_count >= AntivirusScanner._max_scans_per_hour:
                _logger.error("Scan rate limit exceeded")
                return False, "Scan rate limit exceeded"

            AntivirusScanner._scan_count += 1

            # Validate file content
            is_valid, error_msg = AntivirusScanner.validate_file_content(file_content, filename)
            if not is_valid:
                return False, error_msg

            # Nếu không có ClamAV (local development), chỉ check basic validation
            if not AntivirusScanner.is_clamav_available():
                _logger.warning("ClamAV not available - using basic validation only")
                return AntivirusScanner._basic_validation(file_content, filename)

            # Tạo file tạm để scan
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{filename}") as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Chạy ClamAV scan
                result = subprocess.run(
                    ['clamscan', '--no-summary', temp_file_path],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                # Kiểm tra kết quả
                if result.returncode == 0:
                    _logger.info(f"File {filename} is clean")
                    return True, "Clean"
                elif result.returncode == 1:
                    _logger.warning(f"Virus detected in {filename}: {result.stdout}")
                    return False, f"Virus detected: {result.stdout}"
                else:
                    _logger.error(f"ClamAV scan error for {filename}: {result.stderr}")
                    return False, f"Scan error: {result.stderr}"

            except subprocess.TimeoutExpired:
                _logger.error(f"ClamAV scan timeout for {filename}")
                return False, "Scan timeout"
            finally:
                # Xóa file tạm
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    _logger.error(f"Error deleting temp file {temp_file_path}: {str(e)}")

        except Exception as e:
            _logger.error(f"Unexpected error in scan_file: {str(e)}", exc_info=True)
            return False, "Internal scan error"

    @staticmethod
    def validate_file_content(file_content, filename):
        """Validate file content before scanning"""
        try:
            # Kiểm tra file empty
            if len(file_content) == 0:
                return False, "Empty file"

            # Kiểm tra file size tối thiểu (giảm xuống 1 byte để cho phép file nhỏ)
            if len(file_content) < 1:
                return False, "File too small"

            # Kiểm tra binary content trong text files
            if filename.lower().endswith('.txt'):
                try:
                    file_content.decode('utf-8')
                except UnicodeDecodeError:
                    return False, "Invalid text file encoding"

            # Kiểm tra file header cho PDF (chỉ kiểm tra nếu file đủ lớn)
            if filename.lower().endswith('.pdf'):
                if len(file_content) >= 4 and not file_content.startswith(b'%PDF'):
                    return False, "Invalid PDF header"

            # Kiểm tra file header cho DOCX (ZIP format)
            elif filename.lower().endswith('.docx'):
                if len(file_content) >= 4 and not file_content.startswith(b'PK\x03\x04'):
                    return False, "Invalid DOCX header"

            # DOC files có header phức tạp hơn, skip validation cho giờ
            elif filename.lower().endswith('.doc'):
                # DOC files có nhiều format khác nhau, tạm thời skip header check
                pass

            # Kiểm tra null bytes (có thể là dấu hiệu của malware)
            if filename.lower().endswith('.txt') and b'\x00' in file_content:
                return False, "Text file contains null bytes"

            return True, "Valid file content"

        except Exception as e:
            _logger.error(f"File validation error for {filename}: {str(e)}")
            return False, f"Validation error: {str(e)}"

    @staticmethod
    def _basic_validation(file_content, filename):
        """Basic validation khi không có ClamAV (cho local dev)"""
        try:
            # Kiểm tra file signature cơ bản
            if len(file_content) == 0:
                return False, "Empty file"

            # Kiểm tra một số signature virus đơn giản
            suspicious_patterns = [
                b'X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR',  # EICAR test virus
                b'<script>',  # Basic XSS
                b'javascript:',  # JavaScript injection
                b'eval(',  # JavaScript eval
                b'exec(',  # Python exec
                b'system(',  # System command
                b'cmd.exe',  # Windows command
                b'/bin/bash',  # Linux shell
                b'powershell',  # PowerShell
                b'<?php',  # PHP code
                b'<%@',  # ASP code
            ]

            for pattern in suspicious_patterns:
                if pattern in file_content:
                    _logger.warning(f"Suspicious pattern found in {filename}")
                    return False, f"Suspicious content detected"

            _logger.info(f"File {filename} passed basic validation")
            return True, "Clean (basic validation)"

        except Exception as e:
            _logger.error(f"Basic validation error for {filename}: {str(e)}")
            return False, f"Validation error: {str(e)}"

    @staticmethod
    def is_allowed_file_type(filename):
        """Kiểm tra file type được phép"""
        if not filename:
            return False

        # Kiểm tra độ dài tên file
        if len(filename) > 255:
            return False

        # Kiểm tra ký tự đặc biệt trong tên file
        if re.search(r'[<>:"/\\|?*]', filename):
            return False

        allowed_extensions = ['.pdf', '.doc', '.docx', '.txt']
        filename_lower = filename.lower()

        # Kiểm tra double extension
        if filename_lower.count('.') > 1:
            return False

        return any(filename_lower.endswith(ext) for ext in allowed_extensions)

    @staticmethod
    def is_allowed_file_size(file_size, max_size_mb=10):
        """Kiểm tra file size (default max 10MB)"""
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes

    @staticmethod
    def get_file_info(file_content, filename):
        """Lấy thông tin file để log"""
        return {
            'filename': filename,
            'size': len(file_content),
            'size_mb': round(len(file_content) / (1024 * 1024), 2),
            'clamav_available': AntivirusScanner.is_clamav_available(),
            'platform': platform.system(),
            'file_hash': hashlib.sha256(file_content).hexdigest(),
            'scan_timestamp': datetime.now().isoformat()
        }
