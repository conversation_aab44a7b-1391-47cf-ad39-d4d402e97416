import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const companyDetail = {
    template: `
        <main class="margin-header" id="vue-app" data-v-app="" style="background-color: #eeeeee; margin-top: 80px" >
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: '案件・人財を探す', link: '/opportunities/active' },
                { text: '会社詳細', link: null },
                { text: '会社詳細/案件一覧', link: null, current: true }
            ], 'container-fluid', '')}
            <div class="container-fluid pt-3" style="background-color: #fff;">
                <div class="w-100">
                    <div class="row">
                        <div class="col-12">
                            <!-- Hiển thị loading khi đang tải dữ liệu -->
                            <div v-if="!dataReady" class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">読み込み中...</span>
                                </div>
                                <p class="mt-2">データを読み込んでいます...</p>
                            </div>

                            <!-- Header của bảng - luôn hiển thị -->
                            <div class="opp-table-header">
                                <div class="header-cell status-header" style="width: 150px;">ステータス</div>
                                <div class="header-cell" style="width: 15%;">案件領域</div>
                                <div class="header-cell" style="width: 15%;">就業場所</div>
                                <div class="header-cell" style="width: 12%;">業種</div>
                                <div class="header-cell" style="width: 15%;">単価（万円）</div>
                                <div class="header-cell" style="width: 12%;">開始日</div>
                                <div class="header-cell" style="width: 8%;">募集人数（人）</div>
                                <div class="header-cell" style="width: 23%;">案件名</div>
                            </div>

                            <!-- Hiển thị dữ liệu khi đã sẵn sàng -->
                            <div v-else>
                                <a v-for="opp in opps" :key="opp.id" class="opp-table-row" :href="'/opportunities/' + opp.id + '/detail?prev_next_display=display'" @click="add_viewer(opp.id, this.isloggedin)">
                                    <!-- Status và bookmark -->
                                    <div class="status-cell" style="width: 150px;">
                                        <div v-if="viewedStatuses[opp.id] === true" class="viewed-flag"><span class="font-small">既読</span></div>
                                        <div v-else-if="check_new(opp.created_at)" class="new-flag"><span class="font-small">新着</span></div>
                                        <div v-else class="unread-flag"><span class="font-small">未読</span></div>

                                        <div class="bookmark-area" data-toggle="tooltip" title="ブックマーク">
                                            <span @click.stop.prevent="toggleBookmark(opp.id)">
                                                <i class="material-icons" style="color: #1072e9;">
                                                    {{ opp.is_react ? 'bookmark' : 'bookmark_border' }}
                                                </i>
                                            </span>
                                            <span class="pl-1 font-small" style="color: black !important;">{{ opp.react_number }}</span>
                                        </div>
                                    </div>

                                    <!-- 案件領域 -->
                                    <div class="cell" style="width: 15%;">
                                        <span>{{ getCategoryName(opp) }}</span>
                                    </div>

                                    <!-- 就業場所 -->
                                    <div class="cell" style="width: 15%;">
                                        <span>{{ opp.specifies_workplaces }}</span>
                                    </div>

                                    <!-- 業種 -->
                                    <div class="cell" style="width: 12%;">
                                        <span>{{ categoryMap[opp.business_field] || '-' }}</span>
                                    </div>

                                    <!-- 単価 -->
                                    <div class="cell" style="width: 15%; text-align: center;">
                                        <span>{{opp.unit_price_min}} 〜 {{opp.unit_price_max}}</span>
                                    </div>

                                    <!-- 開始日 -->
                                    <div class="cell date-cell" style="width: 12%; text-align: center;">
                                        <span>{{ formatDate(opp.contract_startdate_at) }}</span>
                                    </div>

                                    <!-- 募集人数 -->
                                    <div class="cell" style="width: 8%; text-align: center;">
                                        <span>{{ opp.participants }}</span>
                                    </div>

                                    <!-- 案件名 -->
                                    <div class="cell cell-subject" style="width: 23%;">
                                        <span class="default-main-color">{{ opp.subject }}</span>
                                    </div>
                                </a>

                                <!-- Modal for each opportunity -->
                                <div v-for="opp in opps" :key="'modal-'+opp.id" aria-labelledby="display_text_format_modal" class="modal" :id="'display_text_format_' + opp.id" tabindex="-1" aria-modal="true" role="dialog">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title w-100">案件詳細</h4>
                                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-4">**********************************************************<br>
                                                ◆案件ID: {{opp.id}}<br>
                                                ◆案件名: {{opp.subject}}<br>
                                                ◆案件への関わり: {{categoryMap[opp.involvements]}}<br>
                                                ◆案件の商流: {{categoryMap[opp.opp_type_id] || opp.opp_type_id}}<br>
                                                ◆案件内容: <br>{{opp.requirements}}<br>
                                                ◆人財要件: <br>{{opp.skill_requirements}}<br>
                                                ◆単価: {{opp.unit_price_min}} 〜 {{opp.unit_price_max}} / 月<br>
                                                ◆稼働率: {{(opp?.utilization_rate || '').split(',').map(rate => categoryMap[rate] || '').join('／')}}<br>
                                                ◆出社頻度: {{ (opp?.work_frequencies || '').split(',').map(fr => categoryMap[fr] || '').join('／') || '' }}<br>
                                                ◆就業場所: {{opp.specifies_workplaces}}<br>
                                                ◆契約形態: {{ (opp?.contract_types || '').split(',').map(type => categoryMap[type] || '').join('／') || '' }}<br>
                                                ◆募集人数: {{opp.participants}}<br>
                                                ◆面談回数: {{ (opp?.interview_count_id || '').split(',').map(it => categoryMap[it] || '').join('／') || '' }}<br>
                                                ◆契約期間: {{convertDateToJapanese(opp.contract_startdate_at)}} 〜 {{convertDateToJapanese(opp.contract_enddate_at)}}<br>
                                                ◆募集対象: {{ (opp?.trading_restriction || '').split(',').map(tr => categoryMap[tr] || '').join('／') || '' }}<br>
                                                **********************************************************
                                                </div>
                                                <div class="text-center"><a aria-label="Close"
                                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                                            data-dismiss="modal">閉じる</a></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <nav role="navigation" class="pagination" v-if="opps.length > 0">
                        <ul class="pagination">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)"></a>
                            </li>
                            <li v-for="page in visiblePages" :key="page" class="page-item" :class="{ active: page === currentPage }">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                            </li>
                            <li class="page-item disabled" v-if="totalPages > 5 && currentPage < totalPages - 2">
                                <a class="page-link waves-effect" href="#">…</a>
                            </li>
                            <li class="page-item" v-if="totalPages > 5">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(totalPages)">{{ totalPages }}</a>
                            </li>
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)"></a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/opportunities/active.css"/>
    `,
    props: ["id"],
    data(){
        return {
            company_id: this.id,
            opps: [],
            viewedStatuses: {},
            isloggedin: userInfo ? userInfo.user_id : '',
            currentPage: 1,
            totalPages: 1,
            totalRecord: 0,
            dataReady: false,
        }
    },
    async mounted(){
        this.userId = userInfo ? userInfo.user_id : null;
        const params = new URLSearchParams(window.location.search);
        const page = parseInt(params.get('page')) || 1;
        try{
            await this.getOpp(page, this.sortType);
            if (this.opps.length > 0) {
                await this.checkAllViewStatuses();
            } else {
                console.log('No resumes data available.');
            }

            this.dataReady = true;
        } catch (error) {
            console.error('Error fetching opps:', error);
            this.dataReady = true;
        }
    },
    methods: {
        async getOpp(page = 1, sort_type = 'created_at') {
            try {
                const response = await fetch(`/api/getOpp?company_id=${this.id}&page=${page}&sort_type=${sort_type}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': odoo.csrf_token,
                        'X-User-ID': this.userId || ''

                    },
                });

                const data = await response.json();
                if (data.success === true) {
                    this.opps = data.data;
                    this.currentPage = data.current_page;
                    this.totalPages = data.total_pages;
                    this.totalRecord = data.total_records;
                } else {
                    console.warn("API data is not in the correct format:", data);
                }
            } catch (error) {
                console.log('Error API:', error.message);
                this.errorMessage = error.message;
            }
        },

        changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
                window.location.href = `?page=${page}`; // Cập nhật URL và reload trang
            }
        },

        check_new(exp) {
            var today = new Date();
            var expired = new Date(exp);

            // Tính số ngày còn lại
            var diffTime = expired - today;
            var diffDays = diffTime / (1000 * 60 * 60 * 24);

            return diffDays <= 7;
        },
        convertDateToJapanese(date) {
            if (!date) return "日付なし"; // Nếu giá trị rỗng/null, trả về "Không có ngày"

            // Nếu date là chuỗi, chuyển thành Date object
            if (typeof date === "string") {
                date = new Date(date);
            }

            // Kiểm tra nếu date không hợp lệ
            if (!(date instanceof Date) || isNaN(date)) {
                return "無効な日付"; // Trả về "Ngày không hợp lệ"
            }

            // Chuyển đổi sang format Nhật Bản
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        },
        async checkAllViewStatuses() {
            const oppIds = this.opps.map(opp => opp.id);
            await Promise.all(oppIds.map(id => this.isViewed(id))); // Kiểm tra tất cả oppId trong danh sách resumes
        },
        async add_viewer(opp_id, user_id) {
            try {
                // Tạo URL với user_id nếu có
                let url = `/api/view_opp?opp_id=${encodeURIComponent(opp_id)}`;
                if (user_id) {
                    url += `&user_id=${encodeURIComponent(user_id)}`;
                }

                // Gửi yêu cầu GET đến API
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                });

                // Kiểm tra nếu có lỗi từ phía server
                if (!response.ok) {
                    throw new Error('Error: ' + response.statusText);
                }

                // Xử lý kết quả
                const data = await response.json();
                console.log('Response:', data);

            } catch (error) {
                console.error('Fetch error:', error);
                this.errorMessage = error.message;
            }
        },
        async isViewed(oppId) {
            if (this.viewedStatuses[oppId] === undefined) {
                await this.fetchViewedStatus(oppId); // Chỉ gọi API nếu chưa có trong viewedStatuses
            }
            return this.viewedStatuses[oppId] || false; // Trả về trạng thái đã xem nếu có, false nếu chưa xem
        },
        openModal(oppId, event) {
            // Ngừng hành động mặc định của thẻ <a> khi nhấn nút mở modal
            const modalId = '#display_text_format_' + oppId;

            // Dùng jQuery để mở modal
            $(modalId).modal('show');
        },

        // Lấy trạng thái đã xem của opp từ API
        async fetchViewedStatus(oppId) {
            try {
                const response = await fetch(`/api/check_view_opp?user_id=${this.isloggedin}&opp_id=${oppId}`);
                const data = await response.json();
                this.viewedStatuses[oppId] = data.success ? data.viewed : false; // Lưu kết quả vào viewedStatuses
            } catch (error) {
                console.error('Error fetching viewed status:', error);
                this.viewedStatuses[oppId] = false; // Mặc định là chưa xem nếu có lỗi
            }
        },

        async toggleBookmark(id) {
            try {
                const response = await fetch('/api/react_opp', {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        user_id: this.userId,
                        opportunities_id: id,
                    }),
                });

                const result = await response.json();
                if (result.result.success) {
                    this.getOpp();
                }
            } catch (error) {
                console.error("Bookmark failed:", error);
            }
        },

        getCategoryName(opp) {
            // Mapping prefix to Japanese names
            const prefixMapping = {
                'design': '設計',
                'dev': '開発',
                'infra': 'インフラ',
                'operation': '運用保守'
            };

            // Tìm category từ các field categories
            const allCategories = [
                { value: "design_pmo", label: "PMO" },
                { value: "design_pmpl", label: "PM・PL" },
                { label: "DX", value: "design_DX" },
                { label: "クラウド", value: "design_cloud" },
                { label: "モダナイゼション", value: "design_strategy" },
                { label: "セキュリティ", value: "design_work" },
                { label: "ITインフラ", value: "design_it" },
                { label: "AI", value: "design_ai" },
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_embedded", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
                { value: "operation_pmo", label: "業務システム" },
                { value: "operation_pmpl", label: "オープン" },
                { label: "クラウド", value: "operation_DX" },
                { label: "メインフレーム", value: "operation_mainframe" },
                { label: "ヘルプデスク", value: "operation_strategy" },
            ];

            // Lấy tất cả categories fields từ API response với field mapping
            const categoryFields = [
                { field: opp.categories_consultation, prefix: 'design' },
                { field: opp.categories_development, prefix: 'dev' },
                { field: opp.categories_infrastructure, prefix: 'infra' },
                { field: opp.categories_design, prefix: 'operation' }
            ];

            const results = [];

            // Xử lý từng field category
            categoryFields.forEach(({ field, prefix }) => {
                if (field) {
                    // Split các categories trong field (có thể có nhiều categories cách nhau bởi dấu phẩy)
                    const categories = field.split(',').map(cat => cat.trim()).filter(cat => cat);

                    categories.forEach(categoryValue => {
                        const category = allCategories.find(cat => cat.value === categoryValue);
                        if (category) {
                            const prefixJapanese = prefixMapping[prefix] || prefix;
                            results.push(`${prefixJapanese}_${category.label}`);
                        }
                    });
                }
            });

            // Trả về tất cả categories, ngăn cách bởi dấu phẩy
            return results.length > 0 ? results.join(', ') : '';
        },

        formatDate(dateString) {
            if (!dateString) return '-';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return '-';

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');

                return `${year}/${month}/${day}`;
            } catch (error) {
                return '-';
            }
        },
    },

    computed: {
        visiblePages() {
            let pages = [];
            if (this.totalPages <= 5) {
                pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
            } else if (this.currentPage <= 3) {
                pages = [1, 2, 3, 4, 5];
            } else if (this.currentPage >= this.totalPages - 2) {
                pages = [this.totalPages - 4, this.totalPages - 3, this.totalPages - 2, this.totalPages - 1, this.totalPages];
            } else {
                pages = [this.currentPage - 2, this.currentPage - 1, this.currentPage, this.currentPage + 1, this.currentPage + 2];
            }
            return pages;
        },
        displayedRecords() {
            return this.totalRecord < 24 ? this.totalRecord : 24;
        },
        categoryMap() {
            return {
                // categories
                "design_pmo": "PMO",
                "design_pmpl": "PM・PL",
                "design_DX": "DX",
                "design_cloud": "クラウド",
                "design_strategy": "モダナイゼション",
                "design_work": "セキュリティ",
                "design_it": "ITインフラ",
                "design_rpa": "AI",

                // categoriesDev
                "dev_pmo": "PMO",
                "dev_pmpl": "PM・PL",
                "dev_web": "Webシステム",
                "dev_ios": "IOS",
                "dev_android": "Android",
                "dev_control": "制御",
                "dev_embedded": "組込",
                "dev_ai": "AI・DL・ML",
                "dev_test": "テスト",
                "dev_cloud": "クラウド",
                "dev_architect": "サーバ",
                "dev_bridge_se": "データベース",
                "dev_network": "ネットワーク",
                "dev_mainframe": "メインフレーム",

                // categoriesInfra
                "infra_pmo": "PMO",
                "infra_pmpl": "PM・PL",
                "infra_server": "サーバー",
                "infra_network": "ネットワーク",
                "infra_db": "データベース",
                "infra_cloud": "クラウド",
                "infra_virtualized": "仮想化",
                "infra_mainframe": "メインフレーム",

                // categories3
                "operation_pmo": "業務システム",
                "operation_pmpl": "オープン",
                "operation_DX": "クラウド",
                "operation_mainframe": "メインフレーム",
                "operation_strategy": "ヘルプデスク",

                // business_field mapping
                "it_telecom_internet": "情報通信・インターネット",
                "automobile_machinery": "自動車・機械",
                "electronics": "エレクトロニクス機器",
                "resources_energy_materials": "資源・エネルギー・素材",
                "finance_corporate_services": "金融・法人サービス",
                "food_agriculture": "食品・農業",
                "consumer_goods_pharmaceuticals": "生活用品・嗜好品・薬",
                "entertainment_media": "娯楽・エンタメ・メディア",
                "construction_real_estate": "建設・不動産",
                "logistics_transport": "運輸・物流",
                "retail_dining": "流通・外食",
                "public_services": "生活・公共サービス",

                // contract type
                "quas": "業務委託（準委任）",
                "subc": "業務委託（請負）",
                "temp": "派遣契約",

                //invol
                "enter_sales_channels": "商流に入る",
                "agent_only": "仲介のみ",

                //opp_qualities
                "700thousand_or_more": "70万円以上",
                "1million_or_more": "100万円以上",
                "less_1year_ok": "1年未満OK",
                "newcomer_ok": "新人でもOK",
                "over_50years_old_ok": "50代以上OK",
                "foreign_nationality_ok": "外国籍OK",
                "leader_recruitment": "リーダー募集",
                "english_skill": "英語力",
                "interview_once": "面談1回",
                "take_home_project": "持ち帰りOK",
                "team_proposal_ok": "チーム提案OK",
                "web_interview_ok": "ウェブ面談可能",
                "remote__location_ok": "案件場所から遠隔地居住でもOK",
                "long_term": "日本以外の居住者OK",
                "overseas_resident_ok": "長期（２年以上継続可能性）",

                //restriction
                "own_employee": "自社社員",
                "one_subcontract_employee": "協力会社社員（一社先）",
                "more_subcontract_employee": "協力会社社員（二社先以降）",
                "freelance_person": "フリーランス（本人）",
                "subcontract_freelance": "フリーランス（一社先）",
                "more_subcontract_freelance": "フリーランス（二社先以降）",

                //order_accuracy_ids
                "afte": "確定済み",
                "befo": "確定前",

                //opp_type_id
                "clnt": "エンド",
                "prim": "元請",
                "subc": "一次請",
                "msubc": "二次請以降",

                //utilization_rate
                '100': '100%（フル稼働）',
                '75': '75%',
                '50': '50%',
                '25': '25%',

                //work_frequencies
                '5days': '週5日出社',
                '4days': '週4日出社',
                '3days': '週3日出社',
                '2days': '週2日出社',
                '2to4days': '週4 〜 2日出社',
                'less_than_1day': '週1日未満出社',
                'full_remote': 'フルリモート',
                '1day': '週1日出社',

                //interview_count_id
                "once": "1回",
                "few": "1〜2回",
                "twice": "2回",
                "over_three_times": "3回以上"
            };
        }
    },
}

export default companyDetail;
