<odoo>
    <template id="layout_custom" name="Custom Navigation">
        <t t-call="web.layout">
            <meta name="viewport" content="width=device-width, initial-scale=1.0"/>

            <!-- Google Tag Manager -->
            <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&amp;l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-K4QS2QF6');</script>
            <!-- End Google Tag Manager -->

            <!-- Global CSS with Meiryo font -->
            <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/global.css"/>
            <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/layout.css"/>
            <t t-set="title">Mi52</t>

            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-K4QS2QF6"
            height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
            <!-- End Google Tag Manager (noscript) -->

            <div id="app"/>

            <!-- Load CSS for Bootstrap -->
            <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous"/>

            <!-- Load jQuery (full version) -->
            <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>


            <!-- Load Popper.js for Bootstrap tooltips and popups -->
            <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>

            <!-- Load Bootstrap JS -->
            <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>

            <!-- Load Font Awesome for icons -->
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

            <!-- Load Vue.js -->
            <script src="https://unpkg.com/vue@3.2.47/dist/vue.global.js"></script>

            <!-- Load Vue Router -->
            <script src="https://unpkg.com/vue-router@4.0.15/dist/vue-router.global.js"></script>

            <link href="https://cdnjs.cloudflare.com/ajax/libs/pickadate.js/3.6.3/themes/default.css" rel="stylesheet"/>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/pickadate.js/3.6.3/themes/default.date.css" rel="stylesheet"/>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/pickadate.js/3.6.3/picker.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/pickadate.js/3.6.3/picker.date.js"></script>
            <link rel="stylesheet" href="/custom_frontend/static/css/pickadate.css"/>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css"/>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>

            <!-- Load your custom Vue script -->
            <script type="module" src="/custom_frontend/static/js/vue.js"></script>

            <script>
                // Your custom JavaScript for modals
                $('#exampleModal').on('show.bs.modal', event => {
                    var button = $(event.relatedTarget);
                    var modal = $(this);
                    // Use above variables to manipulate the DOM
                });
            </script>
        </t>
    </template>
</odoo>