from odoo import http
from odoo.http import request, Response
import json
from . import common as global_common
import hashlib
import base64

# Import security modules
from ..utils.ip_middleware import IPSecurityMiddleware, require_safe_ip
from ..utils.ip_security import ip_security
from ..utils.brute_force_protection import brute_force_protection
from ..utils.simple_captcha import simple_captcha
import time

class LoginController(http.Controller):

    @http.route('/api/login', type='json', auth='public', methods=['POST'])
    def login(self):
        # ✅ ZEUS SECURITY: IP validation cho login
        is_allowed, reason, country = ip_security.validate_ip_access(request, require_admin=False)
        if not is_allowed:
            ip_security.log_ip_access(request, 'login', success=False, reason=reason)
            return {
                'success': False,
                'message': 'アクセスが拒否されました。',
                'content': 'Access denied from your location'
            }

        data = request.httprequest.get_json(silent=True)

        email = data.get('email')
        password = data.get('password')
        remember_me = data.get('remember_me')

        if not email or not password:
            return {'success': False, 'message': global_common.LOGIN_TOAST_ERROR_MESSAGE, 'content': 'Missing email or password'}

        # ✅ ZEUS SECURITY: Brute force protection
        client_ip = ip_security.get_client_ip(request)

        # Check if account is locked
        is_locked, remaining_time = brute_force_protection.is_account_locked(email)
        if is_locked:
            return {
                'success': False,
                'message': f'アカウントがロックされています。{remaining_time//60}分後に再試行してください。',
                'content': f'Account locked. Try again in {remaining_time//60} minutes.',
                'locked_until': remaining_time
            }

        db_user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email)], limit=1)
        db_username = db_user.username
        db_password = db_user.password
        user_id = db_user.id
        if not db_user.email:
            # ✅ ZEUS SECURITY: Track failed login attempt
            IPSecurityMiddleware.track_failed_login(email)
            is_locked, delay, _ = brute_force_protection.add_failed_attempt(email, client_ip)

            response = {'success': False, 'message': global_common.LOGIN_TOAST_ERROR_MESSAGE, 'content': 'Email not found'}
            if is_locked:
                response['message'] = 'アカウントがロックされました。1時間後に再度お試しください。'
            if delay > 0:
                time.sleep(delay)  # Apply progressive delay
            return response

        if not self._verify_password(password, db_password):
            # ✅ ZEUS SECURITY: Track failed login attempt
            IPSecurityMiddleware.track_failed_login(email)
            is_locked, delay, _ = brute_force_protection.add_failed_attempt(email, client_ip)

            response = {'success': False, 'message': global_common.LOGIN_TOAST_ERROR_MESSAGE, 'content': 'Password is incorrect'}
            if is_locked:
                response['message'] = 'アカウントがロックされました。1時間後に再度お試しください。'
            if delay > 0:
                time.sleep(delay)  # Apply progressive delay
            return response

        # ✅ ZEUS SECURITY: Reset failed attempts on successful login
        brute_force_protection.reset_failed_attempts(email)

        # ✅ ZEUS SECURITY: Bind session to IP
        request.session['loginId'] = db_user.id
        request.session['loginEmail'] = db_user.email  # Lưu vào session
        request.session['loginUsername'] = db_user.username
        request.session['session_ip'] = ip_security.get_client_ip(request)  # Bind IP to session

        # ✅ Log successful login
        ip_security.log_ip_access(request, 'login', success=True, reason='Login successful')

        token_login = request.env['vit.user_sessions'].generate_token()
        db_user.write({'token_login': token_login})

        if remember_me:
            # Tạo token ngẫu nhiên
            token = request.env['vit.user_sessions'].generate_token()
            # Lưu token vào bảng vit.user_sessions
            request.env['vit.user_sessions'].sudo().create({
                'user_id': db_user.id,
                'session_token': token
            })
            return {
                'success': True,
                'message': 'Login successful',
                'username': db_username,
                'role': db_user.role,
                'token': token,
                'userid': user_id,
                'isloggedin': db_user.id,
                'company_id': db_user.company_id.id,
                'active': db_user.active,
                'email': db_user.email,
                'token_login': token_login,
            }

        return {
            'success': True,
            'message': 'Login successful',
            'userid': db_user.id,
            'username': db_username,
            'role': db_user.role,
            'token': None,
            'isloggedin': db_user.id,
            'company_id': db_user.company_id.id,
            'active': db_user.active,
            'email': db_user.email,
            'token_login': token_login,
        }

    @http.route('/api/check_session', type='http', auth='none', csrf=False)
    def check_session(self, **kwargs):
        # Lấy dữ liệu từ request
        data = request.httprequest.get_json(silent=True)
        token = data.get('token')
        loginEmail = request.session.get('loginEmail')

        if token == None and loginEmail == None:
            request.session.pop('loginEmail', None)
            return Response(json.dumps({'loginEmail': None}), content_type='application/json', status=401)

        if token == None and loginEmail != None:
            return Response(json.dumps({'loginEmail': loginEmail}), content_type='application/json', status=200)

        # Kiểm tra token trong bảng vit.user_sessions
        session = request.env['vit.user_sessions'].sudo().search([('session_token', '=', token)], limit=1)

        # Nếu session hợp lệ, trả về email
        if session and session.user_id:
            return Response(json.dumps({'loginEmail': session.user_id.email}), content_type='application/json', status=200)

        # Token không hợp lệ hoặc hết hạn
        return Response(json.dumps({'loginEmail': None}), content_type='application/json', status=403)

    def _verify_password(self, password, stored_password_hash):
        #Xác thực mật khẩu với chuỗi đã băm
        try:
            # Tách các thành phần từ chuỗi đã lưu
            algorithm, iterations, salt_b64, key_b64 = stored_password_hash.split('$')

            # Giải mã salt từ base64
            salt = base64.b64decode(salt_b64)
            # Tạo lại key từ mật khẩu nhập vào và salt đã lưu
            key = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt,
                int(iterations),
                dklen=32
            )
            # So sánh key tính toán với key đã lưu
            calculated_key_b64 = base64.b64encode(key).decode('utf-8')
            return calculated_key_b64 == key_b64
        except Exception:
            return False

    @http.route('/api/get_user_info', type='http', auth='public', methods=['GET'])
    def get_user_info(self):
        token = request.httprequest.headers.get('Authorization')
        if not token:
            return Response(json.dumps({'success': False, 'message': 'Missing token'}), content_type='application/json')

        user = request.env['vit.users'].sudo().search([('token_login', '=', token)], limit=1)
        if not user:
            return Response(json.dumps({'success': False, 'message': 'Invalid token'}), content_type='application/json')

        return Response(json.dumps({
            'success': True,
            'user_id': user.id,
            'user_email': user.email,
            'user_name': user.username,
            'active': user.active,
            'company_id': user.company_id.id,
            # ... các thông tin khác nếu cần
        }), content_type='application/json', status=200)
