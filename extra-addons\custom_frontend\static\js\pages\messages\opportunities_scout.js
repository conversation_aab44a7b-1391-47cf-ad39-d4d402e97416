import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const opportunities_scout = {
    'template': `
<main class="margin-header" style="background-color: white">
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: '人財を探す', link: '/resumes/active' },
        { text: 'スカウト案件選択', link: null, current: true }
    ])}
    <div class="container-fluid grabient pt-3">
        <div aria-labelledby="nothing_opportunity_modal" class="modal" data-backdrop="true" id="nothing_opportunity" tabindex="-1"
             style="display: none; opacity: 1 !important;" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content" style="background-color: #fff !important; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5) !important; border: 1px solid rgba(0, 0, 0, 0.2) !important;">
                    <div class="modal-header" style="background-color: #fff;"><h4 class="modal-title w-100">案件が登録されていません</h4></div>
                    <div class="modal-body" style="background-color: #fff;"><p class="text-center font-extralarge">案件を作成していない場合</p>
                        <div class="row justify-content-md-center">
                            <div class="col-12"><a class="btn btn-default btn-lg btn-block waves-effect waves-light"
                                                   href="/opportunities/manage/new" style="font-weight: 500 !important;">案件を新規登録</a>
                            </div>
                        </div>
                        <p class="text-center font-extralarge mt-5">案件を作成している場合</p>
                        <div class="row justify-content-md-center">
                            <div class="col-12"><a class="btn btn-default btn-lg btn-block waves-effect waves-light"
                                                   href="/opportunities/manage/index" style="font-weight: 500 !important;">案件管理一覧</a>
                            </div>
                            <div class="col-12 text-center">
                                <button aria-label="Close"
                                        class="btn btn-outline-blue-grey mt-5 waves-effect waves-light"
                                        data-dismiss="modal" style="border: 2px solid #78909c !important; color: #78909c !important;">閉じる
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="row justify-content-center">
                    <div class="col-12 col-md-8 col-lg-6 mx-auto">
                        <div class="pt-2 pb-2 font-middle text-left">スカウトする人財</div>
                        <div class="mb-3">
                            <div class="mr-5 mb-3 d-inline-block font-middle"><a target="_blank"
                                    :href="'/resumes/' + resumeId + '/detail'">{{ resumeName }}</a></div>
                        </div>
                        <div class="font-middle mb-3">スカウトする案件を選択しましょう。一度に選択できる案件は１件です。</div>
                        <form novalidate="novalidate" method="get" @submit.prevent>
                            <div class="row">
                                <div class="col-12 col-md-9 col-lg-9">
                                    <div class="mx-auto mb-md-3 white"><input placeholder="フリーキーワード"
                                            class="form-control" autocomplete="off" id="free_keyword_field" type="text"
                                            name="opportunity_manage_condition[free_keyword]" v-model="keyword"></div>
                                </div>
                                <div class="col-12 col-md-3 col-lg-3 text-right py-3 py-md-0">
                                    <button name="button" type="button" class="btn btn-default font-default btn-block py-3 waves-effect waves-light" @click="getOpportunities">検索</button>
                                </div>
                            </div>
                        </form>
                        <form novalidate="novalidate" id="select_opportunities"
                            :action="'/messages/' + resumeId + '/scout_message'" accept-charset="UTF-8" method="get">
                            <input name="utf8" type="hidden" value="✓" autocomplete="off">
                            <div class="row pb-1 justify-content-center">
                                <div class="col-12 mb-3">
                                    <table class="table message-table">
                                        <thead class="stylish-color white-text">
                                            <tr class="d-none d-md-table-row">
                                                <th class="vertical-middle border-0"></th>
                                                <th class="vertical-middle border-0">案件名</th>
                                                <th class="vertical-middle border-0">案件内容</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(opportunity, index) in opportunities" :key="opportunity.id"
                                                class="d-block d-md-table-row mb-3 mb-md-0"
                                                :class="{ 'disabled-opportunity': opportunity.disabled }"
                                                :style="opportunity.disabled ? 'opacity: 0.7; background-color: #f8f9fa;' : ''">
                                                <td
                                                    class="th-lg d-block d-md-table-cell select-record pb-0 pb-md-3 pt-1 pt-md-0">
                                                    <div class="mb-0 px-sm-3 px-md-1">
                                                        <div class="form-inline my-3">
                                                            <div class="form-check ">
                                                                <input class="form-check-input"
                                                                    :id="'choose_opportunity_' + opportunity.id"
                                                                    required="required" type="radio"
                                                                    :value="opportunity.id" name="choose_opportunity" v-model="selectedOpportunity"
                                                                    :disabled="opportunity.disabled"
                                                                    :title="opportunity.disabled ? 'この案件はすでにスカウト済みです' : ''">
                                                                <label :id="'choose_opportunity_field_label_' + index"
                                                                    class="form-check-label"
                                                                    :for="'choose_opportunity_' + opportunity.id"
                                                                    required="required"
                                                                    :style="opportunity.disabled ? 'color: #6c757d;' : ''">選択</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="th-lg d-block d-md-table-cell px-3 pt-0 pt-md-3">
                                                    <span class="d-block custom-grey-text"><a class="d-block"
                                                            target="_blank"
                                                            :href="'/opportunities/' + opportunity.id + '/detail'">{{
                                                            opportunity.name }}</a>
                                                        <span v-if="opportunity.disabled"
                                                            style="background-color: #6c757d; color: white; padding: 2px 5px; border-radius: 4px; font-size: 0.75rem; margin-left: 5px;">
                                                            スカウト済み
                                                        </span>
                                                    </span>
                                                    <div class="small custom-grey-text">{{ opportunity.company }}</div>
                                                </td>
                                                <td class="th-lg d-block d-md-table-cell p-3 status-cell">{{
                                                    opportunity.content }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <nav aria-label="pagination example">
                                    <nav role="navigation" class="pagination">
                                        <ul class="pagination pageul">
                                            <li :class="['page-item', { disabled: currentPage === 1 }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)">← 前</a>
                                            </li>

                                            <li v-for="page in totalPages" :key="page" :class="['page-item', { active: currentPage === page }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                                            </li>

                                            <li :class="['page-item', { disabled: currentPage === totalPages }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)">次 →</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                            <div class="row px-1">
                                <div class="col-12 pt-4"><a class="btn btn-blue-grey waves-effect waves-light"
                                        href="/resumes/active">人財詳細へ戻る</a></div>
                            </div>
                        </form>
                    </div>
                </div>
                <div
                    class="fix_right_buttom d-md-flex justify-content-end align-items-center px-3 py-4 text-right w-100">
                    <span class="vertical-baseline"><button name="button" type="button"
                            class="btn btn-default font-middle m-0 waves-effect waves-light" id="input_scout_message" @click="scoutResume"
                            form="select_opportunities">スカウト</button></span></div>
            </div>
        </div>
    </div>
</main>
<div class="container-fluid mt-3" style="background-color: white">
    <div class="row">
        <div class="col-12">
            <div class="text-right pb-4"><a class="pagetop" href="javascript:void(0)" @click="scrollToTop"><i
                        class="material-icons md-blue-grey md-36">arrow_upward</i></a></div>
        </div>
    </div>
</div>
<link rel="stylesheet" href="/custom_frontend/static/css/messages/opportunities_scout.css" />
<link rel="stylesheet" href="/custom_frontend/static/css/layout.css" />
    `,
    data() {
        return {
            user_id: userInfo ? userInfo.user_id : null,
            resumeId: '',
            resumeName: '',
            resumeGender: '',
            resumeAge: '',
            opportunities: [],
            allOpportunities: [],
            keyword: '',
            selectedOpportunity: null,
            currentPage: 1,
            itemsPerPage: 20,
            totalPages: 1,
            scoutedOpportunityIds: [] // Danh sách các opportunity đã scout resume này
        }
    },
    mounted() {
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        // Lấy resume_id từ URL
        const urlParams = new URLSearchParams(window.location.search);
        this.resumeId = urlParams.get('resume_id');

        if (this.resumeId) {
            this.getResumeDetail();
            this.getScoutedOpportunities();
        }
    },
    methods: {
        scrollToTop(event) {
            event.preventDefault(); // Ngăn hành vi mặc định của liên kết
            window.scrollTo({
                top: 0,
                behavior: 'smooth' // Cuộn mượt mà
            });
        },
        closeWindow() {
            window.close();
        },
        async getResumeDetail() {
            try {
                const response = await fetch(`/api/resume_detail?id=${this.resumeId}`, {
                    method: "GET",
                    headers: { "Content-Type": "application/json" },
                });

                if (!response.ok) {
                    console.error("Error fetching resume:", response.statusText);
                    return;
                }

                const result = await response.json();
                if (result.success) {
                    const initialName = result.data.initial_name.split(' ');
                    const gender = result.data.gender === 'male' ? '男性' : '女性';
                    const age = this.calculateAge(result.data.birthday);

                    this.resumeName = `${initialName[0]}.${initialName[1]}.さん　${gender}　${age}歳`;
                    this.resumeGender = gender;
                    this.resumeAge = age;
                }
            } catch (error) {
                console.error("Error fetching resume detail:", error);
            }
        },
        async getScoutedOpportunities() {
            try {
                if (!this.resumeId) {
                    console.error("Resume ID not found");
                    return;
                }

                const response = await fetch(`/api/get_scouted_opportunities?resume_id=${this.resumeId}`, {
                    method: "GET",
                    headers: { "Content-Type": "application/json" },
                });

                if (!response.ok) {
                    console.error("Error fetching scouted opportunities:", response.statusText);
                    return;
                }

                const result = await response.json();
                if (result.success) {
                    this.scoutedOpportunityIds = result.data || [];
                    console.log("Scouted opportunity IDs:", this.scoutedOpportunityIds);
                    // Sau khi lấy danh sách opportunity đã scout, lấy tất cả opportunity
                    this.getOpportunities();
                }
            } catch (error) {
                console.error("Error fetching scouted opportunities:", error);
                // Vẫn tiếp tục lấy tất cả opportunity nếu có lỗi
                this.getOpportunities();
            }
        },

        async getOpportunities() {
            try {
                if (!this.user_id) {
                    console.error("User ID not found");
                    return;
                }

                const response = await fetch(`/api/get_opportunities_for_scout?user_id=${this.user_id}&keyword=${this.keyword}`, {
                    method: "GET",
                    headers: { "Content-Type": "application/json" },
                });

                if (!response.ok) {
                    console.error("Error fetching opportunities:", response.statusText);
                    return;
                }

                const result = await response.json();
                if (result.success && result.data) {
                    // Thêm thuộc tính disabled cho mỗi opportunity
                    this.allOpportunities = result.data.map(opp => {
                        return {
                            ...opp,
                            disabled: this.scoutedOpportunityIds.includes(opp.id)
                        };
                    });

                    // Không cần hiển thị modal ở đây, chỉ cần hiển thị khi nhấn nút "スカウト"

                    this.totalPages = Math.ceil(this.allOpportunities.length / this.itemsPerPage);
                    this.currentPage = 1;
                    this.updateDisplayedOpportunities();
                }
            } catch (error) {
                console.error("Error fetching opportunities:", error);
            }
        },
        updateDisplayedOpportunities() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            this.opportunities = this.allOpportunities.slice(start, end);
        },
        changePage(page) {
            if (page < 1 || page > this.totalPages) {
                return;
            }
            this.currentPage = page;
            this.updateDisplayedOpportunities();
        },
        calculateAge(birthday) {
            if (!birthday) return "N/A";

            // Xử lý chuỗi có dạng "YYYY-MM-DD HH:MM:SS"
            if (typeof birthday === "string") {
                birthday = birthday.split(" ")[0];
            }

            const birthDate = new Date(birthday);
            if (isNaN(birthDate.getTime())) return "N/A";

            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();

            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            return age;
        },
        loadExternalScript(src) {
            // Kiểm tra xem jQuery đã được tải chưa
            if (typeof $ === 'undefined') {
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = () => {
                    console.log("jQuery loaded successfully!");
                    this.loadToastrAndOtherScripts(src);
                };
                document.body.appendChild(jQuery);
            } else {
                this.loadToastrAndOtherScripts(src);
            }
        },

        loadToastrAndOtherScripts(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";

            const toastrJS = document.createElement("script");
            toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
            toastrJS.onload = function() {
                console.log("Toastr loaded successfully!");
            };

            const script = document.createElement("script");
            script.src = src;
            script.async = true;
            script.onload = function() {
                console.log("External script loaded successfully!");
            };

            document.body.appendChild(toastrCSS);
            document.body.appendChild(toastrJS);
            document.body.appendChild(script);
        },

        async scoutResume() {
            // Kiểm tra nếu không có opportunity nào trong danh sách
            if (this.allOpportunities.length === 0) {
                // Hiển thị modal khi không có opportunity
                console.log("No opportunities available, showing modal");

                // Đảm bảo jQuery đã được tải
                if (typeof $ !== 'undefined' && typeof $.fn.modal !== 'undefined') {
                    // Thêm style cho modal backdrop
                    $('body').append('<style>.modal-backdrop { opacity: 0.5 !important; }</style>');
                    $('#nothing_opportunity').modal('show');
                } else {
                    // Nếu jQuery chưa được tải, đợi một chút và thử lại
                    setTimeout(() => {
                        if (typeof $ !== 'undefined' && typeof $.fn.modal !== 'undefined') {
                            // Thêm style cho modal backdrop
                            $('body').append('<style>.modal-backdrop { opacity: 0.5 !important; }</style>');
                            $('#nothing_opportunity').modal('show');
                        } else {
                            console.error("jQuery or Bootstrap modal not available");
                        }
                    }, 500);
                }
                return;
            }

            // Nếu có opportunity nhưng chưa chọn, hiển thị thông báo lỗi
            if(!this.selectedOpportunity) {
                window.toastr.error('案件を選択してください');
                return;
            }

            // Kiểm tra xem opportunity đã được chọn có bị disabled không
            const selectedOpp = this.allOpportunities.find(opp => opp.id === this.selectedOpportunity);
            if (selectedOpp && selectedOpp.disabled) {
                window.toastr.error('この案件はすでにスカウト済みです');
                return;
            }

            try{
                const response = await fetch(`/api/scout_resume`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        resume_id: this.resumeId,
                        opportunity_id: this.selectedOpportunity,
                        user_id: this.user_id,
                    })
                });

                const result = await response.json();
                if (result.result.success) {
                    await this.createBooking();
                } else {
                    window.toastr.error('スカウトを作成できませんでした');
                }
            } catch (error) {
                console.error("Error fetching opportunities:", error);
            }
        },

        async createBooking() {
            try{
                const res_interview = await fetch(`/api/create_booking`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        opportunities_id: this.selectedOpportunity,
                        resumes_apply: [this.resumeId],
                        created_by: this.user_id,
                        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                    })
                })
                const result_interview = await res_interview.json();
                if (result_interview.result.success) {
                    window.location.href = "/mypage";
                }
                else {
                    window.toastr.warning(`Apply failed: ${result_interview.result.message}`);
                }
            }catch(error){
                console.error("Error fetching opportunities:", error);
            }
        }
    }
}
export default opportunities_scout;