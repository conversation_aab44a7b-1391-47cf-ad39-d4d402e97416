from odoo import http
from odoo.http import request, Response
from datetime import datetime, timedelta
import json
import os
from . import common as global_common
import logging

_logger = logging.getLogger(__name__)

class planController(http.Controller):
    @http.route('/api/get_plan', type='http', auth="public", methods=['GET'], csrf=False)
    def get_plan(self):
        _logger.info("Getting plan data for pricing table")
        plans = request.env['vit.plans'].sudo().search([])
        if not plans:
            return Response(
                    json.dumps({'success': False, 'message': 'Plan not found'}),
                    content_type='application/json',
                    status=400
                )

        # Tạo cấu trúc dữ liệu cho bảng pricing
        pricing_data = []

        # Tạo dữ liệu cho dòng "費用 - 月額"
        monthly_data = {
            'title': '月額',
            'free_plan': '0円',
            'standard_plan': '5,000円',
            'premium_plan': '80,000円'
        }

        # Tạo dữ liệu cho dòng "費用 - 年額"
        yearly_data = {
            'title': '年額',
            'free_plan': '0円',
            'standard_plan': '55,000円',
            'premium_plan': '880,000円'
        }

        # Tạo dữ liệu cho các tính năng
        features_data = [
            {
                'title': '案件/人材を探す',
                'free_plan': '以下、機能制限\n・掲載表示\n・応募\n・スカウト',
                'standard_plan': '全ての機能',
                'premium_plan': '全ての機能'
            },
            {
                'title': 'コンシェルジュ',
                'free_plan': '無',
                'standard_plan': '無',
                'premium_plan': '有'
            },
            {
                'title': '会社引き合わせサービス',
                'free_plan': '無',
                'standard_plan': '30,000円/社',
                'premium_plan': '無料 *1'
            },
            {
                'title': '会社紹介',
                'free_plan': '無',
                'standard_plan': '30,000円',
                'premium_plan': '無料 *2'
            }
        ]

        pricing_data = [monthly_data, yearly_data] + features_data

        _logger.info('pricing_data: %s', pricing_data)

        return Response(
                json.dumps({'success': True, 'message': 'get plan successful', 'plans': pricing_data}),
                content_type='application/json',
                status=200
            )

    @http.route('/api/get_plan_options', type='http', auth="public", methods=['GET'], csrf=False)
    def get_plan_options(self):
        """
        API để lấy danh sách các plan cho dropdown
        Trả về danh sách các plan với định dạng:
        - スタンダード(月額):5,000円
        - スタンダード(年額):55,000円
        - プレミアム(月額):80,000円
        - プレミアム(年額):880,000円
        """
        _logger.info("Fetching plan options for dropdown - API called successfully")

        # Lấy dữ liệu từ database
        plans = request.env['vit.plans'].sudo().search([])
        if not plans:
            return Response(
                    json.dumps({'success': False, 'message': 'Plan not found'}),
                    content_type='application/json',
                    status=400
                )

        plan_options = ['未選択']
        plan_options_mapping = [None]


        # Thêm các plan khác nếu có
        for plan in plans:
            if plan.title:
                plan_options.append(f'{plan.title}:{int(plan.price):,}円'.replace(',', '.'))
                plan_options_mapping.append(plan.id)

        return Response(
                json.dumps({'success': True, 'message': 'get plan options successful', 'plan_options': plan_options, 'plan_options_mapping': plan_options_mapping}),
                content_type='application/json',
                status=200
            )