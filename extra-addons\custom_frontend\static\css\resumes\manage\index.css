/*! CSS Used fontfaces */
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format("woff2");
}

/* Mobile responsive for resumes/manage/index - MOBILE ONLY */
@media (max-width: 767px) {
    /* 1. Search button - change to floating icon */
    .search-toggle {
        position: fixed !important;
        top: 32% !important;
        right: 15px !important;
        z-index: 999 !important;
        background: #1072e9 !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        width: 50px !important;
        height: 50px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
        cursor: pointer !important;
        font-size: 0 !important;
        text-indent: -9999px !important;
        overflow: hidden !important;
        text-decoration: none !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .search-toggle::before {
        content: "search" !important;
        font-family: "Material Icons" !important;
        font-size: 24px !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        text-indent: 0 !important;
    }

    /* 2. Table container - completely fixed frame like active page */
    .row.d-none.d-md-block {
        display: block !important;
        position: relative !important;
        margin: 20px 0 0 0 !important;
        padding: 0 !important;
        border-radius: 0.5rem !important;
        height: 282px !important; /* Fixed height for table container */
        overflow: hidden !important; /* Hide outer scroll completely */
        width: calc(100vw - 26px) !important; /* Full width minus margins */
        left: -2px !important;
    }

    /* Table wrapper with internal scroll only */
    .row.d-none.d-md-block .col-12 {
        height: 100% !important;
        overflow-x: auto !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Override d-less-than-md-none to show table on mobile */
    .row.d-none.d-md-block .table.d-less-than-md-none {
        display: table !important;
        min-width: 800px !important;
        margin-bottom: 0 !important;
        height: auto !important;
    }

    /* Table header - keep original PC colors, make sticky within container */
    .row.d-none.d-md-block .table thead th {
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
        font-size: 12px !important;
        padding: 8px 4px !important;
        background-color: #1072e9 !important; /* Keep original header color */
    }

    .row.d-none.d-md-block .table tbody td {
        font-size: 12px !important;
        padding: 8px 4px !important;
        white-space: nowrap !important;
        border: 1px solid #dee2e6 !important;
    }

    /* 3. Hide mobile cards - use table instead, BUT KEEP TAB CONTAINER AND SEARCH BUTTON */
    .d-md-none:not(.tab-container):not(.tab-button):not(.search-toggle) {
        display: none !important;
    }

    /* Force show search button on mobile */
    .col-12.d-block.d-md-none.mb-4 {
        display: block !important;
    }

    /* Force show tab container and buttons on mobile */
    .container-fluid .tab-container {
        display: flex !important;
        visibility: visible !important;
    }

    .container-fluid .tab-container .tab-button {
        visibility: visible !important;
    }

    /* 4. Tab container adjustments - FORCE DISPLAY ON MOBILE */
    .tab-container {
        display: flex !important; /* Force display on mobile */
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 15px !important;
        margin-top: 150px !important;
        justify-content: flex-start !important;
    }

    .tab-button {
        height: 30px !important;
        font-size: 14px !important;
        width: 48% !important;
        pointer-events: auto !important; /* Enable click on mobile */
        cursor: pointer !important;
        opacity: 1 !important;
        background-color: #f8f9fa !important;
        text-align: center !important;
    }

    .tab-button.active {
        background-color: #1072e9 !important;
    }

    .tab-button:hover {
        opacity: 0.9 !important;
        text-decoration: none !important;
    }

    /* 5. Results and sort container - horizontal layout */
    .d-flex.align-items-center.my-4 {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-top: 37px !important;
        margin-bottom: 65px !important;
    }

    /* 6. Pagination adjustments */
    .pagination {
        justify-content: center !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        margin: 20px 0 !important;
        padding-left: 13px !important;
        padding-right: 15px !important;
    }

    .page-link {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.875rem !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Fix z-index conflicts and overlapping */
    .tab-container {
        z-index: 1 !important;
        position: relative !important;
    }

    .d-flex.align-items-center.my-4 {
        z-index: 1 !important;
        position: relative !important;
    }

    .pagination {
        z-index: 1 !important;
        position: relative !important;
    }

    /* Ensure search form has highest z-index */
    #side-search {
        z-index: 10000 !important;
    }

    #side-search.active {
        z-index: 10001 !important;
    }

    .search-fixed-btn {
        z-index: 10002 !important;
    }

    /* 7. Dropdown mobile styling */
    .divCol-12.col-md-8.d-block {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 13px !important;
        padding-right: 15px !important;
        margin-bottom: 15px !important;
    }

    .divCol-12.col-md-8.d-block select,
    .divCol-12.col-md-8.d-block .dropdown-toggle {
        width: 100% !important;
        font-size: 14px !important;
        padding: 12px 15px !important;
        border-radius: 6px !important;
        border: 1px solid #ced4da !important;
        background-color: #fff !important;
        color: #495057 !important;
        height: auto !important;
        min-height: 45px !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-menu {
        width: 100% !important;
        max-height: 250px !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        border-radius: 6px !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
        z-index: 1050 !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-item {
        padding: 10px 15px !important;
        font-size: 14px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-item:hover,
    .divCol-12.col-md-8.d-block .dropdown-item:focus {
        background-color: #f8f9fa !important;
        color: #1072e9 !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-item.active {
        background-color: #1072e9 !important;
        color: white !important;
    }
}

@media (max-width: 768px){
  /* Modal backdrop */
  #side-search.active{
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.5) !important; /* Dark backdrop */
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
  }

  /* Modal content container */
  #side-search.active .card {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    max-width: 400px !important;
    width: 100% !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    margin: 0 !important;
    position: relative !important;
  }

  #side-search.active .container{
    max-width: 100% !important;
    padding: 0 !important;
  }

  /* Hide mobile search buttons - use PC button instead */
  #side-search.active .search-fixed-btn{display: none !important;}

  /* Show PC search button on mobile when form is active */
  #side-search.active .d-none.d-md-block {
    display: block !important;
  }

  #side-search.active .side-card {
    padding: 20px !important;
    width: 100% !important;
  }

  /* Form styling to match PC version */
  #side-search.active .form-control {
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 14px !important;
  }

  #side-search.active .custom-control-label {
    font-size: 14px !important;
    color: #495057 !important;
  }

  #side-search.active .font-middle {
    font-size: 16px !important;
    font-weight: 500 !important;
    margin-bottom: 12px !important;
  }

  /* Compact spacing for modal */
  #side-search.active .mx-auto.mb-5 {
    margin-bottom: 1rem !important;
  }

  #side-search.active .mx-auto.mt-0.mb-3 {
    margin-bottom: 1rem !important;
  }

  /* Search button styling */
  #side-search.active .btn {
    padding: 8px 20px !important;
    font-size: 14px !important;
  }
}
