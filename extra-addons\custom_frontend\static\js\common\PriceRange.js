const PriceRange = {
    props: {
        min: { type: Number, default: 0 },
        max: { type: Number, default: 300 },
        step: { type: Number, default: 5 },
        startValues: {
            type: Array,
            default: () => [null, null] // null = không giới hạn
        },
        margin: { type: Number, default: 5 },
    },
    data() {
        return {
            selectedRange: [
                Array.isArray(this.startValues) && this.startValues.length > 0 ? (this.startValues[0] ?? this.min) : this.min,
                Array.isArray(this.startValues) && this.startValues.length > 1 ? (this.startValues[1] ?? this.max) : this.max
            ],
            slider: null,
        };
    },
    watch: {
        startValues: {
            handler(newValues) {
                if (!Array.isArray(newValues)) return;

                // Chỉ cập nhật nếu giá trị thực sự thay đổi
                const newMin = newValues.length > 0 ? (newValues[0] ?? this.min) : this.min;
                const newMax = newValues.length > 1 ? (newValues[1] ?? this.max) : this.max;

                if (newMin !== this.selectedRange[0] || newMax !== this.selectedRange[1]) {
                    this.selectedRange = [newMin, newMax];
                    if (this.slider) {
                        this.slider.set(this.selectedRange);
                    }
                }
            },
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) { // Đảm bảo chỉ mount một lần và ref đã được tạo
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedRange,
                    connect: true,
                    range: { min: this.min, max: this.max },
                    step: this.step,
                    margin: this.margin,
                });

                this.slider.on("update", (values, handle) => {
                    let newValue = Math.round(values[handle]);

                    if (handle === 0) {
                        this.selectedRange[0] = newValue;
                    } else {
                        this.selectedRange[1] = newValue;
                    }
                    // Kiểm tra nếu là giá trị tối thiểu/tối đa thì đặt thành null
                    const priceMin = this.selectedRange[0] === this.min ? null : this.selectedRange[0];
                    const priceMax = this.selectedRange[1] === this.max ? null : this.selectedRange[1];

                    // Chỉ emit nếu có sự thay đổi
                    this.$emit("update:price_min", priceMin !== null ? priceMin : this.min);
                    this.$emit("update:price_max", priceMax !== null ? priceMax : this.max);
                });
            }
        });
    },
    computed: {
        displayMin() {
            return this.selectedRange[0] === this.min ? "下限なし" : this.selectedRange[0] + "万円";
        },
        displayMax() {
            return this.selectedRange[1] === this.max ? "上限なし" : this.selectedRange[1] + "万円";
        },
        displayRange() {
            return `${this.displayMin} 〜 ${this.displayMax}`;
        },
    },
    template: `
      <div class="mb-2">
        <p class="d-flex justify-content-center" id="price-range">
          {{ displayRange }}
        </p>
        <div class="slider-styled mx-4 noUi-target noUi-ltr noUi-horizontal noUi-txt-dir-ltr" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>下限なし</span><span>上限なし</span>
        </div>
      </div>
    `,
};

export default PriceRange;
