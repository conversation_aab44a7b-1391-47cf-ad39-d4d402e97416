import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const ResumeDetail = {
    template: `
<main class="margin-header sp_fluid" id="vue-app" style="margin-top: 80px; background-color: white">
    <div class="container-fluid d-none d-md-block">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '探す', link: null },
            { text: '案件・人財を探す', link: null },
            { text: '人財を探す', link: '/resumes/active' },
            { text: '{{initial_name}}さん　{{Gender}}性　{{Age}}歳', link: null, current: true }
        ], 'container-fluid', '')}
    </div>
    <div class="container-fluid grabient pt-3">
        <div class="row">
            <div class="col-12 col-md-10 col-lg-8 mx-auto">
                <div class="mb-4 card sp_sides_uniter">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <!-- Bookmark -->
                            <div class="d-inline-block nowrap" data-toggle="tooltip" title=""
                                data-original-title="ブックマーク">
                                <a id="anchor-bookmark48810" class="z-2 py-2" data-remote="true" rel="nofollow"
                                    @click="toggleBookmark">
                                    <i class="material-icons ml-3" style="color: #1072e9;">
                                        {{ isBookmarked ? 'bookmark' : 'bookmark_border' }}
                                    </i>
                                </a>
                                <span class="pl-1 font-middle z-2 py-2 pr-2">{{ numberReact }}</span>
                            </div>

                            <!-- Nội dung chính -->
                            <h2 class="font-extralarge mb-1 ml-3">
                                <span class="d-md-inline-block mr-3">{{ initial_name }}さん {{ Gender }}性 {{ Age
                                    }}歳</span>
                                <span class="d-md-inline-block">
                                    <a @click="scrollToEvaluation" v-if="!isOwner">
                                        <span
                                            class="badge-pill font-small border px-3 text-center align-baseline border-default text-default"
                                            :style="{ backgroundColor: evaluations.length > 0 ? '#eee' : '' }">
                                            {{ evaluations.length > 0 ? '評価済' : '未評価' }}
                                        </span>
                                    </a>
                                </span>
                                <br>
                                <span class="small">
                                    （ <span v-if="UpdateAt" class="mr-2">更新：{{ UpdateAt }}</span><span>登録：{{ CreateAt }}</span> ）
                                </span>
                            </h2>
                        </div>


                    </div>

                    <div class="card-body resume-detail-sp">
                        <div class="px-3 pb-5">
                            <div>
                                <span
                                    class="badge font-default bg-grey-6 mr-2 z-depth-0 vertical-middle mt-2">リーダー経験あり</span>
                            </div>
                            <div class="row flex-row-reverse">
                                <div class="col-12">
                                    <div class="card my-4 bg-grey-1 z-depth-0">
                                        <div class="card-body px-md-4">
                                            <table>
                                                <tbody>
                                                    <tr class="d-block d-md-table-row">
                                                        <th
                                                            class="pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block d-md-table-cell">
                                                            稼働可能状況 </th>
                                                        <td
                                                            class="pt-1 pb-3 pb-md-1 pl-md-4 font-middle d-block d-md-table-cell">
                                                            <div v-if="ResumeStatus === 'work_available'"
                                                                class="ml-md-3">即日可</div>
                                                            <div v-if="ResumeStatus === 'will_be_available'"
                                                                class="ml-md-3 font-weight-normal">今後可（{{ new
                                                                Date(DatePeriod).toLocaleString("ja-JP", { year:
                                                                '2-digit', month: '2-digit', day: '2-digit', hour12:
                                                                false }) }}~ ）</div>
                                                            <div v-if="ResumeStatus === 'depends_on_opportunities'"
                                                                class="ml-md-3">要相談</div>
                                                            <div v-if="ResumeStatus === 'not_corresponding'"
                                                                class="ml-md-3">対応不可</div>
                                                        </td>
                                                    </tr>
                                                    <tr class="d-block d-md-table-row">
                                                        <th
                                                            class="pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block d-md-table-cell">
                                                            可能な契約形態 </th>
                                                        <td
                                                            class="pt-1 pb-3 pb-md-1 pl-md-4 font-middle d-block d-md-table-cell">
                                                            <div class="ml-md-3 font-weight-normal">{{ Contract }}</div>
                                                        </td>
                                                    </tr>
                                                    <tr class="d-block d-md-table-row">
                                                        <th
                                                            class="pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block d-md-table-cell">
                                                            国籍 </th>
                                                        <td
                                                            class="pt-1 pb-3 pb-md-1 pl-md-4 font-middle d-block d-md-table-cell">
                                                            <div class="ml-md-3 font-weight-normal"> {{ Nationality }}
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr class="d-block d-md-table-row">
                                                        <th
                                                            class="pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block d-md-table-cell align-top">
                                                            所属 </th>
                                                        <td
                                                            class="pt-1 pb-3 pb-md-1 pl-md-4 font-middle d-block d-md-table-cell">
                                                            <div class="ml-md-3">
                                                            {{ (partner_types || '').split(',').map(rate => mapping_partner_types[partner_types] || '').join('') || '' }}
                                                             </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-3">
                            <div class="row">
                                <div class="col-12 col-sm-6">
                                    <h3 class="font-middle heading-label">経験・資格</h3>
                                </div>
                                <div class="col-12 px-4 px-md-5">
                                    <div class="col-12 border-bottom pl-0 py-1">
                                        <div class="row align-items-start pt-3 pb-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">得意領域</div>
                                            <div>
                                                <div class="col-12 col-md-12 font-middle sm-d-block pt-1 pt-md-0 pl-3 pr-0"
                                                    v-if="labelConsul">
                                                    <div class="mb-3 d-md-flex">
                                                        <span
                                                            class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">コンサル</span>
                                                        <div>
                                                            <template v-for="(item, index) in labelConsul">
                                                                <span class="d-inline-block"
                                                                    v-if="index < labelConsul.length-1">{{ item }} /
                                                                </span>
                                                                <span class="d-inline-block" v-else>{{ item }}</span>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-12 font-middle sm-d-block pt-1 pt-md-0 pl-3 pr-0"
                                                    v-if="labelDev">
                                                    <div class="mb-3 d-md-flex">
                                                        <span
                                                            class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">開発</span>
                                                        <div>
                                                            <template v-for="(item, index) in labelDev" :key="index">
                                                                <span class="d-inline-block"
                                                                    v-if="index < labelDev.length-1">{{ item }}/</span>
                                                                <span class="d-inline-block" v-else>{{ item }}</span>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-12 font-middle sm-d-block pt-1 pt-md-0 pl-3 pr-0"
                                                    v-if="labelInfra">
                                                    <div class="mb-3 d-md-flex">
                                                        <span
                                                            class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">インフラ</span>
                                                        <div>
                                                            <template v-for="(item, index) in labelInfra">
                                                                <span class="d-inline-block"
                                                                    v-if="index < labelInfra.length-1">{{ item }} /
                                                                </span>
                                                                <span class="d-inline-block" v-else>{{ item }}</span>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-12 font-middle sm-d-block pt-1 pt-md-0 pl-3 pr-0"
                                                    v-if="labelDesign">
                                                    <div class="mb-3 d-md-flex">
                                                        <span
                                                            class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">デザイン</span>
                                                        <div>
                                                            <template v-for="(item, index) in labelDesign">
                                                                <span class="d-inline-block"
                                                                    v-if="index < labelDesign.length-1">{{ item }} /
                                                                </span>
                                                                <span class="d-inline-block" v-else>{{ item }}</span>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 border-bottom pl-0 py-1">
                                        <div class="row align-items-start py-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">資格</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3 pr-0">
                                                <template v-for="(item, index) in Qualification">
                                                    <p>{{ item }}</p>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 pl-0 py-1">
                                        <div class="row align-items-start py-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">経験PR</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3 pr-0">
                                                <p>{{ ExperiencePR }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-md-center mt-3 mb-0 mb-md-4">
                                <div class="col-12 col-md-6 text-center mb-2 mb-md-1 px-4">
                                    <button id="skillsheet_download_flg"
                                        class="btn btn-default btn-block btn-lg waves-effect waves-light" type="button"
                                        style="color: white" @click="fetchCV">スキルシートダウンロード</button>
                                    <div aria-labelledby="skillsheet_download_modal" class="modal"
                                        id="skillsheet_download" tabindex="-1" style="display: none;"
                                        aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title w-100" id="trouble_confirm_modal">
                                                        1チケットを消費してスキルシートをダウンロードします。</h5>
                                                    <button aria-label="Close" class="close" data-dismiss="modal"
                                                        type="button">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>チケット残数は <span class="deep-orange-text px-1">60</span>チケットです。 </p>
                                                    <div class="row">
                                                        <div class="col-12 col-md-6 mb-2 mb-md-0">
                                                            <p class="btn btn-outline-blue-grey btn-block px-0 trouble-cancel"
                                                                data-dismiss="modal">キャンセル</p>
                                                        </div>
                                                        <div class="col-12 col-md-6">
                                                            <a id="with_ticket_download_btn"
                                                                class="btn btn-default btn-block px-0 mb-0"
                                                                target="_blank" data-target="48817"
                                                                href="/resumes/48817/skillsheet_download?message_room_id=">
                                                                <span style="color: white">スキルシートをダウンロード</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 col-sm-6">
                                    <h3 class="font-middle heading-label">希望条件</h3>
                                </div>
                                <div class="col-12 px-4 px-md-5">
                                    <div class="border-bottom pl-0 py-1">
                                        <div class="row align-items-start pt-3 pb-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">単価
                                                <div class="align-baseline d-inline-block"
                                                    data-html="true" data-toggle="tooltip" title=""
                                                    data-original-title="稼働率100%（フル稼働）換算での単価が表示されています。※稼働率に応じた単価ではありません。">
                                                    <i class="material-icons md-dark pl-2">help</i>
                                                </div>
                                            </div>
                                            <div class="col-12 col-md-9 pt-1 pt-md-0 font-middle pl-3">{{ UnitPriceMin
                                                }}万円 〜 {{ UnitPriceMax }}万円 / 月 </div>
                                        </div>
                                    </div>
                                    <div class="col-12 border-bottom pl-0 py-1">
                                        <div class="row align-items-start pt-3 pb-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">稼働率</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3">
                                                <template v-for="(item, index) in labelUtiRate">
                                                    <span class="d-inline-block" v-if="index < labelUtiRate.length-1">{{
                                                        item }} / </span>
                                                    <span class="d-inline-block" v-else>{{ item }}</span>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 border-bottom pl-0 py-1">
                                        <div class="row align-items-start pt-3 pb-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">出社頻度</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3">
                                                <template v-for="(item, index) in labelWorkFrenq">
                                                    <span class="d-inline-block"
                                                        v-if="index < labelWorkFrenq.length-1">{{ item }} / </span>
                                                    <span class="d-inline-block" v-else> {{ item }}</span>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 border-bottom pl-0 py-1">
                                        <div class="row align-items-start pt-3 pb-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">就業場所</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3 pr-0">
                                                <template v-for="(item, index) in Workplace">
                                                    <span class="d-inline-block" v-if="index < Workplace.length-1">{{
                                                        item }} / </span>
                                                    <span class="d-inline-block" v-else>{{ item }}</span>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 border-bottom pl-0 py-1">
                                        <div class="row align-items-start pt-3 pb-2">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">駅・地域</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3 pr-0">
                                                <p class="mb-0">{{ Region }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 pl-0 py-1">
                                        <div class="row align-items-start pt-3">
                                            <div class="col-12 col-md-3 custom-grey-5-text ex-bold pl-3">詳細</div>
                                            <div class="col-12 col-md-9 font-middle pt-1 pt-md-0 pl-3 pr-0">
                                                <p>{{ WorkHopefully }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div aria-hidden="true" aria-labelledby="belonging_description_modal" class="modal"
                            id="belonging_description_modal" role="dialog" tabindex="-1">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title w-100"></h4>
                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                            <span aria-hidden="true">
                                                <i class="material-icons md-dark mb-36"> clear </i>
                                            </span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div>
                                            <picture>
                                                <source
                                                    srcset="/assets/affiliation_detail_sp-62ebf8b86bae5226928f0d85ee3321ca7924a9f568a9ecc87a9942ae2f1008d0.png"
                                                    media="(max-width: 767px)">
                                                <img
                                                    src="/assets/affiliation_detail-a71ed481f34669972813d2c3d7939ddf757546e4a67ddd5c4c1c978c88c7de37.png">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <div class="col-6">
                                            <button
                                                class="btn btn-default btn-block waves-effect px-0 waves-light btn-blue-grey"
                                                type="button" data-dismiss="modal">閉じる</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div aria-hidden="true" aria-labelledby="request_resume_update_exp_modal" class="modal"
                            data-keyboard="false" id="request_resume_update_exp_modal" role="dialog" tabindex="-1">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h6 class="modal-title font-weight-bold">「もっと知りたい」リクエストを送信しますか？</h6>
                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                            <span aria-hidden="true">
                                                <i class="material-icons md-dark mb-36">clear</i>
                                            </span>
                                        </button>
                                    </div>
                                    <div class="modal-body text-left pb-0 px-3 px-md-5">
                                        <ul class="disc pl-3 pl-md-5">
                                            <li>リクエストが先方に通知されます。（リクエストした方の情報は表示されません。）</li>
                                            <li>人財が更新されるとリクエストした方に通知され、リクエストは解除されます。</li>
                                            <li>商談は禁止されています。会社名・氏名などは名乗らず、連絡先の記載等はご遠慮下さい。</li>
                                        </ul>
                                        <form novalidate="novalidate"
                                            action="/resumes/ajax_create_resume_update_request" accept-charset="UTF-8"
                                            method="post">
                                            <input name="utf8" type="hidden" value="✓" autocomplete="off">
                                            <input type="hidden" name="authenticity_token"
                                                value="bURvkc2Ouil+zddhGcqrpfUeDYlFEM5LnFtAGXDhq2N9go3FSMoR5Okbime6cTwfKQCF8YMzjEov0Lk2aHXHGw=="
                                                autocomplete="off">
                                            <div class="px-3 px-md-4">
                                                <div class="mx-auto mb-1">
                                                    <div class="">
                                                        <label class="font-middle mb-3" for="">リクエスト内容</label>
                                                    </div>
                                                    <textarea v-model="NotifMessage"
                                                        rows="3" bg_grey_class="bg-grey-1"
                                                        pick_target_class="exp_request_comment"
                                                        class="form-control exp_request_comment bg-grey-1"
                                                        autocomplete="off" id="request_comment_field"
                                                        name="request_comment"></textarea>
                                                </div>
                                                <div class="show-count-wrapper">
                                                    <CharacterCounter targetId="request_comment_field"
                                                        :max-length="50" />
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer mt-3 flex-wrap flex-md-nowrap px-1 px-md-5">
                                        <div class="col-12 col-md-6">
                                            <button aria-label="Close"
                                                class="btn btn-outline-default btn-block px-0 waves-effect waves-light"
                                                data-dismiss="modal" type="button"
                                                @click="closeModal('request_resume_update_exp_modal')">キャンセル</button>
                                        </div>
                                        <div class="col-12 col-md-6">
                                            <button
                                                class="btn btn-default btn-block px-0 request_resume_update_submit waves-effect waves-light"
                                                data-dismiss="modal" data-item="experience_qualification" type="button"
                                                @click="sendNotif" style="color: white">送信</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="CompanySettings !== 'private'" class="card my-4 sp_sides_uniter" id="publisher_information_48810">
                    <div class="card-body resume-detail-sp">
                        <div class="mx-auto">
                            <div class="row overflow-hidden w-100 mx-auto">
                                <div class="col-12">
                                    <h3 class="font-middle mb-3 mr-sm-4 mt-3 heading-label">掲載者情報</h3>
                                    <div class="col-12 pl-0 py-1 ml-2 ml-md-3 mb-3">
                                        <div class="align-items-center py-2">
                                            <div class="ex-bold">
                                                <a :href="'/company/' + CompanyId + '/detail'">会社詳細</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card mb-4 sp_sides_uniter evaluation-section" id="resume_evaluation" style="cursor: default"
                    v-if="!isOwner">
                    <div class="card-body resume-detail-sp d-flex flex-column">
                        <div class="px-3 pt-3">
                            <h3 class="font-middle float-sm-left mb-sm-0 mr-sm-4 heading-label">社内人財評価メモ</h3>
                        </div>
                        <div class="pt-3">
                            <div class="evaluation-form">
                                <div class="form-header">
                                    <div class="header-item">項目</div>
                                    <div class="header-stars">評価</div>
                                    <div class="header-reason">理由</div>
                                </div>

                                <div v-for="ev in evaluations" :key="ev.id" class="evaluation-item">
                                    <div class="ev-name">
                                        <span>{{ ev.name }}</span>
                                    </div>

                                    <div class="rating">
                                        <span v-for="n in 5" :key="n" class="star"
                                            :class="{ filled: n <= ev.rating }">★</span>
                                    </div>

                                    <div class="reason">
                                        <input v-model="ev.reason" type="text" class="reason-input" readonly />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div aria-hidden="true" aria-labelledby="internal_evaluation_modal-48810_modal" class="modal"
                    id="internal_evaluation_modal-48810" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                        <div class="modal-content internal_evaluation_modal">
                            <div class="modal-header">
                                <h4 class="modal-title w-100">メモを保存</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"
                                    @click="closeModal('internal_evaluation_modal-48810')">
                                    <span aria-hidden="true">
                                        <i class="material-icons md-dark mb-36">clear</i>
                                    </span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="input_internal_comment_field px-3">
                                    <form class="new_resume_evaluation" id="new_resume_evaluation"
                                        novalidate="novalidate" action="/resume_evaluations/manage/update_evaluation"
                                        accept-charset="UTF-8" method="post">
                                        <input name="utf8" type="hidden" value="✓" autocomplete="off">
                                        <input type="hidden" name="authenticity_token"
                                            value="bURvkc2Ouil+zddhGcqrpfUeDYlFEM5LnFtAGXDhq2N9go3FSMoR5Okbime6cTwfKQCF8YMzjEov0Lk2aHXHGw=="
                                            autocomplete="off">
                                        <div class="mb-5">
                                            <label class="font-middle no-label-bar">評価フラグ</label>
                                            <div class="my-3">
                                                <div class="form-check ">
                                                    <input class="form-check-input" id="evaluation_flag_field0"
                                                        type="radio" value="evaluation_flag_A"
                                                        name="resume_evaluation[evaluation_flag]">
                                                    <label id="evaluation_flag_field_label_0" class="form-check-label"
                                                        for="evaluation_flag_field0">A評価</label>
                                                </div>
                                                <div class="form-check ">
                                                    <input class="form-check-input" id="evaluation_flag_field1"
                                                        type="radio" value="evaluation_flag_B"
                                                        name="resume_evaluation[evaluation_flag]">
                                                    <label id="evaluation_flag_field_label_1" class="form-check-label"
                                                        for="evaluation_flag_field1">B評価</label>
                                                </div>
                                                <div class="form-check ">
                                                    <input class="form-check-input" id="evaluation_flag_field2"
                                                        type="radio" value="evaluation_flag_C"
                                                        name="resume_evaluation[evaluation_flag]">
                                                    <label id="evaluation_flag_field_label_2" class="form-check-label"
                                                        for="evaluation_flag_field2">C評価</label>
                                                </div>
                                                <div class="form-check ">
                                                    <input class="form-check-input" id="evaluation_flag_field3"
                                                        type="radio" value="evaluation_flag_yet" checked="checked"
                                                        name="resume_evaluation[evaluation_flag]">
                                                    <label id="evaluation_flag_field_label_3" class="form-check-label"
                                                        for="evaluation_flag_field3">未評価</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-5">
                                            <div class="">
                                                <label class="mb-3 font-middle no-label-bar" for="">コメント</label>
                                            </div>
                                            <textarea rows="5" class="form-control" autocomplete="off"
                                                id="comment_field" name="resume_evaluation[comment]"></textarea>
                                        </div>
                                    </form>
                                    <div class="row">
                                        <div class="col-6">
                                            <a aria-label="Close"
                                                class="btn btn-outline-blue-grey btn-block px-0 memo_cxl_btn waves-effect waves-light"
                                                data-dismiss="modal"
                                                @click="closeModal('internal_evaluation_modal-48810')">キャンセル</a>
                                        </div>
                                        <div class="col-6">
                                            <button
                                                class="btn btn-default btn-block px-0 memo-btn comment_save_btn waves-effect waves-light"
                                                data-dismiss="modal" id="comment_save_48810" type="submit"
                                                style="color: white">保存</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-center flex-column-reverse flex-md-row">
                    <div class="col-12 col-md-6 px-0 pr-md-2" v-if="res.prev != null">
                        <h6 class="mt-sm-3 mt-4 mb-3 text-center">前の人財情報</h6>
                        <div class="card hoverable font-middle mb-2">
                            <a :href="'/resumes/' + res.prev.id + '/detail'">
                                <div class="p-3 bg-light-green text-default card-header">
                                    {{res.prev.name}}さん　{{res.prev.gender}}性　{{res.prev.age}}歳</div>
                                <div class="card-content prev_next_class default-color-text" style="height: 136px;">
                                    <span class="custom-grey-5-text">経験PR</span>
                                    <p class="mb-2 shorten-into-1-line">{{res.prev.epr}}</p>
                                    <span class="custom-grey-5-text">希望詳細</span>
                                    <p class="mb-0 shorten-into-1-line">{{res.prev.workHope}}</p>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="col-12 col-md-6 px-0 pl-md-2 mb-3 mb-md-0" v-if="res.next != null">
                        <h6 class="my-3 text-center">次の人財情報</h6>
                        <div class="card hoverable font-middle mb-2">
                            <a :href="'/resumes/' + res.next.id + '/detail'">
                                <div class="p-3 bg-light-green text-default card-header">
                                    {{res.next.name}}さん　{{res.next.gender}}性　{{res.next.age}}歳</div>
                                <div class="card-content prev_next_class default-color-text" style="height: 136px;">
                                    <span class="custom-grey-5-text">経験PR</span>
                                    <p class="mb-2 shorten-into-1-line">{{res.next.epr}}</p>
                                    <span class="custom-grey-5-text">希望詳細</span>
                                    <p class="mb-0 shorten-into-1-line">{{res.next.workHope}}</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-12 mt-5 mb-4">
                        <a class="btn btn-blue-grey d-block font-default d-md-inline-block waves-effect waves-light m-0"
                            href="/resumes/active" style="color: white !important">人財検索に戻る</a>
                    </div>
                </div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                    id="display_text_format" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 class="modal-title w-100">人財詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                    <span aria-hidden="true">
                                        <i class="material-icons md-dark mb-36">clear</i>
                                    </span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************
                                    <br>◆人財ID: 012048635305 <br>◆氏名: U.N. 男性 50歳 <br>◆参画可能時期: 今後可（25/04/01 ~ ）
                                    <br>◆単価: 60万円 〜 65万円 / 月 <br>◆稼働率: 100%（フル稼働） <br>◆出社頻度: 週4 〜 2日出社 <br>◆就業場所: 大阪府 /
                                    奈良県 <br>◆所属: 自社社員 <br>◆経験PR: <br>【業務面】
                                    <br>システム要件整理とプロジェクトマネージメントの業務を中心にスキルを培ってきた人物です。
                                    <br>長年にわたるキャリアで得た知識と経験を駆使し、プロジェクトを無事故でリリースするに向けて戦略的かつ効率的な手法を提供してきました。
                                    <br>システム要件整理はプロジェクトの成功に不可欠と考え、要件定義プロセス全体を通して、お客様とのコミュニケーションを重視している方です。また、案件目標と技術的な実現可能性をバランス良く統合するよう心掛けてこられています。ユーザビリティ、拡張性、および費用対効果の観点から要件を検討し、プロジェクトの初期段階から持続的に、お客様に分かりやすい成果物を提供することを重視されています。
                                    <br>
                                    <br>プロジェクトマネージメントにおいては、リスクを予測・管理し、関係者との連携を行うことで、スムーズかつ、柔軟な対応をサポートしてこられました。
                                    <br>システム業界以外でも、様々な業界に携わり、異なる文化や要件に適応してきた経験のある方です。
                                    <br>チームワークと協力を大切にし、新しい方法への順応も対応可能です。 <br>キャリアの深さと幅を活かし、お客様へ最大限のメリットを提供いたします。 <br>
                                    <br>【人物面】 <br>提案力をもって、新しい観点からの解決策を提示することができる方です。
                                    <br>プロジェクトの舵取りを永年行ってきたこともあり、全体を見渡し、的確な判断を下します。
                                    <br>マネジメントにおいて柔軟性と厳格さのバランスを重視している方で、難題も謎解きの楽しさに変えるぐらいの雰囲気作りをし、チームメンバーが最高のパフォーマンスを発揮できる環境を築き、チームを結束させるよう心掛けている方です。
                                    <br>人財育成にも携わり、現職場で後輩の育成もされています。 <br>◆希望詳細: <br>
                                    <br>**********************************************************
                                </div>
                                <div class="text-center">
                                    <a aria-label="Close" class="btn btn-blue-grey waves-effect waves-light"
                                        data-dismiss="modal">閉じる</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if="isOwner"
                    class="fix_right_buttom d-md-flex justify-content-end align-items-center px-3 py-4 text-right w-100">
                    <div class="alert alert-warning w-100">ご自身または自社の人財です</div>
                </div>
                <div v-else
                    class="fix_right_buttom d-md-flex justify-content-end align-items-center px-3 py-4 text-right w-100">
                    <span class="vertical-baseline">
                        <a class="btn btn-default font-middle m-0 vertical-baseline waves-effect waves-light"
                           @click="goToScout">
                            スカウト<i class="material-icons md-18 pl-1 vertical-sub" style="line-height: 3vh !important;">send</i>
                        </a>
                    </span>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/resume_detail.css" />
<link rel="stylesheet" href="/custom_frontend/static/css/layout.css" />
    `,
    data() {
        return {
            evaluations: [],
            categoriesConsul: [
                { value: "consul_pmo", label: "PMO" },
                { value: "consul_pmpl", label: "PM・PL" },
                { label: "DX", value: "consul_DX" },
                { label: "クラウド", value: "consul_cloud" },
                { label: "モダナイゼション", value: "consul_modern" },
                { label: "セキュリティ", value: "consul_security" },
                { label: "ITインフラ", value: "consul_it" },
                { label: "AI", value: "consul_ai" },
            ],
            mapping_partner_types: {
				'subc': '協力会社社員（一社先）',
				'empl': '自社社員',
			},
            categoriesDev: [
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_emb", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
            ],
            categoriesInfra: [
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
            ],
            categoriesDesign: [
                { value: "design_business", label: "業務システム" },
                { value: "design_open", label: "オープン" },
                { value: "design_cloud", label: "クラウド" },
                { value: "design_mainfream", label: "メインフレーム" },
                { value: "design_helpdesk", label: "ヘルプデスク" },
            ],

            UtilizationRateLabels: [
                { label: "100%（フル稼働）", value: "100" },
                { label: "75%", value: "75" },
                { label: "50%", value: "50" },
                { label: "25%", value: "25" },
            ],
            WorkFrenquecyLabels: [
                { label: "週5日出社", value: "5days" },
                { label: "週4 〜 2日出社", value: "2to4days" },
                { label: "週1日出社", value: "1day" },
                { label: "週1日未満出社", value: "less_than_1day" },
                { label: "フルリモート", value: "full_remote" },
            ],
            categories: [
                { id: 1, name: '技術', rating: 3, reason: '', isEditing: false, editName: '' },
                { id: 2, name: '勤務態度', rating: 2, reason: '', isEditing: false, editName: '' },
                { id: 3, name: '積極性', rating: 5, reason: '', isEditing: false, editName: '' },
                { id: 4, name: 'リーダー力', rating: 1, reason: '', isEditing: false, editName: '' },
                { id: 5, name: 'ドキュメント', rating: 2, reason: '', isEditing: false, editName: '' },
                { id: 6, name: 'プレゼン力', rating: 3, reason: '', isEditing: false, editName: '' }
            ],

            //data fill
            Age: "",
            initial_name: "",
            Contract: "",
            labelConsul: [],
            labelDev: [],
            labelInfra: [],
            labelDesign: [],
            labelUtiRate: [],
            labelWorkFrenq: [],

            //data fields
            errMsg: "",
            partner_types:'',
            ResumeVisibility: "",
            ResumeStatus: "",
            DatePeriod: "",
            UserName: "",
            UserResumeId: "",
            InitialName: [],
            Gender: "",
            Birthday: "",
            Nationality: "",
            ContractType: "",
            isResidential: null,
            ConsultCastegories: [],
            DevCastegories: [],
            InfraCastegories: [],
            DesignCastegories: [],
            ExperiencePR: "",
            Qualification: [],
            Characteristic: [],
            UtilizationRate: [],
            UnitPriceMin: "",
            UnitPriceMax: "",
            Region: null,
            WorkFrenquecy: [],
            Workplace: [],
            WorkHopefully: null,
            CompanySettings: "",
            CreateAt: "",
            UpdateAt: "",
            CreateBy: "",
            UpdateBy: "",
            CompanyId: "",
            userID: "",
            isOwner: false,
            //Reaction
            isBookmarked: false,
            numberReact: 0,

            //Notifications
            NotifMessage: "",
            //prev next
            res: {
                prev: {
                    id: "",
                    name: "",
                    gender: "",
                    age: "",
                    epr: "",
                    workHope: ""
                },
                next: {
                    id: "",
                    name: "",
                    gender: "",
                    age: "",
                    epr: "",
                    workHope: ""
                }
            },

            prevName: [],
            prevGender: "",
            nextName: [],
            nextGender: "",
        }
    },

    mounted() {
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        this.get_res();
        this.get_prev_next_res();
        this.userID = userInfo ? userInfo.user_id : null;
        this.getReaction();
        this.getEvaluation();
    },
    props: ["id"],
    methods: {
        scrollToEvaluation() {
            const element = document.getElementById('resume_evaluation');
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        },

        // call api
        async get_res() {
            try {
                const res = await fetch(`/api/resume_detail?id=${this.id}`, {
                    method: "GET", // Hoặc "GET" nhưng phải đổi Controller
                    headers: { "Content-Type": "application/json" },
                });

                if (!res.ok) {
                    console.error("Error fetching resumes:", res.statusText);
                    return;
                }

                const result = await res.json();
                if (result.success) {
                    //console.log("get data successfully");
                    //console.log("result: ", result.data);
                    this.ResumeVisibility = result.data.resume_visibility;
                    this.partner_types = result.data.partner_types
                    this.ResumeStatus = result.data.status;
                    this.CompanyPublish = result.data.company_publish;
                    this.HumanPublish = result.data.human_publish;
                    this.UserResumeId = result.data.user_id;
                    this.UserName = result.data.user_name;
                    this.DatePeriod = result.data.date_period;
                    this.InitialName = result.data.initial_name.split(' ');
                    this.ContractType = result.data.contract_types ? result.data.contract_types.split(',') : "";
                    this.Gender = result.data.gender;
                    this.Birthday = result.data.birthday;
                    this.Nationality = result.data.nationality;
                    this.isResidential = result.data.resident;
                    this.ConsultCastegories = result.data.categories_consultation.split(",") || "";
                    this.DevCastegories = result.data.categories_development.split(",") || "";
                    this.InfraCastegories = result.data.categories_infrastructure.split(",") || "";
                    this.DesignCastegories = result.data.categories_design.split(",") || "";
                    this.ExperiencePR = result.data.experience_pr || "";
                    // Xử lý bằng cấp: đầu tiên tách theo dấu phẩy, sau đó tách mỗi phần tử theo dấu xuống dòng
                    if (result.data.qualification) {
                        const qualificationParts = result.data.qualification.split(",");
                        this.Qualification = [];

                        // Xử lý từng phần sau khi tách theo dấu phẩy
                        qualificationParts.forEach(part => {
                            // Nếu phần tử chứa dấu xuống dòng, tách thành nhiều phần tử
                            if (part.includes('\n')) {
                                const subParts = part.split('\n');
                                this.Qualification = [...this.Qualification, ...subParts.filter(item => item.trim() !== '')];
                            } else if (part.trim() !== '') {
                                this.Qualification.push(part);
                            }
                        });
                    } else {
                        this.Qualification = [];
                    }
                    this.Characteristic = result.data.characteristic || "";
                    this.UtilizationRate = result.data.utilization_rate.split(",") || "";
                    this.UnitPriceMin = result.data.unit_price_min;
                    this.UnitPriceMax = result.data.unit_price_max;
                    this.Region = result.data.region || "";
                    this.WorkFrenquecy = result.data.working_frequency.split(",") || "";
                    this.Workplace = result.data.working_location.split(",") || "";
                    this.WorkHopefully = result.data.working_hope || "";
                    this.CompanySettings = result.data.company_settings;
                    this.CreateAt = result.data.created_at;
                    this.CreateBy = result.data.created_by;
                    this.UpdateAt = result.data.updated_at;
                    this.CompanyId = result.data.company_id;
                    this.isOwner = this.userID == parseInt(this.CreateBy) ? true : false;
                    //console.log("Companyid: ", this.CompanyId)

                    this.dataSep();
                }
            } catch (error) {
                console.log(error);
            }
        },

        async get_prev_next_res() {
            try {
                const res = await fetch(`/api/resume_prev_next?id=${this.id}`, {
                    method: "GET",
                    headers: { "Content-Type": "application/json" },
                })

                const result = await res.json();
                if (result.success) {
                    //console.log("get data prev next successfully");
                    //console.log(result.data)
                    if (result.data.prev) {
                        this.res.prev.id = result.data.prev.id;
                        this.prevName = result.data.prev.initial_name ? result.data.prev.initial_name.split(" ") : [];
                        this.prevGender = result.data.prev.gender;
                        this.res.prev.age = this.calculateAge(result.data.prev.birthday);
                        this.res.prev.epr = result.data.prev.experience_pr || "";
                        this.res.prev.workHope = result.data.prev.working_hope ? result.data.prev.working_hope : "";

                        this.res.prev.name = (this.prevName[0] ? this.prevName[0] : "") + "." + (this.prevName[1] ? this.prevName[1] : "") + ".";
                        this.res.prev.gender = this.prevGender === 'male' ? '男' : '女';
                    } else {
                        this.res.prev = null;
                    }

                    if (result.data.next) {
                        this.res.next.id = result.data.next.id;
                        this.nextName = result.data.next.initial_name ? result.data.next.initial_name.split(" ") : [];
                        this.nextGender = result.data.next.gender;
                        this.res.next.age = this.calculateAge(result.data.next.birthday);
                        this.res.next.epr = result.data.next.experience_pr || "";
                        this.res.next.workHope = result.data.next.working_hope || "";

                        this.res.next.name = this.nextName[0] + "." + this.nextName[1] + ".";
                        this.res.next.gender = this.nextGender === 'male' ? '男' : '女';
                    } else {
                        this.res.next = null;
                    }
                }

            } catch (error) {
                console.log(error);
            }
        },

        //convert data
        calculateAge(birthday) {
			if (!birthday) return "N/A";

			// Xử lý chuỗi có dạng "YYYY-MM-DD HH:MM:SS"
			if (typeof birthday === "string") {
				birthday = birthday.split(" ")[0];
			}

			const birthDate = new Date(birthday);
			if (isNaN(birthDate.getTime())) return "N/A";

			const today = new Date();
			let age = today.getFullYear() - birthDate.getFullYear();

			const monthDiff = today.getMonth() - birthDate.getMonth();
			if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
				age--;
			}

			return age;
		},

        convertDateToJapanese(date) {
            if (!date) return null;
            const year = date.split('-')[0];
            const month = date.split('-')[1];  // Đảm bảo tháng luôn 2 chữ số
            const day = date.split('-')[2].split(' ')[0];  // Đảm bảo ngày luôn 2 chữ số
            return `${year}年${month}月${day}日`;
        },

        mapData(data, cate) {
            if (!Array.isArray(data)) return [];
            return data.map(value => {
                const item = cate.find(cat => cat.value === value);
                return item ? item.label : null;
            });
        },

        dataSep() {
            this.Age = this.calculateAge(this.Birthday);
            this.initial_name = this.InitialName[0] + '.' + this.InitialName[1] + '.';

            this.CreateAt = this.convertDateToJapanese(this.CreateAt);
            this.UpdateAt = this.convertDateToJapanese(this.UpdateAt);

            this.Nationality = this.Nationality === 'Japan' ?
                '日本' : this.Nationality;

            this.Gender = this.Gender === 'male' ? '男' : '女';
            this.ContractType = Array.isArray(this.ContractType) ? this.ContractType : [];
            this.ContractType.forEach((element, i) => {
                if (this.ContractType.length - 1 === i) {
                    this.Contract += (element === "temp" ? "派遣契約" : element === "subc" ? "業務委託（請負）" : "業務委託（準委任）");
                } else {
                    this.Contract += (element === "temp" ? "派遣契約" : element === "subc" ? "業務委託（請負）" : "業務委託（準委任）") + " / ";
                }
            });
            this.labelConsul = this.mapData(this.ConsultCastegories, this.categoriesConsul);
            this.labelDev = this.mapData(this.DevCastegories, this.categoriesDev);
            this.labelInfra = this.mapData(this.InfraCastegories, this.categoriesInfra);
            this.labelDesign = this.mapData(this.DesignCastegories, this.categoriesDesign);
            this.labelUtiRate = this.mapData(this.UtilizationRate, this.UtilizationRateLabels);
            this.labelWorkFrenq = this.mapData(this.WorkFrenquecy, this.WorkFrenquecyLabels);

        },

        async toggleBookmark() {
            try {
                const response = await fetch('/api/react_res', {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        user_id: this.userID,  // ID người dùng
                        resumes_id: this.id,  // ID cơ hội
                    }),
                });

                const result = await response.json();
                //console.log(result);
                if (result.result.success) {
                    this.getReaction();
                }
            } catch (error) {
                console.error("Bookmark failed:", error);
            }
        },

        async getReaction() {
            const response = await fetch("/api/react_count_res", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ 'user_id': this.userID, 'resumes_id': this.id })
            });

            const result = await response.json();
            //console.log("react: ", result);
            if (result.result.success) {
                this.numberReact = result.result.likes;
                this.isBookmarked = result.result.liked;
            }
        },

        async getEvaluation() {
            try {
                const response = await fetch('/api/get_evaluation_workflows_by_res', {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        user_id: this.userID,  // ID người dùng
                        resume_id: this.id,
                    }),
                })
                const result = await response.json();
                if (result.result.success) {
                    this.oppName = result.result.data.opp_name
                    this.evaluations = result.result.data.evaluations.map(item => ({
                        id: item.id,
                        name: item.name,
                        rating: item.score,
                        reason: item.reason,
                    }));
                }
            } catch (error) {

            }
        },

        async fetchCV() {
            try {
                console.log("fetchCV: ", this.id);
                const response = await fetch(`/api/resume/get_cv?resume_id=${this.id}`);
                const data = await response.json();

                if (data.success && data.file_url) {
                    console.log(data.file_name);
                    this.downloadFile(data.file_url, data.file_name);
                } else {
                    alert(data.message || "Không tìm thấy CV.");
                }
            } catch (error) {
                console.error("Lỗi khi lấy CV:", error);
                alert("Đã xảy ra lỗi khi tải CV.");
            }
        },

        async downloadFile(url, fileName) {
            try {
                const response = await fetch(url);
                const blob = await response.blob();

                const objectURL = URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = objectURL;
                a.download = fileName || "resume.pdf";
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(objectURL); // Giải phóng bộ nhớ
            } catch (error) {
                console.error("Lỗi khi tải file:", error);
                alert("Không thể tải file.");
            }
        },

        async sendNotif() {
            try {
                const response = await fetch('/api/send_notification', {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        user_id: this.userID,  // ID người dùng
                        resume_id: this.id,
                        message: this.NotifMessage,
                        is_read: "false",
                        created_by: this.userID,
                        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                    }),
                });
                const result = await response.json();
                if (result.success) {

                } else {
                    console.error("Failed to send notification:", result.message);
                }
            }
            catch (err) {
                console.error("Send notification failed", err);
            }
        },

        goToScout() {
            // Chuyển đến trang select_opportunities_for_scout với resume_id
            window.location.href = `/messages/select_opportunities_for_scout?resume_id=${this.id}`;
        }
    },
}

export default ResumeDetail;

{/* <div class="col">
        <div class="text-right nowrap">
            <div class="d-inline-block">
                <div aria-hidden="true" aria-labelledby="modal-21362_modal" class="modal" id="modal-block-21362" role="dialog" tabindex="-1">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content text-left default-color-text">
                            <div class="modal-header">
                                <h4 class="modal-title w-100">確認</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                    <span aria-hidden="true">
                                        <i class="material-icons md-dark mb-36">clear</i>
                                    </span>
                                </button>
                            </div>
                            <div class="modal-body text-wrap">
                                <p class="font-middle">指定された人財掲載元(会社またはフリーランス)をブロックします。よろしいですか？</p>
                                <form action="/resumes/48810/detail" accept-charset="UTF-8" data-remote="true" method="post">
                                    <input name="utf8" type="hidden" value="✓" autocomplete="off">
                                    <input type="hidden" name="authenticity_token" value="bURvkc2Ouil+zddhGcqrpfUeDYlFEM5LnFtAGXDhq2N9go3FSMoR5Okbime6cTwfKQCF8YMzjEov0Lk2aHXHGw==" autocomplete="off">
                                    <div class="input_memo_field">
                                        <div class="mb-4">
                                            <div class="mx-auto mb-5">
                                                <div class="">
                                                    <label class="font-middle mb-3 no-label-bar" for="">社内メモ</label>
                                                </div>
                                                <textarea rows="5" class="form-control" autocomplete="off" id="memo_field" name="memo"></textarea>
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <a class="btn btn-default btn-lg font-middle block_user waves-effect waves-light" data-action="/block_users/user_blocking/21362" data-dismiss="modal" data-font-color="false" data-name-opened="true" data-target-id="21362">ユーザーブロック</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> */}