import App from "./layouts/app.js"
import router from "./router/router.js"
import pickadateDirective from "./directives/pickadate.js"
import DropdownDirective from "./directives/dropdown.js"
import CharacterCounter from "./common/CharacterCounter.js"
import WorkplaceSelector from "./common/Workplace.js"
import PriceRange from "./common/PriceRange.js"
import RateSlider from "./common/Rate.js"
import FrequencySlider from "./common/Frequency.js"
import FrequencyRangeSlider from "./common/FrequencyRange.js"
import RateRangeSlider from "./common/RateRange.js"
import ParticipantsSlider from "./common/Participants.js"
import AgeSlider from "./common/Age.js"
import Pagination from "./common/Pagination.js"
const app = Vue.createApp(App)
app.directive('pickadate', pickadateDirective);
app.use(DropdownDirective);
app.component('CharacterCounter', CharacterCounter);
app.component('WorkplaceSelector', WorkplaceSelector);
app.component('PriceRange', PriceRange);
app.component('RateSlider', RateSlider);
app.component('FrequencySlider', FrequencySlider);
app.component('FrequencyRangeSlider', FrequencyRangeSlider);
app.component('RateRangeSlider', RateRangeSlider);
app.component('ParticipantsSlider', ParticipantsSlider);
app.component('AgeSlider', AgeSlider);
app.component('Pagination', Pagination);
app.use(router)
app.mount('#app')