.picker__input {
  cursor: default;
}

.picker {
  position: absolute;
  z-index: 1000;
  font-size: 1rem;
  line-height: 1.2;
  color: #000;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.picker .picker__holder {
  position: fixed;
  width: 100%;
  top: 0 !important;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  transition: background .15s ease-out, top 0s .15s;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.picker .picker__holder,
.picker .picker__frame {
  top: auto;
  right: 0;
  bottom: 0;
  left: 0;
}

.picker .picker__frame {
  position: absolute;
  width: 18.75rem;
  min-width: 16rem;
  max-width: 20.3125rem;
  max-height: 21.875rem;
  margin: 0 auto;
  filter: alpha(opacity=0);
  opacity: 1;
  transition: none;
  transform: none;
}

@media (min-height: 28.875em) {
  .picker .picker__frame {
    top: auto;
    bottom: -100%;
    max-height: 80%;
    overflow: visible;
  }
}

@media (min-height: 40.125em) {
  .picker .picker__frame {
    margin-bottom: 7.5%;
  }
}

.picker .picker__frame .picker__wrap {
  display: table;
  width: 100%;
  height: 100%;
}

@media (min-height: 28.875em) {
  .picker .picker__frame .picker__wrap {
    display: block;
  }
}

.picker .picker__box {
  display: table-cell;
  vertical-align: middle;
  background: #fff;
  padding: 0 !important;
}

@media (min-height: 28.875em) {
  .picker .picker__box {
    display: block;
    border: 1px solid #777;
    border-top-color: #898989;
    border-bottom-width: 0;
    border-radius: 5px 5px 0 0;
    box-shadow: 0 0.75rem 2.25rem 1rem rgba(0, 0, 0, 0.24);
  }
}

.picker__date-display {
  padding-bottom: .9375rem;
  margin-bottom: 1rem;
  font-weight: 300;
  text-align: center;
  background-color: #1072e9;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.picker__footer {
  width: 100%;
}

.picker__box {
  padding: 0;
  overflow: hidden;
  border-radius: .125rem;
}

.picker__box .picker__header {
  position: relative;
  margin-bottom: 1.25rem;
  text-align: center;
  margin-top: 0 !important;
}

.picker__box .picker__header select {
  display: inline-block !important;
}

/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }
  }

  :disabled {
    pointer-events: none !important;
  }

  .picker__box .picker__header .picker__date-display,
  .picker__date-display {
    color: #fff !important;
  }

  .picker__date-display {
    padding-bottom: .9375rem;
    margin-bottom: 1rem;
    font-weight: 300;
    text-align: center;
    background-color: #1072e9;
  }

  .picker__box .picker__header .picker__date-display {
    display: flex !important;
    justify-content: center;
    padding-bottom: .3125rem;
    font-weight: 400;
    background-color: #1072e9;
  }

  .picker__box .picker__header .picker__date-display .picker__weekday-display {
    padding: .875rem .4375rem .3125rem .5rem;
    margin-top: 1.25rem;
    font-size: 2.1rem;
    letter-spacing: .5;
  }

  .picker__box .picker__header .picker__date-display .picker__month-display {
    padding: .875rem .3125rem .25rem;
    margin-top: 1.25rem;
    font-size: 2.1rem;
  }

  .picker__box .picker__header .picker__date-display .picker__day-display {
    padding: .875rem .3125rem .25rem;
    margin-top: 1.25rem;
    font-size: 2.1rem;
  }

  .picker__box .picker__header .picker__date-display .picker__year-display {
    position: absolute;
    top: .625rem;
    left: 45%;
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.4);
  }

  :focus {
    outline: 0;
  }
}

.picker__box .picker__header .picker__date-display .picker__weekday-display {
  padding: .875rem .4375rem .3125rem .5rem;
  margin-top: 1.25rem;
  font-size: 2.1rem;
  letter-spacing: .5;
}

.picker__box .picker__header .picker__date-display .picker__month-display {
  padding: .875rem .3125rem .25rem;
  margin-top: 1.25rem;
  font-size: 2.1rem;
}

.picker__box .picker__header .picker__date-display .picker__day-display {
  padding: .875rem .3125rem .25rem;
  margin-top: 1.25rem;
  font-size: 2.1rem;
}

.picker__box .picker__header .picker__date-display .picker__year-display {
  position: absolute;
  top: .625rem;
  left: 45%;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.4);
}

.picker__box .picker__header .picker__select--month,
.picker__box .picker__header .picker__select--year {
  display: inline-block;
  height: 2em;
  padding: 0;
  margin-right: .25em;
  margin-left: .25em;
  background: rgba(0, 0, 0, 0);
  border: none;
  border-bottom: 1px solid #ced4da;
  outline: 0;
}

.picker__box .picker__header .picker__select--month:focus,
.picker__box .picker__header .picker__select--year:focus {
  border-color: rgba(0, 0, 0, 0.05);
}

.picker__box .picker__header .picker__select--year {
  width: 30%;
}

.picker__box .picker__header .picker__nav--prev,
.picker__box .picker__header .picker__nav--next {
  position: absolute;
  top: 95px !important;
  box-sizing: content-box;
  padding: .1875rem .625rem;
}

.picker__box .picker__header .picker__nav--prev:hover,
.picker__box .picker__header .picker__nav--next:hover {
  color: #000;
  cursor: pointer;
  background-color: transparent;
}

.picker__box .picker__header .picker__nav--prev:before,
.picker__box .picker__header .picker__nav--next:before {
  display: block;
  font-family: "Font Awesome 5 Free", sans-serif;
  font-weight: 900;
}

.picker__box .picker__header .picker__nav--prev {
  left: -0.5em;
  padding-right: 1.25em;
}

.picker__box .picker__header .picker__nav--next {
  right: -0.2em;
  padding-left: 1.25em;
}

.picker__box .picker__table {
  width: 100%;
  margin-top: .75em;
  margin-bottom: .5em;
  font-size: 1rem;
  text-align: center;
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: collapse;
}

.picker__box .picker__table th,
.picker__box .picker__table td {
  text-align: center;
}

.picker__box .picker__table td {
  padding: 0;
  margin: 0;
}

.picker__box .picker__table .picker__weekday {
  width: 14%;
  padding-bottom: .25em;
  font-size: .9em;
  font-weight: 500;
  color: #999;
}

@media (min-height: 33.875em) {
  .picker__box .picker__table .picker__weekday {
    padding-bottom: .25em;
  }
}

.picker__box .picker__table .picker__day--today {
  position: relative;
  padding: .75rem 0;
  font-weight: 400;
  letter-spacing: -0.3;
  border: 1px solid rgba(0, 0, 0, 0);
  background-color: transparent;
}

.picker__box .picker__table .picker__day.picker__day--today {
  color: #1072e9;
}

.picker__box .picker__table .picker__day--infocus {
  padding: .75rem 0;
  font-weight: 400;
  color: #595959;
  letter-spacing: -0.3;
  border: #595959 rgba(0, 0, 0, 0);
  background-color: transparent;
}

.picker__box .picker__table .picker__day--infocus:hover {
  color: #000;
  cursor: pointer;
  background-color: transparent;
}

.picker__box .picker__table .picker__day--outfocus {
  display: none;
  padding: .75rem 0;
}

.picker__box .picker__table .picker__day--outfocus:hover {
  font-weight: 500;
  color: #ddd;
  cursor: pointer;
}

.picker__box .picker__table .picker__day--highlighted:hover {
  cursor: pointer;
}

.picker__box .picker__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: .3125rem .625rem;
  text-align: right;
}

.picker__box .picker__footer .picker__button--today,
.picker__box .picker__footer .picker__button--clear,
.picker__box .picker__footer .picker__button--close {
  display: inline-block;
  width: 33%;
  padding: 1rem 0 .7rem 0;
  font-size: .8em;
  font-weight: 700;
  text-transform: uppercase;
  vertical-align: bottom;
  background: #fff;
  border: 1px solid #fff;
}

.picker__box .picker__footer .picker__button--today:hover,
.picker__box .picker__footer .picker__button--clear:hover,
.picker__box .picker__footer .picker__button--close:hover {
  color: #000;
  cursor: pointer;
  background: #b1dcfb;
  border-bottom-color: #b1dcfb;
}

.picker__box .picker__footer .picker__button--today:focus,
.picker__box .picker__footer .picker__button--clear:focus,
.picker__box .picker__footer .picker__button--close:focus {
  background: #b1dcfb;
  border-color: rgba(0, 0, 0, 0.05);
  outline: none;
}

.picker__box .picker__footer .picker__button--today:before,
.picker__box .picker__footer .picker__button--clear:before,
.picker__box .picker__footer .picker__button--close:before {
  position: relative;
  display: inline-block;
  height: 0;
}

.picker__box .picker__footer .picker__button--today:before,
.picker__box .picker__footer .picker__button--clear:before {
  margin-right: .45em;
  content: " ";
}

.picker__box .picker__footer .picker__button--today:before {
  top: -0.05em;
  width: 0;
  border-top: .66em solid #0059bc;
  border-left: 0.66em solid rgba(0, 0, 0, 0);
}

.picker__box .picker__footer .picker__button--clear:before {
  top: -0.25em;
  width: .66em;
  border-top: 3px solid #e20;
}

.picker__box .picker__footer .picker__button--close:before {
  top: -0.1em;
  margin-right: .35em;
  font-size: 1.1em;
  color: #777;
  vertical-align: top;
  content: "×";
}

.picker__box .picker__footer .picker__button--today[disabled],
.picker__box .picker__footer .picker__button--today[disabled]:hover {
  color: #ddd;
  cursor: default;
  background: #f5f5f5;
  border-color: #f5f5f5;
}

.picker__box .picker__footer .picker__button--today[disabled]:before {
  border-top-color: #aaa;
}

.picker--opened .picker__frame {
  top: 20% !important;
}

.picker__box {
  font-size: 1em !important;
}

.picker__nav--prev:before,
.picker__nav--next:before {
  border-right: none !important;
}

.picker__nav--prev:before {
  border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #000000 !important;
}

.picker__date-display {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
  background: #1072e9;
  /* Màu xanh */
  padding: 10px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  margin-bottom: 5px;
}


.picker__day--today:before {
  border-left: 0 !important;
}

.picker__day--selected,
.picker__day--selected:hover,
.picker--focused .picker__day--selected {
  background-color: #1072e9 !important;
  border-radius: 50%;
  border: 0;
  color: #fff !important;
  box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
}

.picker__day--highlighted:hover,
.picker--focused .picker__day--highlighted {
  border: 0;
}

.modal-footer {
  border-top: 0 !important;
}

.modal-header {
  padding: 2rem !important;
}

.modal-body {
  padding: 0 2rem 2rem !important;
}

.picker__nav--prev:before {
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 0 8px 12px;
  border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: rotate(180deg);
}

.picker__nav--next:before {
  content: "";
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 0 8px 12px;
  border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767;
  position: absolute;
  top: 50%;
  right: 40%;
}