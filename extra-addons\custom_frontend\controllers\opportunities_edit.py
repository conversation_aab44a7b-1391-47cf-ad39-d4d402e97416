from odoo import http
from odoo.http import request
import json
from . import common as global_common
from datetime import datetime
import logging
_logger = logging.getLogger(__name__)

class OpportunitiesEditController(http.Controller):
    @http.route('/api/opp_edit', type='http', auth='public', methods=['GET'])
    def get_opportunity(self, **kwargs):
        opp_id = kwargs.get('id')

        if not opp_id or not str(opp_id).isdigit():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Invalid or missing opportunity ID'}),
                headers={'Content-Type': 'application/json'}
            )

        opportunity = request.env['vit.opportunities'].sudo().browse(int(opp_id))

        if not opportunity.exists():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Opportunity not found'}),
                headers={'Content-Type': 'application/json'}
            )

        data = {
            'id': opportunity.id,
            'company_id': opportunity.company_id.id if opportunity.company_id else None,
            'subject': opportunity.subject,
            'categories_design': opportunity.categories_design,
            'categories_development': opportunity.categories_development,
            'categories_infrastructure': opportunity.categories_infrastructure,
            'categories_operation_maintenance': opportunity.categories_operation_maintenance,
            'utilization_rate': opportunity.utilization_rate,
            'unit_price_min': opportunity.unit_price_min,
            'unit_price_max': opportunity.unit_price_max,
            'skill_matching_flg': opportunity.skill_matching_flg,
            'work_frequencies': opportunity.work_frequencies,
            'specifies_workplaces': opportunity.specifies_workplaces,
            'contract_startdate_at': opportunity.contract_startdate_at.isoformat() if opportunity.contract_startdate_at else None,
            'contract_enddate_at': opportunity.contract_enddate_at.isoformat() if opportunity.contract_enddate_at else None,
            'possible_continue_flg': opportunity.possible_continue_flg,
            'requirements': opportunity.requirements,
            'skill_requirements': opportunity.skill_requirements,
            'order_accuracy_ids': opportunity.order_accuracy_ids,
            'involvements': opportunity.involvements,
            'opp_type_id': opportunity.opp_type_id,
            'contract_types': opportunity.contract_types,
            'expired_at': opportunity.expired_at.isoformat() if opportunity.expired_at else None,
            'participants': opportunity.participants,
            'interview_count_id': opportunity.interview_count_id,
            'trading_restriction': opportunity.trading_restriction,
            'opp_qualities': opportunity.opp_qualities,
            'public_status_id': opportunity.public_status_id,
            'publish_company_name_status_id': opportunity.publish_company_name_status_id,
            'business_field': opportunity.business_field,
            'status': opportunity.status,
            'created_at': opportunity.created_at.isoformat() if opportunity.created_at else None,
            'updated_at': opportunity.updated_at.isoformat() if opportunity.updated_at else None,
            'created_by' : opportunity.created_by.id,
            'nationality': opportunity.nationality,
            'resident': opportunity.resident,
            'interview_start_time': opportunity.interview_start_time,
            'interview_end_time': opportunity.interview_end_time,
            'available_monday': opportunity.available_monday,
            'available_tuesday': opportunity.available_tuesday,
            'available_wednesday': opportunity.available_wednesday,
            'available_thursday': opportunity.available_thursday,
            'available_friday': opportunity.available_friday,
            'available_saturday': opportunity.available_saturday
        }

        return request.make_response(
            json.dumps({'success': True, 'data': data}),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/opp_edit', type='json', auth='public', methods=['POST'], csrf=False)
    def edit_opportunity(self):
        data = request.httprequest.get_json(silent=True)

        # Lấy các giá trị từ data
        opp_id = data.get('id')
        subject = data.get('subject')
        categories_consultation = data.get('categories_design')
        categories_development = data.get('categories_development')
        categories_infrastructure = data.get('categories_infrastructure')
        categories_design = data.get('categories_operation_maintenance')
        utilization_rate = data.get('utilization_rate')
        unit_price_min = data.get('unit_price_min')
        unit_price_max = data.get('unit_price_max')
        work_frequencies = data.get('work_frequencies')
        specifies_workplaces = data.get('specifies_workplaces')
        contract_startdate_at = data.get('contract_startdate_at')
        contract_enddate_at = data.get('contract_enddate_at')
        requirements = data.get('requirements')
        skill_requirements = data.get('skill_requirements')
        order_accuracy_ids = data.get('order_accuracy_ids')
        involvements = data.get('involvements')
        opp_type_id = data.get('opp_type_id')
        contract_types = data.get('contract_types')
        expired_at = data.get('expired_at')
        participants = data.get('participants')
        interview_count_id = data.get('interview_count_id')
        trading_restriction = data.get('trading_restriction')
        public_status_id = data.get('public_status_id')
        publish_company_name_status_id = data.get('publish_company_name_status_id')
        business_field = data.get('business_field')
        status = data.get('status')
        nationality = data.get('nationality')
        resident = data.get('resident')
        interview_start_time = data.get('interview_start_time')
        interview_end_time = data.get('interview_end_time')
        available_monday = data.get('available_monday', False)
        available_tuesday = data.get('available_tuesday', False)
        available_wednesday = data.get('available_wednesday', False)
        available_thursday = data.get('available_thursday', False)
        available_friday = data.get('available_friday', False)
        available_saturday = data.get('available_saturday', False)

        _logger.info("opp_type_id: %s", opp_type_id)
        required_fields = [
            subject,
            utilization_rate, unit_price_min, unit_price_max, work_frequencies, specifies_workplaces, contract_startdate_at,
            contract_enddate_at, requirements, skill_requirements, order_accuracy_ids, involvements,
            contract_types, expired_at, participants, interview_count_id, trading_restriction,
            public_status_id, publish_company_name_status_id, business_field
        ]

        # Nếu involvements là "enter_sales_channels", gán giá trị rỗng cho opp_type_id
        if involvements == 'enter_sales_channels':
            opp_type_id = ''
        else:
            required_fields.append(opp_type_id)

        if any(field in [None, ""] for field in required_fields):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE,}
        if not any(map(bool, [categories_consultation, categories_development, categories_infrastructure, categories_design])):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}

        # Cập nhật các trường hợp không phải None
        update_fields = {
            'subject': subject,
            'categories_design': categories_consultation,
            'categories_development': categories_development,
            'categories_infrastructure': categories_infrastructure,
            'categories_operation_maintenance': categories_design,
            'utilization_rate': utilization_rate,
            'unit_price_min': unit_price_min,
            'unit_price_max': unit_price_max,
            'skill_matching_flg': data.get('skill_matching_flg'),
            'work_frequencies': work_frequencies,
            'specifies_workplaces': specifies_workplaces,
            'contract_startdate_at': contract_startdate_at,
            'contract_enddate_at': contract_enddate_at,
            'possible_continue_flg': data.get('possible_continue_flg'),
            'requirements': requirements,
            'skill_requirements': skill_requirements,
            'order_accuracy_ids': order_accuracy_ids,
            'involvements': involvements,
            'opp_type_id': opp_type_id,  # opp_type_id đã được gán giá trị rỗng nếu involvements là "enter_sales_channels"
            'contract_types': contract_types,
            'expired_at': expired_at,
            'participants': participants,
            'interview_count_id': interview_count_id,
            'trading_restriction': trading_restriction,
            'opp_qualities': data.get('opp_qualities'),
            'public_status_id': public_status_id,
            'publish_company_name_status_id': publish_company_name_status_id,
            'business_field': business_field,
            'status': status,
            'updated_at': datetime.now(),
            'resident': resident,
            'interview_start_time': interview_start_time,
            'interview_end_time': interview_end_time,
            'available_monday': available_monday,
            'available_tuesday': available_tuesday,
            'available_wednesday': available_wednesday,
            'available_thursday': available_thursday,
            'available_friday': available_friday,
            'available_saturday': available_saturday
        }

        # Loại bỏ các trường có giá trị None
        update_fields = {key: value for key, value in update_fields.items() if value is not None}

        # Cập nhật cơ hội trong cơ sở dữ liệu
        db_opp = request.env['vit.opportunities'].sudo().search([('id', '=', opp_id)], limit=1)

        if db_opp:
            # Lưu expired_at cũ để so sánh
            old_expired_at = db_opp.expired_at

            # Cập nhật opportunity
            db_opp.sudo().write(update_fields)

            # Nếu expired_at thay đổi, cập nhật các workflow có status = 0
            new_expired_at = update_fields.get('expired_at')
            if new_expired_at and new_expired_at != old_expired_at:
                self._update_workflow_expire_dates(opp_id, new_expired_at)

            return {'success': True, 'message': 'Opportunity updated successfully'}
        else:
            return {'success': False, 'message': 'Opportunity not found'}

    def _update_workflow_expire_dates(self, opp_id, new_expired_at):
        """
        Cập nhật expire_date cho các workflow có status = 0 khi expired_at của opportunity thay đổi
        """
        from datetime import datetime, timedelta

        # Tìm tất cả workflow có status = 0 cho opportunity này
        workflows = request.env['vit.workflow'].sudo().search([
            ('opportunities_id', '=', int(opp_id)),
            ('status', '=', 0)
        ])

        if not workflows:
            return

        # Chuyển đổi new_expired_at thành datetime object nếu cần
        if isinstance(new_expired_at, str):
            try:
                # Thử các format khác nhau
                if 'T' in new_expired_at:
                    new_expired_at = datetime.fromisoformat(new_expired_at.replace('Z', '+00:00'))
                else:
                    new_expired_at = datetime.strptime(new_expired_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    new_expired_at = datetime.strptime(new_expired_at, '%Y-%m-%d')
                except ValueError:
                    _logger.error(f"Cannot parse expired_at: {new_expired_at}")
                    return

        updated_count = 0
        for workflow in workflows:
            # Tính toán expire_date mới dựa trên logic
            application_plus_7_days = workflow.created_at + timedelta(days=7)

            # So sánh với ngày hết hạn ứng tuyển mới
            if new_expired_at and new_expired_at < application_plus_7_days:
                # Nếu expired_at sớm hơn, dùng expired_at
                new_expire_date = new_expired_at
            else:
                # Nếu expired_at muộn hơn hoặc không có, dùng ngày ứng tuyển + 7 ngày
                new_expire_date = application_plus_7_days

            # Chỉ cập nhật nếu expire_date thực sự thay đổi
            if workflow.expire_date != new_expire_date:
                workflow.sudo().write({
                    'expire_date': new_expire_date,
                    'updated_at': datetime.now()
                })
                updated_count += 1

        _logger.info(f"Updated expire_date for {updated_count} workflows with status=0 for opportunity {opp_id}")

    @http.route('/api/close_opp', type='json', auth='public', methods=['POST'], csrf=False)
    def update_status_to_zero(self):
        data = request.httprequest.get_json(silent=True)

        opp_id = data.get('id')
        if not opp_id:
            return {'success': False, 'message': 'ID is required'}

        db_opp = request.env['vit.opportunities'].sudo().search([('id', '=', opp_id)], limit=1)
        if not db_opp:
            return {'success': False, 'message': 'Opportunity not found'}

        db_opp.sudo().write({'status': 0})

        return {'success': True, 'message': 'Opportunity status updated to 0'}

    @http.route('/api/deleteDraft', type='json', auth='public', methods=['POST'], csrf=False)
    def delete_draft(self):
        data = request.httprequest.get_json(silent=True)

        opp_id = data.get('id')
        if not opp_id:
            return {'success': False, 'message': 'ID is required'}

        db_opp = request.env['vit.opportunities'].sudo().search([('id', '=', opp_id)], limit=1)
        if not db_opp:
            return {'success': False, 'message': 'Opportunity not found'}

        # Kiểm tra nếu Opportunity có status là draft (giả sử draft = 0)
        if db_opp.status != 0:
            return {'success': False, 'message': 'Only draft opportunities can be deleted'}

        # Xóa Opportunity
        db_opp.sudo().unlink()

        return {'success': True, 'message': 'Draft opportunity deleted successfully'}

    @http.route('/api/opp_prev_next', type='http', auth='public', methods=['GET'])
    def get_prev_next_opportunity(self, **kwargs):
        opp_id = kwargs.get('id')

        if not opp_id or not str(opp_id).isdigit():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Invalid or missing opportunity ID'}),
                headers={'Content-Type': 'application/json'}
            )

        opp_id = int(opp_id)

        # Lấy cơ hội trước đó, bỏ qua những cái có isdaft = True
        prev_opp = request.env['vit.opportunities'].sudo().search(
            [('id', '<', opp_id)], order='id desc', limit=1
        )

        # Lấy cơ hội tiếp theo, bỏ qua những cái có isdaft = True
        next_opp = request.env['vit.opportunities'].sudo().search(
            [('id', '>', opp_id)], order='id asc', limit=1
        )

        data = {
            'prev': {
                'id': prev_opp.id if prev_opp else None,
                'subject': prev_opp.subject if prev_opp else None,
                'requirements': prev_opp.requirements if prev_opp else None,
                'skill_requirements': prev_opp.skill_requirements if prev_opp else None,
            } if prev_opp else None,
            'next': {
                'id': next_opp.id if next_opp else None,
                'subject': next_opp.subject if next_opp else None,
                'requirements': next_opp.requirements if next_opp else None,
                'skill_requirements': next_opp.skill_requirements if next_opp else None,
            } if next_opp else None
        }

        return request.make_response(
            json.dumps({'success': True, 'data': data}),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/opp_memo_update', type='json', auth='public', methods=['POST'], csrf=False)
    def update_opportunity_memo(self):
        """
        API để cập nhật memo của opportunity sử dụng vit.comments model
        """
        data = request.httprequest.get_json(silent=True)

        opportunity_id = data.get('opportunity_id')
        memo = data.get('memo', '')

        if not opportunity_id:
            return {'success': False, 'message': 'Opportunity ID is required'}

        # Tìm opportunity trong database
        opportunity = request.env['vit.opportunities'].sudo().search([('id', '=', opportunity_id)], limit=1)

        if not opportunity:
            return {'success': False, 'message': 'Opportunity not found'}

        # Tìm comment memo hiện tại cho opportunity này (status = 1 để phân biệt với comment thường)
        existing_memo = request.env['vit.comments'].sudo().search([
            ('opportunities_id', '=', opportunity_id),
            ('status', '=', 1)  # status = 1 cho memo
        ], limit=1)

        from datetime import datetime

        if existing_memo:
            # Cập nhật memo hiện tại
            existing_memo.sudo().write({
                'content': memo,
                'updated_at': datetime.now()
            })
        else:
            # Tạo memo mới
            request.env['vit.comments'].sudo().create({
                'opportunities_id': opportunity_id,
                'content': memo,
                'status': 1,  # status = 1 cho memo
                'created_by': 'system',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            })

        return {
            'success': True,
            'message': 'Memo updated successfully',
            'opportunity_id': opportunity_id,
            'memo': memo
        }

    @http.route('/api/update_all_workflow_deadlines', type='json', auth='public', methods=['POST'], csrf=False)
    def update_all_workflow_deadlines(self):
        """
        API để cập nhật tất cả workflow có status = 0 theo logic mới
        Dùng để migration dữ liệu hiện có
        """
        from datetime import datetime, timedelta

        # Tìm tất cả workflow có status = 0
        workflows = request.env['vit.workflow'].sudo().search([('status', '=', 0)])

        if not workflows:
            return {'success': True, 'message': 'No workflows with status=0 found', 'updated_count': 0}

        updated_count = 0
        for workflow in workflows:
            opportunity = workflow.opportunities_id
            if not opportunity:
                continue

            # Tính toán expire_date mới dựa trên logic
            application_plus_7_days = workflow.created_at + timedelta(days=7)

            # So sánh với ngày hết hạn ứng tuyển
            if opportunity.expired_at and opportunity.expired_at < application_plus_7_days:
                # Nếu expired_at sớm hơn, dùng expired_at
                new_expire_date = opportunity.expired_at
            else:
                # Nếu expired_at muộn hơn hoặc không có, dùng ngày ứng tuyển + 7 ngày
                new_expire_date = application_plus_7_days

            # Chỉ cập nhật nếu expire_date thực sự thay đổi
            if workflow.expire_date != new_expire_date:
                workflow.sudo().write({
                    'expire_date': new_expire_date,
                    'updated_at': datetime.now()
                })
                updated_count += 1

        _logger.info(f"Updated expire_date for {updated_count} workflows with status=0")
        return {'success': True, 'message': f'Updated {updated_count} workflows', 'updated_count': updated_count}