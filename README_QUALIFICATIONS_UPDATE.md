# Hướng dẫn cập nhật danh sách chứng chỉ (資格)

Tài liệu này hướng dẫn cách cập nhật danh sách chứng chỉ (資格) trong dropdown trên trang resume_new và resume_edit.

## Thay đổi đã thực hiện

Danh sách chứng chỉ đã được cập nhật từ danh sách cũ:

```
未選択
基本情報技術者試験
応用情報技術者試験
ネットワークスペシャリスト
```

Sang danh sách mới:

```
未選択
ITパスポート
基本情報処理技術者
情報検定(J検)
情報検索応用能力試験
MCP(マイクロソフト認定プロフェッショナル)
ドットコムマスター
応用情報技術者
情報セキュリティスペシャリスト
ITサービスマネージャ
Linuｘ技術者認定試験(LPIC)
企業情報管理士認定試験
プロジェクトマネージャー
PMP(Project Management Professional)試験
日商PC検定
パソコン検定(P検)
IC3
CompTIAA+
パソコンインストラクター資格認定
パソコン整備士
Excel 表計算処理技能設定試験
Access ビジネスデータベース技能認定試験
マイクロソフトオフィススペシャリスト(MOS)
システムアーキテクト
VBAエキスパート
C言語プログラミング能力認定試験
Javaプログラミング能力認定試験
PHP技術者認定試験試験
Pythonエンジニア認定試験
オラクルJava認定資格(OCJ)
情報処理安全確保支援士試験
情報セキュリティマネジメント試験
ネットワークスペシャリスト
シスコ技術者認定
データベーススペシャリスト
オラクルマスター
システム監査技術者
ITストラテジスト
ITコーディネータ
```

## Các file đã thay đổi

1. `extra-addons\custom_frontend\static\js\directives\dropdown.js`
   - Cập nhật danh sách `optionsList.qualifications`

## Cập nhật dữ liệu trong cơ sở dữ liệu

Không cần thiết phải cập nhật dữ liệu hiện có trong cơ sở dữ liệu vì:

1. Chứng chỉ được lưu trữ dưới dạng văn bản trong trường `qualification` của bảng `vit.partner`
2. Người dùng có thể nhập tùy ý vào trường này thông qua giao diện
3. Dropdown chỉ là một công cụ hỗ trợ để người dùng chọn chứng chỉ phổ biến, không phải là danh sách giới hạn

## Cách hoạt động của chức năng chứng chỉ

1. Người dùng chọn một chứng chỉ từ dropdown
2. Người dùng chọn ngày đạt được chứng chỉ
3. Người dùng nhấn nút "登録" (Đăng ký)
4. Chứng chỉ và ngày được thêm vào trường văn bản `qualification_remark`
5. Người dùng cũng có thể nhập trực tiếp các chứng chỉ không có trong dropdown vào trường văn bản bên dưới

## Lưu ý

- Các chứng chỉ đã được thêm vào dropdown sẽ hiển thị trong danh sách dropdown khi người dùng truy cập trang resume_new hoặc resume_edit
- Không cần khởi động lại server để áp dụng thay đổi này
- Thay đổi này không ảnh hưởng đến dữ liệu hiện có trong cơ sở dữ liệu
