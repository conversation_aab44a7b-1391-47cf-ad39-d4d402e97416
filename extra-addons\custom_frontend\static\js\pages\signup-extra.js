toastr.options.extendedTimeOut = '0';
toastr.options.showDuration = '300';
toastr.options.hideDuration = '1000';
toastr.options.positionClass = 'toast-top-full-width';
toastr.options.showEasing = 'swing';
toastr.options.hideEasing = 'linear';
toastr.options.progressBar = true;
toastr.options.onclick = null;
toastr.options.escapeHtml = false;
toastr.options.closeButton = true;
toastr.options.closeHtml = '<button>&times;</button>';
toastr.options.preventDuplicates = true;

function h() {
    return t.extend({}, {
        tapToDismiss: !0,
        toastClass: "md-toast",
        containerId: "toast-container",
        debug: !1,
        showMethod: "fadeIn",
        showDuration: 300,
        showEasing: "swing",
        onShown: void 0,
        hideMethod: "fadeOut",
        hideDuration: 1e3,
        hideEasing: "swing",
        onHidden: void 0,
        extendedTimeOut: 1e3,
        iconClasses: {
            error: "md-toast-error",
            info: "md-toast-info",
            success: "md-toast-success",
            warning: "md-toast-warning"
        },
        iconClass: "md-toast-info",
        positionClass: "md-toast-top-right",
        timeOut: 5e3,
        titleClass: "md-toast-title",
        messageClass: "md-toast-message",
        target: "body",
        closeHtml: '<button type="button">&times;</button>',
        newestOnTop: !0,
        preventDuplicates: !1,
        progressBar: !1
    }, a.options)
}

// h();