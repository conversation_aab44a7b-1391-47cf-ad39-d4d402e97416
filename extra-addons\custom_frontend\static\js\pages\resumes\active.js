import { userInfo } from "../../router/router.js";
import { createSearchBreadcrumb } from "../../utils/breadcrumbHelper.js";

const resumes_active = {
	'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
    ${createSearchBreadcrumb('人財を探す')}
    <div class="container-fluid pt-1">
        <div class="row justify-content-between align-items-center mb-2">
            <div class="tab-container">
                <div class="tab-button" @click="tabChange(1)">
                    案件を探す
                </div>
                <div class="btn-default tab-button" @click="tabChange(2)" style="margin-left: 7px;">
                    人財を探す
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center results-sort-container">
                <div class="font-middle result-count-container"><span class="font-ll ex-bold"
                        id="total-count">{{this.totalRecord}}</span>&nbsp;件中&nbsp;{{ getStartItemIndex() }}〜{{
                    getEndItemIndex() }}件</div>
                <div class="position-relative" ref="dropdown">
                    <!-- Khu vực hiển thị và bấm vào -->
                    <div style="margin-right: 17px;" class="d-flex justify-content-end sort-display-area p-2"
                        @click="isOpen6 = !isOpen6">
                        <label class="pr-2 mb-0">{{ selectedOption }}</label>
                        <i class="material-icons custom-grey-6-text pl-1" style="line-height: 1 !important;">
                            {{ isOpen6 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                        </i>
                    </div>

                    <!-- Danh sách hiển thị khi bấm vào -->
                    <ul style="margin-right: 17px;" class="bg-white sort-options-area text-right" v-show="isOpen6">
                        <li v-for="option in options" :key="option"
                            :class="{ 'sort-active': option === selectedOption }" @click="selectOption(option)">
                            {{ option }}
                            <i v-if="option === selectedOption" class="material-icons custom-grey-6-text">done</i>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="container-fluid grabient">
            <div class="row">
                <div style="padding-left: 0px;" class="d-none d-md-block col-12 col-md-5 col-lg-4" id="side-search">
                    <div style="padding-top: 10px !important;" class="card py-4 side-card">
                        <i class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
                        <div class="mb-3">
                            <div class="reset_link_area-pc mx-3 d-none"><a
                                    class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light"
                                    href="/resumes/active"><span>保存済み条件で検索</span></a></div>
                            <div class="reset_link_area-sp d-none"><a class="reset_search_condition mx-0"
                                    href="/resumes/active"><span>保存条件で検索</span></a>
                            </div>
                        </div>
                        <form class="new_resume_search_condition" id="resume_search_condition_form" novalidate=""
                            @submit.prevent="resumes_search" accept-charset="UTF-8" method="get" @input="handleInputChange">
                            <div class="container">
                                <div class="row">
                                    <div class="col-12 px-0 px-md-3">
                                        <label class="font-middle mb-3 ex-bold">フリーワード</label>
                                        <div class="mb-4" style="width: 100%;">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="vertical-top d-inline-block w-100" data-html="true" data-toggle="tooltip"
                                                        title=""
                                                        data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
                                                        <div class="mb-1"><input class="form-control" autocomplete="off"
                                                                id="free_keyword_field" v-model="free_keyword" type="text"
                                                                name="resume_search_condition[free_keyword]"></div>
                                                        <div class="mb-3"><span class="font-middle">を含む</span></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-1"><input class="form-control" autocomplete="off"
                                                            id="negative_keyword_field" v-model="negative_keyword"
                                                            type="text" name="resume_search_condition[negative_keyword]">
                                                    </div>
                                                    <div class="mb-3"><span class="font-middle">を除く</span></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-5">
                                            <label class="font-middle mb-3 ex-bold">得意領域</label>
                                            <div v-dropdown="{ modelValue: '', listType: 'exp_categories' }"
                                                @selected="select_exp_categories"></div>
                                            <input type="hidden" v-model="exp_categories"
                                                name="resume_search_condition[exp_categories]">
                                        </div>
                                        <workplace-selector :required-label="false"
                                            :inputName="'resume_search_condition[workplaces][]'"
                                            v-model="workplaces" @update:modelValue="val => {
                                        console.log('Workplace selector value changed:', val);
                                        workplaces = val;
                                    }"></workplace-selector>
                                        <div class="mx-auto mb-3">
                                            <label class="font-middle mb-3 ex-bold" for="">単価</label>
                                            <PriceRange :min="0" :max="300" :step="5" :startValues="[priceMin, priceMax]"
                                                @update:price_min="updatePriceMin" @update:price_max="updatePriceMax" />
                                            <input type="hidden" :value="priceMin !== null ? priceMin : ''"
                                                name="resume_search_condition[asking_unit_price_min]">
                                            <input type="hidden" :value="priceMax !== null ? priceMax : ''"
                                                name="resume_search_condition[asking_unit_price_max]">
                                        </div>
                                        <div class="mx-auto mb-3">
                                            <label class="font-middle mb-3 ex-bold" for="">稼働可能状況</label>
                                            <div v-dropdown="{modelValue: '', listType: 'work_availability'}"
                                                @selected="select_work_status">
                                                <input type="hidden" v-model="status_single"
                                                    name="resume_search_condition[status]">
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3">
                                            <label class="font-middle mb-3 ex-bold" for="">所属</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="custom-control custom-checkbox pl-4 mr-4">
                                                        <input class="custom-control-input"
                                                            id="partner_types_field_resume_search_condition_0"
                                                            v-model="partner_types" type="checkbox"
                                                            name="resume_search_condition[partner_types][]"
                                                            value="empl"><label id="partner_types_field_label_0"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="partner_types_field_resume_search_condition_0">自社社員</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox pl-4">
                                                        <input class="custom-control-input"
                                                            id="partner_types_field_resume_search_condition_1"
                                                            v-model="partner_types" type="checkbox"
                                                            name="resume_search_condition[partner_types][]"
                                                            value="subc"><label id="partner_types_field_label_1"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="partner_types_field_resume_search_condition_1">協力会社社員</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3">
                                            <label class="font-middle mb-3 ex-bold" for="">国籍</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="pl-4 mr-4" id="radio-options">
                                                        <input class="form-check-input" id="nationality0" type="radio"
                                                            v-model="nationality" value="japan"
                                                            name="resume_search_condition[nationality]">
                                                        <label class="form-check-label" for="nationality0">日本人のみ</label>
                                                    </div>
                                                    <div class="pl-4" id="radio-options">
                                                        <input class="form-check-input" id="nationality1" type="radio"
                                                            v-model="nationality" value="all"
                                                            name="resume_search_condition[nationality]">
                                                        <label class="form-check-label" for="nationality1">国籍問わず</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3" v-show="nationality === 'all'">
                                            <label class="font-middle mb-3 ex-bold" for="">在留資格有無</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="d-flex">
                                                    <div class="pl-4 mr-4" id="radio-options">
                                                        <input class="form-check-input" id="resident0" type="radio"
                                                            v-model="resident" value="yes"
                                                            name="resume_search_condition[resident]">
                                                        <label class="form-check-label" for="resident0">有</label>
                                                    </div>
                                                    <div class="pl-4" id="radio-options">
                                                        <input class="form-check-input" id="resident1" type="radio"
                                                            v-model="resident" value="no"
                                                            name="resume_search_condition[resident]">
                                                        <label class="form-check-label" for="resident1">無し</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3">
                                            <label class="font-middle mb-3 ex-bold" for="">可能な契約形態</label>
                                            <div v-dropdown="{modelValue: '', listType: 'contract_types'}"
                                                @selected="select_contract_type">
                                                <input type="hidden" v-model="contract_type_single"
                                                    name="resume_search_condition[contract_types]">
                                            </div>
                                        </div>
                                        <div class="accordion_details py-3 px-2 border-top accordion_close" @click="toggleAccordion4"
                                            style="display: flex; align-items: center; justify-content: space-between;">
                                                <span class="float-left pr-7">詳細検索条件</span>
                                                <i class="material-icons md-dark float-right d-block">{{ isOpen4 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="mx-auto mb-3" v-show="isOpen4">
                                            <label class="font-middle mb-3 ex-bold" for="">参画可能時期</label>
                                            <div class="row pl-3">
                                                <div class="col-9">
                                                    <div class="mx-auto mb-3">
                                                        <input class="form-control picker__input" autocomplete="off"
                                                            v-pickadate="{ model: 'date_period' }" v-model="date_period"
                                                            id="available_time_at_earliest_field" type="text"
                                                            name="resume_search_condition[available_time_at_earliest]"
                                                            readonly="" aria-haspopup="true" aria-expanded="false"
                                                            aria-readonly="false"
                                                            aria-owns="available_time_at_earliest_field_root">

                                                    </div>
                                                </div>
                                                <div class="col-1 p-0"><i
                                                        class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3" v-show="isOpen4">
                                            <label class="mb-3 font-middle ex-bold" for="">性別</label>
                                            <div v-dropdown="{modelValue: '', listType: 'gender'}"
                                                @selected="select_gender"><input type="hidden" v-model="gender"
                                                    name="resume_search_condition[gender]"></div>
                                        </div>
                                        <div class="mx-auto mb-3" v-show="isOpen4">
                                            <label class="font-middle mb-3 ex-bold" for="">稼働率</label>
                                            <RateRangeSlider :min="25" :max="100" :step="25"
                                                :startValues="utilization_rate_range"
                                                @update:rate="updateUtilizationRateRange" />
                                            <input type="hidden" :value="utilization_rate_min || ''"
                                                name="resume_search_condition[utilization_rate_min]">
                                            <input type="hidden" :value="utilization_rate_max || ''"
                                                name="resume_search_condition[utilization_rate_max]">
                                        </div>
                                        <div class="mx-auto mb-3" v-show="isOpen4">
                                            <label class="font-middle mb-3 ex-bold" for="">出社頻度</label>
                                            <FrequencyRangeSlider :min="0" :max="6" :step="1"
                                                :startValues="working_frequency_range"
                                                @update:frequency="updateWorkingFrequencyRange" />
                                            <input type="hidden" :value="working_frequency_min || ''"
                                                name="resume_search_condition[working_frequency_min]">
                                            <input type="hidden" :value="working_frequency_max || ''"
                                                name="resume_search_condition[working_frequency_max]">
                                        </div>
                                        <div class="mx-auto">
                                            <label class="font-middle mb-3 ex-bold">社内コメント</label>
                                            <div class="row pl-3">
                                                <div class="col-9">
                                                    <div class="mx-auto mb-3">
                                                        <div class="selecting-form custom-control custom-checkbox z-2">
                                                            <input id="with_internal_comment_field"
                                                                class="custom-control-input" v-model="with_internal_comment"
                                                                type="checkbox"
                                                                name="resume_search_condition[with_internal_comment]"
                                                                value="社内コメントあり"><label
                                                                id="with_internal_comment_field_label"
                                                                class="custom-control-label"
                                                                for="with_internal_comment_field">社内コメントあり</label></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="border-bottom mb-5"></div>
                                        <div class="text-center d-none d-md-block search-area py-2">
                                            <button name="button" id="resume-search-button"
                                                class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light"
                                                data-disable-with="検索中" @click="resumes_search">
                                                <div id="btn-text" v-if="isFinding" class="">検索中</div>
                                                <div class="py-2" id="loader" v-else-if="isLoading">
                                                    <div class="loader"></div>
                                                </div>
                                                <div id="btn-text" v-else class=""><span class="font-extralarge"
                                                        id="search-count">{{count_resumes}}</span> 件<br>この条件で検索</div>
                                            </button>
                                            <div class="py-2"><a href="/resumes/active?search_reset=true"><span>条件をリセット</span></a></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div style="padding-right:0px;" class="col-12 col-md-7 col-lg-8">
                    <div class="row">

                    </div>
                    <hr class="mt-0 mb-3">

                    <!-- Table container with shadow -->
                    <div class="table-container table-containter-ip-none-p">
                        <!-- Table header -->
                        <div class="resume-table-header resume-table-header-ip-none-ml">
                            <div class="header-cell status-header" style="width: 130px;">ステータス</div>
                            <div class="header-cell" style="width: 8%;">名前</div>
                            <div class="header-cell" style="width: 17%;">得意領域</div>
                            <div class="header-cell" style="width: 15%;">就業場所</div>
                            <div class="header-cell" style="width: 12%;">単価（万円）</div>
                            <div class="header-cell" style="width: 15%;">稼働状況</div>
                            <div class="header-cell" style="width: 10%;">性別</div>
                            <div class="header-cell" style="width: 5%;">年齢</div>
                            <div class="header-cell" style="width: 5%;">評価</div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <!-- Hiển thị loading khi đang tải dữ liệu -->
                                <div v-if="!dataReady" class="text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">読み込み中...</span>
                                    </div>
                                    <p class="mt-2">データを読み込んでいます...</p>
                                </div>

                                <!-- Chỉ hiển thị khi dữ liệu đã sẵn sàng - Dạng bảng -->
                                <div v-else class="table-rows-container">
                                    <a v-for="resume in resumes" :key="resume.id" class="resume-table-row"
                                        @click="add_viewer(resume.id, this.isloggedin)"
                                        :href="'/resumes/' + resume.id + '/detail?prev_next_display=display'">
                                        <!-- Status và bookmark -->
                                        <div class="status-cell status-cell-notip-w-130px">
                                            <div v-if="viewedStatuses[resume.id] === true" class="viewed-flag"><span
                                                    class="font-small">既読</span></div>
                                            <div v-else-if="check_new(resume.created_at)" class="new-flag"><span
                                                    class="font-small">新着</span></div>
                                            <div v-else class="unread-flag"><span class="font-small">未読</span></div>

                                            <div class="bookmark-area" data-toggle="tooltip" title="ブックマーク">
                                                <span @click.stop.prevent="toggleBookmark(resume.id)">
                                                    <i class="material-icons" style="color: #1072e9;">
                                                        {{ resume.is_react ? 'bookmark' : 'bookmark_border' }}
                                                    </i>
                                                </span>
                                                <span class="pl-1 font-small" style="color: black;">{{
                                                    resume.react_number }}</span>
                                            </div>
                                        </div>

                                        <!-- 名前 -->
                                        <div class="cell" style="width: 8%;">
                                            <span class="default-main-color">{{ resume.initial_name }}</span>
                                        </div>

                                        <!-- 得意領域 -->
                                        <div class="cell" style="width: 17%;">
                                            <span>{{ getExpCategoryName(resume) }}</span>
                                        </div>

                                        <!-- 就業場所 -->
                                        <div class="cell" style="width: 15%;">
                                            <span>{{ resume.working_location }}</span>
                                        </div>

                                        <!-- 単価 -->
                                        <div class="cell" style="width: 12%;">
                                            <span>{{resume.unit_price_min}} 〜 {{resume.unit_price_max}}</span>
                                        </div>

                                        <!-- 稼働状況 -->
                                        <div class="cell" style="width: 15%;">
                                            <span>{{ mapping_work_status[resume.status] || '' }}</span>
                                        </div>

                                        <!-- 性別 -->
                                        <div class="cell" style="width: 10%;">
                                            <span>{{getGenderLabel(resume.gender)}}</span>
                                        </div>

                                        <!-- 年齢 -->
                                        <div class="cell" style="width: 5%;">
                                            <span>{{ calculateAge(resume.birthday) }}歳</span>
                                        </div>

                                        <!-- 評価 -->
                                        <div class="cell" style="width: 5%;">
                                            <span v-if="resume.has_evaluation" class="evaluation-yes">有</span>
                                            <span v-else class="evaluation-no">無</span>
                                        </div>
                                    </a>
                                </div>

                                <!-- Modal for each resume -->
                                <div v-for="resume in resumes" :key="'modal-'+resume.id" aria-labelledby="display_text_format_modal"
                                    class="modal" :id="'display_text_format_' + resume.id" tabindex="-1" aria-modal="true"
                                    role="dialog">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title w-100">人財詳細</h4>
                                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                                        aria-hidden="true"><i
                                                            class="material-icons md-dark mb-36">clear</i></span></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-4">**********************************************************<br>◆人財ID:
                                                    {{resume.id}}<br>◆氏名: {{
                                                    resume.initial_name }} {{getGenderLabel(resume.gender)}} {{
                                                    calculateAge(resume.birthday) }}歳<br>◆参画可能時期:
                                                    {{resume.status}}<br>◆単価: {{resume.unit_price_min}} 〜 {{resume.unit_price_max}} / 月
                                                    <br>◆稼働率: {{
                                                    mapping_utilization[resume?.utilization_rate] || (resume?.utilization_rate ||
                                                    '').split(',').map(rate => mapping_utilization[rate] || '').join('／') || ''
                                                    }}<br>◆就業場所:
                                                    {{ (resume.working_location ?? "").split(',').join('／') }}<br>◆所属:
                                                    自社社員<br>◆経験PR:<br>◆人物サマリー<br>--------------------------------------------<br><br><br>◆スキルサマリー<br>{{resume.experience_pr}}<br><br>◆備考<br>・<br><br>-------------------------------------------------<br><br><br>◆希望詳細:<br>・{{resume.working_hope}}<br><br>**********************************************************
                                                </div>
                                                <div class="text-center"><a aria-label="Close"
                                                        class="btn btn-blue-grey waves-effect waves-light" data-dismiss="modal">閉じる</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Pagination :currentPage="currentPage" :totalPages="totalPages" @page-change="updateURLAndFetch" v-if="resumes.length"/>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Mobile search toggle button -->
<button class="mobile-search-toggle d-md-none" @click="toggleMobileSearch" v-if="!isMobileSearchOpen">
    <i class="material-icons">search</i>
</button>

<!-- Mobile search overlay -->
<div class="mobile-search-overlay" :class="{ 'active': isMobileSearchOpen }" @click="closeMobileSearch"></div>

<link rel="stylesheet" href="/custom_frontend/static/css/resumes/active.css" />
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css" />
    `,
	data() {
		return {
			isOpen: false,
			isOpen1: false,
			isOpen2: false,
			isOpen3: false,
			isOpen4: false,
			isOpen5: false,
			isOpen6: false,
			isLoading: false,
			isFinding: false,
			isSearching: false, // Trạng thái đang tìm kiếm
            showLoadingOverlay: false, // Hiển thị overlay loading
            isMobileSearchOpen: false, // Mobile search form state
			priceMin: null,
			priceMax: null,
			isloggedin: userInfo ? userInfo.user_id : '',
			currentPage: 1,
			totalPages: 1,
			totalRecord: 0,
			count_resumes: 0,
			sortType: 'created_at',
			resumes: [],
			viewedStatuses: {},
			free_keyword: '',
			negative_keyword: '',
			nationality: '',
			resident: '',
			gender: '',
			age_min: null,
			age_max: null,
			with_internal_comment: false,
			status: [],
			status_single: '',
			exp_categories: '',
			partner_types: [],
			contract_types: [],
			contract_type_single: '',
			utilization_rate: null,
			utilization_rate_range: [25, 100], // Range for search
			utilization_rate_min: null,
			utilization_rate_max: null,
			working_frequency: null,
			working_frequency_range: [0, 6], // Range for search
			working_frequency_min: null,
			working_frequency_max: null,
			workplaces: [],
			rating_flag: [],
			date_period: '',
			selectedOption: "新着（降順）", // Mặc định
			options: ["新着（降順）", "更新（降順）", "単価（降順）"],
			sortMap: {
				"新着（降順）": "created_at",
				"更新（降順）": "updated_at",
				"単価（降順）": "unit_price_max"
			},
			mapping_work_status: {
				'work_available': "即日可",
				'will_be_available': "今後可",
				'depends_on_opportunities': "要相談",
				'not_corresponding': "対応不可",
			},
			mapping_working_frequency: {
				'5days': '週5日出社',
				'4days': '週4日出社',
				'3days': '週3日出社',
				'2days': '週2日出社',
				'2to4days': '週4 〜 2日出社',
				'less_than_1day': '週1日未満出社',
				'full_remote': 'フルリモート',
				"1day": '週1日出社'
			},
			mapping_utilization: {
				'100': '100%（フル稼働）',
				'75': '75%',
				'50': '50%',
				'25': '25%'
			},
			mapping_resume_visibility: {
				'private': '非公開',
				'limited': 'ログイン後に表示',
			},
			mapping_partner_types: {
				'subc': '協力会社社員（一社先）',
				'empl': '自社社員',
			},
			categories: [
                { value: "consul_pmo", label: "PMO" },
                { value: "consul_pmpl", label: "PM・PL" },
                { label: "DX", value: "consul_DX" },
                { label: "クラウド", value: "consul_cloud" },
                { label: "モダナイゼション", value: "consul_modern" },
                { label: "セキュリティ", value: "consul_security" },
                { label: "ITインフラ", value: "consul_it" },
                { label: "AI", value: "consul_ai" },
            ],
            categoriesDev: [
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_emb", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
            ],
            categoriesInfra: [
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
            ],
			categories3: [
				{ value: "design_business", label: "業務システム" },
				{ value: "design_open", label: "オープン" },
				{ label: "クラウド", value: "design_cloud" },
				{ label: "メインフレーム", value: "design_mainfream" },
				{ label: "ヘルプデスク", value: "design_helpdesk" },
			],
			dataReady: false,
			touchStartTime: null,
		}
	},
	methods: {
		tabChange(tabNumber) {
            this.activeTab = tabNumber;

            if (tabNumber === 1) {
                window.location.href ='/opportunities/active';
            } else if (tabNumber === 2) {
                window.location.href ='/resumes/active';
            }
        },
        handleTouchStart(event) {
            this.touchStartTime = Date.now();
            event.target.style.transform = 'scale(0.95)';
        },
        handleTouchEnd(tabNumber) {
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - this.touchStartTime;

            // Reset visual feedback
            event.target.style.transform = 'scale(1)';

            // Only trigger if it's a quick tap (not a long press)
            if (touchDuration < 500) {
                this.tabChange(tabNumber);
            }
        },
		updatePriceMin(value) {
			if (this.priceMin !== value) {
				this.priceMin = value;
			}
		},
		updatePriceMax(value) {
			if (this.priceMax !== value) {
				this.priceMax = value;
			}
		},
		updateUtilizationRate(values) {
			this.utilization_rate = values;
			this.handleInputChange();
		},
		updateUtilizationRateRange(values) {
			// values is an array [minValue, maxValue]
			if (Array.isArray(values) && values.length >= 2) {
				this.utilization_rate_min = values[0];
				this.utilization_rate_max = values[1];
				this.handleInputChange();
			}
		},
		updateWorkingFrequency(values) {
			this.working_frequency = values;
			this.handleInputChange();
		},
		updateWorkingFrequencyRange(values) {
			// values is an array [minKey, maxKey]
			if (Array.isArray(values) && values.length >= 2) {
				this.working_frequency_min = values[0];
				this.working_frequency_max = values[1];
				this.handleInputChange();
			}
		},
		updateAge(values) {
			this.age_min = values[0];
			this.age_max = values[1];
			this.handleInputChange();
		},
		toggleAccordion() {
			this.isOpen = !this.isOpen; // Đảo trạng thái mở/đóng
		},
		toggleAccordion1() {
			this.isOpen1 = !this.isOpen1; // Đảo trạng thái mở/đóng
		},
		toggleAccordion2() {
			this.isOpen2 = !this.isOpen2; // Đảo trạng thái mở/đóng
		},
		toggleAccordion3() {
			this.isOpen3 = !this.isOpen3; // Đảo trạng thái mở/đóng
		},
		toggleAccordion4() {
			this.isOpen4 = !this.isOpen4; // Đảo trạng thái mở/đóng
		},
		toggleAccordion5() {
			this.isOpen5 = !this.isOpen5; // Đảo trạng thái mở/đóng
		},
		async selectOption(option) {
            this.selectedOption = option; // Cập nhật nội dung hiển thị
            this.isOpen6 = false; // Đóng dropdown
            const sort_type = this.sortMap[option]; // Lấy giá trị sort_type từ map
            await this.updateURLAndFetch(1, sort_type); // Cập nhật URL & tải dữ liệu mới
        },
        async updateURLAndFetch(page, sort_type) {
            // Hiển thị hiệu ứng loading
            this.showLoadingOverlay = true;

            try {
                // Lấy tất cả các tham số hiện tại từ URL
                const currentUrl = new URL(window.location.href);
                const params = new URLSearchParams(currentUrl.search);

                // Cập nhật tham số page và sort_type
                params.set('page', page);
                params.set('sort_type', sort_type);
                this.sortType = sort_type;

                // Cập nhật URL mà không reload trang
                window.history.pushState({}, '', `?${params.toString()}`);

                // Gọi API để lấy dữ liệu mới
                if (params.has('resume_search_condition[free_keyword]') ||
                    Array.from(params.keys()).some(key => key.startsWith('resume_search_condition'))) {
                    // Nếu có tham số tìm kiếm, sử dụng API tìm kiếm
                    await this.fetchSearchResults(params, page, sort_type);
                } else {
                    // Nếu không có tham số tìm kiếm, sử dụng API fetchResumes
                    await this.fetchResumes(page, sort_type);
                }
            } catch (error) {
                console.error('Error updating sort:', error.message);
            } finally {
                this.showLoadingOverlay = false;
            }
        },
		closeDropdown(e) {
			if (!this.$refs.dropdown.contains(e.target)) this.isOpen6 = false;
		},
		toggleMobileSearch() {
            this.isMobileSearchOpen = !this.isMobileSearchOpen;
            const sideSearch = document.getElementById('side-search');
            if (sideSearch) {
                if (this.isMobileSearchOpen) {
                    sideSearch.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Prevent body scroll
                } else {
                    sideSearch.classList.remove('active');
                    document.body.style.overflow = ''; // Restore body scroll
                }
            }
        },
        closeMobileSearch() {
            this.isMobileSearchOpen = false;
            const sideSearch = document.getElementById('side-search');
            if (sideSearch) {
                sideSearch.classList.remove('active');
                document.body.style.overflow = ''; // Restore body scroll
            }
        },
		openModal(resumeId) {
			// Mở modal với ID tương ứng
			const modalId = '#display_text_format_' + resumeId;

			// Dùng jQuery để mở modal
			$(modalId).modal('show');
		},
		async fetchResumes(page = 1, sort_type = 'created_at') {
			try {
				const response = await fetch(`/api/resumes_active?page=${page}&sort_type=${sort_type}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'X-CSRFToken': odoo.csrf_token,
						'X-User-ID': this.userId
					},
				});
				const data = await response.json();
				console.log('data',data);
				if (data.success === true) {
					this.resumes = data.data;
					console.log(this.resumes);

					this.currentPage = data.current_page;
					this.totalPages = data.total_pages;
					this.totalRecord = data.total_records;
				} else {
					console.warn("API data is not in the correct format:", data);
				}
			} catch (error) {
				console.log('Error API:', error.message);
				this.errorMessage = error.message;
			}
		},

		// Hàm gọi API tìm kiếm và cập nhật dữ liệu
        async fetchSearchResults(params, page = 1, sort_type = 'created_at') {
            try {
                // Hiển thị hiệu ứng loading
                this.showLoadingOverlay = true;
                this.isSearching = true;

                // Nếu params là URLSearchParams, chuyển thành string
                const paramsString = params instanceof URLSearchParams ? params.toString() : params;

                // Gọi API lấy dữ liệu tìm kiếm
				const user_id = userInfo ? userInfo.user_id : null;
				const company_id = userInfo ? userInfo.user_company_id : null;
                const response = await fetch(`/api/resume_search_condition/search?${paramsString}&page=${page}&sort_type=${sort_type}`, {
                    method: 'GET',
                    headers: {
						'Content-Type': 'application/json',
						'X-User-ID': user_id,
						'X-Company-ID': company_id
					},
                });

                const data = await response.json();
                if (data.success === true) {
                    // Cập nhật dữ liệu
                    this.resumes = data.data;
                    this.currentPage = data.current_page;
                    this.totalPages = data.total_pages;
                    this.totalRecord = data.total_records;

                    // Kiểm tra trạng thái xem cho tất cả dữ liệu
                    if (this.resumes.length > 0) {
                        await this.checkAllViewStatuses();
                    }

                    return true;
                } else {
                    console.warn("API data is not in the correct format:", data);
                    return false;
                }
            } catch (error) {
                console.error('Error fetching search results:', error.message);
                this.errorMessage = `Error fetching resumes: ${error.message}`;
                return false;
            } finally {
                // Ẩn hiệu ứng loading sau 500ms để người dùng thấy sự thay đổi
                setTimeout(() => {
                    this.showLoadingOverlay = false;
                    this.isSearching = false;
                }, 500);
            }
        },
		async add_viewer(partner_id, user_id) {
			try {
				// Tạo URL với user_id nếu có
				let url = `/api/view_partner?partner_id=${encodeURIComponent(partner_id)}`;
				if (user_id) {
					url += `&user_id=${encodeURIComponent(user_id)}`;
				}

				// Gửi yêu cầu GET đến API
				const response = await fetch(url, {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
				});

				// Kiểm tra nếu có lỗi từ phía server
				if (!response.ok) {
					throw new Error('Error: ' + response.statusText);
				}

				// Xử lý kết quả
				const data = await response.json();
				console.log('Response:', data);

			} catch (error) {
				console.error('Fetch error:', error);
				this.errorMessage = error.message;
			}
		},
		select_gender(event) {
			this.gender = event.detail; // Nhận giá trị từ dropdown
			this.handleInputChange(); // Gọi hàm để cập nhật số lượng kết quả tìm kiếm
		},
		select_exp_categories(event) {
			this.exp_categories = event.detail; // Nhận giá trị từ dropdown
			this.handleInputChange(); // Gọi hàm để cập nhật số lượng kết quả tìm kiếm
		},
		select_work_status(event) {
			this.status_single = event.detail; // Nhận giá trị từ dropdown
			this.status = event.detail ? [event.detail] : []; // Cập nhật mảng status để tương thích với logic cũ
			this.handleInputChange(); // Gọi hàm để cập nhật số lượng kết quả tìm kiếm
		},
		// Hàm tính toán chỉ số item bắt đầu
		getStartItemIndex() {
			if (this.totalRecord === 0) return 0;
			return (this.currentPage - 1) * 20 + 1; // Mỗi trang hiển thị 24 item
		},
		// Hàm tính toán chỉ số item kết thúc
		getEndItemIndex() {
			if (this.totalRecord === 0) return 0;
			return Math.min(this.currentPage * 20, this.totalRecord); // Không vượt quá tổng số item
		},
		select_contract_type(event) {
			this.contract_type_single = event.detail; // Nhận giá trị từ dropdown
			this.contract_types = event.detail ? [event.detail] : []; // Cập nhật mảng contract_types để tương thích với logic cũ
			this.handleInputChange(); // Gọi hàm để cập nhật số lượng kết quả tìm kiếm
		},
		async resumes_count() {
			this.isLoading = true;
			try {
                console.log("nationality: ",this.nationality);
				let params = new URLSearchParams();
				params.append("resume_search_condition[free_keyword]", this.free_keyword || '');
				params.append("resume_search_condition[negative_keyword]", this.negative_keyword || '');
				params.append("resume_search_condition[nationality]", this.nationality || '');
				params.append("resume_search_condition[resident]", this.nationality === 'all' ? this.resident : '');
				params.append("resume_search_condition[gender]", this.gender === "未選択" ? '' : this.gender);
				params.append("resume_search_condition[age_min]", this.age_min === null || this.age_min === "未選択" ? '' : this.age_min);
				params.append("resume_search_condition[age_max]", this.age_max === null || this.age_max === "未選択" ? '' : this.age_max);
				params.append("resume_search_condition[with_internal_comment]", this.with_internal_comment || false);
				params.append("resume_search_condition[asking_unit_price_min]", this.priceMin || '');
				params.append("resume_search_condition[asking_unit_price_max]", this.priceMax || '');
				params.append("resume_search_condition[available_time_at_earliest]", this.date_period || '');

				// Xử lý status (đã chuyển từ checkbox sang dropdown)
				if (this.status_single) {
					params.append("resume_search_condition[status]", this.status_single);
				}

				// Xử lý exp_categories
				if (this.exp_categories) {
					params.append("resume_search_condition[exp_categories]", this.exp_categories);
				}

				if (this.partner_types.length > 0) {
					this.partner_types.forEach(value => {
						params.append("resume_search_condition[partner_types][]", value);
					});
				}

				// Xử lý contract_type (đã chuyển từ checkbox sang dropdown)
				if (this.contract_type_single) {
					params.append("resume_search_condition[contract_types]", this.contract_type_single);
				}

				// Send range parameters for utilization rate (only if not full range)
				if (this.utilization_rate_min !== null && this.utilization_rate_min !== undefined &&
					this.utilization_rate_max !== null && this.utilization_rate_max !== undefined) {
					// Check if it's not the full range (25% to 100%)
					if (!(this.utilization_rate_min === 25 && this.utilization_rate_max === 100)) {
						params.append("resume_search_condition[utilization_rate_min]", this.utilization_rate_min);
						params.append("resume_search_condition[utilization_rate_max]", this.utilization_rate_max);
					}
				}

				// Send range parameters for working frequency (only if not full range)
				if (this.working_frequency_min !== null && this.working_frequency_min !== undefined &&
					this.working_frequency_max !== null && this.working_frequency_max !== undefined) {
					// Check if it's not the full range (0 to 6, which is フルリモート to 5日)
					if (!(this.working_frequency_min === 0 && this.working_frequency_max === 6)) {
						params.append("resume_search_condition[working_frequency_min]", this.working_frequency_min);
						params.append("resume_search_condition[working_frequency_max]", this.working_frequency_max);
					}
				}

				if (this.workplaces.length > 0) {
					this.workplaces.forEach(value => {
						params.append("resume_search_condition[workplaces][]", value);
					});
				}

				if (this.rating_flag.length > 0) {
					this.rating_flag.forEach(value => {
						params.append("resume_search_condition[rating_flag][]", value);
					});
				}

				// Gọi API lấy dữ liệu
				const user_id = userInfo ? userInfo.user_id : null;
				const company_id = userInfo ? userInfo.user_company_id : null;
				const response = await fetch(`/api/resume_count?${params.toString()}`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'X-User-ID': user_id,
						'X-Company-ID': company_id
					},
				});

				const data = await response.json();
				console.log('data',data);
				if (data.success === true) {
					this.count_resumes = data.total_records;
				} else {
					console.warn("API data is not in the correct format:", data);
				}
			} catch (error) {
				console.error('Error fetching API:', error.message);
				this.errorMessage = `Error fetching opportunities: ${error.message}`;
			} finally {
				this.isLoading = false; // Dừng loading khi API trả về kết quả
			}
		},
		// Hàm gọi khi có sự thay đổi của form
		handleInputChange() {
			// Nếu đã có một timeout trước đó, xóa nó đi để tránh gọi API quá nhiều
			if (this.searchTimeout) {
				clearTimeout(this.searchTimeout);
			}

			// Thiết lập timeout mới để trì hoãn việc gọi API
			this.searchTimeout = setTimeout(() => {
				this.resumes_count(); // Gọi API sau khi người dùng ngừng thay đổi trong 500ms
			}, 500); // 500ms là khoảng thời gian trì hoãn
		},
		async resumes_keyword(keyword) {
            // Hiển thị hiệu ứng loading
            this.showLoadingOverlay = true;

            let params = new URLSearchParams();
            params.append("resume_search_condition[free_keyword]", keyword);

            try {
                // Cập nhật URL mà không reload trang
                const newUrl = new URL(window.location.href);
                // Xóa tất cả các tham số hiện tại
                Array.from(newUrl.searchParams.keys()).forEach(key => {
                    newUrl.searchParams.delete(key);
                });

                // Thêm tham số từ keyword
                newUrl.searchParams.append("resume_search_condition[free_keyword]", keyword);

                // Cập nhật URL mà không reload trang
                window.history.pushState({}, '', newUrl);

                // Cập nhật biến free_keyword
                this.free_keyword = keyword;

                // Gọi API để lấy kết quả tìm kiếm
                await this.fetchSearchResults(params);
            } catch (error) {
                console.error('Error during keyword search:', error);
            } finally {
                this.showLoadingOverlay = false;
            }
        },
		async resumes_search() {
			this.isFinding = true;
			let params = new URLSearchParams();

			// Các tham số đơn giản
			params.append("resume_search_condition[free_keyword]", this.free_keyword || '');
			params.append("resume_search_condition[negative_keyword]", this.negative_keyword || '');
			params.append("resume_search_condition[nationality]", this.nationality || '');
			params.append("resume_search_condition[resident]", this.resident || '');
			params.append("resume_search_condition[gender]", this.gender || '');
			params.append("resume_search_condition[age_min]", this.age_min === null ? '' : this.age_min);
			params.append("resume_search_condition[age_max]", this.age_max === null ? '' : this.age_max);
			params.append("resume_search_condition[with_internal_comment]", this.with_internal_comment || false);
			params.append("resume_search_condition[asking_unit_price_min]", this.priceMin || '');
			params.append("resume_search_condition[asking_unit_price_max]", this.priceMax || '');
			params.append("resume_search_condition[available_time_at_earliest]", this.date_period || '');

			// Xử lý status (đã chuyển từ checkbox sang dropdown)
			if (this.status_single) {
				params.append("resume_search_condition[status]", this.status_single);
			}

			// Xử lý exp_categories
			if (this.exp_categories) {
				params.append("resume_search_condition[exp_categories]", this.exp_categories);
			}

			if (this.partner_types.length > 0) {
				this.partner_types.forEach(value => {
					params.append("resume_search_condition[partner_types][]", value);
				});
			}

			// Xử lý contract_type (đã chuyển từ checkbox sang dropdown)
			if (this.contract_type_single) {
				params.append("resume_search_condition[contract_types]", this.contract_type_single);
			}

			// Send range parameters for utilization rate (only if not full range)
			if (this.utilization_rate_min !== null && this.utilization_rate_min !== undefined &&
				this.utilization_rate_max !== null && this.utilization_rate_max !== undefined) {
				// Check if it's not the full range (25% to 100%)
				if (!(this.utilization_rate_min === 25 && this.utilization_rate_max === 100)) {
					params.append("resume_search_condition[utilization_rate_min]", this.utilization_rate_min);
					params.append("resume_search_condition[utilization_rate_max]", this.utilization_rate_max);
				}
			}

			// Send range parameters for working frequency (only if not full range)
			if (this.working_frequency_min !== null && this.working_frequency_min !== undefined &&
				this.working_frequency_max !== null && this.working_frequency_max !== undefined) {
				// Check if it's not the full range (0 to 6, which is フルリモート to 5日)
				if (!(this.working_frequency_min === 0 && this.working_frequency_max === 6)) {
					params.append("resume_search_condition[working_frequency_min]", this.working_frequency_min);
					params.append("resume_search_condition[working_frequency_max]", this.working_frequency_max);
				}
			}

			if (this.workplaces.length > 0) {
				this.workplaces.forEach(value => {
					params.append("resume_search_condition[workplaces][]", value);
				});
			}

			if (this.rating_flag.length > 0) {
				this.rating_flag.forEach(value => {
					params.append("resume_search_condition[rating_flag][]", value);
				});
			}

			try {
                // Cập nhật URL mà không reload trang
                const newUrl = new URL(window.location.href);
                // Xóa tất cả các tham số hiện tại
                Array.from(newUrl.searchParams.keys()).forEach(key => {
                    newUrl.searchParams.delete(key);
                });

                // Thêm các tham số mới từ form tìm kiếm
                const searchParams = new URLSearchParams(params.toString());
                Array.from(searchParams.entries()).forEach(([key, value]) => {
                    newUrl.searchParams.append(key, value);
                });

                // Cập nhật URL mà không reload trang
                window.history.pushState({}, '', newUrl);

                // Gọi API để lấy kết quả tìm kiếm
                await this.fetchSearchResults(params);

                // Đóng mobile search form sau khi search thành công
                this.closeMobileSearch();
            } catch (error) {
                console.error('Error during search:', error);
            } finally {
                this.isFinding = false;
            }
		},
		restoreSelection() {
			const urlParams = new URLSearchParams(window.location.search);
			const sort_type = urlParams.get('sort_type') || 'created_at';
			this.selectedOption = Object.keys(this.sortMap).find(key => this.sortMap[key] === sort_type) || "新着（降順）";
		},

		calculateAge(birthday) {
			if (!birthday) return "N/A";

			// Xử lý chuỗi có dạng "YYYY-MM-DD HH:MM:SS"
			if (typeof birthday === "string") {
				birthday = birthday.split(" ")[0];
			}

			const birthDate = new Date(birthday);
			if (isNaN(birthDate.getTime())) return "N/A";

			const today = new Date();
			let age = today.getFullYear() - birthDate.getFullYear();

			const monthDiff = today.getMonth() - birthDate.getMonth();
			if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
				age--;
			}

			return age;
		},
		getGenderLabel(gender) {
			if (gender === "male") return "男性";
			if (gender === "female") return "女性";
			return '';
		},

		// Phương thức để lấy tên danh mục của resume
		getExpCategoryName(resume) {
			const categoryResults = [];

			// Kiểm tra các danh mục và thêm vào kết quả
			if (resume.categories_consultation) {
				const consulCategories = resume.categories_consultation.split(',');
				const consulNames = consulCategories.map(cat => {
					const category = this.categories.find(c => c.value === cat);
					return category ? `コンサル - ${category.label}` : '';
				}).filter(Boolean);

				categoryResults.push(...consulNames);
			}

			if (resume.categories_development) {
				const devCategories = resume.categories_development.split(',');
				const devNames = devCategories.map(cat => {
					const category = this.categoriesDev.find(c => c.value === cat);
					return category ? `開発 - ${category.label}` : '';
				}).filter(Boolean);

				categoryResults.push(...devNames);
			}

			if (resume.categories_infrastructure) {
				const infraCategories = resume.categories_infrastructure.split(',');
				const infraNames = infraCategories.map(cat => {
					const category = this.categoriesInfra.find(c => c.value === cat);
					return category ? `インフラ - ${category.label}` : '';
				}).filter(Boolean);

				categoryResults.push(...infraNames);
			}

			if (resume.categories_design) {
				const opCategories = resume.categories_design.split(',');
				const opNames = opCategories.map(cat => {
					const category = this.categories3.find(c => c.value === cat);
					return category ? `運用・保守 - ${category.label}` : '';
				}).filter(Boolean);

				categoryResults.push(...opNames);
			}

			return categoryResults.length > 0 ? categoryResults.join('／') : '-';
		},
		async isViewed(partnerId) {
			if (this.viewedStatuses[partnerId] === undefined) {
				await this.fetchViewedStatus(partnerId); // Chỉ gọi API nếu chưa có trong viewedStatuses
			}
			return this.viewedStatuses[partnerId] || false; // Trả về trạng thái đã xem nếu có, false nếu chưa xem
		},

		// Lấy trạng thái đã xem của partner từ API
		async fetchViewedStatus(partnerId) {
			try {
				const response = await fetch(`/api/check_view?user_id=${this.isloggedin}&partner_id=${partnerId}`);
				const data = await response.json();
				this.viewedStatuses[partnerId] = data.success ? data.viewed : false; // Lưu kết quả vào viewedStatuses
			} catch (error) {
				console.error('Error fetching viewed status:', error);
				this.viewedStatuses[partnerId] = false; // Mặc định là chưa xem nếu có lỗi
			}
		},

		// Kiểm tra tất cả các partner đã được xem hay chưa (chỉ gọi API 1 lần cho mỗi partner)
		async checkAllViewStatuses() {
			const partnerIds = this.resumes.map(resume => resume.id);
			await Promise.all(partnerIds.map(id => this.isViewed(id))); // Kiểm tra tất cả partnerId trong danh sách resumes
		},

		async toggleBookmark(id) {
			try {
				const response = await fetch('/api/react_res', {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						user_id: this.userId,
						resumes_id: id,
					}),
				});

				const result = await response.json();
				console.log(result);
				if (result.result.success) {
					// Kiểm tra xem URL có tham số tìm kiếm không
					const params = new URLSearchParams(window.location.search);
					const hasSearchParams = Array.from(params.keys()).some(key =>
						key.startsWith('resume_search_condition'));

					if (hasSearchParams) {
						// Nếu có tham số tìm kiếm, sử dụng fetchSearchResults
						await this.fetchSearchResults(params, this.currentPage, this.sortType);
					} else {
						// Nếu không có tham số tìm kiếm, sử dụng fetchResumes
						await this.fetchResumes(this.currentPage, this.sortType);
					}
				}
			} catch (error) {
				console.error("Bookmark failed:", error);
			}
		},

		check_new(date) {
			var today = new Date();
			var createdDate = new Date(date);

			// Tính số ngày từ ngày tạo đến hiện tại
			var diffTime = today - createdDate;
			var diffDays = diffTime / (1000 * 60 * 60 * 24);

			// Nếu số ngày <= 7, coi là mới
			return diffDays <= 7;
		},
	},
	async mounted() {
        document.querySelectorAll('label').forEach(function(label) {
            if (label.textContent.trim() === '就業場所') {
                label.style.height = '75%';
            }
        });
		$(function () {
			$('[data-toggle="tooltip"]').tooltip()
		});
		document.addEventListener("click", this.closeDropdown);

        // Add event listener for close button
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('search-close-btn')) {
                this.closeMobileSearch();
            }
        });

		// Handle window resize to close mobile search on desktop
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileSearch();
            }
        });

		const params = new URLSearchParams(window.location.search);
		this.userId = userInfo ? userInfo.user_id : null;
		const page = parseInt(params.get('page')) || 1;
		this.sortType = params.get('sort_type') || 'created_at';

		// Đặt ở đầu script, trước khi bất kỳ code nào khác chạy
		(function() {
			// Kiểm tra ngay khi script được thực thi, trước khi DOM load
			const previousUrl = sessionStorage.getItem('previous_url');
			const isRefresh = performance.navigation.type === 1;

			// Nếu đây là refresh và có params
			if (isRefresh && window.location.search) {
				// Redirect ngay lập tức trước khi trang render
				window.location.replace('/resumes/active');
				// Ngăn phần còn lại của script chạy
				throw new Error('Redirecting to clean URL');
			}

			// Xóa previous_url để không gây nhầm lẫn trong các lần load sau
			sessionStorage.removeItem('previous_url');

			// Vẫn giữ event beforeunload để set previous_url cho lần refresh tiếp theo
			window.addEventListener('beforeunload', () => {
				sessionStorage.setItem('previous_url', window.location.href);
			});
		})();

		try {
            // Kiểm tra xem URL có chứa tham số tìm kiếm không
            const hasSearchParams = Array.from(params.keys()).some(key =>
                key.startsWith('resume_search_condition'));

            if (hasSearchParams) {
                // Hiển thị hiệu ứng loading
                this.showLoadingOverlay = true;

                // Nếu có tham số tìm kiếm, sử dụng API tìm kiếm
                await this.fetchSearchResults(params, page, this.sortType);

                // Khôi phục các giá trị form từ URL
                this.restoreFormValues();
            } else {
                // Nếu không có tham số tìm kiếm, sử dụng API fetchResumes
                await this.fetchResumes(page, this.sortType);

                // Đợi kiểm tra trạng thái xem cho tất cả dữ liệu
                if (this.resumes.length > 0) {
                    await this.checkAllViewStatuses();
                }
            }

            // Đếm số lượng kết quả tìm kiếm
            await this.resumes_count();

            // Khôi phục lựa chọn
            this.restoreSelection();

            // Đánh dấu dữ liệu đã sẵn sàng
            this.dataReady = true;
        } catch (error) {
            console.error('Error loading data:', error);
            // Vẫn đánh dấu là sẵn sàng để người dùng thấy nội dung, dù có lỗi
            this.dataReady = true;
        } finally {
            // Ẩn hiệu ứng loading
            this.showLoadingOverlay = false;
        }
	},

	// Khôi phục các giá trị form từ URL
    restoreFormValues() {
        const params = new URLSearchParams(window.location.search);

        // Khôi phục các giá trị text input
        this.free_keyword = params.get('resume_search_condition[free_keyword]') || '';
        this.negative_keyword = params.get('resume_search_condition[negative_keyword]') || '';
        this.date_period = params.get('resume_search_condition[available_time_at_earliest]') || '';
        this.priceMin = params.get('resume_search_condition[asking_unit_price_min]') || null;
        this.priceMax = params.get('resume_search_condition[asking_unit_price_max]') || null;

        // Khôi phục các giá trị radio button
        this.nationality = params.get('resume_search_condition[nationality]') || '';
        this.resident = params.get('resume_search_condition[resident]') || '';
        this.with_internal_comment = params.get('resume_search_condition[with_internal_comment]') === 'true';

        // Khôi phục các giá trị dropdown
        this.gender = params.get('resume_search_condition[gender]') || '';

        // Khôi phục giá trị tuổi cho slider
        const ageMin = params.get('resume_search_condition[age_min]');
        const ageMax = params.get('resume_search_condition[age_max]');
        this.age_min = ageMin ? parseInt(ageMin) : null;
        this.age_max = ageMax ? parseInt(ageMax) : null;
        this.status_single = params.get('resume_search_condition[status]') || '';
        this.exp_categories = params.get('resume_search_condition[exp_categories]') || '';
        this.contract_type_single = params.get('resume_search_condition[contract_types]') || '';

        // Khôi phục các giá trị mảng
        this.partner_types = params.getAll('resume_search_condition[partner_types][]');
        this.workplaces = params.getAll('resume_search_condition[workplaces][]');
        this.rating_flag = params.getAll('resume_search_condition[rating_flag][]');

        // Handle single values for sliders
        const utilizationRateParams = params.getAll('resume_search_condition[utilization_rate][]');
        this.utilization_rate = utilizationRateParams.length > 0 ? utilizationRateParams[0] : null;

        const workingFrequencyParams = params.getAll('resume_search_condition[working_frequency][]');
        this.working_frequency = workingFrequencyParams.length > 0 ? workingFrequencyParams[0] : null;
    },
	computed: {
		displayedRecords() {
			return Math.min(this.resumes.length, 2);
		}
	},
	watch: {
		workplaces: {
			handler() {
				this.handleInputChange();
			},
			deep: true,
		},
		priceMin: {
			handler() {
				this.handleInputChange();
			},
			deep: true,
		},
		priceMax: {
			handler() {
				this.handleInputChange();
			},
			deep: true,
		},
		date_period: {
			handler() {
				this.handleInputChange();
			},
			deep: true,
		},

	},
}
export default resumes_active;