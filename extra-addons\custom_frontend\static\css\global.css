/* Global CSS for Mi52 - Font Meiryo */

/* Font face declaration for Meiryo */
@font-face {
    font-family: 'Mei<PERSON>';
    src: local('<PERSON><PERSON>'), local('<PERSON><PERSON> UI');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Meiryo';
    src: local('Meiryo Bold'), local('Meiryo UI Bold');
    font-weight: bold;
    font-style: normal;
}

/* Apply Meiryo font to all elements */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    font-family: 'Meiryo', 'Meiryo UI', sans-serif !important;
}

/* Ensure font is applied to inputs and buttons */
input, button, textarea, select {
    font-family: 'Meiryo', 'Meiryo UI', sans-serif !important;
}

/* Apply to all elements with specific font-family */
[style*="font-family"] {
    font-family: 'Meiryo', 'Meiryo UI', sans-serif !important;
}

/* Override any other font families that might be set */
.material-icons {
    font-family: 'Material Icons' !important;
}

/* Font weights */
.font-weight-light {
    font-weight: 300 !important;
}

.font-weight-normal {
    font-weight: 400 !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

/* Font smoothing for better rendering */
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: 'Meiryo', 'Meiryo UI', sans-serif !important;
}
