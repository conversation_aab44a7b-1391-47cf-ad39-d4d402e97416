/*! CSS Used from: https://assign-navi.jp/assets/application-5c62c0bf10dc71ffa28617d13da03c81eeb71d47f2b1b1f657b8b6641e347cff.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    main,
    section {
        display: block;
    }

    h1,
    h2,
    h3,
    h4,
    h5 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    h1,
    h2,
    h3,
    h4,
    h5 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.75rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    h5 {
        font-size: 1.25rem;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12,
    .col-md-10,
    .col-md-3,
    .col-md-4,
    .col-md-8 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 768px) {
        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }

        .col-md-10 {
            flex: 0 0 83.333333%;
            max-width: 83.333333%;
        }
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .card-body {
        flex: 1 1 auto;
        min-height: 1px;
        padding: 1.25rem;
    }

    .bg-white {
        background-color: #fff !important;
    }

    .rounded-circle {
        border-radius: 50% !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-block {
            display: block !important;
        }
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .position-relative {
        position: relative !important;
    }

    .position-absolute {
        position: absolute !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mt-1 {
        margin-top: 0.25rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mt-4 {
        margin-top: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mt-5 {
        margin-top: 3rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .px-1 {
        padding-right: 0.25rem !important;
    }

    .px-1 {
        padding-left: 0.25rem !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pl-3 {
        padding-left: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .px-4 {
        padding-right: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4,
    .px-4 {
        padding-left: 1.5rem !important;
    }

    .pt-5,
    .py-5 {
        padding-top: 3rem !important;
    }

    .py-5 {
        padding-bottom: 3rem !important;
    }

    .mx-auto {
        margin-right: auto !important;
    }

    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 768px) {
        .px-md-0 {
            padding-right: 0 !important;
        }

        .px-md-0 {
            padding-left: 0 !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .py-md-5 {
            padding-top: 3rem !important;
        }

        .py-md-5 {
            padding-bottom: 3rem !important;
        }

        .mx-md-auto {
            margin-right: auto !important;
        }

        .mx-md-auto {
            margin-left: auto !important;
        }
    }

    .text-center {
        text-align: center !important;
    }

    @media (min-width: 768px) {
        .text-md-center {
            text-align: center !important;
        }
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        h2,
        h3,
        p {
            orphans: 3;
            widows: 3;
        }

        h2,
        h3 {
            page-break-after: avoid;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    h1,
    h2,
    h3,
    h4,
    h5 {
        font-weight: 300;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .card .card-body h3,
    .card .card-body h4,
    .card .card-body h5 {
        font-weight: 400;
    }

    .btn-rounded {
        border-radius: 10em;
    }

    .bold {
        font-weight: 500;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-default {
        font-size: .875rem !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-extralarge {
        font-size: 1.25rem !important;
    }

    .font-ll {
        font-size: 1.5rem !important;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-sub-text {
        color: #3479ca;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .px-1 {
        padding-right: .25rem !important;
    }

    .px-1 {
        padding-left: .25rem !important;
    }

    .mt-1 {
        margin-top: .25rem !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pl-3 {
        padding-left: 1rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .px-4 {
        padding-right: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4,
    .px-4 {
        padding-left: 1.5rem !important;
    }

    .mt-4 {
        margin-top: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .pt-5,
    .py-5 {
        padding-top: 2rem !important;
    }

    .py-5 {
        padding-bottom: 2rem !important;
    }

    .mt-5 {
        margin-top: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .pl-6 {
        padding-left: 2.5rem !important;
    }

    .mb-6 {
        margin-bottom: 2.5rem !important;
    }


    @media (min-width: 768px) {
        .px-md-0 {
            padding-right: 0 !important;
        }

        .px-md-0 {
            padding-left: 0 !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .py-md-5 {
            padding-top: 2rem !important;
        }

        .py-md-5 {
            padding-bottom: 2rem !important;
        }
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    .bg-sub-color {
        background-color: #3479ca;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-white {
        color: #fff;
    }

    .material-icons.md-36 {
        font-size: 36px;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    /* Breadcrumb styles moved to layout.css */

    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }

        main .sp_sides_uniter {
            margin: 0 -15px;
        }
    }

    .title {
        background-color: #1072e9;
        background-size: cover;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    :focus {
        outline: 0;
    }

    ul {
        list-style: none;
        padding: 0;
    }

    ul.disc {
        list-style-type: disc;
    }

    .line-height-1-8 {
        line-height: 1.8;
    }

    .mb-60px {
        margin-bottom: 3.75rem;
    }

    .anchor-links-margin {
        margin-top: 1.75rem;
        margin-bottom: 27px;
    }

    .userguide-text-black {
        color: #2a3942;
    }

    .guide-section .card-body {
        padding-top: 3.75rem;
        padding-bottom: 3.75rem;
    }

    .userguide-sub-title {
        border-left: .375rem solid;
        padding-left: .75rem;
    }

    .userguide-sub-title.prohibited {
        border-color: #f44336;
    }

    .userguide-sub-title.recommended {
        border-color: #1072e9;
    }

    .ellipse_btn {
        background-color: rgba(255, 255, 255, 0.8);
        position: fixed;
        right: 1rem;
        bottom: 1rem;
    }

    .anchor-icon {
        width: 30px;
        height: 30px;
        background-image: url(https://assign-navi.jp/assets/img/plan/round_arrow_down-9f7fff42a30837c7003ffed6e25df16cc6c19d7902df1278ac049715e0f429e5.png);
        background-size: contain;
        left: calc(50% - 15px);
        bottom: -15px;
        transition: .5s ease;
    }

    @media (min-width: 768px) {
        .anchor-nav:hover .anchor-icon {
            bottom: -27px;
        }

        .userguide-card-pc {
            max-width: 256px;
            margin-left: .75rem;
            margin-right: .75rem;
        }
    }

    @media (max-width: 767px) {
        .anchor-icon {
            top: calc(50% - 15px);
            right: 16px;
            bottom: auto;
            left: auto;
        }

        .guide-section .card-body {
            padding-top: 2.5rem;
            padding-bottom: 2.5rem;
        }

        .anchor-links-margin {
            margin-top: .5rem;
            margin-bottom: .75rem;
        }
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

/* Userguide specific styles moved to layout.css */

    .userguide-content {
        padding: 15px 10px !important;
    }

    .section-title {
        font-size: 16px !important;
        margin-bottom: 12px !important;
    }

    .category-title {
        font-size: 14px !important;
        margin-bottom: 8px !important;
        font-weight: 600 !important;
    }

    .prohibition-list li {
        font-size: 13px !important;
        line-height: 1.4 !important;
        margin-bottom: 6px !important;
    }

    .sub-list li {
        font-size: 12px !important;
        line-height: 1.3 !important;
        margin-bottom: 4px !important;
    }

    .disclaimer p {
        font-size: 12px !important;
        line-height: 1.3 !important;
        color: #666 !important;
        font-style: italic !important;
    }

    .userguide-intro p {
        font-size: 13px !important;
        line-height: 1.4 !important;
        margin-bottom: 8px !important;
    }
}

/* Custom Responsive Userguide Styles */
.userguide-content {
    padding: 40px 20px;
}

.userguide-intro {
    margin-bottom: 30px;
}

.userguide-intro p {
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 10px;
    color: #333;
}

.userguide-section {
    margin-top: 20px;
}

.section-title {
    color: #333;
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.category-section {
    margin-bottom: 25px;
}

.category-title {
    color: #333;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 15px;
    padding-left: 10px;
    border-left: 4px solid #28a745;
}

.prohibition-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 20px;
}

.prohibition-list li {
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    color: #333;
}

.sub-list {
    list-style: none;
    padding-left: 20px;
    margin-top: 8px;
}

.sub-list li {
    font-size: 14px;
    color: #555;
    margin-bottom: 5px;
    padding-left: 15px;
    position: relative;
}

.sub-list li:before {
    content: "→";
    position: absolute;
    left: 0;
    color: #007bff;
}

.disclaimer {
    margin-top: 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border-left: 4px solid #ffc107;
    border-radius: 4px;
}

.disclaimer p {
    font-size: 14px;
    color: #666;
    font-style: italic;
    margin-bottom: 0;
    line-height: 1.5;
}

/* Mobile responsive styles - iPhone optimized */
@media (max-width: 767px) {
    .userguide-content {
        padding: 15px 10px !important;
    }

    .userguide-intro {
        margin-bottom: 20px !important;
    }

    .userguide-intro p {
        font-size: 14px !important;
        line-height: 1.5 !important;
        margin-bottom: 8px !important;
    }

    .userguide-section {
        margin-top: 15px !important;
    }

    .section-title {
        font-size: 16px !important;
        margin-bottom: 12px !important;
        padding-bottom: 6px !important;
        border-bottom-width: 1px !important;
    }

    .category-section {
        margin-bottom: 18px !important;
    }

    .category-title {
        font-size: 14px !important;
        margin-bottom: 10px !important;
        padding-left: 6px !important;
        border-left-width: 3px !important;
    }

    .prohibition-list {
        margin-bottom: 12px !important;
    }

    .prohibition-list li {
        font-size: 13px !important;
        padding-left: 12px !important;
        margin-bottom: 5px !important;
        line-height: 1.4 !important;
    }

    .sub-list {
        padding-left: 12px !important;
        margin-top: 5px !important;
    }

    .sub-list li {
        font-size: 12px !important;
        padding-left: 10px !important;
        margin-bottom: 3px !important;
        line-height: 1.3 !important;
    }

    .disclaimer {
        padding: 10px !important;
        margin-top: 15px !important;
    }

    .disclaimer p {
        font-size: 12px !important;
        line-height: 1.4 !important;
    }

    /* iPhone specific optimizations */
    .sp_sides_uniter {
        margin: 0 5px !important;
    }

    .card-body {
        padding: 15px 10px !important;
    }

    /* Override existing mobile styles */
    .guide-section .card-body {
        padding: 15px 10px !important;
    }
}

/* Tablet responsive styles */
@media (min-width: 768px) and (max-width: 1023px) {
    .userguide-content {
        padding: 30px 20px;
    }

    .section-title {
        font-size: 17px;
    }

    .category-title {
        font-size: 15px;
    }

    .prohibition-list li {
        font-size: 14px;
    }
}