from odoo import http
from odoo.http import request
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)
class ReactionController(http.Controller):
    from datetime import datetime

    @http.route('/api/react_opp', type='json', auth="public", methods=['POST'], csrf=False)
    def react_opp(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get('user_id')
        opportunities_id = data.get("opportunities_id")

        if not user_id or not opportunities_id:
            return {"success": False, "message": "Missing required fields"}

        Reaction = request.env['vit.reaction'].sudo()

        # Kiểm tra xem user đã có reaction chưa
        existing_react = Reaction.search([
            ('user_id', '=', int(user_id)),
            ('opportunities_id', '=', opportunities_id)
        ], limit=1)

        if existing_react:
            # Nếu đã tồn tại thì đảo trạng thái 1 ⇄ 0
            new_status = 0 if existing_react.status == 1 else 1
            existing_react.write({
                'status': new_status,
                'updated_at': datetime.now(),
                'updated_by': str(user_id),
            })
            return {"success": True, "message": "Reaction updated", "status": new_status}
        else:
            # Nếu chưa có thì tạo mới
            new_react = Reaction.create({
                'user_id': int(user_id),
                'opportunities_id': opportunities_id,
                'status': 1,
                'created_at': datetime.now(),
                'created_by': str(user_id),
            })
            return {"success": True, "message": "Reaction created", "status": 1}

    @http.route('/api/react_count_opp', type='json', auth="public", methods=['POST'], csrf=False)
    def react_count(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get("user_id")
        opportunities_id = data.get("opportunities_id")

        if not opportunities_id:
            return {"success": False, "message": "Missing opportunities_id"}

        # Count reactions
        like_count = request.env['vit.reaction'].sudo().search_count([
            ('opportunities_id', '=', opportunities_id),
            ('status', '=', 1)
        ])

        # Check user react opportunities
        user_liked = False
        if user_id:
            existing_react = request.env['vit.reaction'].sudo().search([
                ('user_id', '=', int(user_id)),
                ('opportunities_id', '=', opportunities_id),
                ('status', '=', 1)
            ], limit=1)
            user_liked = bool(existing_react)

        return {
            "success": True,
            "opportunities_id": opportunities_id,
            "likes": like_count,
            "liked": user_liked
        }

    @http.route('/api/react_res', type='json', auth="public", methods=['POST'], csrf=False)
    def react_res(self):
        data = request.httprequest.get_json(silent=True)
        
        user_id = data.get('user_id')
        resumes_id = data.get("resumes_id")

        if not user_id or not resumes_id:
            return {"success": False, "message": "Missing required fields"}

        Reaction = request.env['vit.reaction'].sudo()

        # Kiểm tra xem user đã có reaction chưa
        existing_react = Reaction.search([
            ('user_id', '=', int(user_id)),
            ('resumes_id', '=', int(resumes_id))
        ], limit=1)

        if existing_react:
            # Nếu đã tồn tại, đảo trạng thái 0 ⇄ 1
            new_status = 0 if existing_react.status == 1 else 1
            existing_react.write({
                'status': new_status,
                'updated_at': datetime.now(),
                'updated_by': str(user_id),
            })
            return {"success": True, "message": "Reaction updated", "status": new_status}
        else:
            # Nếu chưa có thì tạo mới với status = 1
            Reaction.create({
                'user_id': int(user_id),
                'resumes_id': resumes_id,
                'status': 1,
                'created_at': datetime.now(),
                'created_by': str(user_id),
            })
            return {"success": True, "message": "Reaction created", "status": 1}

    @http.route('/api/react_count_res', type='json', auth="public", methods=['POST'])
    def react_count_res(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get("user_id")
        resumes_id = data.get("resumes_id")

        if not resumes_id:
            return {"success": False, "message": "Missing resumes_id"}

        try:
            resumes_id = int(resumes_id)  # Chuyển resumes_id về int
            user_id = int(user_id) if user_id else None  # Chuyển user_id về int nếu có
        except ValueError:
            return {"success": False, "message": "Invalid user_id or resumes_id"}

        # Count reactions (status = 1)
        like_count = request.env['vit.reaction'].sudo().search_count([
            ('resumes_id', '=', resumes_id),
            ('status', '=', 1)
        ])

        # Check if user has reacted (status = 1)
        user_liked = False
        if user_id:
            existing_react = request.env['vit.reaction'].sudo().search([
                ('user_id', '=', int(user_id)),
                ('resumes_id', '=', resumes_id),
                ('status', '=', 1)
            ], limit=1)

            user_liked = user_liked = True if existing_react else False  

        return {
            "success": True,
            "resumes_id": resumes_id,
            "likes": like_count,
            "liked": user_liked
        }
        
    @http.route('/api/get_reaction', type='json', auth="public", methods=['POST'])
    def get_reaction(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get("user_id")
        if not user_id:
            return {"success": False, "message": "Missing user_id"}

        domain = [('user_id', '=', int(user_id)),
                  ('status', '!=', 0)]
        reactions = request.env['vit.reaction'].sudo().search(domain)

        opportunities_list = []
        resumes_list = []
        companies_list = []

        for reaction in reactions:
            if reaction.opportunities_id:
                opportunities_list.append({
                    "id": reaction.opportunities_id.id,
                    "name": reaction.opportunities_id.subject,
                    "reaction_date": str(reaction.created_at)
                })

            elif reaction.resumes_id:
                resumes_list.append({
                    "id": reaction.resumes_id.id,
                    "name": reaction.resumes_id.initial_name,
                    "reaction_date": str(reaction.created_at)
                })

            elif reaction.company_id:
                companies_list.append({
                    "id": reaction.company_id.id,
                    "name": reaction.company_id.name,
                    "reaction_date": str(reaction.created_at)
                })

        return {
            "success": True,
            "opportunities": opportunities_list,
            "resumes": resumes_list,
            "companies": companies_list,
        }
    
    @http.route('/api/react_company', type='json', auth="public", methods=['POST'], csrf=False)
    def react_company(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get('user_id')
        company_id = data.get("company_id")

        if not user_id or not company_id:
            return {"success": False, "message": "Missing required fields"}

        Reaction = request.env['vit.reaction'].sudo()

        # Kiểm tra xem user đã có reaction chưa
        existing_react = Reaction.search([
            ('user_id', '=', int(user_id)),
            ('company_id', '=', int(company_id))
        ], limit=1)

        if existing_react:
            # Nếu đã tồn tại thì đảo trạng thái 1 ⇄ 0
            new_status = 0 if existing_react.status == 1 else 1
            existing_react.write({
                'status': new_status,
                'updated_at': datetime.now(),
                'updated_by': str(user_id),
            })
            return {"success": True, "message": "Reaction updated", "status": new_status}
        else:
            # Nếu chưa có thì tạo mới
            new_react = Reaction.create({
                'user_id': int(user_id),
                'company_id': company_id,
                'status': 1,
                'created_at': datetime.now(),
                'created_by': str(user_id),
            })
            
            if new_react:
                return {"success": True, "message": "Reaction created", "status": 1}
            
            return {"success": False, "message": "Fail to create new"}
        
    @http.route('/api/react_count_company', type='json', auth="public", methods=['POST'], csrf=False)
    def react_count_company(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get("user_id")
        company_id = data.get("company_id")

        if not company_id:
            return {"success": False, "message": "Missing opportunities_id"}

        # Count reactions
        like_count = request.env['vit.reaction'].sudo().search_count([
            ('company_id', '=', int(company_id)),
            ('status', '=', 1)
        ])

        # Check user react opportunities
        user_liked = False
        if user_id:
            existing_react = request.env['vit.reaction'].sudo().search([
                ('user_id', '=', int(user_id)),
                ('company_id', '=', company_id),
                ('status', '=', 1)
            ], limit=1)
            user_liked = bool(existing_react)

        return {
            "success": True,
            "opportunities_id": company_id,
            "likes": like_count,
            "liked": user_liked
        }
        
    @http.route('/api/delete_reaction', type='json', auth="public", methods=['POST'], csrf=False)
    def delete_reaction(self):
        data = request.httprequest.get_json(silent=True)
        reaction_type = data.get("type")
        target_id = data.get("id")

        if not reaction_type or not target_id:
            return {"success": False, "message": "Missing type or id"}

        if reaction_type not in ["opportunities_id", "resumes_id", "company_id"]:
            return {"success": False, "message": "Invalid type"}

        domain = [(reaction_type, '=', int(target_id))]

        # Tìm các reaction thoả điều kiện
        reactions = request.env['vit.reaction'].sudo().search(domain)

        if reactions:
            reactions.unlink()
            return {"success": True, "message": f"Deleted {len(reactions)} reaction(s)"}
        else:
            return {"success": False, "message": "No matching reaction found"}