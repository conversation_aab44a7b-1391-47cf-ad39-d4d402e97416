import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const MatchingRequest = {
    'template': `

<main class="pb-3 margin-header" id="vue-app">
    <!-- Loading overlay -->
    <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div class="loading-text mt-3">処理中です。しばらくお待ちください...</div>
        </div>
    </div>
    ${createBreadcrumb([
        { text: 'サービスメニュー', link: null },
        { text: 'コンシェルジュサービス', link: null },
        { text: '{{title}}', link: null, current: true }
    ])}
    <div class="container-fluid">
        <div style="display: flex; justify-content: flex-start; width: 100%; gap: 2%;">
            <div :style="tabStyles(1)" @click="tabChange(1)" class="tabButton">
                案件リクエスト
            </div>
            <div :style="tabStyles(2)" @click="tabChange(2)" style="border: 2px solid  #f5c542; margin-left: -1.2%" class="tabButton">
                人財リクエスト
            </div>
        </div>
    </div>
    <div class="container-fluid grabient position-relative">
        <div class="row mb-4 mb-md-0">
            <div class="col-12 mb-4 mb-md-0">
                <div class="text-center mb-4">
                    <p>必要項目を入力してお申し込みください。</p>
                    <p>担当者よりご連絡を差し上げます。</p>
                </div>
                <div class="row justify-content-center">
                    <div class="hihi col-12 col-md-8 col-lg-6">
                        <div class="card matching-request-form">
                            <form id="matching_request_form" @submit.prevent="submitForm">
                                <div class="form-group mb-4">
                                    <label class="label-com" for="company_name">
                                        会社名
                                        <span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block required-badge">必須</span>
                                    </label>
                                    <input type="text" id="company_name" v-model="companyName"
                                        class="form-control">
                                    <div class="text-danger text-left" v-if="!check_company_name">
                                        会社名を入力してください。
                                    </div>
                                </div>

                                <div class="form-group mb-4">
                                    <label class="label-com" for="full_name">
                                        名前(姓・名)
                                        <span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block required-badge">必須</span>
                                    </label>
                                    <input type="text" id="full_name" v-model="fullName" class="form-control">
                                    <div class="text-danger text-left" v-if="!check_full_name">
                                        名前を入力してください。
                                    </div>
                                </div>

                                <div class="form-group mb-4">
                                    <label class="label-com" for="email">
                                        メールアドレス
                                        <span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block required-badge">必須</span>
                                    </label>
                                    <input type="email" id="email" v-model="email" class="form-control">
                                    <div class="text-danger text-left" v-if="!check_email">
                                        メールアドレスを入力してください。
                                    </div>
                                </div>

                                <div class="form-group mb-4">
                                    <label class="label-com" for="phone">
                                        電話番号
                                    </label>
                                    <input type="tel" id="phone" v-model="phone" class="form-control" required>
                                </div>

                                <div class="form-group mb-4">
                                    <label class="label-com" for="request_content">
                                        リクエスト内容
                                        <span
                                            class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block required-badge">必須</span>
                                    </label>
                                    <textarea id="request_content" v-model="requestContent"
                                        class="form-control" rows="5"></textarea>
                                    <div class="text-danger text-left" v-if="!check_request_content">
                                        リクエスト内容を入力してください。
                                    </div>
                                </div>

                                <div class="text-center mt-5 mb-4">
                                    <button type="submit" class="btn px-5" style="background: linear-gradient(to right,rgb(236, 228, 111),rgb(252, 189, 17)); color: white; border-color: #f5c542;">申し込み</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/matching_request.css" />
    `,
    data() {
        return {
            companyName: '',
            fullName: '',
            email: '',
            phone: '',
            requestContent: '',
            title: 'コンシェルジュサービス',
            referenceCode: null,
            loading: false,
            check_company_name: true,
            check_full_name: true,
            check_email: true,
            check_request_content: true,
            activeTab: 1,
            // Store form data for each tab
            opportunityData: {
                companyName: '',
                fullName: '',
                email: '',
                phone: '',
                requestContent: ''
            },
            resumeData: {
                companyName: '',
                fullName: '',
                email: '',
                phone: '',
                requestContent: ''
            }
        }
    },
    mounted() {
        this.user_id = userInfo ? userInfo.user_id : null;
        this.getUserInfo();
        this.type = this.$route.params.type;
        if (this.type === 'opportunity') {
            this.title += '/案件';
            this.activeTab = 1;
        } else if (this.type === 'resume') {
            this.title += '/人財';
            this.activeTab = 2;
        }
        this.company_id = userInfo ? userInfo.user_company_id : null;
        this.getCompany();

        // Load saved form data if available
        this.loadFormData();
    },
    methods: {
        tabChange(tabNumber) {
            // Save current form data before switching tabs
            this.saveFormData();

            this.activeTab = tabNumber;

            // Update route without page reload
            const newType = tabNumber === 1 ? 'opportunity' : 'resume';
            if (this.type !== newType) {
                this.$router.push({
                    name: 'matching_request',
                    params: { type: newType }
                }).then(() => {
                    // Update title and type after route change
                    this.type = newType;
                    this.title = 'コンシェルジュサービス';
                    this.title += newType === 'opportunity' ? '/案件' : '/人財';

                    // Load form data for the selected tab
                    this.loadFormData();
                });
            }
        },

        // Save form data for the current tab
        saveFormData() {
            const currentData = {
                companyName: this.companyName,
                fullName: this.fullName,
                email: this.email,
                phone: this.phone,
                requestContent: this.requestContent
            };

            if (this.activeTab === 1) {
                this.opportunityData = currentData;
            } else if (this.activeTab === 2) {
                this.resumeData = currentData;
            }
        },

        // Load form data for the current tab
        loadFormData() {
            const data = this.activeTab === 1 ? this.opportunityData : this.resumeData;

            // Only load saved data if it exists (has been saved before)
            if (data.companyName || data.fullName || data.email || data.requestContent) {
                this.companyName = data.companyName;
                this.fullName = data.fullName;
                this.email = data.email;
                this.phone = data.phone;
                this.requestContent = data.requestContent;
            }
        },

        tabStyles(tabNumber) {
            if (this.activeTab === tabNumber) {
                return {
                    background: 'linear-gradient(to right,rgb(236, 228, 111),rgb(252, 189, 17))',
                    color: 'white',
                    borderColor: '#f5c542',
                };
            } else {
                return {
                    backgroundColor: '#e9ebd3',
                    border: '1px solid  #f5c542'
                };
            }
        },

        async getUserInfo() {
            try {
                const response = await fetch('/api/get_user_info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: this.user_id
                    })
                });

                const result = await response.json();

                console.log("Get info: ", result);
                if (result.result.success) {
                    // Set user info for both tabs if they haven't been filled yet
                    if (!this.opportunityData.companyName) {
                        this.opportunityData.companyName = result.result.company_name;
                        this.opportunityData.fullName = result.result.username;
                        this.opportunityData.email = result.result.email;
                        this.opportunityData.phone = result.result.phone;
                    }

                    if (!this.resumeData.companyName) {
                        this.resumeData.companyName = result.result.company_name;
                        this.resumeData.fullName = result.result.username;
                        this.resumeData.email = result.result.email;
                        this.resumeData.phone = result.result.phone;
                    }

                    // Set current form fields
                    this.companyName = result.result.company_name;
                    this.fullName = result.result.username;
                    this.email = result.result.email;
                    this.phone = result.result.phone;
                }
            } catch (error) {
                console.error('Error API:', error);
            }
        },
        async submitForm() {
            if (!this.validateForm()) {
                return;
            }

            this.loading = true;
            try {
                const response = await fetch('/api/send_request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        referral_code: this.referenceCode,
                        type: this.type,
                        content: this.requestContent
                    })
                });

                const result = await response.json();
                console.log("Add request: ", result);
                if (result.result.success) {
                    await this.sendMailRequest();
                } else {
                    alert('Có lỗi xảy ra: ' + result.message);
                }
            } catch (error) {
                console.error('Error:', error);
            } finally {
                this.loading = false;
            }
        },

        validateForm() {
            let isValid = true;

            // Check company name
            this.check_company_name = this.companyName.trim() !== '';
            isValid = isValid && this.check_company_name;

            // Check full name
            this.check_full_name = this.fullName.trim() !== '';
            isValid = isValid && this.check_full_name;

            // Check email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            this.check_email = this.email.trim() !== '' && emailRegex.test(this.email.trim());
            isValid = isValid && this.check_email;

            // Check request content
            this.check_request_content = this.requestContent.trim() !== '';
            isValid = isValid && this.check_request_content;

            return isValid;
        },

        async getCompany(){
            try{
               const response = await fetch('/api/get_company_name', {
                  method: 'POST',
                  headers: {
                     'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                     id: this.company_id
                  })
               });

               const data = await response.json();

               console.log("Code: ", data);
               if (data.result.success) {
                  this.referenceCode = data.result.referral_code;
               } else {
                  console.log(data.result.message || 'Không thể lấy thông tin công ty');
               }
            } catch(error){
               console.log("Error API: ", error);
            }
         },

        async sendMailRequest(){
            try {
                const response = await fetch('/api/send_mail_request', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        reference_code: this.referenceCode,
                        type: this.title,
                        message: this.requestContent,
                        company: this.companyName,
                        user_name: this.fullName,
                        email: this.email,
                        phone: this.phone
                    })
                });

                const data = await response.json();
                console.log("Send mail: ", data);

                if(data.result.success){
                    this.$router.push({
                        name: 'request_success',
                        params: { type: this.type }
                    });
                }
            } catch (error) {
                console.error('Error API:', error);
            }
        }
    },
    watch: {
        companyName  : {
            handler() {
                this.check_company_name = true;
            }
        },
        fullName: {
            handler() {
                this.check_full_name = true;
            }
        },
        email: {
            handler() {
                this.check_email = true;
            }
        },
        requestContent: {
            handler() {
                this.check_request_content = true;
            }
        }
    }
}

export default MatchingRequest