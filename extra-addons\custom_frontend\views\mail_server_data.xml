<odoo>
    <record id="custom_mail_server" model="ir.mail_server">
        <field name="name">Custom SMTP Server</field>
        <field name="smtp_host">mi52.jp</field>
        <field name="smtp_port">465</field>
        <field name="smtp_authentication">login</field>
        <field name="smtp_user"><EMAIL></field>
        <field name="smtp_pass">VerticallimIT</field>
        <field name="smtp_encryption">ssl</field>
        <field name="sequence">10</field>
        <field name="active" eval="True"/>
    </record>

    <record id="custom_mail_config" model="ir.config_parameter">
        <field name="key">mail.use_custom_mail_server</field>
        <field name="value">True</field>
    </record>
</odoo>
