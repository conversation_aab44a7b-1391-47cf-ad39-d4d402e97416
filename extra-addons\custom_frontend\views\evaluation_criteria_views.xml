<?xml version="1.0"?>
<odoo>
    <record id="vit_evaluationcriteria_view_form" model="ir.ui.view">
        <field name="name">vit.evaluation_criteria.form</field>
        <field name="model">vit.evaluation_criteria</field>
        <field name="arch" type="xml">
            <form string="Lost Reason">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="description"/>
                        </group>
                        <group>
                            <field name="created_at"/>
                            <field name="created_by"/>
                            <field name="updated_at"/>
                            <field name="updated_by"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="vit_evaluationcriteria_view_for_tree" model="ir.ui.view">
        <field name="name">vit.evaluation_criteria.tree</field>
        <field name="model">vit.evaluation_criteria</field>
        <field name="arch" type="xml">
            <list string="Channel">
                    <field name="name"/>
                    <field name="description"/>
                    <field name="created_at"/>
                    <field name="created_by"/>
                    <field name="updated_at"/>
                    <field name="updated_by"/>
            </list>
        </field>
    </record>

    <record id="vit_rating_action" model="ir.actions.act_window">
        <field name="name">VIT Rating</field>
        <field name="res_model">vit.evaluation_criteria</field>
        <field name="view_mode">list,form</field>
    </record>
    <menuitem id="vit_rating_menu_root" name="VIT Rating"/>
    <menuitem id="vit_rating_menu" name="Rating" parent="vit_rating_menu_root" action="vit_rating_action"/>
</odoo>