from odoo import http
from odoo.http import request

class Note<PERSON><PERSON>roller(http.Controller):
    @http.route('/api/get_note', type='json', auth='public', methods=['POST'], csrf=False)
    def get_note(self):
        notes = request.env['vit.note'].sudo().search([])
        data = []
        for note in notes:
            data.append({
                'id': note.id,
                'content': note.content,
            })
        
        return {'success': True, 'data': data}
        
        