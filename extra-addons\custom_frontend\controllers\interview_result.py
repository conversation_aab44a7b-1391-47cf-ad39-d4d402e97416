from odoo import http
from odoo.http import request
import json
from datetime import datetime
import logging
from . import common

_logger = logging.getLogger(__name__)

class InterviewResultController(http.Controller):

    # 1️⃣ API LẤY THÔNG TIN CHI TIẾT INTERVIEW RESULT
    @http.route('/api/interview_result_view', type='http', auth='public', methods=['GET'], csrf=False)
    def get_res(self, **kwargs):
        """API lấy thông tin chi tiết của một Interview Result"""
        interview_result_flow_id = request.params.get('id')

        if not interview_result_flow_id or not interview_result_flow_id.isdigit():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Invalid or missing Interview Result ID'}),
                headers={'Content-Type': 'application/json'}
            )

        query = """
            SELECT
                i.id, i.result, i.reject_reason,
                o.id AS opp_id, o.subject AS opportunity_subject,
                p.id AS resume_id, p.initial_name AS candidate_name
            FROM vit_interviewresult i
            LEFT JOIN vit_opportunities o ON i.opp_id = o.id
            LEFT JOIN vit_partner p ON i.resume_id = p.id
            WHERE i.flow_id = %s
        """
        request.env.cr.execute(query, (int(interview_result_flow_id),))
        result = request.env.cr.fetchone()

        schedules = request.env['vit.interviewbooking'].sudo().search([
            ('opp_id', '=', int( result[3])),
            ('resume_id', '=', int(result[5]))
        ], limit=1)
        contract = schedules.contract_types if schedules else "Unknown"

        if not result:
            return request.make_response(
                json.dumps({'success': False, 'message': 'Interview Result not found'}),
                headers={'Content-Type': 'application/json'}
            )

        # Định dạng dữ liệu trả về
        data = {
            'id': result[0],
            'contract_types': contract,
            'result': result[1],
            'reject_reason': result[2],
            'opp_id': result[3],
            'opportunity_subject': result[4],
            'resume_id': result[5],
            'candidate_name': result[6],
        }

        return request.make_response(
            json.dumps({'success': True, 'data': data}),
            headers={'Content-Type': 'application/json'}
        )

    # 2️⃣ API CẬP NHẬT INTERVIEW RESULT
    @http.route('/api/interview_result_update', type='json', auth='public', methods=['POST'], csrf=False)
    def update_res(self, **kwargs):
        """API cập nhật Interview Result"""
        data = request.jsonrequest
        interview_result_id = data.get('id')

        if not interview_result_id:
            return {'success': False, 'message': 'Missing Interview Result ID'}

        interview_result = request.env['vit.interviewresult'].sudo().search([('id', '=', interview_result_id)], limit=1)
        schedules = request.env['vit.interviewbooking'].sudo().search([
            ('opp_id', '=', interview_result.opp_id.id),
            ('resume_id', '=', interview_result.resume_id.id),
        ], limit=1)

        if not interview_result:
            return {'success': False, 'message': 'Interview Result not found'}

        # Cập nhật các trường
        interview_result.sudo().write({
            'result': data.get('result', interview_result.result),
            'reject_reason': data.get('message', interview_result.reject_reason),
            'opp_id': data.get('opp_id', interview_result.opp_id.id),
            'resume_id': data.get('resume_id', interview_result.resume_id.id),
            'updated_at': datetime.now(),
            'updated_by': 'API',
        })

        if schedules:
            schedules.sudo().write({
                'contract_types': data.get('contract_types', interview_result.contract_types),
                'updated_at': datetime.now(),
            })

        return {
            'success': True,
            'message': 'Interview Result updated successfully',
            'id': interview_result.id
        }

    # 3️⃣ API TẠO MỚI INTERVIEW RESULT
    @http.route('/api/interview_result_create', type='json', auth='public', methods=['POST'], csrf=False)
    def create_res(self, **kwargs):
        """API tạo mới Interview Result"""
        data = request.httprequest.get_json(silent=True)
        opp_id = data.get('opp_id')
        resume_id = data.get('resume_id')
        flow_id = data.get('flow_id')
        result = data.get('result')
        message = data.get('message')

        required_fields = ['result', 'opp_id', 'resume_id']
        missing_fields = [field for field in required_fields if field not in data]

        if missing_fields:
            return {'success': False, 'message': f'Missing required fields: {", ".join(missing_fields)}'}

        # Kiểm tra opp_id & resume_id
        # opp = request.env['vit.opportunities'].sudo().search([('id', '=', data['opp_id'])], limit=1)
        # resume = request.env['vit.partner'].sudo().search([('id', '=', data['resume_id'])], limit=1)

        # if not opp:
        #     return {'success': False, 'message': 'Invalid opp_id, opportunity not found'}
        # if not resume:
        #     return {'success': False, 'message': 'Invalid resume_id, candidate not found'}

        # Tạo bản ghi mới
        interview_result = request.env['vit.interviewresult'].sudo().create({
            'result': result,
            'reject_reason': message,
            'opp_id': opp_id,
            'resume_id': resume_id,
            'flow_id': flow_id,
            'created_at': datetime.now(),
            # 'created_by': 'API',
        })

        schedules = request.env['vit.interviewbooking'].sudo().search([
            ('opp_id', '=', int(opp_id)),
            ('resume_id', '=', int(resume_id))
        ], limit=1)

        # Update contract_types trong bảng vit.partner
        # resume.sudo().write({'contract_types': data['contract_types']}) #TODO: confused about this line

        # Cập nhật status trong bảng vit.workflow
        workflow = request.env['vit.workflow'].sudo().search([
            ('id', '=', flow_id),
            ('opportunities_id', '=', opp_id),
            ('resumes_id', '=', resume_id)
        ], limit=1)

        if schedules:
            schedules.sudo().write({
                'contract_types': data.get('contract_types'),
                'updated_at': datetime.now(),
            })

        if workflow:
            if result == 'Pass':
                workflow.sudo().write({
                'status': 3,
                'updated_at': datetime.now(),
                })
            else:
                workflow.sudo().write({
                    'status': 6,
                    'updated_at': datetime.now(),
                })

        opportunities = request.env['vit.opportunities'].sudo().browse(int(opp_id))
        opportunities_name = opportunities.subject if opportunities else 'なし'
        user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
        company_name = user.company_id.name if user.company_id else 'なし'
        user_name = user.username if user.username else 'なし'
        user_email = user.email if user.email else 'なし'

        partner = request.env['vit.partner'].sudo().browse(int(resume_id))
        partner_name = partner.initial_name if partner else 'なし'
        user = request.env['vit.users'].sudo().browse(int(partner.created_by))
        company_name_res = user.company_id.name if user.company_id else 'なし'
        user_name_res = user.username if user.username else 'なし'
        user_email_res = user.email if user.email else 'なし'

        common.sendMailWorkflowOppChange(company_name, user_name, opportunities_name, workflow.status, user_email, interview_result=result, message=message)
        common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, workflow.status, user_email_res, interview_result=result, message=message, opp_name=opportunities_name)

        return {
            'success': True,
            'message': 'Interview Result created successfully',
            'id': interview_result.id
        }

    @http.route('/api/get_opportunity_and_partner', type='json', auth='public', methods=['POST'])
    def get_opportunity_and_partner(self):
        try:
            # Lấy dữ liệu từ request
            data = request.httprequest.get_json(silent=True)
            opp_id = data.get('opp_id')
            resume_id = data.get('resume_id')

            if not opp_id or not resume_id:
                return {'success': False, 'error': 'Missing required parameters'}

            # Lấy案件名 từ bảng vit.opportunities
            opportunity = request.env['vit.opportunities'].sudo().search([('id', '=', int(opp_id))], limit=1)
            opportunity_name = opportunity.subject if opportunity else "Unknown"

            # Lấy人財名 từ bảng vit.partner
            partner = request.env['vit.partner'].sudo().search([('id', '=', int(resume_id))], limit=1)
            partner_name = partner.initial_name if partner else "Unknown"

            # Lấy thông tin từ bảng Schedules
            schedules = request.env['vit.interviewbooking'].sudo().search([
                ('opp_id', '=', int(opp_id)),
                ('resume_id', '=', int(resume_id))
            ], limit=1)
            contract = schedules.contract_types if schedules else "Unknown"

            return {
                'success': True,
                'contract_types': contract,
                'opportunity_name': opportunity_name,
                'partner_name': partner_name,
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}