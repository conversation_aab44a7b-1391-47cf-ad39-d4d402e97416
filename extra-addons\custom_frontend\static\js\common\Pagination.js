export default {
    name: 'Pagination',
    props: {
        currentPage: {
            type: Number,
            required: true
        },
        totalPages: {
            type: Number,
            required: true
        }
    },
    computed: {
        visiblePages() {
            let pages = [];
            if (this.totalPages <= 5) {
                pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
            } else if (this.currentPage <= 3) {
                pages = [1, 2, 3, 4, 5];
            } else if (this.currentPage >= this.totalPages - 2) {
                pages = [
                    this.totalPages - 4,
                    this.totalPages - 3,
                    this.totalPages - 2,
                    this.totalPages - 1,
                    this.totalPages
                ];
            } else {
                pages = [
                    this.currentPage - 2,
                    this.currentPage - 1,
                    this.currentPage,
                    this.currentPage + 1,
                    this.currentPage + 2
                ];
            }
            return pages;
        }
    },
    template: `
        <nav role="navigation" class="pagination">
            <ul class="pagination">
                <!-- Nút đầu tiên -->
                <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <a class="page-link waves-effect" href="#" @click.prevent="$emit('page-change', 1)">«</a>
                </li>
                <!-- Nút previous -->
                <li class="page-item" :class="{ disabled: currentPage === 1 }">
                    <a class="page-link waves-effect" href="#" @click.prevent="$emit('page-change', currentPage - 1)">‹</a>
                </li>
                <!-- Các số trang -->
                <template v-for="page in visiblePages" :key="page">
                    <li class="page-item" :class="{ active: page === currentPage }">
                        <a class="page-link waves-effect" href="#" @click.prevent="$emit('page-change', page)">{{ page }}</a>
                    </li>
                </template>
                <!-- Nút next -->
                <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <a class="page-link waves-effect" href="#" @click.prevent="$emit('page-change', currentPage + 1)">›</a>
                </li>
                <!-- Nút cuối -->
                <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                    <a class="page-link waves-effect" href="#" @click.prevent="$emit('page-change', totalPages)">»</a>
                </li>
            </ul>
        </nav>
    `
}
