/**
 * Common Breadcrumb Component
 * Sử dụng để tạo breadcrumb navigation cho tất cả các pages
 * 
 * Usage:
 * import Breadcrumb from '../components/Breadcrumb.js';
 * 
 * // Trong template:
 * <breadcrumb :items="breadcrumbItems"></breadcrumb>
 * 
 * // Trong data:
 * breadcrumbItems: [
 *   { text: 'サービスメニュー', link: null },
 *   { text: '探す', link: '/search' },
 *   { text: '案件を探す', link: null, current: true }
 * ]
 */

const Breadcrumb = {
    name: 'Breadcrumb',
    props: {
        items: {
            type: Array,
            required: true,
            default: () => []
        },
        containerClass: {
            type: String,
            default: 'container-fluid'
        }
    },
    template: `
        <div :class="containerClass">
            <div class="breadcrumbs">
                <template v-for="(item, index) in items" :key="index">
                    <!-- Breadcrumb item -->
                    <a v-if="item.link && !item.current" :href="item.link">{{ item.text }}</a>
                    <span v-else :class="{ 'current': item.current }">{{ item.text }}</span>
                    
                    <!-- Arrow separator (không hiển thị cho item cuối) -->
                    <span v-if="index < items.length - 1" class="breadcrumb-arrow">
                        <span></span>
                    </span>
                </template>
            </div>
        </div>
    `,
    methods: {
        // Helper method để tạo breadcrumb items từ route meta
        createFromRoute(route, customItems = []) {
            const items = [];

            // Thêm custom items trước
            items.push(...customItems);

            // Thêm current page từ route meta
            if (route.meta && route.meta.jp) {
                items.push({
                    text: route.meta.jp,
                    link: null,
                    current: true
                });
            }

            return items;
        }
    },

    // Static helper methods
    static: {
        // Common breadcrumb patterns
        createServiceMenu(currentPage) {
            return [
                { text: 'サービスメニュー', link: null },
                { text: currentPage, link: null, current: true }
            ];
        },

        createManagementPath(currentPage) {
            return [
                { text: 'サービスメニュー', link: null },
                { text: '登録・管理', link: null },
                { text: '登録データ管理', link: null },
                { text: currentPage, link: null, current: true }
            ];
        },

        createSearchPath(currentPage) {
            return [
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: currentPage, link: null, current: true }
            ];
        }
    }
};

export default Breadcrumb;
