/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }







    :focus {
        outline: 0;
    }
}

.tab-container {
    display: flex;
    width: 41.66%; /* Matches col-md-5 width */
    margin: 0;
    overflow: hidden;
    gap: 0.5%;
    margin-top: 5px;
    margin-bottom: 5px;
    padding-right: 0; /* Remove padding to make tabs wider */
    padding-left: 14px; /* Add padding to align with form */
}

.tab-button {
    height: 5vh;
    width: 48%;
    border: 2px solid #4472C4;
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    background-color: #f4f4f4;
    color: #333;
    box-sizing: border-box;
}

.tab-button.btn-default {
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    color: white;
    border-color: #4472C4 !important;
}

/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    h1 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    h1 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    @media (min-width: 768px) {
        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    h1 {
        font-weight: 300;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .py-2 {
        padding-bottom: .5rem !important;
    }

    @media (min-width: 768px) {
        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    .title {

        background-color: #1072e9 !important;


        background-size: cover;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    :focus {
        outline: 0;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    nav {
        display: block;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h4,
    h5 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    table {
        border-collapse: collapse;
    }

    th {
        text-align: inherit;
        text-align: -webkit-match-parent;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button,
    input,
    select {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button,
    input {
        overflow: visible;
    }

    button,
    select {
        text-transform: none;
    }

    [role=button] {
        cursor: pointer;
    }

    select {
        word-wrap: normal;
    }

    [type=button],
    [type=submit],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    [type=submit]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    input[type=checkbox] {
        box-sizing: border-box;
        padding: 0;
    }

    h4,
    h5 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h4 {
        font-size: 1.5rem;
    }

    h5 {
        font-size: 1.25rem;
    }

    .container,
    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    @media (min-width: 576px) {
        .container {
            max-width: 540px;
        }
    }

    @media (min-width: 768px) {
        .container {
            max-width: 720px;
        }
    }

    @media (min-width: 992px) {
        .container {
            max-width: 960px;
        }
    }

    @media (min-width: 1200px) {
        .container {
            max-width: 1140px;
        }
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col,
    .col-1,
    .col-12,
    .col-3,
    .col-4,
    .col-6,
    .col-8,
    .col-9,
    .col-lg-3,
    .col-lg-6,
    .col-lg-9,
    .col-md-12,
    .col-md-4,
    .col-md-8,
    .col-sm-2,
    .col-sm-6,
    .col-xl-2 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }

    .col-1 {
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }

    .col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 576px) {
        .col-sm-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }

        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    @media (min-width: 768px) {
        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }

        .col-md-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-lg-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-lg-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }
    }

    @media (min-width: 1200px) {
        .col-xl-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
    }

    .form-control {
        display: block;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        padding: .375rem .75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-control {
            transition: none;
        }
    }

    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .form-control:disabled,
    .form-control[readonly] {
        background-color: #e9ecef;
        opacity: 1;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .custom-control {
        position: relative;
        z-index: 1;
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5rem;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .custom-control-input {
        position: absolute;
        left: 0;
        z-index: -1;
        width: 1rem;
        height: 1.25rem;
        opacity: 0;
    }

    .custom-control-input:checked~.custom-control-label::before {
        color: #fff;
        border-color: #007bff;
        background-color: #007bff;
    }

    .custom-control-input:focus~.custom-control-label::before {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #80bdff;
    }

    .custom-control-input:not(:disabled):active~.custom-control-label::before {
        color: #fff;
        background-color: #b3d7ff;
        border-color: #b3d7ff;
    }

    .custom-control-input:disabled~.custom-control-label {
        color: #6c757d;
    }

    .custom-control-input:disabled~.custom-control-label::before {
        background-color: #e9ecef;
    }

    .custom-control-label {
        position: relative;
        margin-bottom: 0;
        vertical-align: top;
    }

    .custom-control-label::before {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        pointer-events: none;
        content: "";
        background-color: #fff;
        border: #adb5bd solid 1px;
    }

    .custom-control-label::after {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        content: "";
        background: 50%/50% 50% no-repeat;
    }

    .custom-checkbox .custom-control-label::before {
        border-radius: .25rem;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
    }

    .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
        background-color: rgba(0, 123, 255, 0.5);
    }

    .custom-control-label::before {
        transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .custom-control-label::before {
            transition: none;
        }
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .card-header {
        padding: .75rem 1.25rem;
        margin-bottom: 0;
        background-color: rgba(0, 0, 0, 0.03);
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-header:first-child {
        border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0;
    }

    .badge-pill {
        padding-right: .6em;
        padding-left: .6em;
        border-radius: 10rem;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 10000 !important;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        z-index: 10000 !important;
        flex: 1 1 auto;
        padding: 1rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }
    }

    @media (min-width: 992px) {
        .modal-lg {
            max-width: 800px;
        }
    }

    .align-middle {
        vertical-align: middle !important;
    }

    .bg-white {
        background-color: #fff !important;
    }

    .border {
        border: 1px solid #dee2e6 !important;
    }

    .border-top {
        border-top: 1px solid #dee2e6 !important;
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .rounded-sm {
        border-radius: 0.2rem !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 576px) {
        .d-sm-inline {
            display: inline !important;
        }

        .d-sm-flex {
            display: flex !important;
        }
    }

    @media (min-width: 768px) {
        .d-md-none {
            display: none !important;
        }

        .d-md-inline-block {
            display: inline-block !important;
        }

        .d-md-block {
            display: block !important;
        }

        .d-md-flex {
            display: flex !important;
        }
    }

    .justify-content-start {
        justify-content: flex-start !important;
    }

    .justify-content-end {
        justify-content: flex-end !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-start {
        align-items: flex-start !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .float-left {
        float: left !important;
    }

    .float-right {
        float: right !important;
    }

    .position-relative {
        position: relative !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mr-0,
    .mx-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .ml-0,
    .mx-0 {
        margin-left: 0 !important;
    }

    .mt-1 {
        margin-top: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .ml-1 {
        margin-left: 0.25rem !important;
    }

    .mt-2,
    .my-2 {
        margin-top: 0.5rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2,
    .my-2 {
        margin-bottom: 0.5rem !important;
    }

    .ml-2 {
        margin-left: 0.5rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3,
    .mx-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3,
    .mx-3 {
        margin-left: 1rem !important;
    }

    .mt-4 {
        margin-top: 1.5rem !important;
    }

    .mr-4,
    .mx-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mx-4 {
        margin-left: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .p-1 {
        padding: 0.25rem !important;
    }

    .py-1 {
        padding-top: 0.25rem !important;
    }

    .px-1 {
        padding-right: 0.25rem !important;
    }

    .py-1 {
        padding-bottom: 0.25rem !important;
    }

    .pl-1,
    .px-1 {
        padding-left: 0.25rem !important;
    }

    .p-2 {
        padding: 0.5rem !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .pr-2,
    .px-2 {
        padding-right: 0.5rem !important;
    }

    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .px-2 {
        padding-left: 0.5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .pl-5 {
        padding-left: 3rem !important;
    }

    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 576px) {
        .mb-sm-0 {
            margin-bottom: 0 !important;
        }

        .ml-sm-0 {
            margin-left: 0 !important;
        }

        .mb-sm-n2 {
            margin-bottom: -0.5rem !important;
        }
    }

    @media (min-width: 768px) {
        .mt-md-0 {
            margin-top: 0 !important;
        }

        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .ml-md-2 {
            margin-left: 0.5rem !important;
        }

        .ml-md-3 {
            margin-left: 1rem !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    .text-nowrap {
        white-space: nowrap !important;
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        thead {
            display: table-header-group;
        }

        tr {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }

        .container {
            min-width: 992px !important;
        }
    }

    .grey {
        background-color: #9e9e9e !important;
    }

    .hoverable {
        box-shadow: none;
        transition: all .55s ease-in-out;
    }

    .hoverable:hover {
        box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        transition: all .55s ease-in-out;
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none !important;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    a:not([href]):not([tabindex]),
    a:not([href]):not([tabindex]):focus,
    a:not([href]):not([tabindex]):hover {
        color: inherit;
        text-decoration: none;
    }

    .border-light {
        border-color: rgba(0, 0, 0, 0.37) !important;
    }

    h4,
    h5 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn.btn-sm {
        padding: .5rem 1.6rem;
        font-size: .64rem;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn.btn-sm[class*=btn-outline-] {
        padding-top: .38rem;
        padding-bottom: .38rem;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-default {
        color: #fff !important;
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    }

    .btn-default:hover {
        color: #fff;
        background-color: #61b8f7;
    }

    .btn-default:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:focus,
    .btn-default:active {
        background-color: #005650;
    }

    .btn-default:not([disabled]):not(.disabled):active {
        background-color: #005650 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #1072e9 !important;
    }

    .btn-outline-default:hover,
    .btn-outline-default:focus,
    .btn-outline-default:active,
    .btn-outline-default:active:focus {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
    }

    .btn-outline-default:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey {
        color: #fff !;
        background-color: #78909c !important;
    }

    .btn-blue-grey:hover {
        color: #fff;
        background-color: #879ca7;
    }

    .btn-blue-grey:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:focus,
    .btn-blue-grey:active {
        background-color: #4a5b64;
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active {
        background-color: #4a5b64 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .badge-pill {
        padding-right: .5rem;
        padding-left: .5rem;
        border-radius: 10rem;
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    table th {
        font-size: .9rem;
        font-weight: 400;
    }

    table td {
        font-size: .9rem;
        font-weight: 300;
    }

    .btn.btn-flat {
        font-weight: 500;
        color: inherit;
        background-color: rgba(0, 0, 0, 0);
        box-shadow: none;
    }

    .btn.btn-flat:not([disabled]):not(.disabled):active {
        box-shadow: none;
    }

    button,
    html [type=button],
    [type=submit] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    [type=checkbox]:not(:checked),
    [type=checkbox]:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    button:focus {
        outline: 0 !important;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-extralarge {
        font-size: 1.25rem !important;
    }

    .font-ll {
        font-size: 1.5rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-5-text {
        color: #738a97;
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9 !important;
    }

    .line-height-180 {
        line-height: 180%;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mr-0,
    .mx-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .ml-0,
    .mx-0 {
        margin-left: 0 !important;
    }

    .p-1 {
        padding: .25rem !important;
    }

    .py-1 {
        padding-top: .25rem !important;
    }

    .px-1 {
        padding-right: .25rem !important;
    }

    .py-1 {
        padding-bottom: .25rem !important;
    }

    .pl-1,
    .px-1 {
        padding-left: .25rem !important;
    }

    .mt-1 {
        margin-top: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .ml-1 {
        margin-left: .25rem !important;
    }

    .p-2 {
        padding: .5rem !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .pr-2,
    .px-2 {
        padding-right: .5rem !important;
    }

    .py-2 {
        padding-bottom: .5rem !important;
    }

    .px-2 {
        padding-left: .5rem !important;
    }

    .mt-2,
    .my-2 {
        margin-top: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2,
    .my-2 {
        margin-bottom: .5rem !important;
    }

    .ml-2 {
        margin-left: .5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3,
    .mx-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3,
    .mx-3 {
        margin-left: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .mt-4 {
        margin-top: 1.5rem !important;
    }

    .mr-4,
    .mx-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mx-4 {
        margin-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .pl-5 {
        padding-left: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .pr-7 {
        padding-right: 3rem !important;
    }

    @media (min-width: 576px) {
        .mb-sm-0 {
            margin-bottom: 0 !important;
        }

        .ml-sm-0 {
            margin-left: 0 !important;
        }
    }

    @media (min-width: 768px) {
        .mt-md-0 {
            margin-top: 0 !important;
        }

        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .ml-md-2 {
            margin-left: .5rem !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .ml-md-3 {
            margin-left: 1rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    @media (min-width: 576px) {
        .mb-sm-n2 {
            margin-bottom: -0.5rem !important;
        }
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    body a:not([href]):not([tabindex]):focus,
    body a:not([href]):not([tabindex]):hover {
        color: #1072e9;
    }

    .bg-grey-1 {
        background-color: #f4f4f4;
    }

    .bg-grey-2 {
        background-color: #e6eaec;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-dark {
        color: rgba(0, 0, 0, 0.54);
    }

    .material-icons.md-grey {
        color: rgba(0, 0, 0, 0.38);
    }

    .material-icons.md-18 {
        font-size: 18px;
    }

    .calender-icon {
        cursor: default;
    }

    .vertical-middle {
        vertical-align: middle !important;
    }

    .vertical-top {
        vertical-align: top;
    }

    .nowrap {
        white-space: nowrap;
    }

    .clear:after {
        clear: both;
        content: "";
        display: block;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .btn.btn-sm {
        padding: .5rem 1rem;
        font-size: .875rem;
    }

    .badge-pill {
        padding: .1rem .5rem;
        color: #fff;
    }

    .badge-blue {
        background: #3479ca;
    }

    .dropdown-content li>span {
        color: #595959 !important;
    }

    .dropdown-content li.active,
    .dropdown-content li:hover {
        background-color: #1072e9;
    }

    .dropdown-content li.active>span,
    .dropdown-content li:hover>span {
        color: #fff;
    }

    .inline-unit-label,
    .inline-unit-icon {
        position: absolute;
        top: 1.2em;
    }

    .inline-unit-icon {
        line-height: .6rem !important;
    }

    main .grabient {
        min-height: 50vh;
    }

    :focus {
        outline: 0;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #1072e9;
    }

    input.form-control[type=text],
    input.form-control[type=number] {
        border: none;
        border-bottom: 1px solid #1072e9;
        border-radius: 0;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        height: inherit;
        padding: .75rem .75rem;
        cursor: text;
    }

    .custom-checkbox label[class*=custom-control-label] {
        cursor: pointer;
    }

    .custom-control-label::before {
        width: 1.125rem;
        height: 1.125rem;
        background-color: rgba(0, 0, 0, 0);
        border: 2px solid #d5d9db;
        top: 3px;
        box-shadow: none !important;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
        background-color: #1072e9;
        border-color: #1072e9;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background: url(https://assign-navi.jp/assets/img/common/check_white.png) !important;
        background-size: contain !important;
        top: 3px;
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #d5d9db;
    }

    .card-header {
        padding: 1rem;
        background: #1072e9;
        border-bottom: none;
        color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    a .card-header:hover {
        opacity: .7;
    }

    .modal {
        opacity: 1;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none !important;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul {
        list-style: none;
        padding: 0;
    }

    @media (min-width: 576px) {
        .accordion_search_condition_btn {
            position: absolute;
            bottom: 0;
            right: 0;
        }
    }

    .accordion_open,
    .accordion_close,
    .accordion-close-area {
        cursor: pointer;
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    .border-grey-3 {
        border: 1px solid #d5d9db !important;
    }

    @media (max-width: 767px) {
        .btn-outline-default:hover {
            border-color: #1072e9 !important;
            background-color: inherit !important;
            color: #1072e9 !important;
        }
    }

    .font-size-middle {
        font-size: custom-(1rem) !important;
        line-height: 28.8px !important;
    }

    .search-area {
        background-color: rgba(255, 255, 255, 0.9);
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        left: 0;
        z-index: 10;
    }

    .search-area-sm {
        background: rgba(230, 234, 236, 0.9);
        z-index: 1030 !important;
    }

    .loader {
        width: 19px;
        height: 19px;
        border: 2px solid #fff;
        border-bottom-color: rgba(0, 0, 0, 0.05);
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        -webkit-animation: rotation 1s linear infinite;
        animation: rotation 1s linear infinite;
    }

    .search-close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    #slider-handles {
        height: 4px;
    }

    #slider-handles .noUi-handle,
    .slider-styled .noUi-handle {
        height: 16px;
        width: 16px;
        top: -6px;
        right: -8px;
        border-radius: 8px;
        background-color: #1072e9;
        box-shadow: none;
        border: none;
    }

    #slider-handles .noUi-handle:before,
    #slider-handles .noUi-handle:after,
    .slider-styled .noUi-handle:before,
    .slider-styled .noUi-handle:after {
        content: none;
    }

    .noUi-target {
        background: #eee !important;
        border-radius: 2px;
        border: none !important;
    }

    .slider-styled {
        height: 4px;
        background: #eee;
        border-radius: 2px;
    }

    /* Style cho slider 単価 */
    #price-range + .slider-styled .noUi-connect {
        background: #1072e9 !important;
    }

    /* Đảm bảo thanh slider có độ dày đúng */
    #price-range + .slider-styled {
        height: 4px !important;
        background: #eee !important;
    }

    #price-range + .slider-styled .noUi-base {
        height: 4px !important;
    }

    #price-range + .slider-styled .noUi-connects {
        height: 4px !important;
    }

    /* Style cho handle của slider 単価 */
    #price-range + .slider-styled .noUi-handle {
        background-color: #1072e9 !important;
        border-radius: 50% !important;
        height: 14px !important;
        width: 14px !important;
        top: -6px !important;
        right: -7px !important;
    }

    /* Style cho slider 募集人数 */
    #participants-range + .slider-styled .noUi-connect {
        background: #1072e9 !important;
    }

    /* Đảm bảo thanh slider có độ dày giống với slider 単価 */
    #participants-range + .slider-styled {
        height: 4px !important;
        background: #eee !important;
    }

    /* Đảm bảo thanh slider có độ dày giống với slider 単価 khi có connect */
    #participants-range + .slider-styled .noUi-base {
        height: 4px !important;
    }

    #participants-range + .slider-styled .noUi-connects {
        height: 4px !important;
    }

    /* Style cho handle của slider 募集人数 */
    #participants-range + .slider-styled .noUi-handle {
        background-color: #1072e9 !important;
        border-radius: 50% !important;
        height: 14px !important;
        width: 14px !important;
        top: -6px !important;
        right: -7px !important;
    }

    .noUi-horizontal .noUi-handle {
        cursor: pointer;
    }

    .noUi-tooltip {
        display: block;
        position: absolute;
        border: 1px solid #D9D9D9;
        border-radius: 3px;
        background: #fff;
        color: #000;
        padding: 5px;
        text-align: center;
        white-space: nowrap;
        font-size: 12px;
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}


/*! CSS Used from: Embedded */
/* Results and sort container */
.results-sort-container {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 15px;
}

/* Result count container styling */
.result-count-container {
    background-color: #f8f9fa;
    padding: 8px 15px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-right: 10px;
    height: 5vh;
    display: flex;
    align-items: center;
}

/* Sort dropdown styling */
.sort-display-area {
    cursor: pointer;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    height: 40px;
    width: 160px;
}

.sort-display-area:hover {
    opacity: .8;
    border-color: #1072e9;
}

.sort-display-area label {
    cursor: pointer;
}

.sort-options-area {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
    position: absolute;
    z-index: 5;
    right: 0;
    width: max-content;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #eee;
    overflow: hidden;
}

.sort-options-area li {
    position: relative;
    padding: 8px 44px 8px 16px;
    transition: all 0.2s ease;
}

.sort-options-area li:hover {
    opacity: 1;
    background-color: #f0f7ff;
}

.sort-options-area li.sort-active {
    background-color: #e6f7ff;
    color: #1072e9;
}

.sort-options-area li i {
    position: absolute;
    top: 6px;
    right: 10px;
}

/*! CSS Used from: Embedded */
.default-bg-color-opacity-10[data-v-1c51a755] {
    background-color: rgba(0, 188, 174, .1);
}

.default-bg-color-opacity-10[data-v-1c51a755]:hover {
    opacity: 1;
}

a.card[data-v-1c51a755] {
    color: rgba(0, 0, 0, .87);
    box-shadow: none;
}

a.card[data-v-1c51a755]:hover {
    box-shadow: 0 8px 17px 0 rgba(0, 0, 0, .2), 0 6px 20px 0 rgba(0, 0, 0, .19);
}

.badge-blue[data-v-1c51a755] {
    background-color: #3479ca;
}

/*! CSS Used keyframes */
@-webkit-keyframes rotation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Opportunity table styles */
.table-container {
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
    border-radius: 6px;
    overflow: hidden;
}

.opp-table-header {
    display: flex;
    width: 100%;
    background: linear-gradient(to right, #1072e9, #0066cc);
    color: white;
    border-bottom: 1px solid #dee2e6;
    overflow: hidden;
    font-size: 1rem;
}

.opp-table-header .header-cell {
    padding: 14px 10px;
    font-weight: bold;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    line-height: 1.4;
    word-break: break-word;
    white-space: normal;
}

.opp-table-header .status-header {
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.opp-table-header .header-cell:last-child {
    border-right: none;
}

.opp-table-row {
    display: flex;
    width: 100%;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    color: black;
    text-decoration: none;
    align-items: center;
}

/* Table rows container */
.table-rows-container {
    background-color: white;
}

/* Last row with rounded bottom corners */
.opp-table-row:last-child {
    border-bottom: none;
}

.opp-table-row:hover {
    background-color: rgba(0, 176, 241, 0.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

/* First row hover effect */
.opp-table-row:first-child:hover {
    border-radius: 0 0 0 0;
}

/* Last row hover effect */
.opp-table-row:last-child:hover {
    border-radius: 0 0 6px 6px;
}

.opp-table-row .cell {
    padding: 12px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: normal;
    word-break: break-word;
    line-height: 1.4;
    min-height: 60px;
}

/* Specific styling for date cell */
.opp-table-row .date-cell {
    white-space: nowrap;
    overflow: visible;
    text-overflow: ellipsis;
    min-width: 80px;
}

/* Specific styling for price cell */
.opp-table-row .cell[style*="width: 18%"] {
    white-space: normal;
    word-break: break-word;
    line-height: 1.4;
}

/* Đặc biệt cho cột 案件名 */
.opp-table-row .cell-subject {
    padding: 12px 10px;
    display: flex;
    align-items: flex-start;
    overflow: visible;
    text-overflow: initial;
    word-break: break-word;
    white-space: normal;
    line-height: 1.4;
    text-align: left;
    justify-content: flex-start;
}

.opp-table-row .status-cell {
    align-items: center;
    justify-content: flex-start;
    padding: 12px 10px;
    width: 120px;
    border-right: 1px solid #f0f0f0;
}

.opp-table-row .bookmark-area {
    display: flex;
    align-items: center;
}

.viewed-flag, .new-flag, .unread-flag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-right: 5px;
    white-space: nowrap;
    display: inline-block;
    text-align: center;
    min-width: 40px;
}

.viewed-flag {
    background-color: #e9ecef; /* Màu xám cho 既読 */
    color: #333;
}

.new-flag {
    background-color: #ff4d4f; /* Màu đỏ cho 新着 */
    color: white;
}

.unread-flag {
    background-color: #1072e9; /* Màu xanh cho 未読 */
    color: white;
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

#select-options-interview_count_id_field {
    position: absolute !important;
    /* Đảm bảo nó hiển thị đúng */
    z-index: 9999 !important;
    /* Đảm bảo không bị che bởi phần tử khác */
    top: 100%;
    /* Đặt ngay dưới input */
    left: 0;
    width: 100%;
    background: white;
    border: 1px solid #ccc;
}

.selected-btn {
    background-color: #1072e9 !important;
    color: white !important;
}

.selected-btn:hover {
    background-color: #1072e9 !important;
    /* Giữ nguyên màu khi hover */
    color: #fff !important;
    opacity: 0.7 !important;
}

.select-wrapper {
    position: relative;
}

.select-wrapper.anavi-select span.caret.material-icons {
    font-size: 16px;
}

.select-wrapper.anavi-select span.caret {
    top: 1rem;
    right: 1rem;
}

.select-wrapper span.caret {
    position: absolute;
    top: .8rem;
    right: 0;
    font-size: .63rem;
    color: #495057;
}

.tooltip {
    z-index: 1000
}

.tooltip-inner {
    text-align: left;
    max-width: 200px
}

@media (min-width: 576px) {
    .tooltip-inner {
        text-align: left !important;
        max-width: 400px !important;
    }
}

/* Loading overlay styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Styles to ensure form and tab have same width */
#side-search {
    padding-top: 0;
}

.card.side-card {
    width: 100%;
    box-sizing: border-box;
}

/* Ensure tab container matches form width exactly */
.tab-container {
    box-sizing: border-box;
    max-width: 41.66%; /* Match exactly with form width */
}

/* Mobile responsive styles for opportunities/active page */
@media (max-width: 767px) {
    /* Tab container adjustments */
    .tab-container {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 13px !important;
        padding-right: 15px !important;
        margin-bottom: 15px !important;
        margin-top: 50px !important;
    }

    .tab-button {
        height: 30px !important;
        font-size: 14px !important;
        width: 48% !important;
    }

    /* Layout adjustments */
    .row.justify-content-between.align-items-center {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-top: 30px !important;
    }

    .row.justify-content-between.align-items-center > div {
        margin-bottom: 0 !important;
        margin-top: 10px;
        justify-content: center;
    }

    .results-sort-container {
        flex-direction: row !important;
        width: 100% !important;
        margin-left: 0 !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .result-count-container {
        width: auto !important;
        text-align: left !important;
        margin-right: 0 !important;
        margin-bottom: 0 !important;
        flex: 0 0 auto !important;
    }

    .sort-display-area {
        width: auto !important;
        justify-content: flex-end !important;
        flex: 0 0 auto !important;
    }

    /* Mobile search form - hide by default, show with menu button */
    .mobile-search-toggle {
        display: block !important;
        position: fixed;
        top: 32.5%;
        left: 84%;
        right: 15px;
        z-index: 1000;
        background: #1072e9;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        cursor: pointer;
    }

    /* Mobile search form styling */
    #side-search {
        display: none !important;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background: white;
        z-index: 1001;
        overflow-y: auto;
        padding: 20px;
    }

    #side-search.active {
        display: block !important;
    }

    /* Close button for mobile search */
    .search-close-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 24px !important;
        cursor: pointer;
        z-index: 1002;
    }

    .mobile-search-toggle i {
        font-size: 24px;
    }

    /* Search form mobile behavior */
    #side-search {
        position: fixed !important;
        top: 0;
        left: -100%;
        width: 90% !important;
        max-width: 350px !important;
        height: 70vh;
        background: white;
        z-index: 100000002;
        transition: left 0.3s ease;
        overflow-y: auto;
        padding-top: 5% !important;
        padding-left: 5%!important;
    }

    #side-search.active {
        left: 5% !important;
        top: 23%!important;
        border-radius: 0.5rem;
    }

    /* Search form overlay */
    .mobile-search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100vw;
        height: 100vh;
        max-width: 100vw;
        max-height: 100vh;
        margin: 0;
        background: rgba(0,0,0,0.5);
        z-index: 100000001;
        display: none;
        box-sizing: border-box;
    }

    .mobile-search-overlay.active {
        display: block;
    }

    /* Prevent body scroll when overlay is active */
    body.mobile-search-open {
        overflow: hidden !important;
    }

    /* Table container - horizontal scroll */
    .table-container {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
        margin: 60px 0 0 -13px !important;
        padding: 0 15px !important;
        border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
                0 8px 25px rgba(0, 0, 0, 0.12),
                0 2px 6px rgba(0, 0, 0, 0.08);
    }

    .opp-table-header,
    .opp-table-row {
        min-width: 800px !important; /* Maintain table width for horizontal scroll */
    }

    /* Adjust table header for mobile */
    .opp-table-header {
        position: sticky;
        top: 0;
        background: linear-gradient(to right, #1072e9, #0066cc) !important;
        z-index: 10;
        height: 42px;
        margin-left: -5%;
    }

    /* Mobile table adjustments */
    .header-cell,
    .table-cell {
        font-size: 12px !important;
        padding: 8px 4px !important;
    }

    /* Status column adjustments */
    .status-header,
    .status-cell {
        width: 100px !important;
    }

    /* Pagination adjustments */
    .pagination {
        justify-content: center !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        margin: 20px 0 !important;
    }

    .page-link {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.875rem !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Mobile search card padding */
    #side-search .card.py-4.side-card {
        padding: 20px !important;
        min-height: calc(100vh - 50px) !important;
        overflow: visible !important;
    }

    /* Ensure all form elements are visible */
    #side-search .form-group {
        margin-bottom: 20px !important;
    }

    #side-search .form-group:last-child {
        margin-bottom: 150px !important;
    }

    /* Show search area on mobile */
    #side-search .search-area {
        display: block !important;
        position: static !important;
        background: transparent !important;
        padding: 20px 0 !important;
        margin-top: 30px !important;
    }

    /* Override d-none d-md-block to show on mobile */
    #side-search .search-area.d-none.d-md-block {
        display: block !important;
    }

    /* Ensure buttons are visible */
    #side-search .btn-group,
    #side-search .button-group,
    #side-search .search-buttons {
        margin-bottom: 100px !important;
        padding-bottom: 50px !important;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .tab-container {
        width: 41.66%; /* Matches col-md-5 width */
        max-width: 41.66%; /* Match exactly with form width */
    }
}

@media (min-width: 992px) {
    .tab-container {
        width: 33.33%; /* Matches col-lg-4 width */
        max-width: 33.33%; /* Match exactly with form width */
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    nav {
        display: block;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .pagination {
        display: flex;
        padding-left: 0;
        list-style: none;
        border-radius: .25rem;
    }

    .page-link {
        position: relative;
        display: block;
        padding: .5rem .75rem;
        margin-left: -1px;
        line-height: 1.25;
        color: #007bff;
        background-color: #fff;
        border: 1px solid #dee2e6;
    }

    .page-link:hover {
        z-index: 2;
        color: #0056b3;
        text-decoration: none;
        background-color: #e9ecef;
        border-color: #dee2e6;
    }

    .page-link:focus {
        z-index: 3;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .page-item:first-child .page-link {
        margin-left: 0;
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem;
    }

    .page-item:last-child .page-link {
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem;
    }

    .page-item.active .page-link {
        z-index: 3;
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }

    .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        cursor: auto;
        background-color: #fff;
        border-color: #dee2e6;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }
    }

    .disabled,
    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect {
        display: inline-block;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #4285f4;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: all .2s linear;
    }

    .pagination .page-item.active .page-link:hover {
        background-color: #4285f4;
    }

    .pagination .page-item.disabled .page-link {
        color: #868e96;
    }

    .pagination .page-item .page-link {
        font-size: .9rem;
        color: #212529;
        background-color: rgba(0, 0, 0, 0);
        border: 0;
        outline: 0;
        transition: all .3s linear;
    }

    .pagination .page-item .page-link:hover {
        background-color: #eee;
        border-radius: .25rem;
        transition: all .3s linear;
    }

    .pagination .page-item .page-link:focus {
        background-color: rgba(0, 0, 0, 0);
        box-shadow: none;
    }

    body a:hover {
        color: #1072e9;
    }

    :focus {
        outline: 0;
    }

    ul {
        list-style: none;
        padding: 0;
    }

    .pagination {
        margin-left: auto;
        margin-right: auto;
        overflow: overlay;
    }

    .pagination .previous_page,
    .pagination .next_page {
        display: none;
    }

    .pagination .page-item {
        padding: .5rem;
    }

    .pagination .page-item .page-link {
        font-size: 1rem;
        border-radius: 50%;
        width: 3rem;
        height: 3rem;
        padding: .875rem 0;
        text-align: center;
    }

    .pagination .page-item .page-link:hover {
        border-radius: 50%;
        background-color: #e6f7ff;
        color: #1072e9;
    }

    .pagination .page-item .page-link:focus {
        box-shadow: none;
    }

    .pagination .page-item.active .page-link {
        border-radius: 50%;
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
        color: white !important;
    }

    .pagination .page-item.active .page-link:hover {
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
        opacity: 0.9;
    }
}

.form-check-input:not(:checked),
.form-check-input:checked {
    position: absolute;
    pointer-events: none;
    opacity: 0;
}

.form-check-input[type=radio]:not(:checked)+label,
.form-check-input[type=radio]:checked+label {
    position: relative;
    display: inline-block;
    height: 1.5625rem;
    padding-left: 28px;
    line-height: 1.5625rem;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: .28s ease;
}

.form-check-input[type=radio]+label:before,
.form-check-input[type=radio]+label:after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 18px;
    height: 18px;
    margin: 4px;
    content: "";
    transition: .28s ease;
}

.form-check-input[type=radio]:not(:checked)+label:before,
.form-check-input[type=radio]:not(:checked)+label:after,
.form-check-input[type=radio]:checked+label:before,
.form-check-input[type=radio]:checked+label:after {
    border-radius: 50%;
}

.form-check-input[type=radio]:not(:checked)+label:before,
.form-check-input[type=radio]:not(:checked)+label:after {
    border: 2px solid #d5d9db;
}

.form-check-input[type=radio]:not(:checked)+label:after {
    transform: scale(0);
}

.form-check-input[type=radio]:checked+label:before {
    border: 2px solid rgba(0, 0, 0, 0);
}

.form-check-input[type=radio]:checked+label:after {
    border: 2px solid #1072e9;
}

.form-check-input[type=radio]:checked+label:after {
    background-color: #1072e9;
}

.form-check-input[type=radio]:checked+label:after {
    transform: scale(1.02);
}

.form-check-input[type=radio]:disabled:not(:checked)+label:before,
.form-check-input[type=radio]:disabled:checked+label:before {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0.46);
}

.form-check-input[type=radio]:focus+label:before {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#radio-options {
    padding-left: 0 !important;
    margin-left: .6rem !important;
    margin-bottom: 16px !important;
}

.new-flag-pc {
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 4px;
}

.new-flag-pc span {
    position: relative;
    z-index: 10;
    color: #fff;
}

.new-flag-pc::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 4px 0 0 0;
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 80px;
    height: 60px;
}

.new-flag-pc::before {
    background-color: #ff0000;
}

.viewed-flag-pc {
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 4px;
}

.viewed-flag-pc span {
    position: relative;
    z-index: 10;
    color: #fff;
}

.viewed-flag-pc::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 4px 0 0 0;
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 80px;
    height: 60px;
}

.viewed-flag-pc::before {
    background-color: #9da9b2;
}

.exp-flag-pc {
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 4px;
}

.exp-flag-pc span {
    position: relative;
    z-index: 10;
    color: #fff;
}

.exp-flag-pc::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 4px 0 0 0;
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 80px;
    height: 60px;
}

.exp-flag-pc::before {
    background-color: #1072e9;
}

/* Fix dropdown z-index issues */
.dropdown-content {
    z-index: 9999 !important;
}

.sort-options-area {
    z-index: 9998 !important;
}

/* Mobile dropdown improvements */
@media (max-width: 767px) {
    .dropdown-content {
        z-index: 10000 !important;
        position: fixed !important;
        max-height: 250px !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
    }

    .sort-options-area {
        z-index: 10001 !important;
        position: fixed !important;
        max-height: 200px !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        min-width: 150px !important;
    }

    .sort-display-area {
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: transparent !important;
        width: 160px !important;
    }
}

#border-light {
    border-color: rgba(0, 0, 0, 0.37) !important;
}

hr {
    border-top: 1px solid rgba(0, 0, 0, 0.2) !important;
}

/* Add vertical bar before all form field labels */
.font-middle.mb-3.ex-bold {
    position: relative;
    padding-left: 1.285rem;
}

.font-middle.mb-3.ex-bold::before {
    content: "";
    background-color: #455965;
    display: block;
    height: 100%;
    width: .428rem;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

@media screen and (min-width: 375px) and (max-width: 390px) {
    .result-count-container{
        margin-left: 4%;
    }

    .mobile-search-toggle{
        position: absolute !important;
        top: 29.5% !important;
    }

    .opp-table-row .status-cell{
        padding-left: 0px;
        width: 80px !important;
    }
}


@media screen and (min-width: 1024px) and (max-width: 1279px) {
    .tab-button {
        height: 3vh !important;
    }

    .opp-table-row .status-cell{
        width: 130px !important;
    }
}

@media screen and (min-width: 1280px) {
    .opp-table-row .status-cell{
        width: 130px !important;
    }
}

.mb-2.col-12#work_place{
    padding-right: 0px !important;
    padding-left: 0px !important;
}