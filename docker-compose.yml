version: '3'
services:
  odoo:
    image: odoo:18.0
    command: odoo -c /etc/odoo/odoo.conf -u all --dev=all
    env_file: .env
    depends_on:
      - postgres
    ports:
      - "8069:8069"
    volumes:
      - data:/var/lib/odoo
      - ./extra-addons:/mnt/extra-addons
      - ./odoo.conf:/etc/odoo/odoo.conf:ro
  postgres:
    image: postgres:13
    env_file: .env
    volumes:
      - db:/var/lib/postgresql/data/pgdata

volumes:
    data:
    db: