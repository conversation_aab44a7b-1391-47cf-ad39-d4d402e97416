/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used from: https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css */
:root {
    --blue: #007bff;
    --indigo: #6610f2;
    --purple: #6f42c1;
    --pink: #e83e8c;
    --red: #dc3545;
    --orange: #fd7e14;
    --yellow: #ffc107;
    --green: #28a745;
    --teal: #20c997;
    --cyan: #17a2b8;
    --white: #fff;
    --gray: #6c757d;
    --gray-dark: #343a40;
    --primary: #007bff;
    --secondary: #6c757d;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #343a40;
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

*,
::after,
::before {
    box-sizing: border-box;
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: transparent;
}

article,
footer,
header,
nav,
section {
    display: block;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    background-color: #fff;
}

[tabindex="-1"]:focus {
    outline: 0 !important;
}

h2,
h3,
h4 {
    margin-top: 0;
    margin-bottom: .5rem;
}

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

ul {
    margin-top: 0;
    margin-bottom: 1rem;
}

ul ul {
    margin-bottom: 0;
}

a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
    -webkit-text-decoration-skip: objects;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

img {
    vertical-align: middle;
    border-style: none;
}

label {
    display: inline-block;
    margin-bottom: .5rem;
}

button {
    border-radius: 0;
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color;
}

button,
input,
textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

button,
input {
    overflow: visible;
}

button {
    text-transform: none;
}

[type=submit],
button,
html [type=button] {
    -webkit-appearance: button;
}

textarea {
    overflow: auto;
    resize: vertical;
}

h2,
h3,
h4 {
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

.small {
    font-size: 80%;
    font-weight: 400;
}

.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-6 {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px;
}

.col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}

.form-control {
    display: block;
    width: 100%;
    height: calc(2.25rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

@media screen and (prefers-reduced-motion:reduce) {
    .form-control {
        transition: none;
    }
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

textarea.form-control {
    height: auto;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

@media screen and (prefers-reduced-motion:reduce) {
    .btn {
        transition: none;
    }
}

.btn:focus,
.btn:hover {
    text-decoration: none;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.btn:disabled {
    opacity: .65;
}

.btn:not(:disabled):not(.disabled) {
    cursor: pointer;
}

.btn-sm {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.dropdown {
    position: relative;
}

.navbar {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: .5rem 1rem;
}

.navbar-brand {
    display: inline-block;
    padding-top: .3125rem;
    padding-bottom: .3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap;
}

.navbar-brand:focus,
.navbar-brand:hover {
    text-decoration: none;
}

.navbar-nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

@media (min-width:768px) {
    .navbar-expand-md {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }

    .navbar-expand-md .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row;
    }
}

.progress {
    display: -ms-flexbox;
    display: flex;
    height: 1rem;
    overflow: hidden;
    font-size: .75rem;
    background-color: #e9ecef;
    border-radius: .25rem;
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.close:not(:disabled):not(.disabled) {
    cursor: pointer;
}

.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
    color: #000;
    text-decoration: none;
    opacity: .75;
}

button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: .5rem;
    pointer-events: none;
}

.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - (.5rem * 2));
}

.modal-dialog-centered::before {
    display: block;
    height: calc(100vh - (.5rem * 2));
    content: "";
}

.modal-content {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    outline: 0;
}

.modal-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem;
}

.modal-header .close {
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
}

.modal-body {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1rem;
}

@media (min-width:576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }

    .modal-dialog-centered {
        min-height: calc(100% - (1.75rem * 2));
    }

    .modal-dialog-centered::before {
        height: calc(100vh - (1.75rem * 2));
    }
}

@media (min-width:992px) {
    .modal-lg {
        max-width: 800px;
    }
}

.bg-white {
    background-color: #fff !important;
}

.border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}

.d-none {
    display: none !important;
}

.d-inline-block {
    display: inline-block !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: -ms-flexbox !important;
    display: flex !important;
}

@media (min-width:768px) {
    .d-md-none {
        display: none !important;
    }

    .d-md-block {
        display: block !important;
    }

    .d-md-flex {
        display: -ms-flexbox !important;
        display: flex !important;
    }
}

@media (min-width:1200px) {
    .d-xl-none {
        display: none !important;
    }

    .d-xl-inline-block {
        display: inline-block !important;
    }

    .d-xl-block {
        display: block !important;
    }
}

.flex-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
}

.flex-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
}

.justify-content-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
}

.align-items-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
}

.align-items-center {
    -ms-flex-align: center !important;
    align-items: center !important;
}

@media (min-width:576px) {
    .flex-sm-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important;
    }
}

@media (min-width:768px) {
    .flex-md-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important;
    }

    .align-items-md-start {
        -ms-flex-align: start !important;
        align-items: flex-start !important;
    }
}

.float-right {
    float: right !important;
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
}

.w-100 {
    width: 100% !important;
}

.h-100 {
    height: 100% !important;
}

.m-0 {
    margin: 0 !important;
}

.mt-0,
.my-0 {
    margin-top: 0 !important;
}

.mb-0,
.my-0 {
    margin-bottom: 0 !important;
}

.mr-1 {
    margin-right: .25rem !important;
}

.mb-1 {
    margin-bottom: .25rem !important;
}

.ml-1 {
    margin-left: .25rem !important;
}

.mb-2 {
    margin-bottom: .5rem !important;
}

.ml-2 {
    margin-left: .5rem !important;
}

.mt-3,
.my-3 {
    margin-top: 1rem !important;
}

.mr-3 {
    margin-right: 1rem !important;
}

.mb-3,
.my-3 {
    margin-bottom: 1rem !important;
}

.mr-4 {
    margin-right: 1.5rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mb-5 {
    margin-bottom: 3rem !important;
}

.p-0 {
    padding: 0 !important;
}

.px-0 {
    padding-right: 0 !important;
}

.pb-0 {
    padding-bottom: 0 !important;
}

.px-0 {
    padding-left: 0 !important;
}

.pt-1 {
    padding-top: .25rem !important;
}

.p-2 {
    padding: .5rem !important;
}

.pb-2 {
    padding-bottom: .5rem !important;
}

.pl-2 {
    padding-left: .5rem !important;
}

.pr-3,
.px-3 {
    padding-right: 1rem !important;
}

.px-3 {
    padding-left: 1rem !important;
}

.p-4 {
    padding: 1.5rem !important;
}

.py-4 {
    padding-top: 1.5rem !important;
}

.pr-4 {
    padding-right: 1.5rem !important;
}

.py-4 {
    padding-bottom: 1.5rem !important;
}

.pl-4 {
    padding-left: 1.5rem !important;
}

.mr-auto,
.mx-auto {
    margin-right: auto !important;
}

.ml-auto,
.mx-auto {
    margin-left: auto !important;
}

@media (min-width:768px) {
    .mb-md-0 {
        margin-bottom: 0 !important;
    }

    .pl-md-0 {
        padding-left: 0 !important;
    }
}

.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}

@media (min-width:768px) {
    .text-md-center {
        text-align: center !important;
    }
}

.font-weight-bold {
    font-weight: 700 !important;
}

@media print {

    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important;
    }

    a:not(.btn) {
        text-decoration: underline;
    }

    img {
        page-break-inside: avoid;
    }

    h2,
    h3,
    p {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }

    body {
        min-width: 992px !important;
    }

    .navbar {
        display: none;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/css/common.css?ver=002 */
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-y: scroll;
}

body {
    margin: 0;
}

article,
footer,
nav,
section {
    display: block;
}

a {
    background-color: transparent;
}

a:active {
    outline: 0;
}

img {
    border: 0;
}

.font-small {
    font-size: 0.75rem !important;
    line-height: 1.8;
}

.font-default {
    font-size: 0.875rem !important;
    line-height: 1.8;
}

.font-middle {
    font-size: 1rem !important;
    line-height: 1.8 !important;
}

.font-large {
    font-size: 1.125rem !important;
    line-height: 1.8 !important;
}

.font-extralarge {
    font-size: 1.25rem !important;
    line-height: 1.6;
}

.font-xxl {
    font-size: 2rem !important;
    line-height: 1.5;
}

.font-xxxl {
    font-size: 2.5rem !important;
    line-height: 1.5;
}

.bold,
.font-weight-bold {
    font-weight: 700;
}

.default-color-text {
    color: #1072e9;
}

.custom-grey-5-text {
    color: #738A97;
}

.custom-grey-6-text {
    color: #455965;
}

.custom-grey-7-text {
    color: #2A3942;
}

.bg-white {
    background-color: #fff;
}

.bg-introgray {
    background-color: #FAFAFA;
}

.d-flex {
    display: flex;
}

.flex-1 {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0%;
}

.overflow-hidden {
    overflow: hidden;
}

@media (min-width: 768px) {
    .w-md-50 {
        width: 50% !important;
    }
}

.text-center {
    text-align: center !important;
}

.mb-5 {
    margin-bottom: 2rem !important;
}

.mt-6 {
    margin-top: 2.5rem !important;
}

.mb-6 {
    margin-bottom: 2.5rem !important;
}

.mb-20px {
    margin-bottom: 1.25rem !important;
}

.mb-28px {
    margin-bottom: 1.75rem !important;
}

.p-28px {
    padding: 1.75rem !important;
}

.mb-60px {
    margin-bottom: 3.75rem;
}

.max-w-300 {
    max-width: 300px;
}

.max-w-320 {
    max-width: 320px;
}

.max-w-360 {
    max-width: 360px;
}

.max-w-400 {
    max-width: 400px;
}

.max-w-840 {
    max-width: 840px;
}

.btn-w-300 {
    width: 300px;
}

.gap-6 {
    gap: 2.5rem;
}

.gap-20px {
    gap: 1.25rem !important;
}

.btn-outline-default {
    color: #1072e9 !important;
    background-color: #fff !important;
    border: 2px solid #1072e9 !important;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%);
}

.rounded-3 {
    border-radius: 3px;
}

.shadow {
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%) !important;
}

button,
input,
textarea {
    color: inherit;
    font: inherit;
    margin: 0;
}

button {
    overflow: visible;
}

button {
    text-transform: none;
}

button {
    -webkit-appearance: button;
    cursor: pointer;
}

input {
    line-height: normal;
    outline: none;
}

textarea {
    overflow: auto;
}

a.btn {
    display: block;
    padding: 13px;
    text-decoration: none;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 3px;
    transition: 200ms ease-in;
}

.btn.btn-fill {
    background-color: #3479CA;
    color: #fff;
}

.btn.btn-fill.btn-fill-green {
    background-color: #1072e9;
    box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%), 0 2px 10px 0 rgb(0 0 0 / 12%);
}

.btn.btn-outline {
    border: 1px solid #3479CA;
    color: #3479CA;
    background-color: #fff;
}

.btn.btn-outline.btn-outline-green {
    border: 1px solid #1072e9 !important;
    color: #1072e9 !important;
    background-color: #fff !important;
    box-shadow: 0px 1px 2px 1px rgba(0, 0, 0, 0.12);
    filter: drop-shadow(0px 0px 8px rgba(0, 0, 0, 0.1));
}

.btn.btn-outline.btn-outline-green:hover {
    color: #1072e9;
}

a.btn:hover {
    opacity: .6;
}

.btn.btn-fill:hover {
    color: #fff;
}

.btn.btn-outline:hover {
    color: #3479CA;
}

html {
    text-rendering: optimizeLegibility;
}

br {
    letter-spacing: 0 !important;
}

img {
    -ms-interpolation-mode: bicubic;
}

ul {
    padding: 0;
}

li {
    list-style: none;
}

a {
    color: inherit;
    text-decoration: none;
}

h2,
h3,
h4,
p,
ul {
    margin-top: 0;
    margin-bottom: 0;
}

* {
    vertical-align: top;
}

* {
    filter: inherit;
}

p,
li {
    -ms-line-break: strict;
    line-break: strict;
    -ms-word-break: break-strict;
    word-break: break-strict;
    word-wrap: break-word;
}

body {
    background-color: #ffffff;
    font-family: YakuHanJP_Noto, YakuHanJP, "Roboto", "Noto Sans Japanese", sans-serif;
    font-size: 62.5%;
    line-height: 1.6;
    font-weight: 500;
    color: #141414;
    -webkit-text-size-adjust: 100%;
    font-feature-settings: "palt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    letter-spacing: 0.025em;
}

h2,
h3,
h4,
p,
ul,
li {
    line-height: 1;
}

a {
    color: #141414;
}

.notouch a:hover {
    color: #141414;
    text-decoration: none;
}

* {
    box-sizing: border-box;
}

img {
    width: auto;
    max-width: 100%;
    height: auto;
}

html,
body {
    width: 100%;
    min-width: 320px;
}

@media screen and (min-width: 1024px) {
    img {
        width: 100%;
        height: auto;
    }

    .sp_only {
        display: none !important;
    }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
    .sp_only {
        display: none !important;
    }
}

@media screen and (max-width: 767px) {
    .tbpc_only {
        display: none !important;
    }
}

@media screen and (max-width: 320px) {
    body {
        overflow-x: scroll;
    }
}

[class*=" icon-"] {
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.wrapper {
    overflow: hidden;
}

footer {
    padding-top: 45px;
    padding-bottom: 22px;
    border-top: 1px solid #e6e6e6;
    background-color: #fff;
    text-decoration: none;
}

footer a {
    color: #141414 !important;
}

footer .inner {
    width: calc(980 / 1280 * 100%);
    max-width: 1600px;
    margin: 0 auto;
}

footer .nav_wrap {
    display: flex;
    justify-content: space-between;
    margin-bottom: 54px;
}

footer .fnav {
    width: calc(850 / 980 * 100%);
}

footer .fnav ul {
    display: flex;
    flex-wrap: wrap;
    padding-top: 5px;
}

footer .fnav ul.snav {
    padding-top: 0;
}

footer .fnav li {
    margin-bottom: 25px;
    margin-right: calc(45 / 850 * 100%);
}

footer .fnav a {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.025em;
    text-decoration: none;
    white-space: nowrap;
}

footer .fnav .snav a {
    font-size: 0.75rem;
}

footer .fnav a .en {
    font-size: 108.33%;
}

footer .f_logo {
    width: calc(130 / 980 * 100%);
}

footer .copyright_wrap {
    display: flex;
    justify-content: space-between;
    padding-top: 16px;
    border-top: 1px solid #e6e6e6;
}

footer .copyright_wrap .copyright,
footer .copyright_wrap .company_name {
    font-size: 10px;
}

footer .copyright_wrap .company_name a {
    text-decoration: none;
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
    footer .inner {
        width: calc(944 / 1024 * 100%);
    }

    footer .fnav li {
        margin-right: calc(25 / 850 * 100%);
    }
}

@media screen and (max-width: 767px) {
    footer {
        padding-top: 0;
        padding-bottom: 22px;
        padding-right: 0;
        padding-left: 0;
        border-top: 1px solid #e6e6e6;
    }

    footer .inner {
        width: 100%;
    }

    footer .nav_wrap {
        display: block;
        margin-bottom: 60px;
    }

    footer .fnav {
        width: auto;
    }

    footer .fnav ul {
        display: block;
        padding-top: 0;
    }

    footer .fnav li {
        margin-bottom: 0;
        margin-right: 0;
    }

    footer .fnav ul:not(.snav) a {
        display: block;
        padding: 24px 23px;
        border-bottom: 1px solid #e6e6e6;
    }

    footer .fnav a .en {
        font-family: "Noto Sans Japanese", sans-serif;
        font-size: 100%;
    }

    footer .fnav .snav {
        margin-top: 25px;
        padding: 0 23px;
    }

    footer .fnav .snav li {
        display: inline-block;
        margin-bottom: 12px;
        margin-right: 32px;
    }

    footer .fnav .snav a {
        font-size: 10px;
    }

    footer .f_logo {
        width: 130px;
    }

    footer .copyright_wrap {
        display: block;
        padding: 0 23px;
        border-top: none;
    }

    footer .copyright_wrap .copyright {
        font-size: 10px;
    }
}

.btn_conversion {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10;
    width: 110px;
    height: 110px;
    opacity: 0;
    transition: opacity 400ms ease-in-out, transform 600ms cubic-bezier(0.165, 0.84, 0.44, 1);
    text-align: center;
}

.btn_conversion a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #1072e9;
    border-radius: 100%;
    font-size: 13px;
    line-height: 1.38;
    font-weight: 700;
    text-decoration: none;
    color: #ffffff;
}

@media screen and (max-width: 767px) {
    .btn_conversion {
        width: 90px;
        height: 90px;
    }

    .btn_conversion a {
        font-size: 12px;
    }
}

#contact.section {
    padding-top: 60px;
    padding-bottom: 60px;
    background-color: #EAF8F7;
    background-image: url(https://assign-navi.jp/assets/img/common/bg_conv_area_circle.png);
    background-size: 987px, auto, contain;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    z-index: 0;
}

#contact.section::before {
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    content: '';
    width: 740px;
    height: 194px;
    z-index: -1;
    background-image: url(https://assign-navi.jp/assets/img/common/bg_conv_area_left.png);
    background-size: 740px, auto, contain;
}

#contact.section::after {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    content: '';
    width: 375px;
    height: 101px;
    z-index: -1;
    background-image: url(https://assign-navi.jp/assets/img/common/bg_conv_area_right.png);
    background-size: 375px, auto, contain;
    mix-blend-mode: multiply;
}

#contact.section .sec_title {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1.6;
    text-align: center;
}

#contact.section .sub_title {
    margin-bottom: 2.5rem;
    font-size: 0.875rem;
    line-height: 1.8;
}

#contact.section .conversion-btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 1.75rem;
}

#contact.section .conversion-btn-wrapper .btn {
    width: 100%;
    max-width: 350px;
    font-size: 1.125rem;
    line-height: 1.8;
    padding: 13px;
    margin: 0;
}

@media (max-width: 767px) {
    #contact.section {
        padding-right: 20px;
        padding-left: 20px;
    }

    #contact.section .sub_title {
        margin-bottom: 1.25rem;
    }

    #contact.section .conversion-btn-wrapper {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    #contact.section::before {
        width: 518px;
        height: 135px;
        background-size: 518px, auto, contain;
    }

    #contact.section::after {
        width: 390px;
        height: 101px;
        background-size: 390px, auto, contain;
    }
}

@media screen and (min-width: 768px) {
    .notouch a {
        transition: 400ms ease;
    }

    .notouch footer .fnav a:hover {
        color: #969696;
    }

    .notouch .btn_conversion a {
        transition: background 400ms ease-in-out;
    }

    .notouch .btn_conversion a:hover {
        background-color: #1072e9;
        opacity: .6;
        color: #ffffff;
    }

    .notouch #voice.section .voice_wrap a.voice_panel,
    .notouch #voice.section .voice_wrap a.voice_panel .text_wrap {
        transition: box-shadow 400ms ease-in-out, border 400ms ease-in-out;
    }

    .notouch #voice.section .voice_wrap a:hover.voice_panel {
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
        border-color: #ffffff;
    }

    .notouch #voice.section .voice_wrap a.voice_panel {
        transition: 400ms ease-in-out;
    }

    .notouch #voice.section .voice_wrap a:hover.voice_panel {
        opacity: 0.6;
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.25);
    }

    .notouch #voice.section .voice_wrap a.voice_panel .img_wrap {
        overflow: hidden;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/css/layout.css?ver=002 */
#usage-flow.section {
    padding: 60px 20px;
}

#usage-flow.section .inner {
    max-width: 980px;
}

#usage-flow.section .usage-flow-content {
    position: relative;
    border-radius: 5px;
}

#usage-flow.section .usage-flow-content .number {
    display: block;
    position: absolute;
    top: -1rem;
    left: 1.5rem;
    color: #1072e9;
}

#usage-flow.section .usage-flow-content h3 {
    width: 9.75rem;
}

#usage-flow.section .usage-flow-content .usage-flow-description {
    border-left: 1px solid #E6EAEC;
    padding-left: 1.75rem;
}

@media screen and (max-width: 767px) {
    #usage-flow.section .usage-flow-content .usage-flow-description {
        border-top: 1px solid #E6EAEC;
        border-left: initial;
        margin-top: 0.5rem;
        padding-top: 1rem;
        padding-left: initial;
    }

    #usage-flow.section .usage-flow-content h3 {
        width: auto;
    }
}

#usage-flow.section .usage-flow-triangle {
    width: 30px;
    height: 15px;
    border-top: 15px solid #9DA9B2;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
}

#voice.section .inner {
    padding: 3.75rem 1.25rem;
}

#voice.section .voice_wrap {
    max-width: 800px;
    margin-right: auto;
    margin-left: auto;
}

#voice.section .voice_wrap a {
    text-decoration: none;
}

#voice.section .voice_wrap .voice_panel {
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    overflow: hidden;
}

#voice.section .voice_wrap .voice_panel .img_wrap img {
    width: 100%;
}

@media (min-width: 576px) {
    #voice.section .voice_wrap .voice_panel .img_wrap {
        width: calc((264/1280)* 100vw);
        height: calc((150/1280) * 100vw);
    }
}

#faq.section .inner {
    width: calc(980 / 1280 * 100%);
    max-width: 1600px;
    margin: 0 auto;
    padding-top: 172px;
    padding-bottom: 180px;
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
    #faq.section .inner {
        width: calc(944 / 1024 * 100%);
    }
}

@media screen and (max-width: 767px) {
    #faq.section {
        padding-bottom: 100px;
    }

    #faq.section .inner {
        position: relative;
        width: auto;
        padding-top: 96px;
        padding-bottom: 110px;
        padding-right: 23px;
        padding-left: 23px;
    }
}

#faq.section .faq-card-link-wrapper {
    transition: 400ms ease;
}

#faq.section .faq-card-link-wrapper:hover {
    opacity: .6;
    transition: 400ms ease;
}

#faq.section .faq-card {
    padding: 1.75rem;
    gap: 1.75rem;
    background: #FFFFFF;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
}

#faq.section .faq-card .faq-card-img {
    width: 60px;
    height: 60px;
}

@media (max-width: 767px) {
    #faq.section .faq-card {
        gap: 0.75rem;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/css/header.css?ver=002 */
body header a {
    color: rgba(0, 0, 0, 0.87);
    overflow: visible;
}

body header a:hover,
body header a:hover .material-icons {
    opacity: 0.7;
    text-decoration: none;
}

body header .navbar.scrolling-navbar {
    padding: 28px 32px;
}

@media (min-width: 768px) {
    body header .navbar.scrolling-navbar {
        height: 80px;
    }
}

body header .navbar-brand img {
    width: 113px;
    vertical-align: baseline;
}

@media (min-width: 768px) {
    body header .navbar-brand img {
        width: 8.5rem;
        margin-right: 1.5rem;
        margin-bottom: 0;
        vertical-align: sub;
    }
}

body header .navbar-left>ul {
    margin: 0;
}

body header .navbar-left>ul li {
    display: inline-block;
    position: relative;
    line-height: 1.7;
}

body header .navbar-left>ul li span {
    cursor: pointer;
}

body header .navbar-left>ul li ul {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background-color: #fff;
    display: none;
    position: absolute;
    left: 15px;
}

body header .navbar-left>ul li ul li {
    display: block;
    padding: 6px 0;
    margin: 0;
}

body header .navbar-left>ul li ul li a {
    padding: 0 20px;
    display: block;
    text-decoration: none;
    white-space: nowrap;
}

body header .navbar-left>ul li:hover ul {
    display: block;
}

body header .navbar-left>ul li:hover ul a:hover {
    opacity: 0.7;
}

body header #login_link,
body header #registration_link {
    width: 90px;
    height: 32px;
    font-weight: 700;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
}

body header #login_link {
    line-height: 0.8;
}

body header #registration_link {
    line-height: 1;
}

body header .header-search-menu {
    font-weight: 500;
}

body header .header-search-menu a {
    color: #2a3942;
}

body header .header-search-menu span:hover {
    opacity: 0.7;
}

body header .navbar-nav {
    flex-direction: row;
    -ms-flex-direction: row;
}

body header .icon-wrapper {
    position: relative;
    float: left;
}

body header .icon-wrapper i {
    width: 28px;
    height: 28px;
    font-size: 28px;
    text-align: center;
    vertical-align: middle;
    color: #455965;
}

@media (min-width: 768px) {
    body header .icon-wrapper i {
        width: 32px;
        height: 32px;
        font-size: 32px;
    }
}

body header .link_area {
    font-size: 13px;
    line-height: 1;
}

body header .phone-area label {
    font-size: 18px;
}

body header .phone-icon {
    width: 17px;
    height: 17px;
    display: inline-block;
    margin: 0 4px 3px 0;
}

body header .phone-reception-time {
    font-weight: 500;
    line-height: normal;
}

body header .phone-outside-reception-hours {
    font-family: "YakuHanJP";
    font-feature-settings: "palt" 1;
}

body header .sp-phone-area {
    display: block;
    text-align: center;
    background-color: #eaf8f7;
}

body header .sp-phone-invitation-message {
    font-weight: 700;
    margin-bottom: 0;
    white-space: nowrap;
}

body header .sp-phone-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    margin: 0 4px 6px 0;
    vertical-align: bottom;
}

body header .sp-phone-number {
    margin-bottom: 0;
    font-size: 1.5rem;
    line-height: normal;
    white-space: nowrap;
}

body header .sp-phone-reception-time {
    color: #2a3942;
    font-weight: 500;
    line-height: normal;
}

body header .sp-phone-outside-reception-hours {
    color: #2a3942;
    font-family: "YakuHanJP";
    font-feature-settings: "palt" 1;
}

body header .sp-phone-area .tel-btn {
    font-size: 1.5rem;
    line-height: normal;
}

body header .line-height-normal {
    line-height: 1.6;
}

body header .line-height-mini {
    line-height: 0.7;
}

body header .roboto {
    font-family: "Roboto";
}

body header .header-color {
    color: #2a3942;
}

@media screen and (min-width: 481px) {
    body header .tel-btn {
        display: none;
    }
}

@media (max-width: 767px) {
    body header .navbar.scrolling-navbar {
        padding: 12px 16px;
        height: 64px;
    }
}

@media (max-width: 480px) {
    body header .text-phone-number {
        display: none;
    }
}

.account-side-scrollbar {
    overflow-y: scroll;
    height: 100vh;
    border-left: 2px solid #9DA9B2;
}

.custom-side-nav {
    color: #2a3942;
    display: none;
    position: fixed;
    right: 0;
    width: 318px !important;
    z-index: 10;
    height: 100%;
}

.custom-side-nav .sign_in_area {
    text-align: center;
    margin-top: 26px;
    margin-bottom: 32px;
}

.custom-side-nav .sign_in_area a {
    width: 138px;
    height: 48px;
    line-height: 2.75rem;
}

.custom-side-nav ul li .border-bottom {
    margin-bottom: 12px;
}

.custom-side-nav ul li .side-nav-title {
    padding-bottom: 12px;
}

.custom-side-nav ul li .side-nav-title,
.custom-side-nav ul li .side-nav-contents {
    cursor: pointer;
}

.custom-side-nav ul li .side-nav-title a,
.custom-side-nav ul li .side-nav-contents a {
    color: #2a3942;
    padding: 0 0 12px 24px;
}

.custom-side-nav ul li .side-nav-title a:hover span,
.custom-side-nav ul li .side-nav-contents a:hover span {
    opacity: 0.7;
}

.custom-side-nav ul li .side-nav-title a span,
.custom-side-nav ul li .side-nav-contents a span {
    font-size: 1rem;
    line-height: 32px;
}

i {
    cursor: pointer;
}

body header .navbar-left>ul li:hover ul a:hover {
    color: #2a3942;
}

.side-nav-title span {
    font-family: 'Noto Sans Japanese';
}

.default-main-color {
    color: #1072e9;
}

.custom-grey-text {
    color: rgba(84, 110, 122, .87);
}

.btn {
    margin: 0.375rem;
    color: inherit;
    text-transform: uppercase;
    word-wrap: break-word;
    white-space: normal;
    cursor: pointer;
    border: 0;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    padding: 0.84rem 2.14rem;
    font-size: .81rem;
}

.btn-default {
    color: #fff !important;
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
}

.btn-outline-blue-grey {
    color: #78909c !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border: 2px solid #78909c !important;
}

.white {
    background-color: #fff;
}

.vertical-text-bottom {
    vertical-align: text-bottom;
}

.ex-bold {
    font-weight: 700 !important;
}

.white-text {
    color: #fff !important;
}

.custom-side-nav .sign_in_area a {
    line-height: 2.75rem !important;
}

.modal-body {
    font-size: 14px;
}

.modal-body p {
    line-height: 1.5;
}

.create_trouble_contact {
    color: #000000;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
}

.ex-bold {
    font-weight: 700 !important;
}

.modal-dialog .modal-content {
    border: 0;
    border-radius: 0.25rem;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
}

.modal-dialog .modal-content .modal-header {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
}

.modal {
    padding-right: 0 !important;
}

.modal {
    padding-right: 0 !important;
}

.modal {
    opacity: 0.5;
}

.modal-header {
    padding: 2rem;
    border-bottom: none;
}

.modal-body {
    padding: 0 2rem 2rem;
}

@media (max-width: 767px) {
    .modal-header {
        padding: 2rem 1rem;
        border-bottom: none;
    }

    .modal-body {
        padding: 0 1rem 2rem;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/css/project.css?ver=002 */
.project article.main_section {
    margin-top: 60px;
}

@media (min-width: 768px) {
    .project article.main_section {
        margin-top: 80px;
    }
}

#headline.section {
    background-color: #ffffff;
    position: relative;
}

#headline.section::before {
    position: absolute;
    content: "";
    display: block;
    width: 522px;
    height: 214px;
    background-image: url(https://assign-navi.jp/assets/img/common/bg-wave-left.png);
    background-repeat: no-repeat;
    background-size: contain;
    z-index: 0;
}

#headline.section::after {
    position: absolute;
    top: 225px;
    right: 0;
    content: "";
    display: block;
    width: 529px;
    height: 320px;
    background-image: url(https://assign-navi.jp/assets/img/common/bg-wave-right.png);
    background-position: right;
    background-repeat: no-repeat;
    background-size: contain;
    z-index: 0;
}

#headline.section .inner {
    position: relative;
    margin: 0 auto;
    padding: 5rem 1.25rem 4rem;
    z-index: 2;
}

#headline.section .inner .project-content-wrapper {
    position: relative;
    gap: 2.5rem;
    margin-bottom: 40px;
}

#headline.section .inner .project-content-title {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
    z-index: 0;
}

#headline.section .inner .project-content-title::before {
    content: '';
    display: block;
    position: absolute;
    width: calc(100% + 24px);
    height: 1rem;
    top: 50%;
    left: -0.75rem;
    background-color: #EAF8F7;
    z-index: -1;
}

#headline.section .inner .project-content-items {
    background-color: #fafafa;
    border-radius: 8px;
    padding-top: 28px;
    padding-bottom: 28px;
}

#headline.section .inner .project-content-item {
    gap: 14px;
}

#headline.section .inner .project-content-item-text {
    width: 10.562rem;
}

#headline.section .inner .project-content-item-img {
    width: 48px;
    height: 48px;
}

#headline.section .inner .bg-illust-01 {
    position: absolute;
    display: block;
    top: -60px;
    left: -80px;
    width: 145px;
    height: 188px;
    background-image: url(https://assign-navi.jp/assets/img/common/new_work_hero_illust2.png);
    background-repeat: no-repeat;
    background-size: contain;
    z-index: 1;
}

#headline.section .inner .bg-illust-02 {
    position: absolute;
    display: block;
    bottom: -6px;
    right: -40px;
    width: 81px;
    height: 165px;
    background-image: url(https://assign-navi.jp/assets/img/common/walk_find_illust_freelance.png);
    background-repeat: no-repeat;
    background-size: contain;
    z-index: 1;
}

@media screen and (max-width: 767px) {
    #headline.section .inner {
        padding-left: 20px;
        padding-right: 20px;
    }

    .project-content-wrapper {
        max-width: 320px;
    }
}

#feature.section {
    background-color: #FAFAFA;
    padding: 3.75rem 1.25rem;
}

#feature.section .feature-description a {
    color: #1072e9;
}

#feature.section .feature-description a:hover {
    color: #1072e9;
    opacity: .6;
}

#feature.section .inner {
    max-width: 980px;
    margin-right: auto;
    margin-left: auto;
}

@media (max-width: 767px) {
    #feature.section .inner {
        padding-left: 20px;
        padding-right: 20px;
    }

    .feature-content-wrapper {
        align-items: center;
    }
}

#fee.section {
    padding: 3.75rem 1.25rem;
    background-color: #fff;
}

#fee.section .inner {
    max-width: 800px;
}

#voice.section {
    background-color: #ffffff;
}

#faq.section {
    background-color: #fafafa;
    padding: 0 1.25rem 0;
}

#faq.section .inner {
    padding: 3.75rem 0;
    max-width: 800px;
    width: 100%;
    gap: 1.75rem;
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: url('https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff') format('woff');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: url('https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff') format('woff');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: url('https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff') format('woff');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: url('https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff') format('woff');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: url('https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff') format('woff');
}

@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: url('https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff') format('woff');
}

@font-face {
    font-family: 'Noto Sans Japanese';
    font-style: normal;
    font-weight: 500;
    src: local('Noto Sans CJK JP Regular'), local('NotoSansCJKjp-Regular'), local('NotoSansJP-Regular'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Medium.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Medium.woff') format('woff');
}

@font-face {
    font-family: 'Noto Sans Japanese';
    font-style: normal;
    font-weight: 700;
    src: local('Noto Sans CJK JP Bold'), local('NotoSansCJKjp-Bold'), local('NotoSansJP-Bold'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff') format('woff');
}

@font-face {
    font-family: 'icomoon';
    src: url('https://assign-navi.jp/assets/font/icomoon.eot?nakydk');
    src: url('https://assign-navi.jp/assets/font/icomoon.eot?nakydk#iefix') format('embedded-opentype'), url('https://assign-navi.jp/assets/font/icomoon.ttf?nakydk') format('truetype'), url('https://assign-navi.jp/assets/font/icomoon.woff?nakydk') format('woff'), url('https://assign-navi.jp/assets/font/icomoon.svg?nakydk#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: MySansSerif;
    font-weight: normal;
    src: local('HelveticaNeue'), local('Helvetica Neue'), local('Helvetica'), local('Arial');
}

@font-face {
    font-family: MySansSerif;
    font-weight: 700;
    src: local('HelveticaNeueBold'), local('HelveticaNeue-Bold'), local('Helvetica Neue Bold'), local('HelveticaBold'), local('Helvetica-Bold'), local('Helvetica Bold'), local('Arial Bold');
}

@font-face {
    font-family: MySansSerif;
    font-weight: 900;
    src: local('HelveticaNeueBlack'), local('HelveticaNeue-Black'), local('Helvetica Neue Black'), local('HelveticaBlack'), local('Helvetica-Black'), local('Helvetica Black'), local('Arial Black');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 100;
    src: local('HiraginoSans-W1'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 200;
    src: local('HiraginoSans-W2'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 300;
    src: local('HiraginoSans-W3'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 400;
    src: local('HiraginoSans-W3'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 500;
    src: local('HiraginoSans-W5'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 600;
    src: local('HiraginoSans-W6'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 700;
    src: local('HiraginoSans-W6'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 800;
    src: local('HiraginoSans-W7'), local('Hiragino Sans');
}

@font-face {
    font-family: 'HiraginoCustom';
    font-weight: 900;
    src: local('HiraginoSans-W8'), local('Hiragino Sans');
}

@font-face {
    font-family: MyYugo;
    font-weight: normal;
    src: local("YuGothic-Medium"), local("Yu Gothic Medium"), local("YuGothic-Regular");
}

@font-face {
    font-family: MyYugo;
    font-weight: bold;
    src: local("YuGothic-Bold"), local("Yu Gothic");
}

@font-face {
    font-family: 'Noto Sans Japanese';
    font-style: normal;
    font-weight: 500;
    src: local('Noto Sans CJK JP Regular'), local('NotoSansCJKjp-Regular'), local('NotoSansJP-Regular'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Medium.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Medium.woff') format('woff');
}

@font-face {
    font-family: 'Noto Sans Japanese';
    font-style: normal;
    font-weight: 700;
    src: local('Noto Sans CJK JP Bold'), local('NotoSansCJKjp-Bold'), local('NotoSansJP-Bold'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2') format('woff2'), url('https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff') format('woff');
}