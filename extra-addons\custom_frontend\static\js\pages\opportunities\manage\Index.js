import { userInfo } from "../../../router/router.js";
import { createManagementBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const Index = {
    template: `
        <main class="pb-3" style="background-color: white;">
    ${createManagementBreadcrumb('案件管理一覧')}
    <div class="container-fluid">
        <div class="tab-container" style="display: flex; justify-content: flex-start;margin-top: 0px;">
            <div class="tab-button active">案件管理</div>
            <div class="tab-button" @click="navigateToResumes" style="margin-left: 0.2%;">人財管理</div>
        </div>
    </div>
    <div class="container-fluid grabient">
        <div class="row">
            <div :class="['d-md-block', 'col-12', 'col-md-4', isSearchOpen ? 'active' : 'd-none']" id="side-search">
                <div class="card py-4 side-card" style="width: 101%;">
                    <form class="new_opportunity_manage_condition" id="opportunity_manage_condition_form"
                          novalidate="novalidate" accept-charset="UTF-8" @submit.prevent="opp_search"
                          method="get"><input name="utf8" type="hidden" value="✓" autocomplete="off">
                        <div class="container">
                            <div class="row">
                                <div class="col-12">
                                    <div class="mx-auto mb-5"><input class="form-control"
                                                                     autocomplete="off" id="free_keyword_field"
                                                                     type="text"
                                                                     placeholder="フリーキーワード"
                                                                     v-model="free_keyword"
                                                                     name="opportunity_manage_condition[free_keyword]">
                                    </div>
                                    <div class="mx-auto mt-0 mb-3"><label class="font-middle mb-3"
                                                                          for="">案件ステータス</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_manage_condition[status][]"
                                                                                               value="">

                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="opportunity_status_field_opportunity_manage_condition_0"
                                                       type="checkbox" value="wanted"
                                                       name="opportunity_manage_condition[status][]"
                                                       v-model="status"><label
                                                id="opportunity_status_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opportunity_status_field_opportunity_manage_condition_0">募集中</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="opportunity_status_field_opportunity_manage_condition_1"
                                                       type="checkbox" value="end"
                                                       name="opportunity_manage_condition[status][]"
                                                       v-model="status"><label
                                                id="opportunity_status_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opportunity_status_field_opportunity_manage_condition_1">応募期間終了</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="opportunity_status_field_opportunity_manage_condition_2"
                                                       type="checkbox" value="close"
                                                       name="opportunity_manage_condition[status][]"
                                                       v-model="status"><label
                                                id="opportunity_status_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opportunity_status_field_opportunity_manage_condition_2">案件終了</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mx-auto mb-5"><label
                                        class="font-middle mb-3"><span>選考ステータス</span></label>
                                        <div class="selecting-form custom-control custom-checkbox with-title ml-md-3">
                                            <input value="false" autocomplete="off" type="hidden"
                                                   name="opportunity_manage_condition[match_status]"
                                                   id="opportunity_manage_condition_match_status"><input
                                            id="match_status_field" class="custom-control-input" type="checkbox"
                                            value="選考ステータス"
                                            name="opportunity_manage_condition[match_status]"
                                            v-model="match_status"><label
                                            id="match_status_field_label" class="custom-control-label"
                                            for="match_status_field">選考中</label></div>
                                    </div>
                                    <label class="font-middle mb-3">期間</label>
                                    <div class="ml-md-3 mb-4">
                                        <div class="mx-auto mb-5">
                                            <div v-dropdown="{ modelValue: '', listType: 'optionPeriodKinds'}" @selected="handleSelection"><input type="hidden" v-model="optionPeriodKinds"></div>
                                        </div>
                                        <div class="row">
                                            <div class="col-9 input-calendar">
                                                <div class="input-calendar-input-holder">
                                                    <div class="mx-auto"><input v-pickadate="{ model: 'date_start' }"
                                                                                 class="form-control picker__input"
                                                                                 autocomplete="off"
                                                                                 v-model="date_start"
                                                                                 id="date_start_field" type="text"
                                                                                 name="opportunity_manage_condition[date_start]"
                                                                                 readonly="" aria-haspopup="true"
                                                                                 aria-expanded="false"
                                                                                 aria-readonly="false"
                                                                                 aria-owns="date_start_field_root">

                                                    </div>
                                                </div>
                                                <div class="p-0 input-calendar-icon-holder"><i
                                                    class="material-icons md-grey md-18 calender-icon"
                                                    style="line-height: 0.6em;">date_range</i>
                                                </div>
                                            </div>
                                            <div class="col-3 px-0" style="display: flex; justify-content: center; align-items: center;"><label class="inline-unit-label">から</label></div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-9 input-calendar">
                                                <div class="input-calendar-input-holder">
                                                    <div class="mx-auto"><input v-pickadate="{ model: 'date_end' }"
                                                                                    class="form-control picker__input"
                                                                                    autocomplete="off" id="date_end_field"
                                                                                    type="text"
                                                                                    v-model="date_end"
                                                                                    name="opportunity_manage_condition[date_end]"
                                                                                    readonly="" aria-haspopup="true"
                                                                                    aria-expanded="false"
                                                                                    aria-readonly="false"
                                                                                    aria-owns="date_end_field_root">

                                                    </div>
                                                </div>
                                                <div class="p-0 input-calendar-icon-holder"><i
                                                    class="material-icons md-grey md-18 calender-icon"
                                                    style="line-height: 0.6em;">date_range</i>
                                                </div>
                                            </div>
                                            <div class="col-3 px-0" style="display: flex; justify-content: center; align-items: center;"><label class="inline-unit-label">まで</label></div>
                                        </div>
                                    </div>
                                    <div class="mx-auto mt-0 mb-3"><label class="font-middle mb-3"
                                                                          for="">案件の公開範囲</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_manage_condition[public_status_id][]"
                                                                                               value="">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="public_status_id_field_opportunity_manage_condition_0"
                                                       type="checkbox" value="public"
                                                       name="opportunity_manage_condition[public_status_id][]"
                                                       v-model="public_status_id"><label
                                                id="public_status_id_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="public_status_id_field_opportunity_manage_condition_0">公開</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="public_status_id_field_opportunity_manage_condition_1"
                                                       type="checkbox" value="limited"
                                                       name="opportunity_manage_condition[public_status_id][]"
                                                       v-model="public_status_id"><label
                                                id="public_status_id_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="public_status_id_field_opportunity_manage_condition_1">ブックマーク先のみに公開</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="public_status_id_field_opportunity_manage_condition_2"
                                                       type="checkbox" value="private"
                                                       name="opportunity_manage_condition[public_status_id][]"
                                                       v-model="public_status_id"><label
                                                id="public_status_id_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="public_status_id_field_opportunity_manage_condition_2">非公開</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center d-none d-md-block">
                                        <button name="button"
                                                class="btn btn-default font-middle waves-effect waves-light" @click="opp_search">検索
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="container-fluid blue-grey lighten-4" :class="['search-fixed-btn', isSearchOpen && isMobile ? 'd-block' : 'd-none']">
                        <div class="row py-5">
                            <div class="col-12">
                                <button name="button"
                                        class="btn btn-default btn-block font-middle mb-3 submit-btn waves-effect waves-light"
                                        @click="opp_search">検索
                                </button>
                            </div>
                            <div class="col-12">
                                <div
                                    class="btn btn-blue-grey btn-block font-middle search-toggle waves-effect waves-light" @click="closeSearchMenu">
                                    閉じる
                                </div>
                            </div>
                        </div>
                    </div>
                    <div aria-hidden="true" aria-labelledby="charge_account-modal" class="modal"
                         id="charge_account-modal" role="dialog" tabindex="-1">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header"><h4 class="modal-title w-100">案件担当者を選択</h4>
                                    <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <div class="row mb-3">
                                        <div class="col-6 col-md-3">
                                            <div class="form-check"><input type="checkbox" name="choiced_accounts"
                                                                           id="choiced_accounts_25694" value="Nguyen Son"
                                                                           multiple="multiple" class="form-check-input" v-model="isChecked"><label
                                                class="custom-control-label h-100 mb-2" for="choiced_accounts_25694">Nguyen
                                                Son</label></div>
                                        </div>
                                        <div class="col-6 col-md-3">
                                            <div class="form-check"></div>
                                        </div>
                                    </div>
                                    <div class="row justify-content-center">
                                        <div class="col-12 col-md-4">
                                            <button aria-label="Close"
                                                    class="btn btn-default btn-block waves-effect waves-light"
                                                    data-dismiss="modal">決定
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div :class="['col-12', 'col-md-8', isSearchOpen ? 'd-none' : 'd-block']" style="margin-top: -94px;">

                <div class="row">
                    <div class="col-12 d-block d-md-none mb-4"><a
                        class="btn btn-default search-toggle m-0 waves-effect waves-light" @click="openSearchMenu" href="javascript:void(0);">検索条件</a>
                    </div>
                </div>
                <div class="d-flex align-items-center my-4">
                    <div class="mr-auto"><p class="font-middle mb-0"><span class="green-text">{{ opportunities.length }}件</span>ヒットしました
                    </p></div>
                    <div class="mb-n3">
                        <div v-dropdown="{modelValue: '', listType: 'options'}" @selected="change_options"></div>
                    </div>
                </div>
                <div class="row d-none d-md-block">
                    <div class="col-12">
                        <table class="table white d-less-than-md-none">
                            <thead class="bg-grey-6 white-text">
                                <tr>
                                    <th class="vertical-middle">案件</th>
                                    <th class="vertical-middle text-center">応募期限 / ステータス</th>
                                    <th class="vertical-middle text-center">メモ</th>
                                    <th class="vertical-middle text-center">更新 / 登録</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in paginatedSortedOpps" :key="index">
                                    <td class="th-lg vertical-middle">
                                        <a class="d-block manage-index-title" :href="'/opportunities/' + item.id + '/manage/edit'">{{ item.subject }}</a>
                                        <div class="d-flex flex-wrap align-items-center">
                                            <div>
                                                <div class="mr-4" style="display: flex; align-items: center;">
                                                    <i class="material-icons vertical-bottom font-extralarge font-weight-bold custom-grey-5-text mr-2">remove_red_eye</i>
                                                    <span>{{ item.view_count }}</span>
                                                </div>
                                                <div class="d-inline-block mr-4">
                                                    <span class="custom-grey-5-text font-small font-weight-bold mr-2">応募受領</span>
                                                    <span>{{ item.apply_number }}</span>
                                                </div>
                                                <div class="d-inline-block">
                                                    <span class="custom-grey-5-text font-small font-weight-bold mr-2">スカウト</span>
                                                    <span>{{ item.scout_number }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="th-lg vertical-middle text-center">
                                        <span>{{ item.expired_at }}</span><br>
                                        <div class="d-inline-block badge-pill font-small bg-grey-5">
                                            <span v-if="item.status === '0'">案件終了</span>
                                            <span v-else-if="isExpired(item.expired_at)">応募期間終了</span>
                                            <span v-else>募集中</span>
                                        </div>
                                    </td>
                                    <td class="th-extrasm vertical-middle text-center">
                                        <a class="mdb-modal-form" :data-target="'#modal-' + item.id" data-toggle="modal" href="">
                                            <i class="material-icons font-extralarge">sticky_note_2</i>
                                        </a>
                                    </td>
                                    <td class="th-lg vertical-middle text-center">
                                        {{ item.updated_at }}<br>
                                        <div class="font-small">（{{ item.created_at }}）</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- Dynamic Memo Modals for Desktop -->
                        <div v-for="(item, index) in paginatedSortedOpps" :key="'modal-' + item.id" :aria-hidden="true" :aria-labelledby="'modal-' + item.id + '_modal'" class="modal" :id="'modal-' + item.id" role="dialog" tabindex="-1">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title w-100">メモを保存</h4>
                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                            <span aria-hidden="true">
                                                <i class="material-icons md-dark mb-36">clear</i>
                                            </span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form class="edit_opportunity" :id="'edit_opportunity_' + item.id" action="manage_update_memo_opportunities_path" accept-charset="UTF-8" data-remote="true" method="post">
                                            <input name="utf8" type="hidden" autocomplete="off" value="✓">
                                            <input type="hidden" name="_method" autocomplete="off" value="patch">
                                            <div class="input_memo_field">
                                                <div class="mb-4">
                                                    <div class="mx-auto mb-5">
                                                        <div class="">
                                                            <label class="font-middle mb-3" for="">社内メモ</label>
                                                        </div>
                                                        <textarea rows="5" class="form-control" autocomplete="off" :id="'memo_field_' + item.id" name="opportunity[memo]" v-model="item.memo"></textarea>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <button class="btn btn-default btn-lg font-middle memo-btn memo_save_btn waves-effect waves-light" data-dismiss="modal" :id="item.id" type="button">保存</button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-md-none">
                    <div class="d-flex" v-for="(item, index) in opportunities" :key="index">
                        <div class="card mb-4 w-100 hoverable p-3">
                            <div class="card-header d-block bg-white border-bottom p-0" style="height: 3em;"><h5
                                class="mb-1 font-middle pr-3"><a :href="'/opportunities/' + item.id + '/manage/edit'">{{ item.subject }}</a></h5>
                                <div class="d-flex align-items-center">
                                    <div class="text-dark">
                                        <div class="    mr-4" style="display: flex; align-items: center;">
                                            <i class="material-icons vertical-bottom font-extralarge font-weight-bold custom-grey-5-text mr-2">remove_red_eye</i>
                                            <span>{{ item.view_count }}</span>
                                        </div>
                                        <div class="d-inline-block mr-4">
                                            <span class="custom-grey-5-text font-small font-weight-bold mr-2">応募受領</span>
                                            <span>{{ item.apply_number }}</span>
                                        </div>
                                        <div class="d-inline-block">
                                            <span class="custom-grey-5-text font-small font-weight-bold mr-2">スカウト</span>
                                            <span>{{ item.scout_number }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-content opportunity_cell pt-3 px-0 pb-0" style="height: 8em;">
                                <div class="d-flex align-items-center mb-2"><label
                                    class="custom-grey-text font-small font-weight-bold mb-0 mr-2">応募期限 /
                                    ステータス</label>
                                    <div>
                                        <div class="d-inline-block mr-2">{{ item.expired_at }}</div>
                                        <div class="d-inline-block badge-pill font-small grey">
                                            <span v-if="item.status === 'close'">案件終了</span>
                                            <span v-else-if="isExpired(item.expired_at)">応募期間終了</span>
                                            <span v-else>募集中</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-2 mt-1"><label
                                    class="custom-grey-text font-small font-weight-bold mb-0 mr-2">メモ</label>
                                    <a class="mdb-modal-form" :data-target="'#modal-mobile-' + item.id" data-toggle="modal" href="">
                                            <i class="material-icons font-extralarge">sticky_note_2</i>
                                        </a></div>
                                <div class="d-flex align-items-center mt-1"><label
                                    class="custom-grey-text font-small font-weight-bold mb-0 mr-2">更新（登録）</label><span>{{item.updated_at || '＿'}}</span><span>（{{item.created_at}}）</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Dynamic Memo Modals for Mobile -->
                    <div v-for="(item, index) in opportunities" :key="'modal-mobile-' + item.id" :aria-hidden="true" :aria-labelledby="'modal-mobile-' + item.id + '_modal'" class="modal" :id="'modal-mobile-' + item.id" role="dialog" tabindex="-1">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title w-100">メモを保存</h4>
                                    <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                        <span aria-hidden="true">
                                            <i class="material-icons md-dark mb-36">clear</i>
                                        </span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <form class="edit_opportunity" :id="'edit_opportunity_mobile_' + item.id" action="manage_update_memo_opportunities_path" accept-charset="UTF-8" data-remote="true" method="post">
                                        <input name="utf8" type="hidden" autocomplete="off" value="✓">
                                        <input type="hidden" name="_method" autocomplete="off" value="patch">
                                        <div class="input_memo_field">
                                            <div class="mb-4">
                                                <div class="mx-auto mb-5">
                                                    <div class="">
                                                        <label class="font-middle mb-3" for="">社内メモ</label>
                                                    </div>
                                                    <textarea rows="5" class="form-control" autocomplete="off" :id="'memo_field_mobile_' + item.id" name="opportunity[memo]" v-model="item.memo"></textarea>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <button class="btn btn-default btn-lg font-middle memo-btn memo_save_btn waves-effect waves-light" data-dismiss="modal" :id="'mobile_' + item.id" type="button">保存</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Pagination -->
                <nav aria-label="pagination example">
                    <nav role="navigation" class="pagination">
                        <ul class="pagination pageul">
                            <li :class="['page-item', { disabled: currentPage === 1 }]">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)">← 前</a>
                            </li>

                            <li v-for="page in totalPages" :key="page" :class="['page-item', { active: currentPage === page }]">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                            </li>

                            <li :class="['page-item', { disabled: currentPage === totalPages }]">
                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)">次 →</a>
                            </li>
                        </ul>
                    </nav>
                </nav>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/opportunities/manage/index.css">
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css">
<link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/data_tabs.css">
    `,
    data() {
        return {
            UserId: "",
            isChecked: false,
            opportunities: [],
            filteredOpps: [], // Dữ liệu đã lọc
            sortType: "",
            date_start: "",
            date_end: "",
            free_keyword: "",
            optionPeriodKinds: "",
            status: [],
            match_status: "",
            public_status_id: [],
            currentPage: 1,
            itemsPerPage: 20,
            isSearchOpen: false,
            isMobile: window.innerWidth < 768
        }
    },

    computed: {
        totalPages() {
            return Math.ceil(this.filteredOpps.length / this.itemsPerPage);
        },

        sortedOpps() {
            const sortKey = this.sortMapping[this.sortValue] || "updated_at"; // Giá trị mặc định
            return [...this.filteredOpps].sort((a, b) => {
                const valA = sortKey.includes("_at") ? new Date(a[sortKey]) : a[sortKey] || 0;
                const valB = sortKey.includes("_at") ? new Date(b[sortKey]) : b[sortKey] || 0;
                return valB - valA; // Sắp xếp giảm dần (mới nhất trước)
            });
        },

        paginatedSortedOpps() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            return this.filteredOpps.slice(start, start + this.itemsPerPage);
        }
    },

    methods:{
        navigateToResumes() {
            window.location.href = '/resumes/manage/index';
        },

        async opp_index(sortType) {
            try{
                const response = await fetch(`/api/opp_index?user_id=${this.UserId}&&sort_type=${sortType}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': odoo.csrf_token},
                });
                const data = await response.json();
                if (data.success) {
                    console.log("API Response:", data.data); // Debug API response
                    this.opportunities = data.data;
                    this.filteredOpps = [...this.opportunities];
                    this.currentPage = 1; // Reset current page khi có dữ liệu mới
                } else {
                    console.warn("API data is not in the correct format:", data);
                }
            }catch (error) {
                console.log('Error API:', error.message);
                this.errorMessage = error.message;
            }
        },
        changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
              this.currentPage = page;
            }
        },
        async opp_search() {
            try {
                let params = new URLSearchParams();

                // Các tham số đơn giản
                params.append("opportunity_manage_condition[period_kinds]", this.optionPeriodKinds || '');
                params.append("opportunity_manage_condition[date_start]", this.date_start || '');
                params.append("opportunity_manage_condition[date_end]", this.date_end || '');
                params.append("opportunity_manage_condition[free_keyword]", this.free_keyword || '');
                params.append("opportunity_manage_condition[match_status]", this.match_status || '');

                // Xử lý mảng status[]
                if (this.status.length > 0) {
                    this.status.forEach(value => {
                        params.append("opportunity_manage_condition[status][]", value);
                    });
                }

                // Xử lý mảng public_status_id[]
                if (this.public_status_id.length > 0) {
                    this.public_status_id.forEach(value => {
                        params.append("opportunity_manage_condition[public_status_id][]", value);
                    });
                }

                // Thêm user_id để filter theo user (tương tự như opp_index)
                if (this.UserId) {
                    params.append("user_id", this.UserId);
                }

                // Gọi API search thay vì redirect
                console.log("Search URL:", `/api/opportunity_manage_condition/search?${params.toString()}`);
                const response = await fetch(`/api/opportunity_manage_condition/search?${params.toString()}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                console.log("Response status:", response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("Search API response:", data); // Debug response

                if (data.results && Array.isArray(data.results)) {
                    // Cập nhật dữ liệu với kết quả search
                    console.log("Updating opportunities with:", data.results);
                    this.opportunities = [...data.results]; // Force reactivity
                    this.filteredOpps = [...data.results]; // Force reactivity
                    this.currentPage = 1; // Reset về trang đầu

                    // Force Vue to re-render
                    this.$forceUpdate();

                    // Đóng search menu trên mobile sau khi search
                    if (this.isMobile && this.isSearchOpen) {
                        this.closeSearchMenu();
                    }

                    console.log("Search completed:", data.results.length, "results found");
                    console.log("Current opportunities:", this.opportunities);
                    window.toastr.success(`${data.results.length}件の結果が見つかりました。`);
                } else {
                    console.warn("API search response format error:", data);
                    window.toastr.error('検索に失敗しました。');
                }
            } catch (error) {
                console.error('Search error:', error);
                window.toastr.error('検索中にエラーが発生しました。');
            }
        },
        change_options(event) {
            this.sortType = event.detail;
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.set("sort_type", this.sortType);
            window.location.href = newUrl.toString();

            // Gọi API lấy dữ liệu mới
            this.opp_index(this.sortType);
          },
          restoreDropdownSelection() {
            const urlParams = new URLSearchParams(window.location.search);
            const sortTypeEN = urlParams.get("sort_type");  // Lấy giá trị EN từ URL
            // Chuyển đổi giá trị EN → JP (hiển thị trong dropdown)
            this.sortType = Object.keys(optionsMappings.options).find(
                key => optionsMappings.options[key] === sortTypeEN
            ) || "更新日";  // Mặc định nếu không tìm thấy
            // Gửi sự kiện để directive cập nhật dropdown
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: this.sortType }));
        },
        handleSelection(event) {
            this.optionPeriodKinds = event.detail; // Nhận giá trị từ dropdown
        },
        openSearchMenu() {
            this.isSearchOpen = true;
            const footer = document.querySelector('footer');
            if (footer) {
            footer.classList.add('d-none');
            }
        },
        closeSearchMenu() {
            this.isSearchOpen = false;
            const footer = document.querySelector('footer');
            if (footer) {
            footer.classList.remove('d-none');
            }
        },
        handleResize() {
            this.isMobile = window.innerWidth < 768;
            // Nếu resize lên desktop thì tự đóng menu
            if (!this.isMobile && this.isSearchOpen) {
              this.closeSearchMenu();
            }
        },
        isExpired(expiredAt) {
            if (!expiredAt) return false;
            const today = new Date();
            // Convert YYYY/MM/DD format to Date object
            const expiredDate = new Date(expiredAt.replace(/\//g, '-'));
            return expiredDate < today;
        },
        debugStatus(item) {
            console.log(`Debug status for ${item.subject}:`, {
                status: item.status,
                expired_at: item.expired_at,
                isExpired: this.isExpired(item.expired_at)
            });
        },

        async saveMemo(opportunityId, memo) {
            try {
                const response = await fetch('/api/opp_memo_update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': odoo.csrf_token
                    },
                    body: JSON.stringify({
                        opportunity_id: opportunityId,
                        memo: memo
                    })
                });

                const result = await response.json();
                if (result.success) {
                    // Update the memo in the local data
                    const opportunity = this.opportunities.find(opp => opp.id == opportunityId);
                    if (opportunity) {
                        opportunity.memo = memo;
                    }

                    // Update the filtered data as well
                    const filteredOpp = this.filteredOpps.find(opp => opp.id == opportunityId);
                    if (filteredOpp) {
                        filteredOpp.memo = memo;
                    }

                    window.toastr.success('メモが保存されました。');
                } else {
                    window.toastr.error('メモの保存に失敗しました。');
                }
            } catch (error) {
                console.error('Error saving memo:', error);
                window.toastr.error('メモの保存に失敗しました。');
            }
        }
    },
    mounted(){
        const urlParams = new URLSearchParams(window.location.search);
        const sortTypeFromURL = urlParams.get("sort_type") || "updated_at"; // Nếu không có thì mặc định là updated_at
        this.UserId = userInfo ? userInfo.user_id : null;
        // Cập nhật sortType và gọi API với giá trị lấy từ URL
        this.sortType = sortTypeFromURL;
        this.opp_index(this.sortType);
        this.restoreDropdownSelection();
        window.addEventListener('resize', this.handleResize);

        // Add event listener for backdrop click to close search modal
        document.addEventListener('click', (event) => {
            const sideSearch = document.getElementById('side-search');
            if (sideSearch && sideSearch.classList.contains('active')) {
                // Check if click is on backdrop (not on the card content)
                if (event.target === sideSearch) {
                    this.closeSearchMenu();
                }
            }
        });

        // Add event listener for memo save buttons
        this.$nextTick(() => {
            document.addEventListener('click', (event) => {
                if (event.target.classList.contains('memo_save_btn')) {
                    event.preventDefault(); // Ngăn chặn form submit
                    event.stopPropagation(); // Ngăn chặn event bubbling

                    console.log('Memo save button clicked:', event.target.id);
                    const opportunityId = event.target.id.replace('mobile_', ''); // Remove mobile_ prefix if exists
                    const memoField = document.getElementById(`memo_field_${opportunityId}`) ||
                                    document.getElementById(`memo_field_mobile_${opportunityId}`);

                    console.log('Memo field found:', memoField);
                    if (memoField) {
                        const memo = memoField.value;
                        console.log('Saving memo:', memo, 'for opportunity:', opportunityId);
                        this.saveMemo(opportunityId, memo);
                    }
                }
            });
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
    }
}

export default Index;