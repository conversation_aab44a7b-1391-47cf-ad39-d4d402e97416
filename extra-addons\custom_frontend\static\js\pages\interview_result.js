const InterviewResult = {
    template: `
        <main class="margin-header sp_fluid" id="vue-app" data-v-app>


            <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                <div class="card px-3 px-md-4 mt-2 pt-5 form-card sp_sides_uniter">
                    <!-- Checkbox section -->
                    <div class="d-flex checkbox-container">
                        <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field0" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="subc">
                            <label id="contract_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="contract_types_field0">人財スカウト</label>
                        </div>
                        <div class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field1" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="subc">
                            <label id="contract_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="contract_types_field1">案件ヒアリング</label>
                        </div>
                    </div>
                    <!-- Info section -->
                    <div class="info-container">
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">案件名:</span>
                                <a class="info-value">xxxxx</a>
                            </div>
                            <div class="info-item">
                                <span class="info-label">人財名:</span>
                                <a class="info-value">xxxxx</a>
                            </div>
                        </div>
                    </div>

                    <!-- Radio buttons -->
                    <div class="d-flex mb-5">
                        <div class="form-check">
                            <input class="form-check-input" id="result_interview0" required="required" type="radio" value="false" name="result" checked @click="ResultInterviewChange">
                            <label id="result_interview_label0" class="form-check-label" for="result_interview0" required="required" >OK</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" id="result_interview1" required="required" type="radio" value="true" name="result" @click="ResultInterviewChange">
                            <label id="result_interview_label1" class="form-check-label" for="result_interview1" required="required">NG</label>
                        </div>
                    </div>

                    <!-- Input section -->
                    <div class="input-section mb-5">
                        <div class="d-flex flex-column calender-section">
                            <span class="date-label">要員ロック期限 (任意) </span>
                            <div class="mb-3 d-flex align-items-center">
                                <input class="form-control picker__input"
                                    autocomplete="off"
                                    v-pickadate
                                    id="contract_startdate_at_field"
                                    type="text"
                                    name="opportunity_search_condition[contract_startdate_at]"
                                    readonly="" aria-haspopup="true"
                                    aria-expanded="false"
                                    aria-readonly="false"
                                    aria-owns="contract_startdate_at_field_root">

                                <div class="col-1 p-0">
                                    <i class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                </div>
                            </div>
                        </div>

                        <div class="textarea-container" id="textareaContainer" v-show="isRejected">
                            <textarea id="reject_reason" rows="5" class="form-control"></textarea>
                            <div class="d-flex justify-content-between">
                                <CharacterCounter targetId="reject_reason" :max-length="2000" />
                                <div class="text-info d-flex justify-content-end">
                                    <span class="sample-usage">サンプルを使用</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Button -->
                    <div class="d-flex justify-content-center">
                        <button class="btn mb-5 font-middle white-text btn-default btn-lg width-200 sp-width-150 px-0 submit_change_send_to waves-effect waves-light">登録する</button>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="custom_frontend/static/css/interview_result.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            isRejected: false,
        }
    },
    methods: {
        ResultInterviewChange(event) {
            if (event.target.value === 'true') {
                this.isRejected = true;
            } else {
                this.isRejected = false;
            }
        }
    },
}

export default InterviewResult;