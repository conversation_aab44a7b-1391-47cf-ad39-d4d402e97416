import logging
import json
import os
from datetime import datetime

class SecurityLogger:
    def __init__(self):
        try:
            # <PERSON><PERSON><PERSON> thư mục log nếu chưa có
            log_dir = '/var/log/odoo'
            print(f"Checking log directory: {log_dir}")
            
            if not os.path.exists(log_dir):
                print(f"Production log dir not found, using development path")
                # Fallback cho local development - sử dụng đường dẫn tuyệt đối
                current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                log_dir = os.path.join(current_dir, 'logs')
                print(f"Development log directory will be: {log_dir}")
                
                try:
                    os.makedirs(log_dir, exist_ok=True)
                    print(f"Created log directory: {log_dir}")
                except Exception as e:
                    print(f"Error creating log directory: {str(e)}")
            
            # Tạo dedicated logger cho security events
            self.security_logger = logging.getLogger('security')
            
            # Tr<PERSON>h duplicate handlers
            if not self.security_logger.handlers:
                log_file = os.path.join(log_dir, 'security.log')
                print(f"Creating log file: {log_file}")
                
                try:
                    security_handler = logging.FileHandler(log_file)
                    security_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                    security_handler.setFormatter(security_formatter)
                    self.security_logger.addHandler(security_handler)
                    self.security_logger.setLevel(logging.INFO)
                    print(f"Successfully initialized logger with file: {log_file}")
                except Exception as e:
                    print(f"Error creating log file: {str(e)}")
            
            # Log thông tin khởi tạo
            self.security_logger.info(f"Security logger initialized. Log directory: {log_dir}")
            
        except Exception as e:
            print(f"Critical error in SecurityLogger initialization: {str(e)}")
            raise
    
    def log_antivirus_scan(self, filename, result, user_id=None, file_size=None):
        """Log antivirus scan results"""
        try:
            log_data = {
                'event': 'antivirus_scan',
                'filename': filename,
                'result': result,
                'user_id': user_id,
                'file_size': file_size,
                'timestamp': datetime.now().isoformat()
            }
            self.security_logger.info(json.dumps(log_data))
            print(f"Logged antivirus scan: {filename}")
        except Exception as e:
            print(f"Error logging antivirus scan: {str(e)}")
    
    def log_file_upload(self, filename, size, user_id=None, success=True, reason=None):
        """Log file upload attempts"""
        try:
            log_data = {
                'event': 'file_upload',
                'filename': filename,
                'size': size,
                'user_id': user_id,
                'success': success,
                'reason': reason,
                'timestamp': datetime.now().isoformat()
            }
            self.security_logger.info(json.dumps(log_data))
            print(f"Logged file upload: {filename}")
        except Exception as e:
            print(f"Error logging file upload: {str(e)}")
    
    def log_security_violation(self, event_type, details, user_id=None):
        """Log security violations"""
        try:
            log_data = {
                'event': 'security_violation',
                'type': event_type,
                'details': details,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat()
            }
            self.security_logger.warning(json.dumps(log_data))
            print(f"Logged security violation: {event_type}")
        except Exception as e:
            print(f"Error logging security violation: {str(e)}")

# Singleton instance
security_logger = SecurityLogger()
