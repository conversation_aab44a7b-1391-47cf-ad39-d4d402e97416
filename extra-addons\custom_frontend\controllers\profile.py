from odoo import http
from odoo.http import request
import secrets
from datetime import datetime, timedelta
import os
import logging
import requests
from .login import LoginController
from .common import send_email_from_template
import hashlib
import base64
_logger = logging.getLogger(__name__)

class Profile(http.Controller):
    @http.route('/api/request_change_mail', type='json', auth="public", methods=['POST'])
    def request_change_email(self):
        data = request.httprequest.get_json(silent=True)
        email_old = data.get('email')
        email_new = data.get('new_email')

        if not email_new or not email_old:
            return {'success': False, 'message': 'メールアドレスが入力されていません。'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email_old)], limit=1)
        check_mail = request.env['vit.users'].with_context(active_test=False).sudo().search_count([('email', '=', email_new)])
        if not user:
            return {'success': False, 'message': 'ユーザーが見つかりません。'}
        if check_mail > 0:
            return {'success': False, 'message': 'このメールアドレスは既に使用されています。'}
        reset_token = secrets.token_urlsafe(32)
        expiry_time = datetime.now() + timedelta(minutes=15)

        user.sudo().write({
            'token': reset_token,
            'token_expiry': expiry_time,
        })

        reset_link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}users/profile/temp_change_mail?token={reset_token}&email={email_new}"

        context_data = {
            'company_name': user.company_id.name if user.company_id else '',
            'user_name': user.username,
            'reset_link': reset_link,
            'user_email': email_new
        }
        
        result = send_email_from_template('Profile: Change Email', context_data)
        
        if not result['success']:
            return {'success': False, 'message': result['message']}

        return {'success': True, 'message': 'メールアドレス変更リンクが送信されました。メールをご確認ください。'}

    @http.route('/api/change_mail', type='json', auth="public", methods=['POST'], csrf=False)
    def change_email(self):
        data = request.httprequest.get_json(silent=True)
        email = data.get('email')
        token = data.get('token')

        if not email or not token:
            return {'success': False, 'message': 'Missing email or token'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('token', '=', token)], limit=1)

        if not user:
            return {'success': False, 'message': 'Invalid token'}

        if user.token_expiry < datetime.now():
            return {'success': False, 'message': 'Token expired'}


        user.sudo().write({
            'email': email,
            'token': False,
            'token_expiry': False,
        })

        user_new = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email)], limit=1)
        if not user_new:
            return {'success': False, 'message': 'User not found'}
        return {
            'success': True,
            'message': 'Login successful',
            'userid': user_new.id,
            'username': user_new.username,
            'role': user_new.role,
            'token': None,
            'isloggedin': user_new.id,
            'company_id': user_new.company_id.id,
            'active': user_new.active,
            'email': user_new.email,
        }

    @http.route('/api/change_pass', type='json', auth="public", methods=['POST'])
    def change_pass(self):
        data = request.httprequest.get_json(silent=True)
        old_password = data.get('old_pass')
        new_password = data.get('new_pass')
        email = data.get('email')

        if not old_password or not new_password:
            return {'success': False, 'message': 'Missing old or new password'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email)], limit=1)
        if not user:
            return {'success': False, 'message': 'User not found'}

        current_password = user.password
        if not self._verify_password(old_password, current_password):
            return {'success': False, 'message': 'Current password is incorrect'}

        hashed_password = self._hash_password(new_password)
        user.sudo().write({
            'password': hashed_password,
        })

        return {'success': True, 'message': 'Password changed successfully'}

    @http.route('/api/check_old_pass', type='json', auth="public", methods=['POST'])
    def check_old_pass(self):
        data = request.httprequest.get_json(silent=True)
        old_password = data.get('old_pass')
        email = data.get('email')

        if not old_password:
            return {'success': False, 'message': 'Missing old password'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email)], limit=1)
        if not user:
            return {'success': False, 'message': 'User not found'}

        current_password = user.password
        if not self._verify_password(old_password, current_password):
            return {'success': False, 'message': 'Password is incorrect'}

        return {'success': True, 'message': 'Password is correct'}

    @http.route('/api/get_profile', type='json', auth="public", methods=['POST'])
    def get_profile(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get('user_id')

        if not user_id:
            return {'success': False, 'message': 'Missing user ID'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('id', '=', user_id)], limit=1)

        if not user:
            return {'success': False, 'message': 'User not found'}

        return {
            'success': True,
            'username': user.username,
            'phone': user.phone,
        }

    @http.route('/api/update_profile', type='json', auth="public", methods=['POST'])
    def update_profile(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get('user_id')
        username = data.get('username')
        phone = data.get('phone')

        if not user_id:
            return {'success': False, 'message': 'Missing user ID'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('id', '=', user_id)], limit=1)

        if not user:
            return {'success': False, 'message': 'User not found'}

        user.sudo().write({
            'username': username,
            'phone': phone,
        })

        return {'success': True, 'message': 'Profile updated successfully'}

    @http.route('/api/get_user_info', type='json', auth="public", methods=['POST'])
    def get_user_info(self):
        data = request.httprequest.get_json(silent=True)
        user_id = data.get('user_id')

        if not user_id:
            return {'success': False, 'message': 'Missing user ID'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('id', '=', user_id)], limit=1)

        if not user:
            return {'success': False, 'message': 'User not found'}

        return {
            'success': True,
            'username': user.username,
            'phone': user.phone,
            'company_name': user.company_id.name,
            'refer_number': user.company_id.referral_code,
            'email': user.email
        }

    def _hash_password(self, password, salt=None):
        #Sử dụng PBKDF2 để băm mật khẩu - không cần thư viện ngoài
        if not salt:
            # Tạo salt 16 bytes
            salt = secrets.token_bytes(16)
        # Sử dụng PBKDF2 với thuật toán SHA-256, 100000 vòng lặp
        # và độ dài key đầu ra 32 bytes
        key = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000,  # số vòng lặp, càng cao càng an toàn nhưng càng chậm
            dklen=32  # độ dài key
        )

        # Mã hóa salt và key để có thể lưu vào DB
        salt_b64 = base64.b64encode(salt).decode('utf-8')
        key_b64 = base64.b64encode(key).decode('utf-8')

        # Trả về chuỗi có định dạng: algorithm$iterations$salt$key
        password_hash = f"pbkdf2_sha256$100000${salt_b64}${key_b64}"
        return password_hash

    def _verify_password(self, password, stored_password_hash):
        try:
            # Tách các thành phần từ chuỗi đã lưu
            algorithm, iterations, salt_b64, key_b64 = stored_password_hash.split('$')

            # Giải mã salt từ base64
            salt = base64.b64decode(salt_b64)
            # Tạo lại key từ mật khẩu nhập vào và salt đã lưu
            key = hashlib.pbkdf2_hmac(
                'sha256',
                password.encode('utf-8'),
                salt,
                int(iterations),
                dklen=32
            )
            # So sánh key tính toán với key đã lưu
            calculated_key_b64 = base64.b64encode(key).decode('utf-8')
            return calculated_key_b64 == key_b64
        except Exception:
            return False