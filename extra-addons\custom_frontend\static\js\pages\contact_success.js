import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const ContactSuccess = {
	'template': `
<main class="pb-3 margin-header" id="vue-app" data-v-app="">
    ${createBreadcrumb([
        { text: '利用方法', link: null },
        { text: 'お問い合わせ', link: '/contact_new' },
        { text: '問い合わせ完了', link: null, current: true }
    ])}
    <div class="container-fluid">
        <div class="mail-settings-container">
            <!-- Settings form -->
            <h3 style="text-align: center; padding-top: 3vw;">お問い合わせ送信完了</h3>
            <h3 style="text-align: center;">お問い合わせが正常に送信されました。</h3>

            <div class="text-center" style="padding-top: 8vw;">
                <button class="btn btn-default waves-effect waves-light" style="color: white;"
                    @click="goToHome">ホームページへ</button>
            </div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/contactsuccess.css" />
	`,
    data() {
        return {
            newProjectMail: null,
            newPersonnelMail: null,
            type: this.$route.params.type
        }
    },
    methods: {
        goToHome() {
            window.location.href = '/home';
        }
    }
}

export default ContactSuccess