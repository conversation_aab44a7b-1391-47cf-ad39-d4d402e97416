/**
 * Date Helper Utilities
 * Xử lý chuyển đổi ngày tháng giữa format Nhật Bản và JavaScript Date
 * Tránh lỗi Safari iPhone với Japanese date format
 */

/**
 * Chuyển đổi Japanese date format thành JavaScript Date object
 * @param {string} japaneseDate - Date string in format "2025年10月20日"
 * @returns {Date|null} JavaScript Date object hoặc null nếu invalid
 */
export function parseJapaneseDate(japaneseDate) {
    if (!japaneseDate || typeof japaneseDate !== 'string') {
        return null;
    }

    // Remove whitespace
    const cleanDate = japaneseDate.trim();
    
    // Convert Japanese format to ISO format
    // 2025年10月20日 -> 2025/10/20 (not 2025/10/20/)
    const isoDateString = cleanDate
        .replace(/年/g, '/')
        .replace(/月/g, '/')
        .replace(/日/g, ''); // Remove 日 completely to avoid trailing slash

    // Create Date object
    const date = new Date(isoDateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
        console.warn(`Invalid Japanese date format: ${japaneseDate}`);
        return null;
    }
    
    return date;
}

/**
 * Chuyển đổi JavaScript Date object thành Japanese date format
 * @param {Date} date - JavaScript Date object
 * @returns {string} Date string in format "2025年10月20日"
 */
export function formatToJapaneseDate(date) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        return "無効な日付";
    }

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    return `${year}年${month}月${day}日`;
}

/**
 * Chuyển đổi Japanese date format thành ISO format (YYYY-MM-DD)
 * @param {string} japaneseDate - Date string in format "2025年10月20日"
 * @returns {string|null} ISO date string hoặc null nếu invalid
 */
export function japaneseToISODate(japaneseDate) {
    if (!japaneseDate || typeof japaneseDate !== 'string') {
        return null;
    }

    // Extract year, month, day using regex
    const matches = japaneseDate.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
    if (!matches) {
        console.warn(`Invalid Japanese date format: ${japaneseDate}`);
        return null;
    }

    const [_, year, month, day] = matches;
    
    // Pad month and day with leading zeros
    const paddedMonth = month.padStart(2, '0');
    const paddedDay = day.padStart(2, '0');
    
    return `${year}-${paddedMonth}-${paddedDay}`;
}

/**
 * Validate Japanese date format
 * @param {string} japaneseDate - Date string to validate
 * @returns {boolean} True if valid Japanese date format
 */
export function isValidJapaneseDate(japaneseDate) {
    if (!japaneseDate || typeof japaneseDate !== 'string') {
        return false;
    }

    // Check format with regex
    const formatRegex = /^\d{4}年\d{1,2}月\d{1,2}日$/;
    if (!formatRegex.test(japaneseDate.trim())) {
        return false;
    }

    // Try to parse and validate
    const date = parseJapaneseDate(japaneseDate);
    return date !== null;
}

/**
 * Compare two Japanese dates
 * @param {string} date1 - First Japanese date
 * @param {string} date2 - Second Japanese date
 * @returns {number} -1 if date1 < date2, 0 if equal, 1 if date1 > date2, NaN if invalid
 */
export function compareJapaneseDates(date1, date2) {
    const d1 = parseJapaneseDate(date1);
    const d2 = parseJapaneseDate(date2);
    
    if (!d1 || !d2) {
        return NaN;
    }
    
    if (d1 < d2) return -1;
    if (d1 > d2) return 1;
    return 0;
}

/**
 * Check if Japanese date is in the future
 * @param {string} japaneseDate - Japanese date to check
 * @returns {boolean} True if date is in the future
 */
export function isJapaneseDateInFuture(japaneseDate) {
    const date = parseJapaneseDate(japaneseDate);
    if (!date) return false;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return date > today;
}

/**
 * Examples:
 * 
 * // Parse Japanese date
 * const date = parseJapaneseDate('2025年10月20日');
 * console.log(date); // Date object
 * 
 * // Format to Japanese
 * const japaneseStr = formatToJapaneseDate(new Date());
 * console.log(japaneseStr); // "2025年1月15日"
 * 
 * // Convert to ISO
 * const isoStr = japaneseToISODate('2025年10月20日');
 * console.log(isoStr); // "2025-10-20"
 * 
 * // Validate
 * const isValid = isValidJapaneseDate('2025年10月20日');
 * console.log(isValid); // true
 */
