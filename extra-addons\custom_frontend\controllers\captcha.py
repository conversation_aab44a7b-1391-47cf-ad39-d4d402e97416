from odoo import http
from odoo.http import request, Response
import json
import logging

from ..utils.simple_captcha import simple_captcha
from ..utils.brute_force_protection import brute_force_protection

_logger = logging.getLogger(__name__)

class CaptchaController(http.Controller):

    @http.route('/api/captcha/generate', type='json', auth='public', methods=['POST'])
    def generate_captcha(self):
        """Generate CAPTCHA challenge"""
        try:
            data = request.httprequest.get_json(silent=True) or {}
            email = data.get('email')
            
            # Get session ID
            session_id = request.session.sid or 'anonymous'
            
            # Generate challenge
            challenge_text, challenge_id = simple_captcha.generate_challenge(session_id)
            
            # Log CAPTCHA generation
            _logger.info(f"CAPTCHA generated for email: {email}, session: {session_id}")
            
            return {
                'success': True,
                'challenge_text': challenge_text,
                'challenge_id': challenge_id,
                'message': 'CAPTCHA challenge generated'
            }
            
        except Exception as e:
            _logger.error(f"Error generating CAPTCHA: {str(e)}")
            return {
                'success': False,
                'error': 'Failed to generate CAPTCHA',
                'message': 'CA<PERSON><PERSON><PERSON>の生成に失敗しました。'
            }

    @http.route('/api/captcha/verify', type='json', auth='public', methods=['POST'])
    def verify_captcha(self):
        """Verify CAPTCHA challenge"""
        try:
            data = request.httprequest.get_json(silent=True) or {}
            
            challenge_id = data.get('challenge_id')
            user_answer = data.get('answer')
            email = data.get('email')
            
            if not challenge_id or not user_answer:
                return {
                    'success': False,
                    'error': 'Missing challenge ID or answer',
                    'message': 'チャレンジIDまたは回答が不足しています。'
                }
            
            # Get session ID
            session_id = request.session.sid or 'anonymous'
            
            # Verify challenge
            is_valid, error_message = simple_captcha.verify_challenge(
                session_id, challenge_id, user_answer
            )
            
            if is_valid:
                # Reset CAPTCHA requirement for this email
                if email:
                    brute_force_protection.captcha_required.discard(email)
                
                _logger.info(f"CAPTCHA verified successfully for email: {email}")
                
                return {
                    'success': True,
                    'message': 'CAPTCHA verified successfully'
                }
            else:
                _logger.warning(f"CAPTCHA verification failed for email: {email}, error: {error_message}")
                
                return {
                    'success': False,
                    'error': error_message,
                    'message': f'CAPTCHA認証に失敗しました: {error_message}'
                }
                
        except Exception as e:
            _logger.error(f"Error verifying CAPTCHA: {str(e)}")
            return {
                'success': False,
                'error': 'Failed to verify CAPTCHA',
                'message': 'CAPTCHA認証の処理に失敗しました。'
            }

    @http.route('/api/captcha/status', type='json', auth='public', methods=['POST'])
    def captcha_status(self):
        """Check if CAPTCHA is required for email"""
        try:
            data = request.httprequest.get_json(silent=True) or {}
            email = data.get('email')
            
            if not email:
                return {
                    'success': False,
                    'error': 'Email is required'
                }
            
            # Check if CAPTCHA is required
            requires_captcha = brute_force_protection.requires_captcha(email)
            
            # Get account status
            account_status = brute_force_protection.get_account_status(email)
            
            # Get session info
            session_id = request.session.sid or 'anonymous'
            has_active_challenge = simple_captcha.has_active_challenge(session_id)
            
            response = {
                'success': True,
                'requires_captcha': requires_captcha,
                'account_status': account_status,
                'has_active_challenge': has_active_challenge
            }
            
            # Add challenge info if exists
            if has_active_challenge:
                challenge_info = simple_captcha.get_challenge_info(session_id)
                if challenge_info:
                    response['challenge_info'] = challenge_info
            
            return response
            
        except Exception as e:
            _logger.error(f"Error checking CAPTCHA status: {str(e)}")
            return {
                'success': False,
                'error': 'Failed to check CAPTCHA status'
            }

    @http.route('/api/security/status', type='json', auth='public', methods=['GET'])
    def security_status(self):
        """Get overall security status (for admin monitoring)"""
        try:
            # Get statistics
            brute_force_stats = brute_force_protection.get_statistics()
            captcha_stats = simple_captcha.get_statistics()
            
            return {
                'success': True,
                'brute_force_protection': brute_force_stats,
                'captcha_system': captcha_stats,
                'timestamp': json.dumps(None, default=str)  # Current timestamp
            }
            
        except Exception as e:
            _logger.error(f"Error getting security status: {str(e)}")
            return {
                'success': False,
                'error': 'Failed to get security status'
            }
