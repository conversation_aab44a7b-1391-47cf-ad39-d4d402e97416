import re
import requests
import urllib.parse
from odoo import http
from odoo.http import request
import json

class ResumeDetailController(http.Controller):
    @http.route('/api/resume_detail', type='http', auth='public', methods=['GET'], csrf=False)
    def get_res(self, **kwargs):
        res_id = request.params.get('id')

        if not res_id or not str(res_id).isdigit():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Invalid or missing resume ID'}),
                headers={'Content-Type': 'application/json'}
            )

        resume = request.env['vit.partner'].sudo().browse(int(res_id))

        if not resume.exists():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Resume not found'}),
                headers={'Content-Type': 'application/json'}
            )

        def serialize_value(value):
            if isinstance(value, bytes):  # Nếu là bytes, chuy<PERSON><PERSON> sang string
                return value.decode('utf-8', errors='ignore')
            elif isinstance(value, (int, float, str, bool, type(None))):
                return value  # Nếu là kiểu dữ liệu JSON hợp lệ
            elif hasattr(value, 'strftime'):  # Nếu là datetime, chuyển thành chuỗi
                return value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, list):  # Nếu là list, xử lý từng phần tử
                return [serialize_value(v) for v in value]
            elif isinstance(value, dict):  # Nếu là dict, xử lý từng phần tử
                return {k: serialize_value(v) for k, v in value.items()}
            else:
                return str(value)  # Chuyển tất cả kiểu dữ liệu khác thành chuỗi

        # Chuyển đổi tất cả giá trị thành JSON-compatible
        data = {
            'partner_types': serialize_value(resume.partner_types),
            'resume_visibility': serialize_value(resume.resume_visibility),
            'status': serialize_value(resume.status),
            'company_publish': serialize_value(resume.company_publish),
            'human_publish': serialize_value(resume.human_publish), 
            'date_period' : serialize_value(resume.date_period),
            'user_id': serialize_value(resume.user_id),
            'cv_id': serialize_value(resume.cv_id),
            'opp_id': serialize_value(resume.opp_id),
            'user_name': serialize_value(resume.user_name),
            'initial_name': serialize_value(resume.initial_name),
            'gender': serialize_value(resume.gender),
            'birthday': serialize_value(resume.birthday),
            'nationality': serialize_value(resume.nationality),
            'contract_types': serialize_value(resume.contract_types),
            'resident': serialize_value(resume.resident),
            'categories_consultation': serialize_value(resume.categories_consultation),
            'categories_development': serialize_value(resume.categories_development),
            'categories_infrastructure': serialize_value(resume.categories_infrastructure),
            'categories_design': serialize_value(resume.categories_design),
            'experience_pr': serialize_value(resume.experience_pr),
            'qualification': serialize_value(resume.qualification),
            'characteristic': serialize_value(resume.characteristic),
            'utilization_rate': serialize_value(resume.utilization_rate),
            'unit_price_min': serialize_value(resume.unit_price_min),
            'unit_price_max': serialize_value(resume.unit_price_max),
            'region': serialize_value(resume.region),
            'working_frequency': serialize_value(resume.working_frequency),
            'working_location': serialize_value(resume.working_location),
            'working_hope': serialize_value(resume.working_hope),
            'company_settings': serialize_value(resume.company_settings),
            'created_at': serialize_value(resume.created_at),
            'created_by': serialize_value(resume.created_by.id),
            'updated_at': serialize_value(resume.updated_at),
            'updated_by': serialize_value(resume.updated_by),
            'company_id': serialize_value(resume.company_id.id),
        }

        return request.make_response(
            json.dumps({'success': True, 'data': data}),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/resume_prev_next', type='http', auth='public', methods=['GET'], csrf=False)
    def get_prev_next_res(self, **kwargs):
        res_id = request.params.get('id')

        if not res_id or not str(res_id).isdigit():
            return request.make_response(
                json.dumps({'success': False, 'message': 'Invalid or missing resume ID'}),
                headers={'Content-Type': 'application/json'}
            )

        def serialize_value(value):
            if isinstance(value, bytes):  # Nếu là bytes, chuyển sang string
                return value.decode('utf-8', errors='ignore')
            elif isinstance(value, (int, float, str, bool, type(None))):
                return value  # Nếu là kiểu dữ liệu JSON hợp lệ
            elif hasattr(value, 'strftime'):  # Nếu là datetime, chuyển thành chuỗi
                return value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, list):  # Nếu là list, xử lý từng phần tử
                return [serialize_value(v) for v in value]
            elif isinstance(value, dict):  # Nếu là dict, xử lý từng phần tử
                return {k: serialize_value(v) for k, v in value.items()}
            else:
                return str(value)  # Chuyển tất cả kiểu dữ liệu khác thành chuỗi

        prev_res = request.env['vit.partner'].sudo().search([('id', '<', res_id)], order='id desc', limit=1)      
        next_res = request.env['vit.partner'].sudo().search([('id', '>', res_id)], order='id asc', limit=1)

        data = {
            'prev': {
                'id': prev_res.id if prev_res.id else None,
                'initial_name': serialize_value(prev_res.initial_name),
                'gender': serialize_value(prev_res.gender),
                'birthday': serialize_value(prev_res.birthday),
                'experience_pr': serialize_value(prev_res.experience_pr),
                'working_hope': serialize_value(prev_res.working_hope),
            } if prev_res else None,
            'next': {
                'id': next_res.id if next_res.id else None,
                'initial_name': serialize_value(next_res.initial_name),
                'gender': serialize_value(next_res.gender),
                'birthday': serialize_value(next_res.birthday),
                'experience_pr': serialize_value(next_res.experience_pr),
                'working_hope': serialize_value(next_res.working_hope),
            } if next_res else None
        }

        return request.make_response(
            json.dumps({'success': True, 'data': data}),
            headers={'Content-Type': 'application/json'}
        )
    