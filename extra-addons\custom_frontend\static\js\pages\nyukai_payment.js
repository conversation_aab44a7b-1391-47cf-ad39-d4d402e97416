import { userInfo } from "../router/router.js";
import { createSingleBreadcrumb } from "../utils/breadcrumbHelper.js";

const NyukaiPayment = {
    'template': `
        <main class="pb-3 margin-header" id="vue-app">
            ${createSingleBreadcrumb('入会')}
            <div class="container-fluid grabient pt-3 position-relative">
                <div class="row mb-4 mb-md-0">
                    <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0 mx-auto">
                        <div class="text-center mb-5">
                            <h3 class="mb-4">入会するプランを選択してください。</h3>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-8 col-lg-6">
                                <form class="new_credit_payment" id="credit_payments_regist_cc_form" name="form1" novalidate="novalidate" autocomplete="off" action="/nyukai/plan" accept-charset="UTF-8" method="post">
                                    <input name="utf8" type="hidden" value="✓" autocomplete="off" />
                                    <input type="hidden" name="authenticity_token" value="0Mt5kUDki+3cMCHw1CQbmddn1bJnH33cSjNvTVzUlUE49C1FJK++b/dFVmWz3M03+jZUaMXdHOJjjsNP8dTe1Q==" autocomplete="off" />
                                    <input value="72327" class="form-control" autocomplete="off" type="hidden" name="credit_payment[merchant_id]" id="credit_payment_merchant_id" />
                                    <input value="001" class="form-control" autocomplete="off" type="hidden" name="credit_payment[service_id]" id="credit_payment_service_id" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[token]" id="credit_payment_token" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[token_key]" id="credit_payment_token_key" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[masked_cc_number]" id="credit_payment_masked_cc_number" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[card_brand_code]" id="credit_payment_card_brand_code" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[cc_expiration]" id="credit_payment_cc_expiration" />
                                    <div class="card px-3 px-md-4 form-card mt-2 pt-5">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mx-auto mb-5">
                                                    <label class="font-middle mb-3" for="">プラン <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                                            </label>
                                                    <div v-dropdown="{modelValue: '', listType: 'planOptions'}" @selected="selectPlan">
                                                        <input type="hidden" v-model="selectedPlan" name="credit_payment[plan]" />
                                                    </div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <span class="card-icon">
                                                        <img src="/custom_frontend/static/img/cards/visa.png" alt="VISA" />
                                                    </span>
                                                    <span class="card-icon ml-2">
                                                        <img src="/custom_frontend/static/img/cards/mastercard.png" alt="MasterCard" />
                                                    </span>
                                                    <span class="card-icon ml-2">
                                                        <img src="/custom_frontend/static/img/cards/jcb.png" alt="JCB" />
                                                    </span>
                                                    <span class="card-icon ml-2">
                                                        <img src="/custom_frontend/static/img/cards/amex.png" alt="American Express" />
                                                    </span>
                                                    <a href="https://www.cardservice.co.jp/service/creditcard/card.html" target="_blank" class="ml-3 card-info-link">
                                                        対応カードの詳細を確認する。
                                                    </a>
                                                </div>
                                                <div id="zeus_token_card_info_area"></div>
                                                <div id="3dscontainer"></div>
                                                <div id="challenge_wait"></div>
                                                <div class="mx-auto mb-3">
                                                    <input type="checkbox" name="credit_payment[auto_renew]" id="credit_payment_auto_renew" v-model="autoRenew" />
                                                    <label class="font-middle mb-3" for="credit_payment_auto_renew">自動更新</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row justify-content-center mt-5">
                                        <div class="col-6 col-md-4 mt-2">
                                            <button name="button" id="go_to_confirm_page" type="button" class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light" @click="beforeSubmit" style="color: white">申し込み</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/new_payment.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/dropdown.css"/>
    `,
    data() {
        return {
            isMobileMenuOpen: false,
            selectedPlan: '未選択',
            zeusTokenIpcode: '2019002213',
            user_id: userInfo ? userInfo.user_id : null,
            iframeUrl: null,
            autoRenew: false,
            transaction_id: null,
        }
    },
    methods: {
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        },
        selectPlan(event) {
            this.selectedPlan = event.detail;
            console.log('Selected plan:', this.selectedPlan);
        },
        beforeSubmit() {
            if(!this.validateInput()) {
                return;
            }

            zeusToken.getToken(async (zeus_token_response_data) => {
                if (!zeus_token_response_data["result"]) {
                    window.toastr.warning(zeusToken.getErrorMessage(zeus_token_response_data["error_code"]));
                } else {
                    const response = await fetch('/api/creditcard', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            user_id: this.user_id,
                            selected_plan: parseInt(this.selectedPlan),
                            token_key: zeus_token_response_data.token_key,
                            masked_card_number: zeus_token_response_data.masked_card_number,
                            card_expiration: zeus_token_response_data.card_expires_month + "/" + zeus_token_response_data.card_expires_year,
                            auto_renew: this.autoRenew,
                        }),
                    });

                    const result = await response.json();

                    if (result.result.success && result.result.response) {
                        this.transaction_id = result.result.transaction_id;

                        // Parse XML để lấy các giá trị cần thiết
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(result.result.response, "application/xml");
                        const xid = xmlDoc.getElementsByTagName("xid")[0].textContent; // md
                        const iframeUrl = xmlDoc.getElementsByTagName("iframeUrl")[0].textContent;
                        const decodedIframeUrl = decodeURIComponent(iframeUrl);

                        // Các tham số cố định
                        const paReq = "PaReq";
                        const threeDSMethod = "2";
                        const termUrl = window.location.origin + "/zeus/3ds/callback";

                        // Gọi hàm setPareqParams theo tài liệu ZEUS
                        setPareqParams(xid, paReq, termUrl, threeDSMethod, decodedIframeUrl);

                        this.statusInterval = setInterval(() => {
                            this.checkStatus(this.transaction_id);
                        }, 2000);
                    }
                }
            });
        },

        async checkStatus(transaction_id) {
            const response = await fetch('/api/payment_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    transaction_id: transaction_id,
                }),
            });
        
            const result = await response.json();
        
            if (result.result.success) {
                if (result.result.status === 'success') {
                    clearInterval(this.statusInterval);
                    window.location.href = "/payment/result?success=true&plan=" + this.selectedPlan +"&type=nyukai&transaction_id=" + transaction_id;
                } else if (result.result.status === 'failed') {
                    clearInterval(this.statusInterval);
                    window.location.href = "/payment/result?success=false&error=" + (result.result.error_code || "unknown") +"&type=nyukai&transaction_id=" + transaction_id;
                }
            }
        },

        async loadZeusScript() {
            const zeusConfig = {
                production: {
                    url: "https://linkpt.cardservice.co.jp/api/token/2.0/zeus_token2.js",
                },
                sandbox: {
                    url: "https://secure2-sandbox.cardservice.co.jp/api/token/2.0/zeus_token_cvv2.js",
                }
            }
            const script = document.createElement("script");
            script.src = "https://secure2-sandbox.cardservice.co.jp/api/token/2.0/zeus_token_cvv2.js";
            script.async = true;
            script.onload = function () {
                zeusTokenStart();
            }
            document.body.appendChild(script);
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },

        validateInput() {
            if (this.selectedPlan === '未選択') {
                window.toastr.warning('プランを選択してください。');
                return false;
            }

            const masked_card_number = document.getElementById('zeus_token_card_number').value;
            if(masked_card_number === '') {
                window.toastr.warning('カード番号を入力してください。');
                return false;
            }

            const card_expiration_month = document.getElementById('zeus_token_card_expires_month').value;
            const card_expiration_year = document.getElementById('zeus_token_card_expires_year').value;
            if(card_expiration_month === '' || card_expiration_year === '') {
                window.toastr.warning('カード有効期限を入力してください。');
                return false;
            }

            const card_cvv = document.getElementById('zeus_token_card_cvv').value;
            if(card_cvv === '') {
                window.toastr.warning('カード背面4桁を入力してください。');
                return false;
            }

            const card_name = document.getElementById('zeus_token_card_name').value;
            if(card_name === '') {
                window.toastr.warning('カード名義を入力してください。');
                return false;
            }
            
            return true;
        },
    },
    async mounted() {
        window.zeusTokenIpcode = this.zeusTokenIpcode;
        await this.loadZeusScript();
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });

        // Dùng MutationObserver để ẩn các phần tử ZEUS render ra
        const observer = new MutationObserver(() => {
            let quickRadio = document.getElementById('zeus_token_action_type_quick');
            let quickLabel = document.querySelector('label[for="zeus_token_action_type_quick"]');
            let regCardArea = document.getElementById('zeus_registerd_card_area');
            let newRadio = document.getElementById('zeus_token_action_type_new');
            let newLabel = document.querySelector('label[for="zeus_token_action_type_new"]');

            // Nếu đã render xong thì ẩn và ngừng observer
            if (quickRadio && quickLabel && regCardArea && newRadio && newLabel) {
                quickRadio.style.display = 'none';
                quickLabel.style.display = 'none';
                regCardArea.style.display = 'none';
                newRadio.style.display = 'none';
                newLabel.style.display = 'none';
                observer.disconnect(); // Ngừng theo dõi
            }
        });

        // Theo dõi thay đổi trong #zeus_token_card_info_area
        const target = document.getElementById('zeus_token_card_info_area');
        if (target) {
            observer.observe(target, { childList: true, subtree: true });
        }
    }
}
export default NyukaiPayment
