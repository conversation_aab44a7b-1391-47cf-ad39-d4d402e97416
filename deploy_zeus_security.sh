#!/bin/bash

# ZEUS Security Deployment Script for mi52.jp
# Chạy script này trên server production sau khi push code

set -e  # Exit on any error

echo "🚀 ZEUS Security Deployment for mi52.jp"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# 1. Install ClamAV
print_status "Installing ClamAV antivirus..."
sudo apt update
sudo apt install -y clamav clamav-daemon clamav-freshclam

# 2. Configure ClamAV
print_status "Configuring ClamAV..."

# Stop services for configuration
sudo systemctl stop clamav-freshclam
sudo systemctl stop clamav-daemon

# Update virus definitions
print_status "Updating virus definitions..."
sudo freshclam

# Start and enable services
sudo systemctl enable clamav-freshclam
sudo systemctl start clamav-freshclam
sudo systemctl enable clamav-daemon
sudo systemctl start clamav-daemon

# 3. Create log directories
print_status "Creating log directories..."
sudo mkdir -p /var/log/odoo
sudo chown odoo:odoo /var/log/odoo
sudo chmod 755 /var/log/odoo

# 4. Configure log rotation
print_status "Setting up log rotation..."
sudo tee /etc/logrotate.d/zeus-security > /dev/null <<EOF
/var/log/odoo/security.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 odoo odoo
}
EOF

# 5. Create cron jobs for maintenance
print_status "Setting up maintenance cron jobs..."
sudo tee /etc/cron.d/zeus-security > /dev/null <<EOF
# Update ClamAV definitions daily at 2 AM
0 2 * * * root /usr/bin/freshclam --quiet

# Weekly full system scan at 3 AM Sunday
0 3 * * 0 root /usr/bin/clamscan -r /var/lib/odoo --log=/var/log/clamav/weekly-scan.log --quiet

# Daily log rotation
0 1 * * * root /usr/sbin/logrotate /etc/logrotate.d/zeus-security
EOF

# 6. Configure Nginx security headers
print_status "Updating Nginx configuration..."

# Backup current nginx config
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)

# Add security headers to nginx config
sudo tee /etc/nginx/conf.d/security-headers.conf > /dev/null <<EOF
# ZEUS Security Headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://linkpt.cardservice.co.jp https://secure2-sandbox.cardservice.co.jp; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://linkpt.cardservice.co.jp https://secure2-sandbox.cardservice.co.jp;" always;

# Disable directory listing
autoindex off;

# Hide nginx version
server_tokens off;
EOF

# Test nginx configuration
print_status "Testing Nginx configuration..."
sudo nginx -t

if [ $? -eq 0 ]; then
    print_status "Reloading Nginx..."
    sudo systemctl reload nginx
else
    print_error "Nginx configuration test failed!"
    exit 1
fi

# 7. Create verification script
print_status "Creating verification script..."
sudo tee /usr/local/bin/zeus-security-check > /dev/null <<'EOF'
#!/bin/bash
echo "=== ZEUS Security Compliance Check ==="
echo "Date: $(date)"
echo ""

# Check ClamAV status
echo "1. ClamAV Services:"
echo "   Daemon: $(systemctl is-active clamav-daemon)"
echo "   Freshclam: $(systemctl is-active clamav-freshclam)"
echo ""

# Check virus definitions date
echo "2. Virus Definitions:"
if [ -f /var/lib/clamav/daily.cvd ]; then
    echo "   Last updated: $(stat -c %y /var/lib/clamav/daily.cvd)"
else
    echo "   Status: Not found"
fi
echo ""

# Check recent security logs
echo "3. Recent Security Events:"
if [ -f /var/log/odoo/security.log ]; then
    echo "   Last 3 entries:"
    tail -3 /var/log/odoo/security.log | sed 's/^/   /'
else
    echo "   No security log found"
fi
echo ""

# Check nginx security headers
echo "4. Security Headers Test:"
curl -s -I http://localhost | grep -E "(X-Frame-Options|X-XSS-Protection|Content-Security-Policy)" | sed 's/^/   /'
echo ""

# Check disk space
echo "5. System Status:"
echo "   Disk usage: $(df -h / | awk 'NR==2{print $5}')"
echo "   Memory usage: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo ""

echo "=== Check Complete ==="
EOF

sudo chmod +x /usr/local/bin/zeus-security-check

# 8. Restart Odoo to load new modules
print_status "Restarting Odoo service..."
if systemctl is-active --quiet odoo; then
    sudo systemctl restart odoo
    print_status "Odoo restarted successfully"
else
    print_warning "Odoo service not found or not running"
fi

# 9. Run verification
print_status "Running security verification..."
sudo /usr/local/bin/zeus-security-check

# 10. Final status
echo ""
print_status "ZEUS Security deployment completed!"
echo ""
echo "📋 Next steps:"
echo "   1. Test file upload functionality"
echo "   2. Check security logs: tail -f /var/log/odoo/security.log"
echo "   3. Run periodic checks: sudo /usr/local/bin/zeus-security-check"
echo "   4. Monitor ClamAV status: systemctl status clamav-daemon"
echo ""
echo "🔒 Security features now active:"
echo "   ✅ ClamAV antivirus scanning"
echo "   ✅ File type validation"
echo "   ✅ File size limits"
echo "   ✅ Security headers"
echo "   ✅ Comprehensive logging"
echo "   ✅ Automated maintenance"
echo ""
print_status "ZEUS compliance level significantly improved!"
