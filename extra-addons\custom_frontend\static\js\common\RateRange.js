const RateRangeSlider = {
    props: {
        min: { type: Number, default: 25 },
        max: { type: Number, default: 100 },
        step: { type: Number, default: 25 },
        startValues: {
            type: Array,
            default: () => [25, 100] // Default range: 25% to 100%
        },
    },
    data() {
        return {
            selectedRange: [25, 100], // Default range
            slider: null,
        };
    },
    watch: {
        startValues: {
            handler(newValues) {
                if (Array.isArray(newValues) && newValues.length >= 2) {
                    // Ensure values are within bounds
                    let minVal = Math.max(this.min, newValues[0] || this.min);
                    let maxVal = Math.min(this.max, newValues[1] || this.max);

                    this.selectedRange = [minVal, maxVal];
                    if (this.slider) {
                        this.slider.set([minVal, maxVal]);
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) {
                // Initialize dual-handle range slider
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedRange,
                    connect: true, // Connect between handles
                    range: { min: this.min, max: this.max },
                    step: this.step,
                    margin: 0, // Allow same value selection
                    format: {
                        to: function (value) {
                            return Math.round(value);
                        },
                        from: function (value) {
                            return Number(value);
                        }
                    }
                });

                // Handle slider update events
                this.slider.on("update", (values) => {
                    const minValue = Math.round(parseFloat(values[0]));
                    const maxValue = Math.round(parseFloat(values[1]));
                    this.selectedRange = [minValue, maxValue];

                    // Emit range of rate values
                    this.$emit("update:rate", [minValue, maxValue]);
                });
            }
        });
    },
    computed: {
        displayRange() {
            if (this.selectedRange[0] === this.selectedRange[1]) {
                // Single value selected
                return `${this.selectedRange[0]}%`;
            } else {
                // Range selected
                return `${this.selectedRange[0]}% 〜 ${this.selectedRange[1]}%`;
            }
        },
    },
    template: `
      <div class="mb-5">
        <p class="d-flex justify-content-center" id="rate-range">
          {{ displayRange }}
        </p>
        <div class="slider-styled mx-4" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>{{ min }}%</span><span>{{ max }}%</span>
        </div>
      </div>
    `,
};
export default RateRangeSlider;
