from odoo import http
from odoo.http import request
import json
from datetime import datetime, timedelta
from . import common as global_common
import logging
import pytz
from . import common
import re

_logger = logging.getLogger(__name__)

class InterviewBookingController(http.Controller):
    def is_valid_url(self, url):
        pattern = re.compile(
            r'^(https?://)?'                  # optional http or https
            r'(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})'  # domain
            r'(:\d+)?'                        # optional port
            r'(/[\w\-./?%&=]*)?$',            # optional path and query
            re.IGNORECASE
        )
        return re.match(pattern, url) is not None

    @http.route('/api/get_booking', type='json', auth='public', methods=['POST'], csrf=False)
    def get_interview_booking(self):
        data = request.httprequest.get_json(silent=True)

        opp_id = data.get('opp_id')
        res_id = data.get('res_id')
        _logger.info("res_id: %s", res_id)
        _logger.info("opp_id: %s", opp_id)

        response_data = []

        if not opp_id:
            return {'success': False, 'message': 'Invalid or missing opportunity ID'}

        try:
            res_id = int(res_id)
        except (TypeError, ValueError):
            return {'success': False, 'message': 'Invalid resume ID format'}

        # Lấy booking của dự án hiện tại
        current_bookings = request.env['vit.interviewbooking'].sudo().search([('opp_id', '=', int(opp_id))])

        # Lấy tất cả booking của cùng resume_id từ các dự án khác
        other_bookings = request.env['vit.interviewbooking'].sudo().search([
            ('resume_id', '=', res_id),
            ('opp_id', '!=', int(opp_id)),
            ('is_booking', '=', True)  # Chỉ lấy những booking đã confirm
        ])

        # Kết hợp cả hai
        bookings = current_bookings + other_bookings

        if not current_bookings:
            return {'success': False, 'message': 'No booking found'}
        
        for booking in bookings:
            if(booking.is_booking):
                # Phân loại className dựa trên dự án và resume
                if booking.resume_id and booking.resume_id.id == res_id:
                    if booking.opp_id.id == int(opp_id):
                        # Booking của resume hiện tại trong dự án hiện tại
                        class_name = 'my-booking'
                    else:
                        # Booking của resume hiện tại nhưng ở dự án khác
                        class_name = 'other-project-booking'
                else:
                    # Booking của resume khác
                    class_name = 'others-booking'

                response_data.append({
                    'id': booking.id,
                    'contract_types': booking.contract_types if booking.contract_types else None,
                    'opp_id': booking.opp_id.id if booking.opp_id else None,
                    'resume_id': booking.resume_id.id if booking.resume_id else None,
                    'is_booking': booking.is_booking,
                    'start': booking.interview_start.strftime('%Y-%m-%d %H:%M:%S') if booking.interview_start else None,
                    'end': booking.interview_end.strftime('%Y-%m-%d %H:%M:%S') if booking.interview_end else None,
                    'className': class_name,
                    'meeting_url': booking.meeting_url if booking.meeting_url else None
                })

        return {'success': True, 'data': response_data}

    @http.route('/api/update_booking', type='json', auth='public', methods=['POST'])
    def update_interview_booking(self):
        data = request.httprequest.get_json(silent=True)

        opp_id = data.get('opp_id')
        resume_id = data.get('resume_id')
        meeting_url = data.get('meeting_url')

        _logger.info('Opp Id: %s Resume Id: %s', opp_id, resume_id)

        def format_datetime(dt_str):
            return datetime.strptime(dt_str, '%Y/%m/%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')

        booking = request.env['vit.interviewbooking'].sudo().search([
            ('opp_id', '=', int(opp_id)),
            ('resume_id', '=', int(resume_id))
        ], limit=1)

        workflow = request.env['vit.workflow'].sudo().search([
            ('opportunities_id', '=', int(opp_id)),
            ('resumes_id', '=', int(resume_id))
        ], limit=1)
        _logger.info('Workflow: %s', workflow)

        if not booking:
            return {'success': False,'message': 'Booking not found'}

        if not workflow:
            return {'success': False,'message': 'Workflow not found'}

        if booking.is_booking:
            booking.sudo().write({
                'is_booking': False,
                'contract_types': data.get('contract_types'),
                'interview_start': None,
                'interview_end': None,
                'updated_at': data.get('updated_at'),
                'updated_by': data.get('updated_by'),
            })

            # Tính toán expire_date dựa trên logic mới cho status = 0
            application_plus_7_days = workflow.created_at + timedelta(days=7)
            opportunity = workflow.opportunities_id

            # So sánh với ngày hết hạn ứng tuyển (expired_at)
            if opportunity.expired_at and opportunity.expired_at < application_plus_7_days:
                # Nếu expired_at sớm hơn, dùng expired_at
                calculated_expire_date = opportunity.expired_at
            else:
                # Nếu expired_at muộn hơn hoặc không có, dùng ngày ứng tuyển + 7 ngày
                calculated_expire_date = application_plus_7_days

            workflow.sudo().write({
                'status': 0,
                'expire_date': calculated_expire_date,
                'updated_at': data.get('updated_at'),
                'updated_by': data.get('updated_by'),
            })
            
            opportunities = request.env['vit.opportunities'].sudo().browse(int(opp_id))
            opportunities_name = opportunities.subject if opportunities else 'なし'
            user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
            company_name = user.company_id.name if user.company_id else 'なし'
            user_name = user.username if user.username else 'なし'
            user_email = user.email if user.email else 'なし'

            partner = request.env['vit.partner'].sudo().browse(int(resume_id))
            partner_name = partner.initial_name if partner else 'なし'
            user = request.env['vit.users'].sudo().browse(int(partner.created_by))
            company_name_res = user.company_id.name if user.company_id else 'なし'
            user_name_res = user.username if user.username else 'なし'
            user_email_res = user.email if user.email else 'なし'

            common.sendMailWorkflowOppChange(company_name, user_name, opportunities_name, workflow.status, user_email)
            common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, workflow.status, user_email_res)
        else:
            # Xử lý meeting_url - cho phép chuỗi rỗng, URL hoặc text thường
            is_link = False
            if meeting_url and meeting_url.strip():  # Kiểm tra không rỗng và không chỉ có khoảng trắng
                # Chỉ set is_link = True nếu là URL hợp lệ, nhưng vẫn cho phép lưu text thường
                if self.is_valid_url(meeting_url.strip()):
                    is_link = True

            booking.sudo().write({
                'is_booking': True,
                'contract_types': data.get('contract_types'),
                'interview_start': format_datetime(data.get('interview_start')),
                'interview_end': format_datetime(data.get('interview_end')),
                'meeting_url': data.get('meeting_url'),
                'updated_at': data.get('updated_at'),
                'updated_by': data.get('updated_by'),
            })

            workflow.sudo().write({
                'status': 1,
                'expire_date': datetime.strptime(data.get('interview_end'), '%Y/%m/%d %H:%M:%S') + timedelta(days=7),
                'updated_at': data.get('updated_at'),
                'updated_by': data.get('updated_by'),
            })

            opportunities = request.env['vit.opportunities'].sudo().browse(int(opp_id))
            opportunities_name = opportunities.subject if opportunities else 'なし'
            user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
            company_name = user.company_id.name if user.company_id else 'なし'
            user_name = user.username if user.username else 'なし'
            user_email = user.email if user.email else 'なし'

            partner = request.env['vit.partner'].sudo().browse(int(resume_id))
            partner_name = partner.initial_name if partner else 'なし'
            user = request.env['vit.users'].sudo().browse(int(partner.created_by))
            company_name_res = user.company_id.name if user.company_id else 'なし'
            user_name_res = user.username if user.username else 'なし'
            user_email_res = user.email if user.email else 'なし'
            
            date = booking.interview_start.strftime('%Y/%m/%d')
            time_start = booking.interview_start.strftime('%H:%M')
            time_end = booking.interview_end.strftime('%H:%M')
            meeting_url = booking.meeting_url

            common.sendMailWorkflowOppChange(company_name, user_name, opportunities_name, workflow.status, user_email, meeting_url, date, time_start, time_end, False, is_link)
            common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, workflow.status, user_email_res, meeting_url, date, time_start, time_end, False, is_link)

        return {'success': True,'message': 'Booking updated'}

    @http.route('/api/update_booking_url', type='json', auth='public', methods=['POST'])
    def update_booking_url_only(self):
        """API để chỉ cập nhật meeting URL mà không thay đổi booking status"""
        data = request.httprequest.get_json(silent=True)

        opp_id = data.get('opp_id')
        resume_id = data.get('resume_id')
        meeting_url = data.get('meeting_url')

        _logger.info('Update URL Only - Opp Id: %s Resume Id: %s', opp_id, resume_id)

        # Xử lý meeting_url - cho phép chuỗi rỗng, URL hoặc text thường
        is_link = False
        if meeting_url and meeting_url.strip():  # Kiểm tra không rỗng và không chỉ có khoảng trắng
            # Chỉ set is_link = True nếu là URL hợp lệ, nhưng vẫn cho phép lưu text thường
            if self.is_valid_url(meeting_url.strip()):
                is_link = True

        booking = request.env['vit.interviewbooking'].sudo().search([
            ('opp_id', '=', int(opp_id)),
            ('resume_id', '=', int(resume_id))
        ], limit=1)

        if not booking:
            return {'success': False, 'message': 'Booking not found'}

        # Chỉ cập nhật meeting_url, không thay đổi is_booking status
        booking.sudo().write({
            'meeting_url': meeting_url,
            'updated_at': data.get('updated_at'),
            'updated_by': data.get('updated_by'),
        })

        # Gửi mail thông báo cập nhật URL
        if booking.is_booking and booking.interview_start and booking.interview_end:
            opportunities = request.env['vit.opportunities'].sudo().browse(int(opp_id))
            opportunities_name = opportunities.subject if opportunities else 'なし'
            user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
            company_name = user.company_id.name if user.company_id else 'なし'
            user_name = user.username if user.username else 'なし'
            user_email = user.email if user.email else 'なし'

            partner = request.env['vit.partner'].sudo().browse(int(resume_id))
            partner_name = partner.initial_name if partner else 'なし'
            user_res = request.env['vit.users'].sudo().browse(int(partner.created_by))
            company_name_res = user_res.company_id.name if user_res.company_id else 'なし'
            user_name_res = user_res.username if user_res.username else 'なし'
            user_email_res = user_res.email if user_res.email else 'なし'

            date = booking.interview_start.strftime('%Y/%m/%d')
            time_start = booking.interview_start.strftime('%H:%M')
            time_end = booking.interview_end.strftime('%H:%M')

            # Gửi mail với URL mới
            common.sendMailWorkflowOppChange(company_name, user_name, opportunities_name, 1, user_email, meeting_url, date, time_start, time_end, False, is_link)
            common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, 1,user_email_res, meeting_url, date, time_start, time_end, False, is_link)

        _logger.info('Meeting URL updated successfully for booking %s', booking.id)
        return {'success': True, 'message': 'Meeting URL updated successfully'}

    @http.route('/api/create_booking', type='json', auth='public', methods=['POST'])
    def create_interview_booking(self):
        data = request.httprequest.get_json(silent=True)

        opp_id = data.get('opportunities_id')
        resumes_apply = data.get('resumes_apply')
        created_by = data.get('created_by')

        if not opp_id or not resumes_apply or not isinstance(resumes_apply, list) or not created_by:
            return {'success': False, 'message': 'Missing or invalid required fields'}

        bookings = []
        for resume_id in resumes_apply:
            booking = request.env['vit.interviewbooking'].sudo().create({
                'opp_id': opp_id,
                'resume_id': resume_id,
                'created_at': datetime.now(),
                'created_by': created_by,
                'is_booking': False,
            })
            bookings.append(booking.id)

        return {'success': True, 'message': 'Bookings created', 'booking_ids': bookings}

    @http.route('/api/check_interview', type='json', auth='public', methods=['POST'], csrf=False)
    def check_interview(self):
        vietnam_tz = pytz.timezone('Asia/Ho_Chi_Minh')
        now = datetime.now(pytz.utc).astimezone(vietnam_tz)

        expired_interviews = request.env['vit.interviewbooking'].sudo().search([
            ('interview_end', '<', now)
        ])

        if not expired_interviews:
            return {'success': False, 'message': 'No expired interviews found'}

        interview_pairs = {
            (interview.opp_id.id, interview.resume_id.id, interview.interview_end)
            for interview in expired_interviews if interview.opp_id and interview.resume_id and interview.interview_end
        }

        workflows_to_update = request.env['vit.workflow'].sudo().search([
            ('opportunities_id', 'in', [pair[0] for pair in interview_pairs]),
            ('resumes_id', 'in', [pair[1] for pair in interview_pairs]),
            ('status', '=', 1)
        ])

        if not workflows_to_update:
            return {'success': False, 'message': 'No workflows found to update'}

        for workflow in workflows_to_update:
            if isinstance(workflow.expire_date, datetime):
                new_expire_date = workflow.expire_date + timedelta(days=7)
                workflow.sudo().write({'status': 2, 'expire_date': new_expire_date})

                opportunities = request.env['vit.opportunities'].sudo().browse(int(workflow.opportunities_id.id))
                opportunities_name = opportunities.subject if opportunities else 'なし'
                user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
                company_name = user.company_id.name if user.company_id else 'なし'
                user_name = user.username if user.username else 'なし'
                user_email = user.email if user.email else 'なし'

                partner = request.env['vit.partner'].sudo().browse(int(workflow.resumes_id.id))
                partner_name = partner.initial_name if partner else 'なし'
                user = request.env['vit.users'].sudo().browse(int(partner.created_by))
                company_name_res = user.company_id.name if user.company_id else 'なし'
                user_name_res = user.username if user.username else 'なし'
                user_email_res = user.email if user.email else 'なし'

                common.sendMailWorkflowOppChange(company_name, user_name, opportunities_name, workflow.status, user_email)
                common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, workflow.status, user_email_res)

                _logger.info(f"Updated workflow {workflow.id}: status = 2, old expire_date = {workflow.expire_date}, new expire_date = {new_expire_date}")
            else:
                _logger.warning(f"Invalid expire_date for workflow {workflow.id}: {workflow.expire_date}")

        return {
            'success': True,
            'message': 'Updated status and extended expire_date in workflows',
            'updated_workflows': workflows_to_update.mapped('id')
        }







