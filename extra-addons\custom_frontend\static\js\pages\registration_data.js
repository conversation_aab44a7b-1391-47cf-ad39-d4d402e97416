import { createServiceMenuBreadcrumb } from "../utils/breadcrumbHelper.js";

const RegistrationData = {
    'template' :  `
    <main class="pb-3 margin-header" id="vue-app" data-v-app="">
        ${createServiceMenuBreadcrumb('案件・人財登録')}
        <div class="container-fluid title py-2 py-md-4">
            <h1 class="mb-0">案件・人財登録</h1>
        </div>
        <div class="container-fluid grabient pt-5">
            <div class="row">
                <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                    <a href="/opportunities/manage/new" class="btn btn-default btn-block btn-lg font-middle mt-3 mb-5 waves-effect waves-light p-3 text-white font-weight-bold">案件を新規登録する</a>
                </div>
                <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                    <a href="/resumes/manage/new" class="btn btn-default btn-block btn-lg font-middle mt-3 mb-5 waves-effect waves-light p-3 text-white font-weight-bold">人財を新規登録する</a>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/registration_data.css"/>
    `,
}

export default RegistrationData;