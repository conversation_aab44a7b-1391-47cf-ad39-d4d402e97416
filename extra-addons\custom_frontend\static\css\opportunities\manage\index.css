/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

  *,
  ::after,
  ::before {
    box-sizing: border-box;
  }

  main,
  nav {
    display: block;
  }

  [tabindex="-1"]:focus:not(:focus-visible) {
    outline: 0 !important;
  }

  h1,
  h4,
  h5 {
    margin-top: 0;
    margin-bottom: 0.5rem;
  }

  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  ul {
    margin-top: 0;
    margin-bottom: 1rem;
  }

  a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
  }

  a:hover {
    color: #0056b3;
    text-decoration: underline;
  }

  img {
    vertical-align: middle;
    border-style: none;
  }

  table {
    border-collapse: collapse;
  }

  th {
    text-align: inherit;
    text-align: -webkit-match-parent;
  }

  label {
    display: inline-block;
    margin-bottom: 0.5rem;
  }

  button {
    border-radius: 0;
  }

  button:focus:not(:focus-visible) {
    outline: 0;
  }

  button,
  input,
  select,
  textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }

  button,
  input {
    overflow: visible;
  }

  button,
  select {
    text-transform: none;
  }

  [role="button"] {
    cursor: pointer;
  }

  select {
    word-wrap: normal;
  }

  [type="button"],
  [type="submit"],
  button {
    -webkit-appearance: button;
  }

  [type="button"]:not(:disabled),
  [type="submit"]:not(:disabled),
  button:not(:disabled) {
    cursor: pointer;
  }

  input[type="checkbox"] {
    box-sizing: border-box;
    padding: 0;
  }

  textarea {
    overflow: auto;
    resize: vertical;
  }

  h1,
  h4,
  h5 {
    margin-bottom: 0.5rem;
    font-weight: 500;
    line-height: 1.2;
  }

  h1 {
    font-size: 2.5rem;
  }

  h4 {
    font-size: 1.5rem;
  }

  h5 {
    font-size: 1.25rem;
  }

  .container,
  .container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
  }

  @media (min-width: 576px) {
    .container {
      max-width: 540px;
    }
  }

  @media (min-width: 768px) {
    .container {
      max-width: 720px;
    }
  }

  @media (min-width: 992px) {
    .container {
      max-width: 960px;
    }
  }

  @media (min-width: 1200px) {
    .container {
      max-width: 1140px;
    }
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  .col-1,
  .col-12,
  .col-2,
  .col-6,
  .col-9,
  .col-lg-3,
  .col-lg-9,
  .col-md-12,
  .col-md-3,
  .col-md-4,
  .col-md-8 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
  }

  .col-1 {
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-2 {
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  @media (min-width: 768px) {
    .col-md-3 {
      flex: 0 0 25%;
      max-width: 25%;
    }

    .col-md-4 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    .col-md-8 {
      flex: 0 0 66.666667%;
      max-width: 66.666667%;
    }

    .col-md-12 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  @media (min-width: 992px) {
    .col-lg-3 {
      flex: 0 0 25%;
      max-width: 25%;
    }

    .col-lg-9 {
      flex: 0 0 75%;
      max-width: 75%;
    }
  }

  .table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
  }

  .table td,
  .table th {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
  }

  .table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
  }

  @media (max-width: 767.98px) {
    .table-responsive-md {
      display: block;
      width: 100%;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }
  }

  .form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .form-control {
      transition: none;
    }
  }

  .form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .form-control::placeholder {
    color: #6c757d;
    opacity: 1;
  }

  .form-control:disabled,
  .form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1;
  }

  textarea.form-control {
    height: auto;
  }

  .form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem;
  }

  .form-check-input {
    position: absolute;
    margin-top: 0.3rem;
    margin-left: -1.25rem;
  }

  .btn {
    display: inline-block;
    font-weight: 400;
    color: #fff !important;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .btn {
      transition: none;
    }
  }

  .btn:hover {
    color: #212529;
    text-decoration: none;
  }

  .btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .btn:disabled {
    opacity: 0.65;
  }

  .btn:not(:disabled):not(.disabled) {
    cursor: pointer;
  }

  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0.3rem;
  }

  .btn-block {
    display: block;
    width: 100%;
  }

  .custom-control {
    position: relative;
    z-index: 1;
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5rem;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .custom-control-input {
    position: absolute;
    left: 0;
    z-index: -1;
    width: 1rem;
    height: 1.25rem;
    opacity: 0;
  }

  .custom-control-input:checked~.custom-control-label::before {
    color: #fff;
    border-color: #007bff;
    background-color: #007bff;
  }

  .custom-control-input:focus~.custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .custom-control-input:focus:not(:checked)~.custom-control-label::before {
    border-color: #80bdff;
  }

  .custom-control-input:not(:disabled):active~.custom-control-label::before {
    color: #fff;
    background-color: #b3d7ff;
    border-color: #b3d7ff;
  }

  .custom-control-input:disabled~.custom-control-label {
    color: #6c757d;
  }

  .custom-control-input:disabled~.custom-control-label::before {
    background-color: #e9ecef;
  }

  .custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
  }

  .custom-control-label::before {
    position: absolute;
    top: 0.25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    background-color: #fff;
    border: #adb5bd solid 1px;
  }

  .custom-control-label::after {
    position: absolute;
    top: 0.25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background: 50%/50% 50% no-repeat;
  }

  .custom-checkbox .custom-control-label::before {
    border-radius: 0.25rem;
  }

  .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
  }

  .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(0, 123, 255, 0.5);
  }

  .custom-control-label::before {
    transition: background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  @media (prefers-reduced-motion: reduce) {
    .custom-control-label::before {
      transition: none;
    }
  }

  .card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.25rem;
  }

  .card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  }

  .card-header:first-child {
    border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
  }

  .badge-pill {
    padding-right: 0.6em;
    padding-left: 0.6em;
    border-radius: 10rem;
  }

  .close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
  }

  .close:hover {
    color: #000;
    text-decoration: none;
  }

  .close:not(:disabled):not(.disabled):focus,
  .close:not(:disabled):not(.disabled):hover {
    opacity: 0.75;
  }

  button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
  }

  .modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
  }

  .modal.show {
    opacity: 1;
    transition: opacity .8s;
  }

  .modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
  }

  .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
  }

  .modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
  }

  .modal-header .close {
    padding: 1rem 1rem;
    margin: -1rem -1rem -1rem auto;
  }

  .modal-title {
    margin-bottom: 0;
    line-height: 1.5;
  }

  .modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
  }

  @media (min-width: 576px) {
    .modal-dialog {
      max-width: 500px;
      margin: 1.75rem auto;
    }
  }

  @media (min-width: 992px) {
    .modal-lg {
      max-width: 800px;
    }
  }

  .bg-white {
    background-color: #fff !important;
  }

  .border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
  }

  .d-none {
    display: none !important;
  }

  .d-inline-block {
    display: inline-block !important;
  }

  .d-block {
    display: block !important;
  }

  .d-flex {
    display: flex !important;
  }

  @media (min-width: 576px) {
    .d-sm-inline-block {
      display: inline-block !important;
    }
  }

  @media (min-width: 768px) {
    .d-md-none {
      display: none !important;
    }

    .d-md-inline-block {
      display: inline-block !important;
    }

    .d-md-block {
      display: block !important;
    }
  }

  .flex-wrap {
    flex-wrap: wrap !important;
  }

  .justify-content-center {
    justify-content: center !important;
  }

  .align-items-center {
    align-items: center !important;
  }

  .w-100 {
    width: 100% !important;
  }

  .h-100 {
    height: 100% !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .mt-0 {
    margin-top: 0 !important;
  }

  .mx-0 {
    margin-right: 0 !important;
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  .mx-0 {
    margin-left: 0 !important;
  }

  .mt-1 {
    margin-top: 0.25rem !important;
  }

  .mb-1 {
    margin-bottom: 0.25rem !important;
  }

  .mr-2 {
    margin-right: 0.5rem !important;
  }

  .mb-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-2 {
    margin-left: 0.5rem !important;
  }

  .mt-3,
  .my-3 {
    margin-top: 1rem !important;
  }

  .mb-3,
  .my-3 {
    margin-bottom: 1rem !important;
  }

  .my-4 {
    margin-top: 1.5rem !important;
  }

  .mr-4 {
    margin-right: 1.5rem !important;
  }

  .mb-4,
  .my-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-5 {
    margin-bottom: 3rem !important;
  }

  .p-0 {
    padding: 0 !important;
  }

  .px-0 {
    padding-right: 0 !important;
  }

  .pb-0 {
    padding-bottom: 0 !important;
  }

  .px-0 {
    padding-left: 0 !important;
  }

  .py-2 {
    padding-top: 0.5rem !important;
  }

  .py-2 {
    padding-bottom: 0.5rem !important;
  }

  .p-3 {
    padding: 1rem !important;
  }

  .pt-3 {
    padding-top: 1rem !important;
  }

  .pr-3,
  .px-3 {
    padding-right: 1rem !important;
  }

  .pb-3 {
    padding-bottom: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .py-4 {
    padding-top: 1.5rem !important;
  }

  .py-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-4 {
    padding-left: 1.5rem !important;
  }

  .pt-5,
  .py-5 {
    padding-top: 3rem !important;
  }

  .py-5 {
    padding-bottom: 3rem !important;
  }

  .mb-n3 {
    margin-bottom: -1rem !important;
  }

  .mr-auto,
  .mx-auto {
    margin-right: auto !important;
  }

  .mx-auto {
    margin-left: auto !important;
  }

  @media (min-width: 576px) {
    .mr-sm-4 {
      margin-right: 1.5rem !important;
    }
  }

  @media (min-width: 768px) {
    .ml-md-3 {
      margin-left: 1rem !important;
    }

    .py-md-4 {
      padding-top: 1.5rem !important;
    }

    .py-md-4 {
      padding-bottom: 1.5rem !important;
    }
  }

  .text-center {
    text-align: center !important;
  }

  @media (min-width: 768px) {
    .text-md-right {
      text-align: right !important;
    }
  }

  .font-weight-bold {
    font-weight: 700 !important;
  }

  .text-dark {
    color: #343a40 !important;
  }

  @media print {

    *,
    ::after,
    ::before {
      text-shadow: none !important;
      box-shadow: none !important;
    }

    a:not(.btn) {
      text-decoration: underline;
    }

    thead {
      display: table-header-group;
    }

    img,
    tr {
      page-break-inside: avoid;
    }

    p {
      orphans: 3;
      widows: 3;
    }

    .container {
      min-width: 992px !important;
    }

    .table {
      border-collapse: collapse !important;
    }

    .table td,
    .table th {
      background-color: #fff !important;
    }
  }

  .green-text {
    color: #1072e9 !important;
  }

  .blue-grey.lighten-4 {
    background-color: #cfd8dc !important;
  }

  .blue-grey {
    background-color: #607d8b !important;
  }

  .grey {
    background-color: #9e9e9e !important;
  }

  .white {
    background-color: #fff !important;
  }

  .white-text {
    color: #fff !important;
  }

  .hoverable {
    box-shadow: none;
    transition: all 0.55s ease-in-out;
  }

  .hoverable:hover {
    box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2),
      0 6px 20px 0 rgba(0, 0, 0, 0.19);
    transition: all 0.55s ease-in-out;
  }

  :disabled {
    pointer-events: none !important;
  }

  a {
    color: #007bff;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  a:hover {
    color: #0056b3;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
  }

  a:disabled:hover {
    color: #007bff;
  }

  a:not([href]):not([tabindex]),
  a:not([href]):not([tabindex]):focus,
  a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none;
  }

  h1,
  h4,
  h5 {
    font-weight: 300;
  }

  .text-dark {
    color: #212121 !important;
  }

  .font-small {
    font-size: 0.9rem;
  }

  .waves-effect {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  a.waves-effect,
  a.waves-light {
    display: inline-block;
  }

  .btn {
    margin: 0.375rem;
    color: inherit;
    text-transform: uppercase;
    word-wrap: break-word;
    white-space: normal;
    cursor: pointer;
    border: 0;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16),
      0 2px 10px 0 rgba(0, 0, 0, 0.12);
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
      border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    padding: 0.84rem 2.14rem;
    font-size: 0.81rem;
  }

  .btn:hover,
  .btn:focus,
  .btn:active {
    outline: 0;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn.btn-block {
    margin: inherit;
  }

  .btn.btn-lg {
    padding: 1rem 2.4rem;
    font-size: 0.94rem;
  }

  .btn:disabled:hover,
  .btn:disabled:focus,
  .btn:disabled:active {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16),
      0 2px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .btn[class*="btn-outline-"] {
    padding-top: 0.7rem;
    padding-bottom: 0.7rem;
  }

  .btn-default {
    color: #fff !important;
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
  }

  .btn-default:hover {
    color: #fff;
    background-color: #61b8f7;
  }

  .btn-default:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-default:focus,
  .btn-default:active {
    background-color: #005650;
  }

  .btn-default:not([disabled]):not(.disabled):active {
    background-color: #005650 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-default:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-default {
    color: #1072e9 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border: 2px solid #1072e9 !important;
  }

  .btn-outline-default:hover,
  .btn-outline-default:focus,
  .btn-outline-default:active,
  .btn-outline-default:active:focus {
    color: #1072e9 !important;
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #1072e9 !important;
  }

  .btn-outline-default:not([disabled]):not(.disabled):active {
    background-color: rgba(0, 0, 0, 0) !important;
    border-color: #1072e9 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-outline-default:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-deep-orange {
    color: #fff;
    background-color: #1072e9 !important;
  }

  .btn-deep-orange:hover {
    color: #fff;
    background-color: #ff8d42;
  }

  .btn-deep-orange:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-deep-orange:focus,
  .btn-deep-orange:active {
    background-color: #002060;
  }

  .btn-deep-orange:not([disabled]):not(.disabled):active {
    background-color: #002060 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-deep-orange:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  a.btn:not([href]):not([tabindex]),
  a.btn:not([href]):not([tabindex]):focus,
  a.btn:not([href]):not([tabindex]):hover {
    color: #fff;
  }

  .btn-blue-grey {
    color: #fff;
    background-color: #78909c !important;
  }

  .btn-blue-grey:hover {
    color: #fff;
    background-color: #879ca7;
  }

  .btn-blue-grey:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-blue-grey:focus,
  .btn-blue-grey:active {
    background-color: #4a5b64;
  }

  .btn-blue-grey:not([disabled]):not(.disabled):active {
    background-color: #4a5b64 !important;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .btn-blue-grey:not([disabled]):not(.disabled):active:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .card {
    font-weight: 400;
    border: 0;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16),
      0 2px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .badge-pill {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
    border-radius: 10rem;
  }

  .modal-dialog .modal-content {
    border: 0;
    border-radius: 0.25rem;
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
  }

  .modal-dialog .modal-content .modal-header {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }

  .modal {
    padding-right: 0 !important;
  }

  table th {
    font-size: 1rem;
    font-weight: 400;
  }

  table td {
    font-size: 0.9rem;
    font-weight: 300;
  }

  table.table thead th {
    border-top: none;
  }

  table.table th,
  table.table td {
    padding-top: 1.1rem;
    padding-bottom: 1rem;
  }

  table.table a {
    margin: 0;
    color: #1072e9;
  }

  /* Box shadow cho bảng opportunities manage */
  table.table.white {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
                0 8px 25px rgba(0, 0, 0, 0.12),
                0 2px 6px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    overflow: hidden;
  }

  table .th-lg {
    min-width: 9rem;
  }

  .btn.btn-flat {
    font-weight: 500;
    color: inherit;
    background-color: rgba(0, 0, 0, 0);
    box-shadow: none;
  }

  .btn.btn-flat:not([disabled]):not(.disabled):active {
    box-shadow: none;
  }

  button,
  html [type="button"],
  [type="submit"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .card .btn-action {
    margin-top: -1.44rem;
    margin-bottom: -1.44rem;
  }

  .form-check-input:not(:checked),
  .form-check-input:checked {
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }

  [type="checkbox"]:not(:checked),
  [type="checkbox"]:checked {
    position: absolute;
    pointer-events: none;
    opacity: 0;
  }

  .form-check-input[type="checkbox"]+label {
    position: relative;
    display: inline-block;
    height: 1.5625rem;
    padding-left: 35px;
    line-height: 1.5625rem;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .form-check-input[type="checkbox"]+label:before,
  .form-check-input[type="checkbox"]:not(.filled-in)+label:after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 18px;
    height: 18px;
    margin-top: 3px;
    content: "";
    border: 2px solid #8a8a8a;
    border-radius: 1px;
    transition: 0.2s;
  }

  .form-check-input[type="checkbox"]:not(.filled-in)+label:after {
    border: 0;
    transform: scale(0);
  }

  .form-check-input[type="checkbox"]:not(:checked):disabled+label:before {
    background-color: #bdbdbd;
    border: none;
  }

  .form-check-input[type="checkbox"]:checked+label:before {
    top: -4px;
    left: -5px;
    width: 12px;
    height: 1.375rem;
    border-top: 2px solid rgba(0, 0, 0, 0);
    border-right: 2px solid #1072e9;
    border-bottom: 2px solid #1072e9;
    border-left: 2px solid rgba(0, 0, 0, 0);
    transform: rotate(40deg);
    transform-origin: 100% 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .form-check-input[type="checkbox"]:checked:disabled+label:before {
    border-right: 2px solid #bdbdbd;
    border-bottom: 2px solid #bdbdbd;
  }

  .form-check-input[type="checkbox"]:disabled:not(:checked)+label:before {
    background-color: #bdbdbd;
    border-color: #bdbdbd;
  }

  .form-check-input[type="checkbox"]:disabled:not(:checked)+label:after {
    background-color: #bdbdbd;
    border-color: #bdbdbd;
  }

  .form-check-input[type="checkbox"]:disabled:checked+label:before {
    background-color: rgba(0, 0, 0, 0);
  }

  .form-check-input[type="checkbox"]:disabled:checked+label:after {
    background-color: #bdbdbd;
    border-color: #bdbdbd;
  }

  .form-check-input[type="checkbox"]:focus:not(:checked)+label::before {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .form-check-input[type="checkbox"]:focus:checked+label::before {
    border-color: #007bff;
    box-shadow: 3px 2px 0 0 rgba(0, 123, 255, 0.25);
    border-top: 2px solid rgba(0, 0, 0, 0);
    border-left: 2px solid rgba(0, 0, 0, 0);
  }

  button:focus {
    outline: 0 !important;
  }

  .font-middle {
    font-size: 1rem !important;
  }

  .font-extralarge {
    font-size: 1.25rem !important;
  }

  .font-small {
    font-size: 0.75rem !important;
  }

  .white-text {
    color: #fff;
  }

  .custom-grey-text {
    color: rgba(84, 110, 122, 0.87);
  }

  .custom-grey-5-text {
    color: #738a97;
  }

  .p-0 {
    padding: 0 !important;
  }

  .px-0 {
    padding-right: 0 !important;
  }

  .pb-0 {
    padding-bottom: 0 !important;
  }

  .px-0 {
    padding-left: 0 !important;
  }

  .m-0 {
    margin: 0 !important;
  }

  .mt-0 {
    margin-top: 0 !important;
  }

  .mx-0 {
    margin-right: 0 !important;
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  .mx-0 {
    margin-left: 0 !important;
  }

  .mt-1 {
    margin-top: 0.25rem !important;
  }

  .mb-1 {
    margin-bottom: 0.25rem !important;
  }

  .py-2 {
    padding-top: 0.5rem !important;
  }

  .py-2 {
    padding-bottom: 0.5rem !important;
  }

  .mr-2 {
    margin-right: 0.5rem !important;
  }

  .mb-2 {
    margin-bottom: 0.5rem !important;
  }

  .ml-2 {
    margin-left: 0.5rem !important;
  }

  .p-3 {
    padding: 1rem !important;
  }

  .pt-3 {
    padding-top: 1rem !important;
  }

  .pr-3,
  .px-3 {
    padding-right: 1rem !important;
  }

  .pb-3 {
    padding-bottom: 1rem !important;
  }

  .px-3 {
    padding-left: 1rem !important;
  }

  .mt-3,
  .my-3 {
    margin-top: 1rem !important;
  }

  .mb-3,
  .my-3 {
    margin-bottom: 1rem !important;
  }

  .py-4 {
    padding-top: 1.5rem !important;
  }

  .py-4 {
    padding-bottom: 1.5rem !important;
  }

  .pl-4 {
    padding-left: 1.5rem !important;
  }

  .my-4 {
    margin-top: 1.5rem !important;
  }

  .mr-4 {
    margin-right: 1.5rem !important;
  }

  .mb-4,
  .my-4 {
    margin-bottom: 1.5rem !important;
  }

  .pt-5,
  .py-5 {
    padding-top: 2rem !important;
  }

  .py-5 {
    padding-bottom: 2rem !important;
  }

  .mb-5 {
    margin-bottom: 2rem !important;
  }

  @media (min-width: 576px) {
    .mr-sm-4 {
      margin-right: 1.5rem !important;
    }
  }

  @media (min-width: 768px) {
    .ml-md-3 {
      margin-left: 1rem !important;
    }

    .py-md-4 {
      padding-top: 1.5rem !important;
    }

    .py-md-4 {
      padding-bottom: 1.5rem !important;
    }
  }

  .mb-n3 {
    margin-bottom: -1rem !important;
  }

  body a {
    color: #1072e9;
  }

  body a:hover {
    color: #1072e9;
  }

  body a:hover img {
    opacity: 0.7;
  }

  body a:not([href]):not([tabindex]):focus,
  body a:not([href]):not([tabindex]):hover {
    color: #1072e9;
  }

  .bg-grey-5 {
    background-color: #738a97;
  }

  .bg-grey-6 {
    background-color: #1072e9;
  }

  .material-icons {
    vertical-align: bottom;
    cursor: pointer;
  }

  .material-icons.md-dark {
    color: rgba(0, 0, 0, 0.54);
  }

  .material-icons.md-grey {
    color: rgba(0, 0, 0, 0.38);
  }

  .material-icons.md-18 {
    font-size: 18px;
  }

  .calender-icon {
    cursor: default;
  }

  @media (max-width: 767px) {
    .d-less-than-md-none {
      display: none;
    }
  }

  .vertical-middle {
    vertical-align: middle !important;
  }

  .vertical-baseline {
    vertical-align: baseline;
  }

  .btn {
    line-height: 1;
    text-transform: none;
  }

  .btn:hover,
  .btn:active,
  .btn:focus {
    opacity: 0.7;
  }

  .btn[class*="btn-outline-"]:hover,
  .btn[class*="btn-outline-"]:active,
  .btn[class*="btn-outline-"]:focus {
    box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18),
      0 4px 15px 0 rgba(0, 0, 0, 0.15);
    outline: 0;
    opacity: 1;
  }

  .badge-pill {
    padding: 0.1rem 0.5rem;
    color: #fff;
  }

  .inline-unit-label {
    margin: 0;
  }







  main {
    margin-top: 79px;
  }

  main .grabient {
    min-height: 50vh;
  }

  @media (max-width: 1024px) and (min-width: 768px) {
    main {
      margin-top: 120px;
    }
  }

  @media (max-width: 767px) {
    main {
      margin-top: 150px;
    }
  }

  .title {

    background-color: #1072e9 !important;


    background-size: cover;
  }

  .title h1 {
    color: #fff;
  }

  @media (max-width: 767px) {
    .title h1 {
      font-size: 1.8rem;
    }
  }

  .fixed_banner {
    bottom: 14px;
    display: flex;
    left: 14px;
    opacity: 1;
    position: fixed;
    transition: opacity 400ms ease-in-out,
      transform 600ms cubic-bezier(0.165, 0.84, 0.44, 1);
    width: 350px;
    z-index: 99;
  }

  .fixed_banner img {
    border-radius: 4px;
    width: 100%;
  }

  @media screen and (max-width: 767px) {
    .fixed_banner {
      display: none;
    }
  }

  :focus {
    outline: 0;
  }

  .form-control:focus {
    box-shadow: none !important;
    border-color: #1072e9;
  }

  input.form-control[type="text"] {
    border: none;
    border-bottom: 1px solid #1072e9;
    border-radius: 0;
    background-color: #eee;
    color: rgba(0, 0, 0, 0.87);
    height: inherit;
    padding: 0.75rem 0.75rem;
    cursor: text;
  }

  textarea.form-control {
    border-color: #1072e9 !important;
  }

  .custom-checkbox label[class*="custom-control-label"] {
    cursor: pointer;
  }

  .custom-control-label::before {
    width: 1.125rem;
    height: 1.125rem;
    background-color: rgba(0, 0, 0, 0);
    border: 2px solid #d5d9db;
    top: 3px;
    box-shadow: none !important;
  }

  .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: #1072e9;
    border-color: #1072e9;
  }

  .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background: url(https://assign-navi.jp/assets/img/common/check_white.png) !important;
    background-size: contain !important;
    top: 3px;
  }

  .custom-control-input:focus:not(:checked)~.custom-control-label::before {
    border-color: #d5d9db;
  }

  .card-header {
    padding: 1rem;
    background: #1072e9;
    border-bottom: none;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-content {
    padding: 1rem;
  }

  .modal-header {
    padding: 2rem;
    border-bottom: none;
  }

  .modal-body {
    padding: 0 2rem 2rem;
  }

  @media (max-width: 767px) {
    .modal-header {
      padding: 2rem 1rem;
      border-bottom: none;
    }

    .modal-body {
      padding: 0 1rem 2rem;
    }
  }

  ul {
    list-style: none;
    padding: 0;
  }

  .border-bottom {
    border-bottom: 1px solid #e6eaec !important;
  }

  table.table .th-extrasm {
    min-width: 4rem;
  }

  @media (max-width: 767px) {
    .btn-outline-default:hover {
      border-color: #1072e9 !important;
      background-color: inherit !important;
      color: #1072e9 !important;
    }
  }

  .form-check-input[type="checkbox"]:focus:not(:checked)+label::before {
    border-color: #8a8a8a;
  }

  .form-check-input[type="checkbox"]:focus:checked+label::before {
    border-right-color: #1072e9;
    border-bottom-color: #1072e9;
  }

  .manage-index-title {
    line-height: 1.8;
  }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
  .material-icons {
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
  }
}

/*! CSS Used fontfaces */
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format("woff2");
}

.input-calendar {
  position: relative;
  display: inline-block;
  width: 100%;
}

.input-calendar-input-holder{
  width: 100%;
}

.input-calendar-icon-holder{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 2.5rem;
}

/* Mobile responsive for opportunities/manage/index - MOBILE ONLY */
@media (max-width: 767px) {
    /* 1. Search button - change to floating icon */
    .col-12.d-block.d-md-none.mb-4 .search-toggle {
        position: fixed !important;
        top: 32% !important;
        right: 15px !important;
        z-index: 999 !important;
        background: #1072e9 !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        width: 50px !important;
        height: 50px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
        cursor: pointer !important;
        font-size: 0 !important;
        text-indent: -9999px !important;
        overflow: hidden !important;
    }

    .col-12.d-block.d-md-none.mb-4 .search-toggle::before {
        content: "search" !important;
        font-family: "Material Icons" !important;
        font-size: 24px !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        text-indent: 0 !important;
    }

    /* 2. Table container - completely fixed frame like active page */
    .row.d-none.d-md-block {
        display: block !important;
        position: relative !important;
        margin: 20px 0 0 0 !important;
        padding: 0 !important;
        border-radius: 0.5rem !important;
        height: 282px !important; /* Fixed height for table container */
        overflow: hidden !important; /* Hide outer scroll completely */
        width: calc(100vw - 26px) !important; /* Full width minus margins */
        left: -2px !important;
    }

    /* Table wrapper with internal scroll only */
    .row.d-none.d-md-block .col-12 {
        height: 100% !important;
        overflow-x: auto !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Override d-less-than-md-none to show table on mobile */
    .row.d-none.d-md-block .table.d-less-than-md-none {
        display: table !important;
        min-width: 800px !important;
        margin-bottom: 0 !important;
        height: auto !important;
    }

    /* Table header - keep original PC colors, make sticky within container */
    .row.d-none.d-md-block .table thead th {
        position: sticky !important;
        top: 0 !important;
        z-index: 10 !important;
        font-size: 12px !important;
        padding: 8px 4px !important;
        background-color: #1072e9 !important; /* Keep original header color */
    }

    .row.d-none.d-md-block .table tbody td {
        font-size: 12px !important;
        padding: 8px 4px !important;
        white-space: nowrap !important;
        border: 1px solid #dee2e6 !important;
    }

    /* 3. Hide mobile cards - use table instead */
    .d-md-none {
        display: none !important;
    }

    /* 4. Tab container adjustments */
    .tab-container {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 15px !important;
        margin-top: 0px !important;
    }

    .tab-button {
        height: 30px !important;
        font-size: 14px !important;
        width: 48% !important;
        pointer-events: auto !important; /* Enable click on mobile */
        cursor: pointer !important;
        opacity: 1 !important;
    }

    /* 5. Results and sort container - horizontal layout */
    .d-flex.align-items-center.my-4 {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-top: 75px !important;
        margin-bottom: 65px !important;
    }

    /* 6. Pagination adjustments */
    .pagination {
        justify-content: center !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        margin: 20px 0 !important;
        padding-left: 13px !important;
        padding-right: 15px !important;
    }

    .page-link {
        padding: 0.375rem 0.5rem !important;
        font-size: 0.875rem !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Fix z-index conflicts and overlapping */
    .tab-container {
        z-index: 1 !important;
        position: relative !important;
    }

    .d-flex.align-items-center.my-4 {
        z-index: 1 !important;
        position: relative !important;
    }

    .pagination {
        z-index: 1 !important;
        position: relative !important;
    }

    /* Ensure search form has highest z-index */
    #side-search {
        z-index: 10000 !important;
    }

    #side-search.active {
        z-index: 10001 !important;
    }

    .search-fixed-btn {
        z-index: 10002 !important;
    }

    /* 7. Dropdown mobile styling */
    .divCol-12.col-md-8.d-block {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 13px !important;
        padding-right: 15px !important;
        margin-bottom: 15px !important;
    }

    .divCol-12.col-md-8.d-block select,
    .divCol-12.col-md-8.d-block .dropdown-toggle {
        width: 100% !important;
        font-size: 14px !important;
        padding: 12px 15px !important;
        border-radius: 6px !important;
        border: 1px solid #ced4da !important;
        background-color: #fff !important;
        color: #495057 !important;
        height: auto !important;
        min-height: 45px !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-menu {
        width: 100% !important;
        max-height: 250px !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        border-radius: 6px !important;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
        z-index: 1050 !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-item {
        padding: 10px 15px !important;
        font-size: 14px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-item:hover,
    .divCol-12.col-md-8.d-block .dropdown-item:focus {
        background-color: #f8f9fa !important;
        color: #1072e9 !important;
    }

    .divCol-12.col-md-8.d-block .dropdown-item.active {
        background-color: #1072e9 !important;
        color: white !important;
    }
}

@media (max-width: 768px){
  /* Modal backdrop */
  #side-search.active{
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.5) !important; /* Dark backdrop */
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
  }

  /* Modal content container */
  #side-search.active .card {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    max-width: 400px !important;
    width: 100% !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    margin: 0 !important;
    position: relative !important;
  }

  #side-search.active .container{
    max-width: 100% !important;
    padding: 0 !important;
  }

  /* Hide mobile search buttons - use PC button instead */
  #side-search.active .search-fixed-btn{display: none !important;}

  /* Show PC search button on mobile when form is active */
  #side-search.active .d-none.d-md-block {
    display: block !important;
  }

  #side-search.active .side-card {
    padding: 20px !important;
    width: 100% !important;
  }

  /* Form styling to match PC version */
  #side-search.active .form-control {
    border: 1px solid #ced4da !important;
    border-radius: 0.25rem !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 14px !important;
  }

  #side-search.active .custom-control-label {
    font-size: 14px !important;
    color: #495057 !important;
  }

  #side-search.active .font-middle {
    font-size: 16px !important;
    font-weight: 500 !important;
    margin-bottom: 12px !important;
  }

  /* Compact spacing for modal */
  #side-search.active .mx-auto.mb-5 {
    margin-bottom: 1rem !important;
  }

  #side-search.active .mx-auto.mt-0.mb-3 {
    margin-bottom: 1rem !important;
  }

  /* Search button styling */
  #side-search.active .btn {
    padding: 8px 20px !important;
    font-size: 14px !important;
  }

  /* Mobile memo modal styling */
  @media (max-width: 767px) {
    .modal-dialog {
      margin: 1rem !important;
      max-width: calc(100% - 2rem) !important;
    }

    .modal-content {
      border-radius: 8px !important;
    }

    .modal-header {
      padding: 1.5rem 1rem !important;
      border-bottom: 1px solid #dee2e6 !important;
    }

    .modal-body {
      padding: 1rem !important;
    }

    .modal-title {
      font-size: 1.1rem !important;
    }

    .modal textarea.form-control {
      font-size: 14px !important;
      min-height: 120px !important;
    }

    .modal .btn {
      padding: 0.75rem 1.5rem !important;
      font-size: 14px !important;
    }
  }
  }

  @media screen and (min-width: 1024px) and (max-width: 1279px) {
    .tab-button{
      height: 3vh !important;
    }
  }