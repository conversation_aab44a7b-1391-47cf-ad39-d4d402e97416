import { createServiceMenuBreadcrumb } from "../utils/breadcrumbHelper.js";

const request_select = {
    'template' :  `
    <main class="pb-3 margin-header" id="vue-app" data-v-app="">
        ${createServiceMenuBreadcrumb('コンシェルジュサービス')}
        <div class="container-fluid title py-2 py-md-4">
            <h1 class="mb-0">コンシェルジュサービス</h1>
        </div>
        <div class="container-fluid grabient pt-5">
            <div class="row">
                <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                    <router-link :to="{name: 'matching_request', params: {type: 'opportunity'}}" class="btn btn-default btn-block btn-lg font-middle mt-3 mb-5 waves-effect waves-light p-3 text-white font-weight-bold">案件をお探しの方へ</router-link>
                </div>
                <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                    <router-link :to="{name: 'matching_request', params: {type: 'resume'}}" class="btn btn-default btn-block btn-lg font-middle mt-3 mb-5 waves-effect waves-light p-3 text-white font-weight-bold">人財をお探しの方へ</router-link>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/request_select.css"/>
    `,
}

export default request_select;