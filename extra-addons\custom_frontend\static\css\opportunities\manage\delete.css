/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    footer,
    header,
    main,
    nav {
        display: block;
    }

    body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        text-align: left;
        background-color: #fff;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h1,
    h4 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    dl,
    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul ul {
        margin-bottom: 0;
    }

    dt {
        font-weight: 700;
    }

    dd {
        margin-bottom: .5rem;
        margin-left: 0;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    img {
        vertical-align: middle;
        border-style: none;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button,
    input,
    textarea {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button,
    input {
        overflow: visible;
    }

    button {
        text-transform: none;
    }

    [type=button],
    [type=submit],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    [type=submit]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    textarea {
        overflow: auto;
        resize: vertical;
    }

    h1,
    h4 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    .small {
        font-size: 80%;
        font-weight: 400;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12,
    .col-6,
    .col-8,
    .col-lg-3,
    .col-lg-8,
    .col-md-10,
    .col-md-4,
    .col-md-6,
    .col-sm-12,
    .col-sm-6 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 576px) {
        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-sm-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    @media (min-width: 768px) {
        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-md-10 {
            flex: 0 0 83.333333%;
            max-width: 83.333333%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-lg-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }
    }

    .form-control {
        display: block;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        padding: .375rem .75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-control {
            transition: none;
        }
    }

    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .form-control:disabled {
        background-color: #e9ecef;
        opacity: 1;
    }

    textarea.form-control {
        height: auto;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .dropdown {
        position: relative;
    }

    .navbar {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: .5rem 1rem;
    }

    .navbar-brand {
        display: inline-block;
        padding-top: .3125rem;
        padding-bottom: .3125rem;
        margin-right: 1rem;
        font-size: 1.25rem;
        line-height: inherit;
        white-space: nowrap;
    }

    .navbar-brand:focus,
    .navbar-brand:hover {
        text-decoration: none;
    }

    .navbar-nav {
        display: flex;
        flex-direction: column;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    @media (min-width: 768px) {
        .navbar-expand-md {
            flex-flow: row nowrap;
            justify-content: flex-start;
        }

        .navbar-expand-md .navbar-nav {
            flex-direction: row;
        }
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .card-body {
        flex: 1 1 auto;
        min-height: 1px;
        padding: 1.25rem;
    }

    .progress {
        display: flex;
        height: 1rem;
        overflow: hidden;
        line-height: 0;
        font-size: .75rem;
        background-color: #e9ecef;
        border-radius: .25rem;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-dialog-centered {
        display: flex;
        align-items: center;
        min-height: calc(100% - 1rem);
    }

    .modal-dialog-centered::before {
        display: block;
        height: calc(100vh - 1rem);
        height: -webkit-min-content;
        height: -moz-min-content;
        height: min-content;
        content: "";
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        .modal-dialog-centered {
            min-height: calc(100% - 3.5rem);
        }

        .modal-dialog-centered::before {
            height: calc(100vh - 3.5rem);
            height: -webkit-min-content;
            height: -moz-min-content;
            height: min-content;
        }
    }

    @media (min-width: 992px) {
        .modal-lg {
            max-width: 800px;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .rounded-circle {
        border-radius: 50% !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    @media (min-width: 1200px) {
        .d-xl-none {
            display: none !important;
        }

        .d-xl-inline-block {
            display: inline-block !important;
        }

        .d-xl-block {
            display: block !important;
        }
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .float-right {
        float: right !important;
    }

    .fixed-top {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 1030;
    }

    .shadow {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .my-0 {
        margin-top: 0 !important;
    }

    .mb-0,
    .my-0 {
        margin-bottom: 0 !important;
    }

    .mr-1 {
        margin-right: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .ml-1 {
        margin-left: 0.25rem !important;
    }

    .mt-2 {
        margin-top: 0.5rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .pt-1 {
        padding-top: 0.25rem !important;
    }

    .p-2 {
        padding: 0.5rem !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .pl-2 {
        padding-left: 0.5rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3 {
        padding-bottom: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .mr-auto,
    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 768px) {
        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    .font-weight-bold {
        font-weight: 700 !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        img {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }

        body {
            min-width: 992px !important;
        }

        .navbar {
            display: none;
        }
    }

    .black-text {
        color: #000 !important;
    }

    .white {
        background-color: #fff !important;
    }

    .white-text {
        color: #fff !important;
    }

    .primary-color-dark {
        background-color: #0d47a1 !important;
    }

    .z-depth-1 {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12) !important;
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    body {
        font-family: "Roboto", sans-serif;
        font-weight: 300;
    }

    h1,
    h4 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn.btn-sm {
        padding: .5rem 1.6rem;
        font-size: .64rem;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn.btn-sm[class*=btn-outline-] {
        padding-top: .38rem;
        padding-bottom: .38rem;
    }

    .btn-default {
        color: #fff !important;
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    }

    .btn-default:hover {
        color: #fff;
        background-color: #61b8f7;
    }

    .btn-default:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:focus,
    .btn-default:active {
        background-color: #005650;
    }

    .btn-default:not([disabled]):not(.disabled):active {
        background-color: #005650 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #1072e9 !important;
    }

    .btn-outline-default:hover,
    .btn-outline-default:focus,
    .btn-outline-default:active,
    .btn-outline-default:active:focus {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
    }

    .btn-outline-default:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #78909c !important;
    }

    .btn-outline-blue-grey:hover,
    .btn-outline-blue-grey:focus,
    .btn-outline-blue-grey:active,
    .btn-outline-blue-grey:active:focus {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .navbar {
        font-weight: 300;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    @media (min-width: 600px) {
        .navbar.scrolling-navbar {
            padding-top: 12px;
            padding-bottom: 12px;
            transition: background .5s ease-in-out, padding .5s ease-in-out;
        }

        .navbar.scrolling-navbar .navbar-nav>li {
            transition-duration: 1s;
        }
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    footer.page-footer {
        bottom: 0;
        color: #fff;
    }

    footer.page-footer .container-fluid {
        width: auto;
    }

    footer.page-footer a {
        color: #fff;
    }

    button,
    html [type=button],
    [type=submit] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    button:focus {
        outline: 0 !important;
    }

    .drag-target {
        position: fixed;
        top: 0;
        z-index: 998;
        width: 10px;
        height: 100%;
    }

    .drag-target {
        position: fixed;
        top: 0;
        z-index: 998;
        width: 10px;
        height: 100%;
    }

    .md-progress {
        position: relative;
        display: block;
        width: 100%;
        height: .25rem;
        margin-bottom: 1rem;
        overflow: hidden;
        background-color: #eee;
        box-shadow: none;
    }

    .md-progress .indeterminate {
        background-color: #90caf9;
    }

    .md-progress .indeterminate:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        content: "";
        background-color: inherit;
        -webkit-animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
        animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
        will-change: left, right;
    }

    .md-progress .indeterminate:after {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        content: "";
        background-color: inherit;
        -webkit-animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        -webkit-animation-delay: 1.15s;
        animation-delay: 1.15s;
        will-change: left, right;
    }

    .bold {
        font-weight: 500;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-default {
        font-size: .875rem !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-large {
        font-size: 1.125rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .white-text {
        color: #fff;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .my-0 {
        margin-top: 0 !important;
    }

    .mb-0,
    .my-0 {
        margin-bottom: 0 !important;
    }

    .pt-1 {
        padding-top: .25rem !important;
    }

    .mr-1 {
        margin-right: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .ml-1 {
        margin-left: .25rem !important;
    }

    .p-2 {
        padding: .5rem !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: .5rem !important;
    }

    .pl-2 {
        padding-left: .5rem !important;
    }

    .mt-2 {
        margin-top: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3 {
        padding-bottom: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .mt-6 {
        margin-top: 2.5rem !important;
    }

    @media (min-width: 768px) {
        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    body {
        font-family: none;
        font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
        font-size: .875rem;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        word-wrap: break-word;
        word-break: break-all;
        transition: color .3s ease 0s;
        height: auto;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    body a:hover img {
        opacity: .7;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-blue-grey {
        color: #546e7a;
    }

    .material-icons.md-36 {
        font-size: 36px;
    }

    .vertical-middle {
        vertical-align: middle !important;
    }

    .vertical-text-bottom {
        vertical-align: text-bottom;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .btn.btn-sm {
        padding: .5rem 1rem;
        font-size: .875rem;
    }

    body header a {
        color: rgba(0, 0, 0, 0.87);
        overflow: visible;
    }

    body header a:hover,
    body header a:hover .material-icons {
        opacity: .7;
        text-decoration: none;
    }

    body header .navbar.scrolling-navbar {
        padding: 28px 32px;
    }

    @media (min-width: 768px) {
        body header .navbar.scrolling-navbar {
            height: 80px;
        }
    }

    body header .navbar-brand img {
        width: 113px;
        vertical-align: baseline;
    }

    @media (min-width: 768px) {
        body header .navbar-brand img {
            width: 8.5rem;
            margin-right: 1.5rem;
            margin-bottom: 0;
            vertical-align: sub;
        }
    }

    body header .navbar-left>ul {
        margin: 0;
    }

    body header .navbar-left>ul li {
        display: inline-block;
        position: relative;
        line-height: 1.7;
    }

    body header .navbar-left>ul li span {
        cursor: pointer;
    }

    body header .navbar-left>ul li ul {
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        background-color: #fff;
        display: none;
        position: absolute;
        left: 15px;
    }

    body header .navbar-left>ul li ul li {
        display: block;
        padding: 6px 0;
        margin: 0;
    }

    body header .navbar-left>ul li ul li a {
        padding: 0 20px;
        display: block;
        text-decoration: none;
        white-space: nowrap;
    }

    body header .navbar-left>ul li:hover ul {
        display: block;
    }

    body header .navbar-left>ul li:hover ul a:hover {
        opacity: .7;
    }

    body header #login_link,
    body header #registration_link {
        width: 90px;
        height: 32px;
        font-weight: 700;
        font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
    }

    body header #login_link {
        line-height: .8;
    }

    body header #registration_link {
        line-height: 1;
    }

    body header .header-search-menu {
        font-weight: 500;
    }

    body header .header-search-menu a {
        color: #2a3942;
    }

    body header .header-search-menu span:hover {
        opacity: .7;
    }

    body header .navbar-nav {
        flex-direction: row;
    }

    body header .icon-wrapper {
        position: relative;
        float: left;
    }

    body header .icon-wrapper i {
        width: 28px;
        height: 28px;
        font-size: 28px;
        text-align: center;
        vertical-align: middle;
        color: #455965;
    }

    @media (min-width: 768px) {
        body header .icon-wrapper i {
            width: 32px;
            height: 32px;
            font-size: 32px;
        }
    }

    body header .link_area {
        font-size: 13px;
        line-height: 1;
    }

    body header .phone-area label {
        font-size: 18px;
    }

    body header .phone-icon {
        width: 17px;
        height: 17px;
        display: inline-block;
        margin: 0 4px 3px 0;
    }

    body header .phone-reception-time {
        font-weight: 500;
        line-height: normal;
    }

    body header .phone-outside-reception-hours {
        font-family: "YakuHanJP";
        font-feature-settings: "palt" 1;
    }

    body header .sp-phone-area {
        display: block;
        text-align: center;
        background-color: #eaf8f7;
    }

    body header .sp-phone-invitation-message {
        font-weight: 700;
        margin-bottom: 0;
        white-space: nowrap;
    }

    body header .sp-phone-icon {
        width: 20px;
        height: 20px;
        display: inline-block;
        margin: 0 4px 6px 0;
        vertical-align: bottom;
    }

    body header .sp-phone-number {
        margin-bottom: 0;
        font-size: 1.5rem;
        line-height: normal;
        white-space: nowrap;
    }

    body header .sp-phone-reception-time {
        color: #2a3942;
        font-weight: 500;
        line-height: normal;
    }

    body header .sp-phone-outside-reception-hours {
        color: #2a3942;
        font-family: "YakuHanJP";
        font-feature-settings: "palt" 1;
    }

    body header .sp-phone-area .tel-btn {
        font-size: 1.5rem;
        line-height: normal;
    }

    body header .line-height-normal {
        line-height: 1.6;
    }

    body header .line-height-mini {
        line-height: .7;
    }

    body header .roboto {
        font-family: "Roboto";
    }

    body header .header-color {
        color: #2a3942;
    }

    @media screen and (min-width: 481px) {
        body header .tel-btn {
            display: none;
        }
    }

    @media (max-width: 767px) {
        body header .navbar.scrolling-navbar {
            padding: 12px 16px;
            height: 64px;
        }
    }

    @media (max-width: 480px) {
        body header .text-phone-number {
            display: none;
        }
    }

    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }
    }

    .title {
        background-color: #1072e9 !important;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    footer {
        width: 100%;
        height: auto;
        background-color: #78909c;
    }

    :focus {
        outline: 0;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #1072e9;
    }

    textarea.form-control {
        border-color: #1072e9;
    }

    .modal {
        opacity: .5;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul,
    dl {
        list-style: none;
        padding: 0;
    }

    .account-side-scrollbar {
        overflow-y: scroll;
        height: 100vh;
        border-left: 2px solid #9da9b2;
    }

    .custom-side-nav {
        color: #2a3942;
        display: none;
        position: fixed;
        right: 0;
        width: 318px !important;
        z-index: 10;
        height: 100%;
    }

    .custom-side-nav .sign_in_area {
        text-align: center;
        margin-top: 26px;
        margin-bottom: 32px;
    }

    .custom-side-nav .sign_in_area a {
        width: 138px;
        height: 48px;
        line-height: 2.75rem;
    }

    .custom-side-nav ul li .border-bottom {
        margin-bottom: 12px;
    }

    .custom-side-nav ul li .side-nav-title {
        padding-bottom: 12px;
    }

    .custom-side-nav ul li .side-nav-title,
    .custom-side-nav ul li .side-nav-contents {
        cursor: pointer;
    }

    .custom-side-nav ul li .side-nav-title a,
    .custom-side-nav ul li .side-nav-contents a {
        color: #2a3942;
        padding: 0 0 12px 24px;
    }

    .custom-side-nav ul li .side-nav-title a:hover span,
    .custom-side-nav ul li .side-nav-contents a:hover span {
        opacity: .7;
    }

    .custom-side-nav ul li .side-nav-title a span,
    .custom-side-nav ul li .side-nav-contents a span {
        font-size: 1rem;
        line-height: 32px;
    }

    .accordion_close {
        cursor: pointer;
    }

    .progress-zindex {
        z-index: 1050;
    }

    .progress {
        border-radius: 1.25rem;
    }

    @media (max-width: 768px) {
        .progress {
            width: 263px;
            margin: 0 auto;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    @media (max-width: 767px) {
        .btn-outline-default:hover {
            border-color: #1072e9 !important;
            background-color: inherit !important;
            color: #1072e9 !important;
        }
    }

    @media (max-width: 767px) {
        .btn-outline-blue-grey:hover {
            border-color: #78909c !important;
            background-color: inherit !important;
            color: #78909c !important;
        }
    }

    .message-validator {
        color: #dc3545;
        text-align: center;
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used keyframes */
@-webkit-keyframes indeterminate {
    0% {
        right: 100%;
        left: -35%;
    }

    60% {
        right: -90%;
        left: 100%;
    }

    100% {
        right: -90%;
        left: 100%;
    }
}

@keyframes indeterminate {
    0% {
        right: 100%;
        left: -35%;
    }

    60% {
        right: -90%;
        left: 100%;
    }

    100% {
        right: -90%;
        left: 100%;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
    font-family: "Noto Sans Japanese";
    font-style: normal;
    font-weight: 300;
    font-display: auto;
    src: local("Noto Sans CJK JP Regular"), local("NotoSansCJKjp-Regular"), local("NotoSansJP-Regular"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff) format("woff");
}

@font-face {
    font-family: "Noto Sans Japanese";
    font-style: normal;
    font-weight: 700;
    src: local("Noto Sans CJK JP Bold"), local("NotoSansCJKjp-Bold"), local("NotoSansJP-Bold"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff) format("woff");
}

@font-face {
    font-family: MySansSerif;
    font-weight: normal;
    font-display: auto;
    src: local("HelveticaNeue"), local("Helvetica Neue"), local("Helvetica"), local("Arial");
}

@font-face {
    font-family: MySansSerif;
    font-weight: 700;
    font-display: auto;
    src: local("HelveticaNeueBold"), local("HelveticaNeue-Bold"), local("Helvetica Neue Bold"), local("HelveticaBold"), local("Helvetica-Bold"), local("Helvetica Bold"), local("Arial Bold");
}

@font-face {
    font-family: MySansSerif;
    font-weight: 900;
    font-display: auto;
    src: local("HelveticaNeueBlack"), local("HelveticaNeue-Black"), local("Helvetica Neue Black"), local("HelveticaBlack"), local("Helvetica-Black"), local("Helvetica Black"), local("Arial Black");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 100;
    font-display: auto;
    src: local("HiraginoSans-W1"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 200;
    font-display: auto;
    src: local("HiraginoSans-W2"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 300;
    font-display: auto;
    src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 400;
    font-display: auto;
    src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 500;
    font-display: auto;
    src: local("HiraginoSans-W5"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 600;
    src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 700;
    font-display: auto;
    src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 800;
    font-display: auto;
    src: local("HiraginoSans-W7"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 900;
    font-display: auto;
    src: local("HiraginoSans-W8"), local("Hiragino Sans");
}

@font-face {
    font-family: MyYugo;
    font-weight: normal;
    font-display: auto;
    src: local("YuGothic-Medium"), local("Yu Gothic Medium"), local("YuGothic-Regular");
}

@font-face {
    font-family: MyYugo;
    font-weight: bold;
    font-display: auto;
    src: local("YuGothic-Bold"), local("Yu Gothic");
}

@font-face {
    font-family: "YakuHanJP";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/YakuHanJP-Medium.woff) format("woff");
}

@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}