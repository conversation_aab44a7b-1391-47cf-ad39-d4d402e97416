import { createSingleBreadcrumb } from "../utils/breadcrumbHelper.js";

const guideline = {
    template: `
    <main class="pb-3 margin-header" id="vue-app" data-v-app="">
        ${createSingleBreadcrumb('利用方法')}
        <div class="container-fluid py-4">
            <div class="video-container">
                <div class="video-wrapper" v-if="videoUrl">
                    <video
                        :src="videoUrl"
                        controls
                        class="video-player"
                    ></video>
                </div>
                <div v-else class="no-video-message">
                    <p>動画が見つかりませんでした。</p>
                </div>
            </div>
        </div>
    </main>
    `,
	data() {
		return {
			videoUrl: null
		};
	},
	mounted() {
		this.getVideo();
	},
    methods: {
        async getVideo() {
            try {
                const response = await fetch('/guideline/video', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                });
                const data = await response.json();
                if (data.name) {
                    console.log("Video fetched successfully: ", data.name);
                    // console.log(data.message)
                    this.videoUrl = data.url;
                } else {
                    console.error("Failed to fetch Video", data.error);
                }
            } catch (error) {
                console.error('Failed to fetch api: ', error.message)
            }
        },
    }
}

const styles = `
<style>
.video-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.video-wrapper {
    width: 100%;
    background: #fff;
    border: 2px solid #000;
    padding: 2px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin: 40px auto;
}

.video-player {
    width: 100%;
    height: auto;
    display: block;
    background: #000;
}

.no-video-message {
    text-align: center;
    padding: 20px;
}
</style>
`

// Thêm styles vào document
document.head.insertAdjacentHTML('beforeend', styles);

export default guideline
