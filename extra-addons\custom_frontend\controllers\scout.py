from odoo import http
from odoo.http import request
import json
from odoo.http import Response
from datetime import datetime, timedelta
from .common import send_email_from_template

class ScoutController(http.Controller):
    @http.route('/api/get_opportunities_for_scout', type='http', auth='public', methods=['GET'], csrf=False)
    def get_opportunities_for_scout(self, **kwargs):
        # API to get list of opportunities for current user's company
        user_id = kwargs.get('user_id')
        keyword = kwargs.get('keyword')

        if not user_id or not str(user_id).isdigit():
            return Response(
                json.dumps({'success': False, 'message': 'Invalid or missing user ID'}),
                content_type='application/json',
                status=200
            )

        user = request.env['vit.users'].sudo().browse(int(user_id))
        if not user.exists():
            return Response(
                json.dumps({'success': False, 'message': 'User not found'}),
                content_type='application/json',
                status=200
            )

        company_id = user.company_id.id if user.company_id else None
        if not company_id:
            return Response(
                json.dumps({'success': False, 'message': 'User has no company'}),
                content_type='application/json',
                status=200
            )
        # Loại bỏ những opportunity đã đóng (status = 'close' hoặc status = '0')
        domain = [
            ('company_id', '=', company_id),
            ('status', 'not in', ['close', '0'])
        ]

        if keyword:
            domain.append(('subject', 'ilike', keyword))

        opportunities = request.env['vit.opportunities'].sudo().search(domain)

        result = []
        for opp in opportunities:
            result.append({
                'id': opp.id,
                'name': opp.subject,
                'company': user.company_id.name,
                'content': opp.requirements or ''
            })

        return Response(
            json.dumps({'success': True, 'data': result}),
            content_type='application/json',
            status=200
        )

    @http.route('/api/scout_resume', type='json', auth='public', methods=['POST'], csrf=False)
    def scout_resume(self):
        data = request.httprequest.get_json(silent=True)
        resume_id = data.get('resume_id')
        opportunity_id = data.get('opportunity_id')
        user_id = data.get('user_id')

        if not resume_id or not opportunity_id or not user_id:
            return {'success': False, 'message': 'Missing required fields'}

        try:
            resume = request.env['vit.partner'].sudo().browse(int(resume_id))
            if not resume.exists():
                return {'success': False, 'message': 'Resume not found'}

            opportunity = request.env['vit.opportunities'].sudo().browse(int(opportunity_id))
            if not opportunity.exists():
                return {'success': False, 'message': 'Opportunity not found'}

            # Create a new scout record
            scout = request.env['vit.scout'].sudo().create({
                'resumes_id': resume_id,
                'opportunities_id': opportunity_id,
                'created_by': user_id,
                'created_at': datetime.now(),
            })


            # Tính toán expire_date dựa trên logic mới cho status = 0
            created_at = datetime.now()
            application_plus_7_days = created_at + timedelta(days=7)

            # So sánh với ngày hết hạn ứng tuyển (expired_at)
            if opportunity.expired_at and opportunity.expired_at < application_plus_7_days:
                # Nếu expired_at sớm hơn, dùng expired_at
                calculated_expire_date = opportunity.expired_at
            else:
                # Nếu expired_at muộn hơn hoặc không có, dùng ngày ứng tuyển + 7 ngày
                calculated_expire_date = application_plus_7_days

            workflow = request.env['vit.workflow'].sudo().create({
                'opportunities_id': opportunity_id,
                'resumes_id': resume_id,
                'created_by': user_id,
                'status': 0,
                'expire_date': calculated_expire_date,
                'created_at': created_at,
            })

            if workflow and scout:
                partner = request.env['vit.partner'].sudo().browse(int(resume_id))
                partner_name = partner.initial_name if partner else 'なし'
                link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}resumes/{resume_id}/detail"

                user = request.env['vit.users'].sudo().browse(int(partner.created_by))
                company = user.company_id.name if user.company_id else 'なし'
                username = user.username if user.username else 'なし'
                user_email = user.email if user.email else 'なし'
                
                context_data = {
                    'company': company,
                    'username': username,
                    'partner_name': partner_name,
                    'link': link,
                    'user_email': user_email
                }
                
                result = send_email_from_template('Scout: Resume Notification', context_data)
                
                if result['success']:
                    return {'success': True, 'message': 'Scout created successfully', 'email_sent': user_email}
                else:
                    return {'success': False, 'message': result['message']}
            else:
                return {'success': False, 'message': 'Failed to create scout or workflow'}
        except Exception as e:
            return {'success': False, 'message': str(e)}

    @http.route('/api/check_scout', type='json', auth='public', methods=['POST'], csrf=False)
    def check_scout(self):
        data = request.httprequest.get_json(silent=True)
        resume_id = data.get('resume_id')
        opportunity_id = data.get('opportunity_id')
        user_id = data.get('user_id')

        if not resume_id or not opportunity_id or not user_id:
            return {'success': False, 'message': 'Missing required fields'}

        try:
            resume = request.env['vit.partner'].sudo().search([('id', '=', int(resume_id))])
            opportunity = request.env['vit.opportunities'].sudo().search([('id', '=', int(opportunity_id))])

            if not resume or not opportunity:
                return {'success': False, 'message': 'Resume or opportunity not found', 'redirect': '/notfound'}

            # Check if user has permission to access resume and opportunity
            if resume.created_by.id != int(user_id) and opportunity.created_by.id != int(user_id):
                return {
                    'success': False,
                    'message': 'Not authorized',
                    'redirect': '/notfound'
                }

            # Check if there's a scout record for this user and opportunity/resume
            scout = request.env['vit.scout'].sudo().search([
                ('opportunities_id', '=', int(opportunity_id)),
                ('resumes_id', '=', int(resume_id)),
                ('created_by', '=', int(user_id))
            ])

            if scout:
                return {'success': True, 'type': 'scout'}

            workflow = request.env['vit.workflow'].sudo().search([
                ('opportunities_id', '=', int(opportunity_id)),
                ('resumes_id', '=', int(resume_id)),
            ])

            if workflow:
                return {'success': True, 'type': 'hiring'}

            return {'success': True, 'type': 'none'}

        except Exception as e:
            return {'success': False, 'message': str(e)}

    @http.route('/api/get_scouted_opportunities', type='http', auth='public', methods=['GET'], csrf=False)
    def get_scouted_opportunities(self, **kwargs):
        # API to get list of opportunities that have already scouted a specific resume
        resume_id = kwargs.get('resume_id')

        if not resume_id or not str(resume_id).isdigit():
            return Response(
                json.dumps({'success': False, 'message': 'Invalid or missing resume ID'}),
                content_type='application/json',
                status=200
            )

        try:
            # Tìm tất cả các scout record cho resume này
            scouts = request.env['vit.scout'].sudo().search([
                ('resumes_id', '=', int(resume_id))
            ])

            # Lấy danh sách opportunity_id từ các scout record
            scouted_opportunity_ids = [scout.opportunities_id.id for scout in scouts if scout.opportunities_id]

            return Response(
                json.dumps({
                    'success': True,
                    'data': scouted_opportunity_ids
                }),
                content_type='application/json',
                status=200
            )
        except Exception as e:
            return Response(
                json.dumps({'success': False, 'message': str(e)}),
                content_type='application/json',
                status=500
            )






