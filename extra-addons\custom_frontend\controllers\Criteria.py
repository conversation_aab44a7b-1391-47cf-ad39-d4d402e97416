
from odoo import http
from odoo.http import request, Response
import json

class CriteriaController(http.Controller):

    @http.route('/api/get_criteria', type='json', auth="public", methods=['POST'], csrf=False)
    def getCriteria(self):
        criteria = request.env['vit.evaluation_criteria'].sudo().search([])

        result = []
        for crit in criteria:
            result.append({
                'id': crit.id,
                'name': crit.name,
                'description': crit.description or '',
                #'created_at': crit.created_at.strftime('%Y-%m-%d %H:%M:%S') if crit.created_at else '',
                #'updated_at': crit.updated_at.strftime('%Y-%m-%d %H:%M:%S') if crit.updated_at else '',
            })

        return result
    
    @http.route('/api/create_criteria', type='json', auth="public", methods=['POST'], csrf=False)
    def createCriteria(self):
        data = request.httprequest.get_json(silent=True)
        name = data.get('name')
        description = data.get('description', '')

        if not name:
            return {'success': False, 'message': 'Missing required field: name'}

        new_criteria = request.env['vit.evaluation_criteria'].sudo().create({
            'name': name,
            'description': description
        })

        if new_criteria:
            return {
            'success': True,
            'message': 'Create criteria successful!',
            'id': new_criteria.id,
            'name': new_criteria.name,
            'description': new_criteria.description or '',
        }
            
        return {'success': False, 'message': 'Create criteria failed!'}
             
    @http.route('/api/get_one_criteria', type='http', auth="public", methods=['GET'], csrf=False)
    def getCriteriaById(self, **kwargs):
        criteria_id = kwargs.get("id")

        if not criteria_id:
            return Response(json.dumps({'success': False, 'message': 'Missing id'}), content_type='application/json', status=400)

        criteria = request.env['vit.evaluation_criteria'].sudo().browse(int(criteria_id))

        if not criteria.exists():
            return Response(json.dumps({'success': False, 'message': 'Criteria not found'}), content_type='application/json', status=404)

        data = {
            'success': True,
            'id': criteria.id,
            'name': criteria.name,
            'description': criteria.description or ''
        }
        return Response(json.dumps(data), content_type='application/json', status=200)

        
    @http.route('/api/update_criteria', type='json', auth="public", methods=['POST'], csrf=False)
    def updateCriteria(self):
        data = request.httprequest.get_json(silent=True)
        criteria_id = data.get("id")
        name = data.get('name')
        description = data.get('description', '')

        criteria = request.env['vit.evaluation_criteria'].sudo().browse(criteria_id)

        if not criteria.exists():
            return {'success': False, 'message': 'Criteria not found'}

        if name:
            criteria.write({'name': name, 'description': description})

        return {
            'success': True,
            'message': 'Update criteria successful!',
            'id': criteria.id,
            'name': criteria.name,
            'description': criteria.description or '',
        }
        
    @http.route('/api/delete_criteria', type='json', auth="public", methods=['POST'], csrf=False)
    def deleteCriteria(self):
        data = request.httprequest.get_json(silent=True)
        criteria_id = data.get("id")
        criteria = request.env['vit.evaluation_criteria'].sudo().browse(criteria_id)

        if not criteria.exists():
            return {'success': False, 'message': 'Criteria not found'}

        criteria.unlink()

        return {'success': True, 'message': 'Criteria deleted successfully!'}

