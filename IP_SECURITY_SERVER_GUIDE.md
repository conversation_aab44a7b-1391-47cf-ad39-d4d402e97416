# IP Security Server Configuration Guide
## Nginx & System Level Security cho mi52.jp

### 🎯 <PERSON><PERSON><PERSON> ti<PERSON>u
<PERSON><PERSON><PERSON> hình server-level IP security để bổ sung cho application-level protection đã implement.

---

## 📋 CHECKLIST SERVER CONFIGURATION

### Phase 1: Nginx Rate Limiting & Geo-blocking
- [ ] Cấu hình rate limiting per IP
- [ ] Setup geo-blocking cho high-risk countries
- [ ] Thêm security headers
- [ ] Test nginx configuration

### Phase 2: Fail2ban Setup
- [ ] Cài đặt fail2ban
- [ ] Cấu hình jail cho Odoo
- [ ] Setup email alerts
- [ ] Test auto-banning

### Phase 3: Firewall Rules
- [ ] Cấu hình UFW basic rules
- [ ] Whitelist admin IPs
- [ ] Block known malicious IPs
- [ ] Setup monitoring

---

## 🔧 IMPLEMENTATION COMMANDS

### 1. Nginx Rate Limiting & Geo-blocking

#### Backup current config
```bash
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup.$(date +%Y%m%d_%H%M%S)
```

#### Install GeoIP module
```bash
sudo apt update
sudo apt install nginx-module-geoip geoip-database
```

#### Create rate limiting config
```bash
sudo tee /etc/nginx/conf.d/rate-limiting.conf > /dev/null <<'EOF'
# Rate limiting zones
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=payment:10m rate=2r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=100r/m;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=perip:10m;
limit_conn_zone $server_name zone=perserver:10m;
EOF
```

#### Create geo-blocking config
```bash
sudo tee /etc/nginx/conf.d/geo-blocking.conf > /dev/null <<'EOF'
# Load GeoIP module
load_module modules/ngx_http_geoip_module.so;

# GeoIP configuration
geoip_country /usr/share/GeoIP/GeoIP.dat;

# Map blocked countries
map $geoip_country_code $blocked_country {
    default 0;
    CN 1;  # China
    RU 1;  # Russia
    KP 1;  # North Korea
    IR 1;  # Iran
    # Add more high-risk countries as needed
}

# Map allowed countries for payments
map $geoip_country_code $payment_allowed {
    default 0;
    JP 1;  # Japan
    US 1;  # United States
    GB 1;  # United Kingdom
    DE 1;  # Germany
    FR 1;  # France
    AU 1;  # Australia
    CA 1;  # Canada
    SG 1;  # Singapore
}
EOF
```

#### Update main nginx config
```bash
sudo tee -a /etc/nginx/sites-available/default > /dev/null <<'EOF'

server {
    listen 80;
    server_name mi52.jp www.mi52.jp;
    
    # Include security configs
    include /etc/nginx/conf.d/security-headers.conf;
    
    # Connection limits
    limit_conn perip 10;
    limit_conn perserver 1000;
    
    # Block countries
    if ($blocked_country) {
        return 403 "Access denied from your country";
    }
    
    # Rate limiting for specific endpoints
    location /api/login {
        limit_req zone=login burst=3 nodelay;
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Country-Code $geoip_country_code;
    }
    
    location ~ ^/(mypage/plan|nyukai/plan) {
        # Strict payment geo-blocking
        if ($payment_allowed = 0) {
            return 403 "Payment not available from your location";
        }
        limit_req zone=payment burst=1 nodelay;
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Country-Code $geoip_country_code;
    }
    
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Country-Code $geoip_country_code;
    }
    
    location / {
        limit_req zone=general burst=20 nodelay;
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Country-Code $geoip_country_code;
    }
}
EOF
```

#### Test and reload nginx
```bash
sudo nginx -t
sudo systemctl reload nginx
```

### 2. Fail2ban Setup

#### Install fail2ban
```bash
sudo apt install fail2ban
```

#### Create Odoo jail config
```bash
sudo tee /etc/fail2ban/jail.d/odoo.conf > /dev/null <<'EOF'
[odoo-login]
enabled = true
port = http,https
filter = odoo-login
logpath = /var/log/odoo/security.log
maxretry = 5
findtime = 600
bantime = 3600
action = iptables-multiport[name=odoo-login, port="http,https", protocol=tcp]
         sendmail-whois[name=odoo-login, dest=<EMAIL>]

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 600
action = iptables-multiport[name=nginx-limit-req, port="http,https", protocol=tcp]
EOF
```

#### Create Odoo filter
```bash
sudo tee /etc/fail2ban/filter.d/odoo-login.conf > /dev/null <<'EOF'
[Definition]
failregex = .*"event": "security_violation".*"type": "failed_login".*"ip": "<HOST>"
            .*"event": "ip_access".*"success": false.*"ip": "<HOST>"
ignoreregex =
EOF
```

#### Start and enable fail2ban
```bash
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 3. UFW Firewall Setup

#### Enable UFW with basic rules
```bash
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (change port if needed)
sudo ufw allow 22/tcp

# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow specific admin IPs (replace with actual IPs)
# sudo ufw allow from YOUR_OFFICE_IP to any port 22
# sudo ufw allow from YOUR_ADMIN_IP to any port 8069

sudo ufw --force enable
```

### 4. Monitoring & Alerting

#### Create monitoring script
```bash
sudo tee /usr/local/bin/ip-security-monitor > /dev/null <<'EOF'
#!/bin/bash

LOG_FILE="/var/log/ip-security-monitor.log"
SECURITY_LOG="/var/log/odoo/security.log"

echo "$(date): Starting IP security monitoring" >> $LOG_FILE

# Check for suspicious activities in last hour
SUSPICIOUS_IPS=$(tail -1000 $SECURITY_LOG | grep -E "(security_violation|ip_access.*false)" | grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" | jq -r '.ip' | sort | uniq -c | sort -nr | head -10)

if [ ! -z "$SUSPICIOUS_IPS" ]; then
    echo "$(date): Suspicious IPs detected:" >> $LOG_FILE
    echo "$SUSPICIOUS_IPS" >> $LOG_FILE
    
    # Send alert email (configure sendmail first)
    # echo "Suspicious IP activity detected on mi52.jp: $SUSPICIOUS_IPS" | mail -s "Security Alert" <EMAIL>
fi

# Check fail2ban status
BANNED_IPS=$(sudo fail2ban-client status odoo-login | grep "Banned IP list" | cut -d: -f2)
if [ ! -z "$BANNED_IPS" ]; then
    echo "$(date): Currently banned IPs: $BANNED_IPS" >> $LOG_FILE
fi
EOF

sudo chmod +x /usr/local/bin/ip-security-monitor
```

#### Setup cron for monitoring
```bash
sudo tee -a /etc/cron.d/ip-security > /dev/null <<'EOF'
# IP security monitoring every 15 minutes
*/15 * * * * root /usr/local/bin/ip-security-monitor

# Daily security report
0 9 * * * root /usr/local/bin/zeus-security-check | mail -s "Daily Security Report" <EMAIL>
EOF
```

---

## 🧪 TESTING

### Test rate limiting
```bash
# Test login rate limit
for i in {1..10}; do curl -X POST http://mi52.jp/api/login -d '{"email":"test","password":"test"}' -H "Content-Type: application/json"; done

# Test payment geo-blocking
curl -H "X-Country-Code: CN" http://mi52.jp/mypage/plan
```

### Test fail2ban
```bash
# Check fail2ban status
sudo fail2ban-client status
sudo fail2ban-client status odoo-login

# Manually ban/unban IP
sudo fail2ban-client set odoo-login banip *******
sudo fail2ban-client set odoo-login unbanip *******
```

### Test firewall
```bash
sudo ufw status verbose
sudo iptables -L -n
```

---

## 📊 MONITORING COMMANDS

```bash
# Check nginx rate limiting logs
sudo tail -f /var/log/nginx/error.log | grep "limiting requests"

# Check security logs
sudo tail -f /var/log/odoo/security.log | jq .

# Check fail2ban logs
sudo tail -f /var/log/fail2ban.log

# Check banned IPs
sudo fail2ban-client banned

# Check firewall logs
sudo tail -f /var/log/ufw.log
```

---

## ⚠️ IMPORTANT NOTES

1. **Backup configs** trước khi thay đổi
2. **Test thoroughly** trước khi apply production
3. **Monitor logs** sau khi deploy
4. **Whitelist admin IPs** để tránh bị lock out
5. **Setup email alerts** để biết khi có security events

## 🆘 EMERGENCY RECOVERY

Nếu bị lock out:
```bash
# Disable fail2ban temporarily
sudo systemctl stop fail2ban

# Clear all bans
sudo fail2ban-client unban --all

# Disable UFW temporarily
sudo ufw disable

# Restart nginx with backup config
sudo cp /etc/nginx/sites-available/default.backup.YYYYMMDD_HHMMSS /etc/nginx/sites-available/default
sudo systemctl reload nginx
```
