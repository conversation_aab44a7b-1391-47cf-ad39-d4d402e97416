from odoo import http
from odoo.http import request
import json

class News(http.Controller):
    @http.route('/api/new_detail', type='http', auth="public", methods=['GET'], website=True)
    def get_by_id(self, **kwargs):
        try:
            news_id = kwargs.get('id')
            if not news_id:
                return request.make_response(
                    json.dumps({"success": False, "message": "Missing required parameter: id"}),
                    headers=[('Content-Type', 'application/json')]
                )

            # T<PERSON><PERSON> bản ghi theo ID
            news = request.env['vit.news'].sudo().browse(int(news_id))
            if not news.exists():
                return request.make_response(
                    json.dumps({"success": False, "message": "News not found"}),
                    headers=[('Content-Type', 'application/json')]
                )

            # Tr<PERSON> về chi tiết bản ghi
            data = {
                'id': news.id,
                'title': news.title,
                'content': news.content,
                'created_at': news.created_at.strftime('%Y-%m-%d %H:%M:%S') if news.created_at else None,
                'created_by': news.created_by,
                'updated_at': news.updated_at.strftime('%Y-%m-%d %H:%M:%S') if news.updated_at else None,
                'updated_by': news.updated_by
            }

            return request.make_response(
                json.dumps({"success": True, "data": data}),
                headers=[('Content-Type', 'application/json')]
            )

        except Exception as e:
            return request.make_response(
                json.dumps({"success": False, "message": str(e)}),
                headers=[('Content-Type', 'application/json')]
            )
