@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .pb-4 {
        padding-bottom: 1.5rem !important;
    }

    .text-right {
        text-align: right !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .pb-4 {
        padding-bottom: 1.5rem !important;
    }

    body a {
        color: #1072e9;
    }


    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-blue-grey {
        color: #546e7a;
    }

    .material-icons.md-36 {
        font-size: 36px;
    }

    :focus {
        outline: 0;
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

.container {
    max-width: 600px;
    margin: auto;
    /* căn giữa ngang */
    padding: 40px 20px;
    min-height: 600px;
}

.section {
    margin-bottom: 20px;
}

.title_bookmark {
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: bold;
}

/* Tab styles */
.bookmark-tabs {
    margin-bottom: 20px;
    margin-left: -4px;
}

.tab-container {
    display: flex;
    width: 100%;
    overflow: hidden;
    gap: 0.5%;
}

.tab-button {
    height: 5vh;
    width: 16%;
    border: 2px solid #4472C4;
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-top: 9px;
}

.tab-button:focus {
    outline: none; /* Remove outline when focused */
}

.tab-button.active {
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    color: white;
    border: 2px solid #4472C4 ;
    outline: none; /* Remove outline when active */
}

.tab-button:hover:not(.active) {
    background-color: #c5dbf8; /* Slightly darker on hover */
}

.link-box {
    background-color: #fff;
    color: #35a3e0;
    padding: 6px 10px;
    width: 50%;
    text-align: left;
    text-decoration: none !important;
    display: flex;
    /* đổi từ inline-block sang flex */
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    margin-left: -20px;
    border: transparent 1px solid;
    margin-top: 4px;
    cursor: pointer;
    font-weight: 400;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
}

.link-box :hover {
    color: #0885c9 !important;
}

.delete-icon i {
    color: #757575;
    font-size: 20px;
    cursor: pointer;
}

p {
    margin-left: 54px !important;
}

.pagination {
    margin-top: 10px;
    text-align: center;
    margin-left: -25px !important;
}

.pagination button {
    margin: 0 5px;
    padding: 5px 10px;
    border: none;
    background-color: #35a3e0;
    color: white;
    border-radius: 4px;
    cursor: pointer;
}

.pagination button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Breadcrumb styles moved to layout.css */

.container {
    max-width: 98% !important;
}
/* Mobile (< 768px) */
@media screen and (max-width: 767px) {
    main {
        margin-top: 0;
    }

    .container-fluid {
        padding-right: 15px;
        padding-left: 15px;
    }

    h1 {
        font-size: 1.8rem !important;  /* Tăng size chữ cho dễ đọc */
    }
    .title_bookmark {
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: bold;
    }
    /* Breadcrumb responsive styles moved to layout.css */

    .title {
        padding: 1.5rem 0;
    }

    .grabient {
        padding-top: 2rem !important;
    }

    /* Tăng khoảng cách giữa các phần tử để dễ bấm hơn */
    .mb-5 {
        margin-bottom: 2.5rem !important;
    }

    .mt-3 {
        margin-top: 1.5rem !important;
    }
    .container-fluid {
        padding-right: 20px;
        padding-left: 20px;
    }

    /* Tab styles for mobile */
    .tab-container {
        flex-direction: row;
        width: 100%;
        gap: 6px;
        padding: 0 5px;
        margin-top: 140px !important; /* Add padding to prevent tabs from being cut off */
    }

    .tab-button {
        padding: 8px 2px; /* Reduce horizontal padding */
        font-size: 13px; /* Slightly reduce font size */
        border-radius: 4px 4px 0 0;
        min-width: 0; /* Allow tabs to shrink if needed */
        flex-shrink: 1;
        width: 32.5%; /* Allow tabs to shrink */
        height: 30px;
    }

    .link-box {
        width: 100%;
        margin-left: 0;
    }

    p {
        margin-left: 0 !important;
    }

    .pagination {
        margin-left: 0 !important;
    }
}

/* Small Mobile (< 375px) - iPhone SE, iPhone 5, etc. */
@media screen and (max-width: 375px) {
    .tab-container {
        gap: 1px;
        padding: 0 2px;
    }

    .tab-button {
        padding: 8px 1px;
        font-size: 12px;
        border-width: 1px; /* Reduce border width for small screens */
    }
}

/* Extra Small Mobile (< 320px) - Old iPhones, very small screens */
@media screen and (max-width: 320px) {
    .tab-container {
        gap: 0;
        padding: 0 1px;
    }

    .tab-button {
        padding: 6px 1px;
        font-size: 11px;
        border-width: 1px;
        letter-spacing: -0.5px; /* Reduce letter spacing to fit text */
    }
}

/* Tablet (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
    .container-fluid {
        padding-right: 20px;
        padding-left: 20px;
    }

    h1 {
        font-size: 2.6rem !important;  /* Size chữ lớn cho dễ đọc */
    }
    /* Breadcrumb tablet styles moved to layout.css */

    /* Tab styles for tablet */
    .tab-container {
        width: 100%;
        gap: 4px;
    }

    .tab-button {
        padding: 10px;
        font-size: 16px;
        border-radius: 5px 5px 0 0;
    }

    .link-box {
        width: 100%;
        margin-left: 0;
    }

    p {
        margin-left: 0 !important;
    }

    .pagination {
        margin-left: 0 !important;
    }
}

/* Additional mobile improvements */
@media screen and (max-width: 767px) {
    /* Improve overall mobile layout */
    body {
        font-size: 16px;
        line-height: 1.5;
    }

    /* Add safe area for notched devices */
    @supports (padding: max(0px)) {
        .container-fluid {
            padding-left: max(15px, env(safe-area-inset-left)) !important;
            padding-right: max(15px, env(safe-area-inset-right)) !important;
        }
    }

    /* Improve scroll behavior */
    html {
        scroll-behavior: smooth;
    }

    /* Better focus states for accessibility */
    .tab-button:focus,
    .link-box:focus,
    .pagination button:focus {
        outline: 2px solid #35a3e0;
        outline-offset: 2px;
    }

    /* Loading states */
    .section:empty::after {
        content: "読み込み中...";
        display: block;
        text-align: center;
        padding: 40px 20px;
        color: #666;
        font-style: italic;
    }
}

@media screen and (min-width: 1024px) and (max-width: 1279px) {
  .tab-button{
    height: 3vh !important;
  }
}