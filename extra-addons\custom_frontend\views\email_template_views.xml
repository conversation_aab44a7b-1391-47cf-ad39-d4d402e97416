<?xml version="1.0"?>
<odoo>
    <!-- Form View -->
    <record id="vit_email_template_view_form" model="ir.ui.view">
        <field name="name">vit.email_template.form</field>
        <field name="model">vit.email_template</field>
        <field name="arch" type="xml">
            <form string="Email Template">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="subject"/>
                            <field name="is_active"/>
                        </group>
                        <group>
                            <field name="created_at"/>
                            <field name="updated_at"/>
                        </group>
                    </group>
                    <group>
                        <field name="body_html" widget="html" options="{'style-inline': true}"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List/Tree View -->
    <record id="vit_email_template_view_tree" model="ir.ui.view">
        <field name="name">vit.email_template.tree</field>
        <field name="model">vit.email_template</field>
        <field name="arch" type="xml">
            <list string="Email Templates">
                <field name="name"/>
                <field name="subject"/>
                <field name="is_active"/>
                <field name="created_at"/>
                <field name="updated_at"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="vit_email_template_action" model="ir.actions.act_window">
        <field name="name">Email Templates</field>
        <field name="res_model">vit.email_template</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- Menu -->
    <menuitem id="vit_email_template_menu_root" name="Email Management"/>
    <menuitem id="vit_email_template_menu" name="Email Templates" parent="vit_email_template_menu_root" action="vit_email_template_action"/>
</odoo> 