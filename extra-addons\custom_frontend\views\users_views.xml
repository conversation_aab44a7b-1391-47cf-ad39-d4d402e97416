<?xml version="1.0"?>
<odoo>
    <record id="vit_users_view_for_tree" model="ir.ui.view">
        <field name="name">vit.users.tree</field>
        <field name="model">vit.users</field>
        <field name="arch" type="xml">
            <list string="Channel">
                    <field name="username"/>
                    <field name="email"/>
                    <field name="password"/>
                    <field name="old_password"/>
                    <field name="role"/>
                    <field name="plan_id"/>
                    <field name="company_id"/>
                    <field name="created_at"/>
                    <field name="created_by"/>
                    <field name="updated_at"/>
                    <field name="updated_by"/>
            </list>
        </field>
    </record>

    <record id="vit_users_action" model="ir.actions.act_window">
        <field name="name">VIT Users</field>
        <field name="res_model">vit.users</field>
        <field name="view_mode">list</field>
    </record>
    <menuitem id="vit_users_menu_root" name="VIT Users"/>
    <menuitem id="vit_users_menu" name="Users" parent="vit_users_menu_root" action="vit_users_action"/>
</odoo>