/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {
    :root {
        --blue: #007bff;
        --indigo: #6610f2;
        --purple: #6f42c1;
        --pink: #e83e8c;
        --red: #dc3545;
        --orange: #fd7e14;
        --yellow: #ffc107;
        --green: #28a745;
        --teal: #20c997;
        --cyan: #17a2b8;
        --white: #fff;
        --gray: #6c757d;
        --gray-dark: #343a40;
        --primary: #007bff;
        --secondary: #6c757d;
        --success: #28a745;
        --info: #17a2b8;
        --warning: #ffc107;
        --danger: #dc3545;
        --light: #f8f9fa;
        --dark: #343a40;
        --breakpoint-xs: 0;
        --breakpoint-sm: 576px;
        --breakpoint-md: 768px;
        --breakpoint-lg: 992px;
        --breakpoint-xl: 1200px;
        --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    }

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    html {
        font-family: sans-serif;
        line-height: 1.15;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
    }

    footer,
    header,
    main,
    nav {
        display: block;
    }

    body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        text-align: left;
        background-color: #fff;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h1,
    h4 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    dl,
    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul ul {
        margin-bottom: 0;
    }

    dt {
        font-weight: 700;
    }

    dd {
        margin-bottom: .5rem;
        margin-left: 0;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    img {
        vertical-align: middle;
        border-style: none;
    }

    svg {
        overflow: hidden;
        vertical-align: middle;
    }

    table {
        border-collapse: collapse;
    }

    th {
        text-align: inherit;
        text-align: -webkit-match-parent;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button,
    input,
    textarea {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button,
    input {
        overflow: visible;
    }

    button {
        text-transform: none;
    }

    [type=button],
    [type=submit],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    [type=submit]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    input[type=checkbox] {
        box-sizing: border-box;
        padding: 0;
    }

    textarea {
        overflow: auto;
        resize: vertical;
    }

    h1,
    h4 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    .small {
        font-size: 80%;
        font-weight: 400;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12,
    .col-6,
    .col-lg-3,
    .col-lg-6,
    .col-md-6,
    .col-md-8 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 768px) {
        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-lg-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
    }

    .table td,
    .table th {
        padding: .75rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
    }

    .form-control {
        display: block;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        padding: .375rem .75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-control {
            transition: none;
        }
    }

    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .form-control:disabled {
        background-color: #e9ecef;
        opacity: 1;
    }

    textarea.form-control {
        height: auto;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .custom-control {
        position: relative;
        z-index: 1;
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5rem;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .custom-control-input {
        position: absolute;
        left: 0;
        z-index: -1;
        width: 1rem;
        height: 1.25rem;
        opacity: 0;
    }

    .custom-control-input:checked~.custom-control-label::before {
        color: #fff;
        border-color: #007bff;
        background-color: #007bff;
    }

    .custom-control-input:focus~.custom-control-label::before {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #80bdff;
    }

    .custom-control-input:not(:disabled):active~.custom-control-label::before {
        color: #fff;
        background-color: #b3d7ff;
        border-color: #b3d7ff;
    }

    .custom-control-input:disabled~.custom-control-label {
        color: #6c757d;
    }

    .custom-control-input:disabled~.custom-control-label::before {
        background-color: #e9ecef;
    }

    .custom-control-label {
        position: relative;
        margin-bottom: 0;
        vertical-align: top;
    }

    .custom-control-label::before {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        pointer-events: none;
        content: "";
        background-color: #fff;
        border: #adb5bd solid 1px;
    }

    .custom-control-label::after {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        content: "";
        background: 50%/50% 50% no-repeat;
    }

    .custom-checkbox .custom-control-label::before {
        border-radius: .25rem;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
    }

    .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
        background-color: rgba(0, 123, 255, 0.5);
    }

    .custom-control-label::before {
        transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    .disable {
        opacity: 0.5;
        pointer-events: none;
    }

    @media (prefers-reduced-motion: reduce) {
        .custom-control-label::before {
            transition: none;
        }
    }

    .navbar {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: .5rem 1rem;
    }

    .navbar-brand {
        display: inline-block;
        padding-top: .3125rem;
        padding-bottom: .3125rem;
        margin-right: 1rem;
        font-size: 1.25rem;
        line-height: inherit;
        white-space: nowrap;
    }

    .navbar-brand:focus,
    .navbar-brand:hover {
        text-decoration: none;
    }

    .navbar-nav {
        display: flex;
        flex-direction: column;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    @media (min-width: 768px) {
        .navbar-expand-md {
            flex-flow: row nowrap;
            justify-content: flex-start;
        }

        .navbar-expand-md .navbar-nav {
            flex-direction: row;
        }
    }

    .progress {
        display: flex;
        height: 1rem;
        overflow: hidden;
        line-height: 0;
        font-size: .75rem;
        background-color: #e9ecef;
        border-radius: .25rem;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-dialog-centered {
        display: flex;
        align-items: center;
        min-height: calc(100% - 1rem);
    }

    .modal-dialog-centered::before {
        display: block;
        height: calc(100vh - 1rem);
        height: -webkit-min-content;
        height: -moz-min-content;
        height: min-content;
        content: "";
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        .modal-dialog-centered {
            min-height: calc(100% - 3.5rem);
        }

        .modal-dialog-centered::before {
            height: calc(100vh - 3.5rem);
            height: -webkit-min-content;
            height: -moz-min-content;
            height: min-content;
        }
    }

    @media (min-width: 992px) {
        .modal-lg {
            max-width: 800px;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .border-0 {
        border: 0 !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-table-row {
            display: table-row !important;
        }

        .d-md-table-cell {
            display: table-cell !important;
        }

        .d-md-flex {
            display: flex !important;
        }
    }

    @media (min-width: 1200px) {
        .d-xl-none {
            display: none !important;
        }

        .d-xl-inline-block {
            display: inline-block !important;
        }

        .d-xl-block {
            display: block !important;
        }
    }

    .justify-content-end {
        justify-content: flex-end !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .float-left {
        float: left !important;
    }

    .float-right {
        float: right !important;
    }

    .fixed-top {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 1030;
    }

    .w-50 {
        width: 50% !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .mt-2,
    .my-2 {
        margin-top: 0.5rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2,
    .my-2 {
        margin-bottom: 0.5rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3,
    .mx-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mx-3 {
        margin-left: 1rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .pt-1 {
        padding-top: 0.25rem !important;
    }

    .px-1 {
        padding-right: 0.25rem !important;
    }

    .pb-1 {
        padding-bottom: 0.25rem !important;
    }

    .px-1 {
        padding-left: 0.25rem !important;
    }

    .pt-2,
    .py-2 {
        padding-top: 0.5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .pl-2 {
        padding-left: 0.5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .pt-4,
    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .mr-auto,
    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 768px) {
        .my-md-0 {
            margin-top: 0 !important;
        }

        .mb-md-0,
        .my-md-0 {
            margin-bottom: 0 !important;
        }

        .mr-md-5 {
            margin-right: 3rem !important;
        }

        .px-md-0 {
            padding-right: 0 !important;
        }

        .px-md-0 {
            padding-left: 0 !important;
        }

        .px-md-1 {
            padding-right: 0.25rem !important;
        }

        .px-md-1 {
            padding-left: 0.25rem !important;
        }

        .pb-md-3 {
            padding-bottom: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    .text-left {
        text-align: left !important;
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    .font-weight-bold {
        font-weight: 700 !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        thead {
            display: table-header-group;
        }

        img,
        tr {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }

        body {
            min-width: 992px !important;
        }

        .navbar {
            display: none;
        }

        .table {
            border-collapse: collapse !important;
        }

        .table td,
        .table th {
            background-color: #fff !important;
        }
    }

    .black-text {
        color: #000 !important;
    }

    .white {
        background-color: #fff !important;
    }

    .white-text {
        color: #fff !important;
    }

    .primary-color-dark {
        background-color: #0d47a1 !important;
    }

    .stylish-color {
        background-color: #4b515d !important;
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none !important;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    a:not([href]):not([tabindex]),
    a:not([href]):not([tabindex]):focus,
    a:not([href]):not([tabindex]):hover {
        color: inherit;
        text-decoration: none;
    }

    body {
        font-family: "Roboto", sans-serif;
        font-weight: 300;
    }

    h1,
    h4 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0 !important;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem !important;
        font-size: .81rem !important;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn-default {
        color: #fff !important;
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    }

    .btn-default:hover {
        color: #fff;
        background-color: #61b8f7;
    }

    .btn-default:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:focus,
    .btn-default:active {
        background-color: #005650;
    }

    .btn-default:not([disabled]):not(.disabled):active {
        background-color: #005650 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey {
        color: #fff !important;
        background-color: #78909c !important;
    }

    .btn-blue-grey:hover {
        color: #fff;
        background-color: #879ca7;
    }

    .btn-blue-grey:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:focus,
    .btn-blue-grey:active {
        background-color: #4a5b64;
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active {
        background-color: #4a5b64 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #78909c !important;
    }

    .btn-outline-blue-grey:hover,
    .btn-outline-blue-grey:focus,
    .btn-outline-blue-grey:active,
    .btn-outline-blue-grey:active:focus {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    @media (min-width: 600px) {
        .navbar.scrolling-navbar {
            padding-top: 12px;
            padding-bottom: 12px;
            transition: background .5s ease-in-out, padding .5s ease-in-out;
        }

        .navbar.scrolling-navbar .navbar-nav>li {
            transition-duration: 1s;
        }

        .navbar.scrolling-navbar.top-nav-collapse {
            padding-top: 5px;
            padding-bottom: 5px;
        }
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    footer.page-footer {
        bottom: 0;
        color: #fff;
    }

    footer.page-footer .container-fluid {
        width: auto;
    }

    footer.page-footer a {
        color: #fff;
    }

    table th {
        font-size: .9rem;
        font-weight: 400;
    }

    table td {
        font-size: .9rem;
        font-weight: 300;
    }

    table.table thead th {
        border-top: none;
    }

    table.table th,
    table.table td {
        padding-top: 1.1rem;
        padding-bottom: 1rem;
    }

    table.table a {
        margin: 0;
        color: #1072e9;
    }

    table .th-lg {
        min-width: 9rem;
    }

    table .th-sm {
        min-width: 6rem;
    }

    button,
    html [type=button],
    [type=submit] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    [type=checkbox]:not(:checked),
    [type=checkbox]:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    button:focus {
        outline: 0 !important;
    }

    .drag-target {
        position: fixed;
        top: 0;
        z-index: 998;
        width: 10px;
        height: 100%;
    }

    .drag-target {
        position: fixed;
        top: 0;
        z-index: 998;
        width: 10px;
        height: 100%;
    }

    .md-progress {
        position: relative;
        display: block;
        width: 100%;
        height: .25rem;
        margin-bottom: 1rem;
        overflow: hidden;
        background-color: #eee;
        box-shadow: none;
    }

    .md-progress .indeterminate {
        background-color: #90caf9;
    }

    .md-progress .indeterminate:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        content: "";
        background-color: inherit;
        -webkit-animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
        animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
        will-change: left, right;
    }

    .md-progress .indeterminate:after {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        content: "";
        background-color: inherit;
        -webkit-animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        -webkit-animation-delay: 1.15s;
        animation-delay: 1.15s;
        will-change: left, right;
    }

    .bold {
        font-weight: 500;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-default {
        font-size: .875rem !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .white-text {
        color: #fff;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-3-text {
        color: #d5d9db;
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .pt-1 {
        padding-top: .25rem !important;
    }

    .px-1 {
        padding-right: .25rem !important;
    }

    .pb-1 {
        padding-bottom: .25rem !important;
    }

    .px-1 {
        padding-left: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .pt-2,
    .py-2 {
        padding-top: .5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: .5rem !important;
    }

    .pl-2 {
        padding-left: .5rem !important;
    }

    .mt-2,
    .my-2 {
        margin-top: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2,
    .my-2 {
        margin-bottom: .5rem !important;
    }

    .p-3 {
        padding: 1rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .mr-3,
    .mx-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mx-3 {
        margin-left: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .pt-4,
    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .mt-6 {
        margin-top: 2.5rem !important;
    }

    .pl-7 {
        padding-left: 3rem !important;
    }

    @media (min-width: 768px) {
        .px-md-0 {
            padding-right: 0 !important;
        }

        .px-md-0 {
            padding-left: 0 !important;
        }

        .my-md-0 {
            margin-top: 0 !important;
        }

        .mb-md-0,
        .my-md-0 {
            margin-bottom: 0 !important;
        }

        .px-md-1 {
            padding-right: .25rem !important;
        }

        .px-md-1 {
            padding-left: .25rem !important;
        }

        .pb-md-3 {
            padding-bottom: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .mr-md-5 {
            margin-right: 2rem !important;
        }
    }

    :root {
        font-family: "Noto Sans Japanese", sans-serif, "HiraginoCustom", MyYugo;
    }

    body {
        font-family: none;
        font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo !important;
        font-size: .875rem;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        word-wrap: break-word;
        word-break: break-all;
        transition: color .3s ease 0s;
        height: auto;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    body a:hover img {
        opacity: .7;
    }

    body a:not([href]):not([tabindex]):focus,
    body a:not([href]):not([tabindex]):hover {
        color: #1072e9;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-blue-grey {
        color: #546e7a;
    }

    .material-icons.md-36 {
        font-size: 36px;
    }

    .user-icon {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
    }

    .vertical-middle {
        vertical-align: middle !important;
    }

    .vertical-text-bottom {
        vertical-align: text-bottom;
    }

    .vertical-baseline {
        vertical-align: baseline;
    }

    .z-2 {
        z-index: 2;
    }

    .clear:after {
        clear: both;
        content: "";
        display: block;
    }

    .overflow-visible {
        overflow: visible !important;
    }

    .btn {
        line-height: 1 !important;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    body header a {
        color: rgba(0, 0, 0, 0.87);
        overflow: visible;
    }

    body header a:hover,
    body header a:hover .material-icons {
        opacity: .7;
        text-decoration: none;
    }

    body header .navbar.scrolling-navbar {
        padding: 28px 32px;
    }

    @media (min-width: 768px) {
        body header .navbar.scrolling-navbar {
            height: 80px;
        }
    }

    body header .navbar-brand img {
        width: 113px;
        vertical-align: baseline;
    }

    @media (min-width: 768px) {
        body header .navbar-brand img {
            width: 8.5rem;
            margin-right: 1.5rem;
            margin-bottom: 0;
            vertical-align: sub;
        }
    }

    body header .navbar-left>ul {
        margin: 0;
    }

    body header .navbar-left>ul li {
        display: inline-block;
        position: relative;
        line-height: 1.7;
    }

    body header .navbar-left>ul li span {
        cursor: pointer;
    }

    body header .navbar-left>ul li ul {
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        background-color: #fff;
        display: none;
        position: absolute;
        left: 15px;
    }

    body header .navbar-left>ul li ul li {
        display: block;
        margin: 0;
    }

    body header .navbar-left>ul li ul li a {
        padding: 0 20px;
        display: block;
        text-decoration: none;
        white-space: nowrap;
    }

    body header .navbar-left>ul li:hover ul {
        display: block;
    }

    body header .navbar-left>ul li:hover ul a:hover {
        opacity: .7;
    }

    body header .header-search-menu {
        font-weight: 500;
    }

    body header .header-search-menu a {
        color: #2a3942;
    }

    body header .header-search-menu span:hover {
        opacity: .7;
    }

    body header .navbar-nav {
        flex-direction: row;
    }

    body header .icon-wrapper {
        position: relative;
        float: left;
    }

    body header .icon-wrapper i {
        width: 28px;
        height: 28px;
        font-size: 28px;
        text-align: center;
        vertical-align: middle;
        color: #455965;
    }

    @media (min-width: 768px) {
        body header .icon-wrapper i {
            width: 32px;
            height: 32px;
            font-size: 32px;
        }
    }

    body header .link_area {
        font-size: 13px;
        line-height: 1;
    }

    body header .phone-area label {
        font-size: 18px;
    }

    body header .phone-icon {
        width: 17px;
        height: 17px;
        display: inline-block;
        margin: 0 4px 3px 0;
    }

    body header .phone-reception-time {
        font-weight: 500;
        line-height: normal;
    }

    body header .phone-outside-reception-hours {
        font-family: "YakuHanJP";
        font-feature-settings: "palt" 1;
    }

    body header .sp-phone-area {
        display: block;
        text-align: center;
        background-color: #eaf8f7;
    }

    body header .sp-phone-invitation-message {
        font-weight: 700;
        margin-bottom: 0;
        white-space: nowrap;
    }

    body header .sp-phone-icon {
        width: 20px;
        height: 20px;
        display: inline-block;
        margin: 0 4px 6px 0;
        vertical-align: bottom;
    }

    body header .sp-phone-number {
        margin-bottom: 0;
        font-size: 1.5rem;
        line-height: normal;
        white-space: nowrap;
    }

    body header .sp-phone-reception-time {
        color: #2a3942;
        font-weight: 500;
        line-height: normal;
    }

    body header .sp-phone-outside-reception-hours {
        color: #2a3942;
        font-family: "YakuHanJP";
        font-feature-settings: "palt" 1;
    }

    body header .sp-phone-area .tel-btn {
        font-size: 1.5rem;
        line-height: normal;
    }

    body header .line-height-normal {
        line-height: 1.6;
    }

    body header .line-height-mini {
        line-height: .7;
    }

    body header .roboto {
        font-family: "Roboto";
    }

    body header .header-color {
        color: #2a3942;
    }

    @media screen and (min-width: 481px) {
        body header .tel-btn {
            display: none;
        }
    }

    @media (max-width: 767px) {
        body header .navbar.scrolling-navbar {
            padding: 12px 16px;
            height: 64px;
        }
    }

    @media (max-width: 480px) {
        body header .text-phone-number {
            display: none;
        }
    }







    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }
    }

    .title {

        background-color: #1072e9 !important;


        background-size: cover;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    .fix_right_buttom {
        position: fixed;
        right: 0;
        bottom: 0;
        z-index: 99;
        background: rgba(0, 0, 0, 0.7);
    }

    @media screen and (max-width: 768px) {
        .fix_right_buttom {
            position: fixed;
            right: 0;
            bottom: 0;
            z-index: 99;
        }
    }

    footer {
        width: 100%;
        height: auto;
        background-color: #78909c;
    }

    :focus {
        outline: 0;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #1072e9;
    }

    textarea.form-control {
        border-color: #1072e9;
    }

    .custom-checkbox label[class*=custom-control-label] {
        cursor: pointer;
    }

    .custom-control-label::before {
        width: 1.125rem !important;
        height: 1.125rem !important;
        background-color: rgba(0, 0, 0, 0);
        border: 2px solid #d5d9db !important;
        top: 3px !important;
        box-shadow: none !important;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
        background-color: #1072e9;
        border-color: #1072e9 !important;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background: url(https://assign-navi.jp/assets/img/common/check_white.png);
        background-size: contain;
        left: -1.4rem;
        top: 3px;
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #d5d9db;
    }

    .modal {
        opacity: .5;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul,
    dl {
        list-style: none;
        padding: 0;
    }

    .message-table tbody tr {
        background-color: #fff;
    }

    @media (max-width: 768px) {
        .message-table tr td:not(.status-cell) {
            padding-left: 0;
            padding-right: 0;
        }

        .message-table tr td:first-child,
        .message-table tr td:nth-child(2) {
            border-top: none;
        }

        .message-table tr td.status-cell {
            border-top: none;
        }

        .message-table tr td.status-cell:last-child {
            padding-right: 0;
        }
    }

    .account-side-scrollbar {
        overflow-y: scroll;
        height: 100vh;
        border-left: 2px solid #9da9b2;
    }

    .custom-side-nav {
        color: #2a3942;
        display: none;
        position: fixed;
        right: 0;
        width: 318px !important;
        z-index: 10;
        height: 100%;
    }

    .custom-side-nav ul li .user-icon {
        width: 2.5rem;
        height: 2.5rem;
    }

    .custom-side-nav ul li .border-bottom {
        margin-bottom: 12px;
    }

    .custom-side-nav ul li .side-nav-title {
        padding-bottom: 12px;
    }

    .custom-side-nav ul li .side-nav-title,
    .custom-side-nav ul li .side-nav-contents {
        cursor: pointer;
    }

    .custom-side-nav ul li .side-nav-title a,
    .custom-side-nav ul li .side-nav-contents a,
    .custom-side-nav ul li .side-nav-contents div {
        color: #2a3942;
        padding: 0 0 12px 24px;
    }

    .custom-side-nav ul li .side-nav-title a:hover span,
    .custom-side-nav ul li .side-nav-contents a:hover span,
    .custom-side-nav ul li .side-nav-contents div:hover span {
        opacity: .7;
    }

    .custom-side-nav ul li .side-nav-title a span,
    .custom-side-nav ul li .side-nav-contents a span,
    .custom-side-nav ul li .side-nav-contents div span {
        font-size: 1rem;
        line-height: 32px;
    }

    .accordion_close {
        cursor: pointer;
    }

    .progress-zindex {
        z-index: 1050;
    }

    .progress {
        border-radius: 1.25rem;
    }

    @media (max-width: 768px) {
        .progress {
            width: 263px;
            margin: 0 auto;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    @media (max-width: 767px) {
        .btn-outline-blue-grey:hover {
            border-color: #78909c !important;
            background-color: inherit !important;
            color: #78909c !important;
        }
    }

    .message-validator {
        color: #dc3545;
        text-align: center;
    }
}

/*! CSS Used from: https://assign-navi.jp/assets/message-e015f581a14e0af4d7d6000908dfa0386a2f75463c13c83f9735c97352dbe5fd.css ; media=screen */
@media screen {
    .clear {
        clear: both;
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used from: Embedded */
ul[data-v-6d56596f] {
    border-bottom: 1px solid #e6eaec;
}

ul li[data-v-6d56596f] {
    padding-bottom: 1rem;
    transition: opacity .2s ease-in-out;
}

ul li[data-v-6d56596f]:hover {
    cursor: pointer;
    opacity: .6;
    color: #1072e9;
    transition: opacity, color .2s ease-in-out;
}

.tab-active[data-v-6d56596f] {
    padding-bottom: calc(1rem - 2px);
    border-bottom: 2px solid #1072e9;
    color: #1072e9;
}

/*! CSS Used from: Embedded */
.separater[data-v-31c30a8b] {
    border-bottom: 1px solid #e6eaec;
}

.link-to-index[data-v-31c30a8b] {
    color: #1072e9;
}

.link-to-index[data-v-31c30a8b]:hover {
    color: #1072e9;
    background-color: #eaf8f7;
    opacity: .6;
}

.header-tab-menu-wrapper[data-v-31c30a8b] {
    position: absolute;
    background-color: #fff;
    width: 432px;
    right: 0;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .12), 0px 2px 5px 0px rgba(0, 0, 0, .16);
    border-radius: 4px;
    z-index: 100;
}

@media screen and (max-width: 768px) {
    .header-tab-menu-wrapper[data-v-31c30a8b] {
        position: fixed;
        max-width: 364px;
        right: 4px;
    }
}

/*! CSS Used from: Embedded */
.mr-20px[data-v-4197fd32] {
    margin-right: 20px;
}

/*! CSS Used from: https://www.gstatic.com/_/translate_http/_/ss/k=translate_http.tr.NJgGN_yGIWM.L.W.O/am=AAY/d=0/rs=AN8SPfrTSMIvWAFISYN4u74dPJrX0HgUsw/m=el_main_css */
.VIpgJd-yAWNEb-L7lbkb div,
.VIpgJd-yAWNEb-L7lbkb span,
.VIpgJd-yAWNEb-L7lbkb iframe,
.VIpgJd-yAWNEb-L7lbkb img,
.VIpgJd-yAWNEb-L7lbkb form {
    margin: 0;
    padding: 0;
    border: 0;
    font: inherit;
    font-size: 100%;
    vertical-align: baseline;
    text-align: left;
    line-height: normal;
}

.VIpgJd-yAWNEb-L7lbkb {
    color: #222;
    background-color: #fff;
    border: 1px solid #eee;
    box-shadow: 0 4px 16px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 4px 16px rgba(0, 0, 0, .2);
    -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, .2);
    display: none;
    font-family: arial;
    font-size: 10pt;
    width: 420px;
    padding: 12px;
    position: absolute;
    z-index: 10000;
}

.VIpgJd-yAWNEb-L7lbkb .VIpgJd-yAWNEb-nVMfcd-fmcmS {
    clear: both;
    font-size: 10pt;
    position: relative;
    text-align: justify;
    width: 100%;
}

.VIpgJd-yAWNEb-L7lbkb span:focus {
    outline: none;
}

.VIpgJd-yAWNEb-hvhgNd {
    font-family: "Google Sans", Arial, sans-serif;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-l4eHX-i3jM8c {
    position: absolute;
    top: 10px;
    left: 14px;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-k77Iif-i3jM8c {
    margin: 16px;
    padding: 0;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-IuizWc {
    margin: 0 0 0 36px;
    padding: 0;
    color: #747775;
    font-size: 14px;
    font-weight: 500;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-axAV1 {
    width: auto;
    padding: 12px 0 0;
    color: #1f1f1f;
    font-size: 16px;
    text-align: initial;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-N7Eqid {
    border-radius: 0 0 12px 12px;
    margin: 0;
    background: #f1f4f9;
    position: relative;
    min-height: 50px;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-N7Eqid-B7I4Od {
    display: inline-block;
    width: 77%;
    padding: 12px;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-UTujCb {
    color: #1f1f1f;
    font-size: 12px;
    font-weight: 500;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-eO9mKe {
    color: #444746;
    font-size: 12px;
    padding-top: 4px;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-xgov5 {
    position: absolute;
    top: 10px;
    right: 5px;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-THI6Vb {
    fill: #0b57d0;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-bgm6sf {
    margin: -4px 2px 0 0;
    padding: 2px 0 0;
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 24px;
    cursor: pointer;
    background: none;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-bgm6sf:hover {
    background: #e8ebec;
}

.VIpgJd-yAWNEb-hvhgNd .VIpgJd-yAWNEb-hvhgNd-aXYTce {
    display: none;
}

/*! CSS Used keyframes */
@-webkit-keyframes indeterminate {
    0% {
        right: 100%;
        left: -35%;
    }

    60% {
        right: -90%;
        left: 100%;
    }

    100% {
        right: -90%;
        left: 100%;
    }
}

@keyframes indeterminate {
    0% {
        right: 100%;
        left: -35%;
    }

    60% {
        right: -90%;
        left: 100%;
    }

    100% {
        right: -90%;
        left: 100%;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
    font-family: "Noto Sans Japanese";
    font-style: normal;
    font-weight: 300;
    font-display: auto;
    src: local("Noto Sans CJK JP Regular"), local("NotoSansCJKjp-Regular"), local("NotoSansJP-Regular"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff) format("woff");
}

@font-face {
    font-family: "Noto Sans Japanese";
    font-style: normal;
    font-weight: 700;
    src: local("Noto Sans CJK JP Bold"), local("NotoSansCJKjp-Bold"), local("NotoSansJP-Bold"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff) format("woff");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 100;
    font-display: auto;
    src: local("HiraginoSans-W1"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 200;
    font-display: auto;
    src: local("HiraginoSans-W2"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 300;
    font-display: auto;
    src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 400;
    font-display: auto;
    src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 500;
    font-display: auto;
    src: local("HiraginoSans-W5"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 600;
    src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 700;
    font-display: auto;
    src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 800;
    font-display: auto;
    src: local("HiraginoSans-W7"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 900;
    font-display: auto;
    src: local("HiraginoSans-W8"), local("Hiragino Sans");
}

@font-face {
    font-family: MyYugo;
    font-weight: normal;
    font-display: auto;
    src: local("YuGothic-Medium"), local("Yu Gothic Medium"), local("YuGothic-Regular");
}

@font-face {
    font-family: MyYugo;
    font-weight: bold;
    font-display: auto;
    src: local("YuGothic-Bold"), local("Yu Gothic");
}

@font-face {
    font-family: MySansSerif;
    font-weight: normal;
    font-display: auto;
    src: local("HelveticaNeue"), local("Helvetica Neue"), local("Helvetica"), local("Arial");
}

@font-face {
    font-family: MySansSerif;
    font-weight: 700;
    font-display: auto;
    src: local("HelveticaNeueBold"), local("HelveticaNeue-Bold"), local("Helvetica Neue Bold"), local("HelveticaBold"), local("Helvetica-Bold"), local("Helvetica Bold"), local("Arial Bold");
}

@font-face {
    font-family: MySansSerif;
    font-weight: 900;
    font-display: auto;
    src: local("HelveticaNeueBlack"), local("HelveticaNeue-Black"), local("Helvetica Neue Black"), local("HelveticaBlack"), local("Helvetica-Black"), local("Helvetica Black"), local("Arial Black");
}

@font-face {
    font-family: "YakuHanJP";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/YakuHanJP-Medium.woff) format("woff");
}

@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}