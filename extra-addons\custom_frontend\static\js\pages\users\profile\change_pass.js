import { userInfo } from "../../../router/router.js";
import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const change_pass = {
   'template': `
   <main class="pb-3 margin-header" id="vue-app">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '登録・管理', link: null },
            { text: '会社データ管理', link: null },
            { text: 'パスワード', link: null, current: true }
        ])}
        <div class="container-fluid">
            <div class="d-flex justify-content-end align-items-center">
                <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                <div class="mobile-menu-content">
                    <button class="mobile-menu-close" @click="closeMobileMenu">
                        <span></span>
                    </button>
                    <ul>
                        <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                        <li><a href="/companies/manage/edit">会社データ</a></li>
                        <li><a href="/users/edit">プロフィール</a></li>
                        <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                        <li><a class="active" href="/users/profile/edit_password">パスワード</a></li>
                        <li><a href="/setting_gmail">メール受信設定</a></li>
                        <li hidden><a href="/mypage/plan">プラン</a></li>
                        <li><a href="/plan/plant_out">退会</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="container-fluid grabient pt-5 position-relative">
            <div class="row mb-4 mb-md-0">
                <div class="d-md-block col-md-4 col-lg-3 side-menu-contents">
                    <div class="card px-3 pb-3 side-card collapsible">
                        <ul class="collapsible mb-0">
                            <div class="d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3 active" aria-current="page" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                            <li class="my-md-1" hidden><a class="d-block py-1 px-3" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                            <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                    <form class="edit_user" @submit.prevent="change_pass">
                        <div class="card mb-5 px-md-3">
                            <div class="card-body py-5">
                                <div class="mx-auto mb-5">
                                    <label class="font-middle mb-3" for="current_password_field">現在のパスワード<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <div class="password-field-container">
                                        <input class="form-control" autocomplete="off" id="old_password_field" :type="oldPasswordFieldType" v-model="old_pass">
                                        <i class="material-icons password-toggle-icon" @click="toggleOldPasswordVisibility">{{ oldPasswordFieldIcon }}</i>
                                    </div>
                                    <div class="text-danger text-left" v-if="!check_pass">現在のパスワードは不正な値です</div>
                                </div>
                                <div class="mx-auto mb-0">
                                    <label class="font-middle mb-3" for="password_field">新しいパスワード<span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span></label>
                                    <span class="custom-grey-6-text d-block mb-3">パスワードは8文字以上30文字以内で、小文字・大文字・数字・記号(!@#$%^&*)を含む必要があります。</span>
                                    <div class="password-field-container">
                                        <input class="form-control" autocomplete="off" id="new_password_field" :type="newPasswordFieldType" v-model="new_pass">
                                        <i class="material-icons password-toggle-icon" @click="toggleNewPasswordVisibility">{{ newPasswordFieldIcon }}</i>
                                    </div>
                                    <div class="text-danger text-left" v-if="!check_pass_new">パスワードは8文字以上30文字以内で、小文字・大文字・数字・記号(!@#$%^&*)を含む必要があります。</div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-4"><button name="button" type="submit" class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light">変更</button></div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css"/>
    <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css">
    <link rel="stylesheet" href="/custom_frontend/static/css/password-toggle.css"/>
   `,
    data(){
        return {
            old_pass: "",
            new_pass: "",
            email: userInfo ? userInfo.user_email : null,
            check_pass: true,
            check_pass_new: true,
            isMobileMenuOpen: false,
            oldPasswordFieldType: 'password', // Thêm biến để theo dõi loại trường mật khẩu cũ
            oldPasswordFieldIcon: 'visibility_off', // Biểu tượng mặc định là ẩn mật khẩu cũ
            newPasswordFieldType: 'password', // Thêm biến để theo dõi loại trường mật khẩu mới
            newPasswordFieldIcon: 'visibility_off', // Biểu tượng mặc định là ẩn mật khẩu mới
        }
    },
    methods: {
        // Phương thức để chuyển đổi hiển thị/ẩn mật khẩu cũ
        toggleOldPasswordVisibility() {
            this.oldPasswordFieldType = this.oldPasswordFieldType === 'password' ? 'text' : 'password';
            this.oldPasswordFieldIcon = this.oldPasswordFieldType === 'password' ? 'visibility_off' : 'visibility';
        },

        // Phương thức để chuyển đổi hiển thị/ẩn mật khẩu mới
        toggleNewPasswordVisibility() {
            this.newPasswordFieldType = this.newPasswordFieldType === 'password' ? 'text' : 'password';
            this.newPasswordFieldIcon = this.newPasswordFieldType === 'password' ? 'visibility_off' : 'visibility';
        },

        async validateInput() {
            let isValid = true;

            if (this.old_pass.trim() === '') {
                this.check_pass = false;
                isValid = false;
            } else {
                this.check_pass = await this.check_old_pass();
                if (!this.check_pass) {
                    isValid = false;
                }
            }

            const passRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[a-zA-Z\d!@#$%^&*]{8,30}$/;
            if (this.new_pass.trim() === '' || !passRegex.test(this.new_pass.trim())) {
                this.check_pass_new = false;
                isValid = false;
            } else {
                this.check_pass_new = true;
            }

            return isValid;
        },

        async change_pass() {
            try{
                if(!await this.validateInput()){
                    return;
                }

                const response = await fetch("/api/change_pass", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({'old_pass': this.old_pass, 'new_pass': this.new_pass, 'email': this.email}),
                });
                const data = await response.json();
                if (data.result.success) {
                    console.log(data.result);
                    // Lưu thông báo để hiển thị sau khi chuyển trang
                    sessionStorage.setItem('toastrMessage', 'パスワードが変更されました。新しいパスワードでログインしてください。');
                    sessionStorage.setItem('toastrType', "info");

                    // Đăng xuất người dùng bằng cách xóa tất cả thông tin đăng nhập
                    localStorage.removeItem('isloggedin');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userId');
                    localStorage.removeItem('user_name');
                    localStorage.removeItem('company_id');

                    // Gọi API để xóa session phía server
                    fetch('/users/sign_out', { method: 'GET' })
                        .finally(() => {
                            // Chuyển hướng đến trang đăng nhập
                            window.location.href = "/login";
                        });
                } else {
                    window.toastr.error(data.result.message);
                }
            }catch(e){

            }
        },
        async check_old_pass(){
            try{
                const response = await fetch("/api/check_old_pass", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({'old_pass': this.old_pass, 'email': this.email}),
                });
                const data = await response.json();
                return data.result.success;
            }catch(e){

            }
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function() {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function() {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function() {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function() {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        }
    },
    mounted(){
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        const toastrCSS = document.createElement("link");
        toastrCSS.rel = "stylesheet";
        toastrCSS.href = "/custom_frontend/static/css/Toastr.css";
        toastrCSS.onload = function() {
            console.log("Toastr CSS loaded successfully!");
        };
        document.head.appendChild(toastrCSS);

        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });
    },

    watch: {
        new_pass: {
            handler() {
                this.check_pass_new = true;
            },
        },
        old_pass: {
            handler() {
                this.check_pass = true;
            },
        },
    },
}
export default change_pass