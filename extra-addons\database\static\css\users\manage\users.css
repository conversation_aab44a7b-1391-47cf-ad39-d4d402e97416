/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    main {
        display: block;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h1,
    h4 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul ul {
        margin-bottom: 0;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button {
        overflow: visible;
    }

    button {
        text-transform: none;
    }

    [type=button],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    h1,
    h4 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col,
    .col-11,
    .col-12,
    .col-6,
    .col-lg-3,
    .col-lg-9,
    .col-md-3,
    .col-md-4,
    .col-md-8 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-11 {
        flex: 0 0 91.666667%;
        max-width: 91.666667%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 768px) {
        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-lg-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-danger {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:hover {
        color: #fff;
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-danger:focus {
        color: #fff;
        background-color: #c82333;
        border-color: #bd2130;
        box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
    }

    .btn-danger:disabled {
        color: #fff;
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:not(:disabled):not(.disabled):active {
        color: #fff;
        background-color: #bd2130;
        border-color: #b21f2d;
    }

    .btn-danger:not(:disabled):not(.disabled):active:focus {
        box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
    }

    .btn-lg {
        padding: .5rem 1rem;
        font-size: 1.25rem;
        line-height: 1.5;
        border-radius: .3rem;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .card>.list-group {
        border-top: inherit;
        border-bottom: inherit;
    }

    .card>.list-group:first-child {
        border-top-width: 0;
        border-top-left-radius: calc(.25rem - 1px);
        border-top-right-radius: calc(.25rem - 1px);
    }

    .card>.list-group:last-child {
        border-bottom-width: 0;
        border-bottom-right-radius: calc(.25rem - 1px);
        border-bottom-left-radius: calc(.25rem - 1px);
    }

    .badge-pill {
        padding-right: .6em;
        padding-left: .6em;
        border-radius: 10rem;
    }

    .list-group {
        display: flex;
        flex-direction: column;
        padding-left: 0;
        margin-bottom: 0;
        border-radius: .25rem;
    }

    .list-group-item {
        position: relative;
        display: block;
        padding: .75rem 1.25rem;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .list-group-item:first-child {
        border-top-left-radius: inherit;
        border-top-right-radius: inherit;
    }

    .list-group-item:last-child {
        border-bottom-right-radius: inherit;
        border-bottom-left-radius: inherit;
    }

    .list-group-item:disabled {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    .modal-footer {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;
        padding: .75rem;
        border-top: 1px solid #dee2e6;
        border-bottom-right-radius: calc(.3rem - 1px);
        border-bottom-left-radius: calc(.3rem - 1px);
    }

    .modal-footer>* {
        margin: .25rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .d-none {
        display: none !important;
    }

    .d-block {
        display: block !important;
    }

    @media (min-width: 768px) {
        .d-md-block {
            display: block !important;
        }
    }

    .position-relative {
        position: relative !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .py-1 {
        padding-top: 0.25rem !important;
    }

    .py-1 {
        padding-bottom: 0.25rem !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .px-5 {
        padding-right: 3rem !important;
    }

    .px-5 {
        padding-left: 3rem !important;
    }

    @media (min-width: 768px) {
        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .my-md-1 {
            margin-top: 0.25rem !important;
        }

        .my-md-1 {
            margin-bottom: 0.25rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }
    }

    .pink {
        background-color: #ff285b !important;
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .collapsible-body {
        display: none;
    }

    h1,
    h4 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn.btn-lg {
        padding: 1rem 2.4rem;
        font-size: .94rem;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn-danger {
        color: #fff;
        background-color: #ff285b !important;
    }

    .btn-danger:hover {
        color: #fff;
        background-color: #ff426e;
    }

    .btn-danger:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-danger:focus,
    .btn-danger:active {
        background-color: #c1002e;
    }

    .btn-danger:not([disabled]):not(.disabled):active {
        background-color: #c1002e !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-danger:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-deep-orange {
        color: #fff;
        background-color: #ff7e28 !important;
    }

    .btn-deep-orange:hover {
        color: #fff;
        background-color: #ff8d42;
    }

    .btn-deep-orange:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-deep-orange:focus,
    .btn-deep-orange:active {
        background-color: #c14d00;
    }

    .btn-deep-orange:not([disabled]):not(.disabled):active {
        background-color: #c14d00 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-deep-orange:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #78909c !important;
    }

    .btn-outline-blue-grey:hover,
    .btn-outline-blue-grey:focus,
    .btn-outline-blue-grey:active,
    .btn-outline-blue-grey:active:focus {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .badge-pill {
        padding-right: .5rem;
        padding-left: .5rem;
        border-radius: 10rem;
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    .list-group .list-group-item:first-child {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .list-group .list-group-item:last-child {
        border-bottom-right-radius: .25rem;
        border-bottom-left-radius: .25rem;
    }

    button,
    html [type=button] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    button:focus {
        outline: 0 !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-large {
        font-size: 1.125rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-5-text {
        color: #738a97;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .py-1 {
        padding-top: .25rem !important;
    }

    .py-1 {
        padding-bottom: .25rem !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .py-2 {
        padding-bottom: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .px-5 {
        padding-right: 2rem !important;
    }

    .px-5 {
        padding-left: 2rem !important;
    }

    .pl-7 {
        padding-left: 3rem !important;
    }

    @media (min-width: 768px) {
        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .my-md-1 {
            margin-top: .25rem !important;
        }

        .my-md-1 {
            margin-bottom: .25rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-dark {
        color: rgba(0, 0, 0, 0.54);
    }

    .material-icons.md-grey {
        color: rgba(0, 0, 0, 0.38);
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .badge-pill {
        padding: .1rem .5rem;
        color: #fff;
    }







    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }
    }

    .title {
        background-color: #1072e9 !important;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    :focus {
        outline: 0;
    }

    .modal {
        opacity: .5;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    .modal-footer {
        border-top: none;
        padding: 0 2rem 2rem;
        margin-top: -2rem;
        flex-wrap: initial;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul {
        list-style: none;
        padding: 0;
    }

    .side-menu-contents ul.collapsible li a {
        color: rgba(0, 0, 0, 0.87);
        position: relative;
    }

    .side-menu-contents ul.collapsible li a.active {
        background: #1072e9;
        color: #fff;
    }

    .side-menu-contents ul.collapsible li a:hover {
        background: #1072e9;
        color: #fff;
    }

    .side-menu-contents ul.collapsible li a:hover i {
        color: #fff;
    }

    .side-menu-contents ul.collapsible li a .rotate-icon {
        position: absolute;
        right: 0;
        top: .5rem;
    }

    .side-menu-contents ul.collapsible li>div {
        cursor: pointer;
    }

    .side-menu-contents ul.collapsible li>div:hover {
        background: #1072e9;
        color: #fff;
    }

    @media (max-width: 768px) {
        .side-menu-contents ul li a {
            border-top: 1px solid #ccc;
        }

        .side-menu-contents ul li .collapsible-body li a {
            border: none;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    @media (max-width: 767px) {
        .btn-outline-blue-grey:hover {
            border-color: #78909c !important;
            background-color: inherit !important;
            color: #78909c !important;
        }
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}