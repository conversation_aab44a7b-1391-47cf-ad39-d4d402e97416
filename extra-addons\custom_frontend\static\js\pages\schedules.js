import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const Schedule = {
    template: `
        <main class="margin-header sp_fluid" id="vue-app" data-v-app>
            <!-- Header -->
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: 'マッチング状況', link: '/mypage' },
                { text: '日程調整待ち', link: null, current: true }
            ], 'container-fluid', '')}


            <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                <div class="card px-3 px-md-4 mt-2 pt-5 form-card sp_sides_uniter">
                    <!-- Checkbox section -->
                    <div class="d-flex checkbox-container justify-content-start">
                        <div v-if="userAccess !== 'hiring'" class="custom-control custom-checkbox pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field0" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="scout"
                                   v-model="contractTypes" disabled>
                            <label id="contract_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="contract_types_field0">人財スカウト</label>
                        </div>
                        <div v-if="userAccess !== 'scout'" class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field1" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="case"
                                   v-model="contractTypes" disabled>
                            <label id="contract_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="contract_types_field1">案件ヒアリング</label>
                        </div>
                    </div>
                    <!-- Info section -->
                    <div class="info-container">
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">案件名:</span>
                                <a class="info-value" :href="'/opportunities/' + id1 + '/detail'">{{ opp.subject }}</a>
                            </div>
                            <div class="info-item">
                                <span class="info-label">人財名:</span>
                                <a class="info-value" :href="'/resumes/' + id2 + '/detail'">{{ res.initial_name }}.</a>
                            </div>
                        </div>
                    </div>
                    <!-- Calender section -->
                    <div class="calendar-container mb-5">
                        <div id="calendar"></div>
                        <div class="calendar-legend">
                            <div class="legend-item">
                                <div class="legend-color my-booking"></div>
                                <span>自分の予約</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color other-project-booking"></div>
                                <span>他案件での予約</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color others-booking"></div>
                                <span>予約不可</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color available-booking"></div>
                                <span>予約可能</span>
                            </div>
                        </div>
                    </div>

                    <!-- Modal -->
                    <div id="bookingModal" class="modal">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h4 id="modalTitle">予約詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button" @click="closeModal">
                                    <span aria-hidden="true">
                                        <i class="material-icons md-dark mb-36">clear</i>
                                    </span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p id="selectedDateTime"></p>

                            </div>
                            <div class="d-flex flex-column pl-4" id="meeting_url">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div for="meeting_url" class="font-weight-bold">ミーティング相手との連絡にご利用ください。</div>
                                    <button type="button" class="btn btn-link p-0 edit-icon-btn transparent-btn" id="editUrlBtn" style="display: none;" @click="toggleEditMode">
                                        <i class="material-icons" style="font-size: 16px; color: #666;">edit</i>
                                    </button>
                                </div>
                                <textarea type="text" style="width: 100%;" v-model="meetingUrl" placeholder="面談URLまたはメッセージを入力してください。" rows="6" id="meetingUrlTextarea"></textarea>
                            </div>
                            <div class="modal-buttons">
                                <button class="btn fc-button-primary" id="confirmBtn" style="display: none;color: white;" @click="UpdateBooking">予約する</button>
                                <button class="btn fc-button-primary" id="saveUrlBtn" style="display: none;color: white;" @click="saveUrlChanges">保存</button>
                                <button class="btn delete-btn" id="deleteBtn" style="display: none;" @click="deleteBooking">キャンセル</button>
                                <button @click="closeModal" class="btn close-btn" id="closeBtn">閉じる</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </main>
        <link rel="stylesheet" :href="cssPath"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            contractTypes: [],
            slot: [],
            opp: [],
            res: [],
            selectedStartTime: "",
            selectedEndTime: "",
            id1: "",
            id2: "",
            userAccess: null,
            meetingUrl: "",
            isEditingUrl: false,
        }
    },

    async mounted() {
        this.UserId = userInfo ? userInfo.user_id : null;
        this.id1 = this.$route.params.opp_id;
        this.id2 = this.$route.params.res_id;
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");

        const scoutResult = await this.checkScout();
        if (!scoutResult) return;

        await Promise.all([
            this.getOpp(),
            this.getRes(),
            this.getBooking()
        ]);

        await this.loadFullCalendar();
    },

    computed: {
        cssPath() {
            return "/custom_frontend/static/css/schedules.css";
        }
    },

    methods: {
        loadFullCalendar() {
            const script = document.createElement("script");
            script.src = "https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/6.1.8/index.global.min.js"; // Update to latest version if needed
            script.onload = this.initCalendar;
            document.head.appendChild(script);
        },
        initCalendar() {
            const calendarEl = document.getElementById("calendar");
            this.calendar = new FullCalendar.Calendar(calendarEl, {
                headerToolbar: {
                    left: "prev,next today",
                    center: "title",
                    right: "weekViewButton,monthViewButton"
                },
                buttonText: {
                    today: "今日"
                },
                customButtons: {
                    weekViewButton: {
                        text: "週",
                        click: () => {
                            this.calendar.changeView("timeGridWeek");
                            this.toggleButtons("week");
                            // VÔ HIỆU HÓA: setTimeout(() => this.applyDayHeaderStyles(), 100);
                        }
                    },
                    monthViewButton: {
                        text: "月",
                        click: () => {
                            this.calendar.changeView("dayGridMonth");
                            this.toggleButtons("month");
                            // VÔ HIỆU HÓA: setTimeout(() => this.applyDayHeaderStyles(), 100);
                        }
                    }
                },
                initialView: "timeGridWeek",
                locale: "ja",
                slotMinTime: "08:00:00",
                slotMaxTime: "22:00:00",
                allDaySlot: false,
                slotDuration: "00:30:00", // Hiển thị mỗi 30 phút
                slotLabelInterval: "00:30", // Nhãn thời gian mỗi 30 phút
                slotLabelFormat: {
                    hour: "numeric",
                    minute: "2-digit",
                    omitZeroMinute: false,
                    meridiem: false, // Không hiển thị AM/PM
                },
                height: "auto",
                events: this.slot,
                eventClick: (info) => {
                    const currentEvent = info.event;
                    const startTime = new Date(currentEvent.start).toLocaleString('ja-JP', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    const endTime = new Date(currentEvent.end).toLocaleString('ja-JP', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    this.showEventModal(currentEvent, startTime, endTime);
                },
                // Instant click for mobile - no drag needed
                dateClick: (info) => {
                    // Create 30-minute slot and reuse select logic
                    const startDate = new Date(info.date);
                    const endDate = new Date(startDate.getTime() + 30 * 60000); // Add 30 minutes

                    // Create fake selectInfo and call existing select handler
                    const fakeSelectInfo = {
                        start: startDate,
                        end: endDate,
                        view: info.view
                    };

                    // Reuse existing select logic
                    this.calendar.trigger('select', fakeSelectInfo);
                },
                selectable: true,
                select: (info) => {
                    if (info.start < new Date()) {
                        window.toastr.warning("過去の時間は選択できません");
                        return;
                    }
                    if (info.view && info.view.type === 'dayGridMonth') {
                        this.calendar.changeView('timeGridWeek', info.start);
                        this.toggleButtons("week");
                        return;
                    }

                    // Kiểm tra xem có thể đặt lịch không (thời gian, ngày, deadline và quá khứ)
                    const isTimeInRange = this.isSlotInRange(info.start, info.view.type);
                    const isDayAvailable = this.isDayAvailable(info.start);
                    const isBeforeDeadline = this.isBeforeApplicationDeadline(info.start);
                    const isNotPastDate = !this.isPastDate(info.start);

                    console.log('Select check:', {
                        date: info.start,
                        isTimeInRange: isTimeInRange,
                        isDayAvailable: isDayAvailable,
                        isBeforeDeadline: isBeforeDeadline,
                        isNotPastDate: isNotPastDate,
                        dayOfWeek: info.start.getDay()
                    });

                    if (!isTimeInRange || !isDayAvailable || !isBeforeDeadline || !isNotPastDate) {
                        if (!isNotPastDate) {
                            window.toastr.warning("過去の時間は選択できません");
                        } else if (!isBeforeDeadline) {
                            window.toastr.warning("応募期限を過ぎているため予約できません");
                        } else {
                            window.toastr.warning("この時間帯は予約できません");
                        }
                        return;
                    }

                    let startTime = new Date(info.start).toLocaleString('ja-JP', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    let endTime = new Date(info.end).toLocaleString('ja-JP', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                    this.showModal(null, info, startTime, endTime);
                },
                eventDrop: function (info) {
                    handleEventDrop(info.event);
                },
                selectAllow: (selectInfo) => {
                    const viewType = selectInfo.view ? selectInfo.view.type : '';
                    console.log('selectAllow view type:', viewType);
                    if (viewType === 'timeGridWeek' || viewType === 'timeGridDay') {
                        // Kiểm tra xem slot có trong range thời gian, ngày khả dụng, deadline và không phải quá khứ
                        const isTimeInRange = this.isSlotInRange(selectInfo.start, viewType) && this.isSlotInRange(selectInfo.end, viewType);
                        const isDayAvailable = this.isDayAvailable(selectInfo.start);
                        const isBeforeDeadline = this.isBeforeApplicationDeadline(selectInfo.start);
                        const isNotPastDate = !this.isPastDate(selectInfo.start);
                        return isTimeInRange && isDayAvailable && isBeforeDeadline && isNotPastDate;
                    }
                    return true;
                },
                slotLaneClassNames: (arg) => {
                    if (arg.view.type === 'timeGridWeek' || arg.view.type === 'timeGridDay') {
                        const isTimeInRange = this.isSlotInRange(arg.date, arg.view.type);

                        console.log('slotLaneClassNames check:', {
                            date: arg.date,
                            time: `${arg.date.getHours()}:${arg.date.getMinutes().toString().padStart(2, '0')}`,
                            isTimeInRange: isTimeInRange
                        });

                        // Chỉ kiểm tra thời gian, không kiểm tra ngày
                        // Hiển thị màu vàng cho khung giờ không khả thi (ngoài 8:00-22:00)
                        if (!isTimeInRange) {
                            console.log('Adding fc-slot-disabled-row class for time:', `${arg.date.getHours()}:${arg.date.getMinutes().toString().padStart(2, '0')}`);
                            return ['fc-slot-disabled-row'];
                        }
                    }
                    return [];
                },
                dayCellClassNames: (arg) => {
                    // VÔ HIỆU HÓA: Không tô màu cả cột cho ngày không khả dụng
                    // Chỉ tô màu từng ô theo khung giờ trong slotLaneClassNames
                    return [];
                },
                datesSet: () => {
                    // VÔ HIỆU HÓA: Không gọi applyDayHeaderStyles nữa
                    console.log('datesSet: Calendar view changed - no column coloring applied');
                },
            });
            this.calendar.render();
            this.toggleButtons("week");

            // VÔ HIỆU HÓA: Không tô màu cả cột nữa
            setTimeout(() => {
                console.log('Calendar initialized - no column coloring applied');
                this.calendar.updateSize();
                this.calendar.render();

                // DEBUG: Kiểm tra và loại bỏ bất kỳ background color nào trên cột
                this.debugAndRemoveColumnColors();
            }, 1000);
        },

        showEventModal(event, startTime, endTime) {
            var modal = document.getElementById('bookingModal');
            var dateTimeText = document.getElementById('selectedDateTime');
            var modalTitle = document.getElementById('modalTitle');
            var confirmBtn = document.getElementById('confirmBtn');
            var deleteBtn = document.getElementById('deleteBtn');
            var saveUrlBtn = document.getElementById('saveUrlBtn');
            var editUrlBtn = document.getElementById('editUrlBtn');
            var meetingUrl = document.getElementById('meeting_url');
            var textarea = document.getElementById('meetingUrlTextarea');

            this.selectedStartTime = startTime;
            this.selectedEndTime = endTime;
            this.isEditingUrl = false; // Reset edit mode

            // Format date and time separately for better display
            const dateObj = new Date(startTime);
            const dateStr = dateObj.toLocaleString('ja-JP', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                weekday: 'long'
            });

            const timeStartStr = dateObj.toLocaleString('ja-JP', {
                hour: '2-digit',
                minute: '2-digit'
            });

            const timeEndStr = new Date(endTime).toLocaleString('ja-JP', {
                hour: '2-digit',
                minute: '2-digit'
            });

            dateTimeText.innerHTML = `${dateStr}<br>${timeStartStr} ~ ${timeEndStr}`

            if (event.classNames.includes('my-booking')) {
                modal.style.display = 'block';
                modalTitle.textContent = '予約詳細';
                confirmBtn.style.display = 'none';
                deleteBtn.style.display = 'block';
                saveUrlBtn.style.display = 'none';
                editUrlBtn.style.display = 'inline-block'; // Show edit icon
                meetingUrl.style.display = 'block'; // Hiện meeting URL khi xem lịch hẹn có sẵn
                meetingUrl.style.setProperty('display', 'block', 'important'); // Force show
                meetingUrl.style.setProperty('width', '100%', 'important'); // Force show
                textarea.disabled = true; // Disable by default

                // Load meeting URL từ event data nếu có
                if (event.extendedProps && event.extendedProps.meeting_url) {
                    this.meetingUrl = event.extendedProps.meeting_url;
                }
            } else if (event.classNames.includes('others-booking')) {
                modal.style.display = 'none';
            } else if (event.classNames.includes('other-project-booking')) {
                // Booking từ dự án khác - hiển thị toast warning
                window.toastr.warning("この時間に予約できません");
                modal.style.display = 'none';
            } else {
                if (this.hasExistingBooking()) {
                    modal.style.display = 'block';
                    modalTitle.textContent = '予約制限';
                    confirmBtn.style.display = 'none';
                    deleteBtn.style.display = 'none';
                    saveUrlBtn.style.display = 'none';
                    editUrlBtn.style.display = 'none';
                    dateTimeText.innerHTML += '<br><br>既に予約があります。新しい予約をするには、既存の予約をキャンセルしてください。';
                    meetingUrl.style.display = 'none'; // Ẩn meeting URL khi có lịch hẹn rồi
                    meetingUrl.style.setProperty('display', 'none', 'important'); // Force hide
                } else {
                    modal.style.display = 'block';
                    modalTitle.textContent = '予約確認';
                    confirmBtn.style.display = 'block';
                    deleteBtn.style.display = 'none';
                    saveUrlBtn.style.display = 'none';
                    editUrlBtn.style.display = 'none';
                    meetingUrl.style.display = 'block'; // Hiện meeting URL khi tạo lịch hẹn mới
                    meetingUrl.style.setProperty('display', 'block', 'important'); // Force show
                    textarea.disabled = false; // Enable for new booking
                }
            }


        },

        hasExistingBooking() {
            var events = this.calendar.getEvents();
            console.log('DEBUG hasExistingBooking: All events =', events);

            const myBookings = events.filter(event => event.classNames.includes('my-booking'));
            console.log('DEBUG hasExistingBooking: My bookings =', myBookings);

            // Chỉ kiểm tra booking của dự án hiện tại (my-booking), không tính booking từ dự án khác
            const hasBooking = events.some(event => event.classNames.includes('my-booking'));
            console.log('DEBUG hasExistingBooking: Result =', hasBooking);

            return hasBooking;
        },

        toggleButtons(view) {
            document.querySelector(".fc-weekViewButton-button").disabled = (view === "week");
            document.querySelector(".fc-monthViewButton-button").disabled = (view === "month");
        },

        applyDayHeaderStyles() {
            // VÔ HIỆU HÓA: Không tô màu cả cột nữa, chỉ tô từng ô theo khung giờ
            console.log('applyDayHeaderStyles: Disabled - only coloring individual time slots');
            return;
        },

        debugAndRemoveColumnColors() {
            console.log('=== DEBUG: Checking for column background colors ===');

            // Tìm tất cả các cột timegrid
            const timegridCols = document.querySelectorAll('.fc-timegrid-col');
            timegridCols.forEach((col, index) => {
                const computedStyle = window.getComputedStyle(col);
                const backgroundColor = computedStyle.backgroundColor;
                const backgroundImage = computedStyle.backgroundImage;
                const inlineStyle = col.style.backgroundColor;

                console.log(`Column ${index}:`, {
                    element: col,
                    classes: col.className,
                    computedBackgroundColor: backgroundColor,
                    computedBackgroundImage: backgroundImage,
                    inlineBackgroundColor: inlineStyle,
                    dataDate: col.getAttribute('data-date')
                });

                // Force remove any background
                if (backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
                    console.log(`REMOVING background from column ${index}:`, backgroundColor);
                    col.style.backgroundColor = 'white';
                    col.style.background = 'white';
                }
            });

            // Tìm tất cả elements có class fc-day
            const dayElements = document.querySelectorAll('[class*="fc-day"]');
            dayElements.forEach((el, index) => {
                const computedStyle = window.getComputedStyle(el);
                const backgroundColor = computedStyle.backgroundColor;

                if (backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent' && backgroundColor !== 'rgb(255, 255, 255)') {
                    console.log(`REMOVING background from day element ${index}:`, {
                        element: el,
                        classes: el.className,
                        backgroundColor: backgroundColor
                    });
                    el.style.backgroundColor = 'white';
                    el.style.background = 'white';
                }
            });

            console.log('=== DEBUG: Column color check completed ===');
        },

        closeModal() {
            var modal = document.getElementById('bookingModal');
            modal.style.display = 'none';
            this.isEditingUrl = false; // Reset edit mode when closing
        },

        toggleEditMode() {
            var textarea = document.getElementById('meetingUrlTextarea');
            var editUrlBtn = document.getElementById('editUrlBtn');
            var saveUrlBtn = document.getElementById('saveUrlBtn');
            var deleteBtn = document.getElementById('deleteBtn');

            this.isEditingUrl = !this.isEditingUrl;

            if (this.isEditingUrl) {
                // Enable edit mode
                textarea.disabled = false;
                textarea.focus();
                editUrlBtn.style.display = 'none';
                saveUrlBtn.style.display = 'inline-block';
                deleteBtn.style.display = 'none'; // Hide delete button during edit
            } else {
                // Disable edit mode
                textarea.disabled = true;
                editUrlBtn.style.display = 'inline-block';
                saveUrlBtn.style.display = 'none';
                deleteBtn.style.display = 'block'; // Show delete button again
            }
        },

        async saveUrlChanges() {
            try {
                const res = await fetch('/api/update_booking_url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        opp_id: this.$route.params.opp_id,
                        resume_id: this.$route.params.res_id,
                        meeting_url: this.meetingUrl,
                        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                        updated_by: this.UserId,
                        action: 'update_url_only' // Flag để backend biết chỉ cập nhật URL
                    })
                });

                const result = await res.json();
                if (result.result && result.result.success) {
                    console.log('URL更新しました');
                    window.toastr.success('面談URLが更新されました');

                    // Exit edit mode
                    this.toggleEditMode();

                    // Refresh booking data
                    await this.getBooking();

                    // Update calendar events
                    if (this.calendar) {
                        this.calendar.removeAllEvents();
                        this.calendar.addEventSource(this.slot);
                    }
                } else {
                    console.error(result.result ? result.result.message : 'Unknown error');
                    window.toastr.error(result.result ? result.result.message : 'Unknown error');
                }
            } catch (err) {
                console.error(err);
                window.toastr.error('更新中にエラーが発生しました');
            }
        },

        showModal(event, selectInfo, startTime, endTime) {
            var modal = document.getElementById('bookingModal');
            var dateTimeText = document.getElementById('selectedDateTime');
            var modalTitle = document.getElementById('modalTitle');
            var confirmBtn = document.getElementById('confirmBtn');
            var deleteBtn = document.getElementById('deleteBtn');
            var saveUrlBtn = document.getElementById('saveUrlBtn');
            var editUrlBtn = document.getElementById('editUrlBtn');
            var meetingUrl = document.getElementById('meeting_url');
            var textarea = document.getElementById('meetingUrlTextarea');
            console.log(meetingUrl);

            this.selectedStartTime = startTime;
            this.selectedEndTime = endTime;
            this.isEditingUrl = false; // Reset edit mode

            if (!event && selectInfo) {
                // Format date and time separately for better display
                const dateObj = new Date(startTime);
                const dateStr = dateObj.toLocaleString('ja-JP', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    weekday: 'long'
                });

                const timeStartStr = dateObj.toLocaleString('ja-JP', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const timeEndStr = new Date(endTime).toLocaleString('ja-JP', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                dateTimeText.innerHTML = `${dateStr}<br>${timeStartStr} ～ ${timeEndStr}`;

                const hasBooking = this.hasExistingBooking();
                console.log('DEBUG: hasExistingBooking =', hasBooking);
                console.log('DEBUG: calendar events =', this.calendar.getEvents());

                if (hasBooking) {
                    modalTitle.textContent = '予約制限';
                    confirmBtn.style.display = 'none';
                    deleteBtn.style.display = 'none';
                    saveUrlBtn.style.display = 'none';
                    editUrlBtn.style.display = 'none';
                    dateTimeText.innerHTML += '<br><br>既に予約があります。新しい予約をするには、既存の予約をキャンセルしてください。';
                    meetingUrl.style.display = 'none'; // Ẩn meeting URL khi có lịch hẹn rồi
                    meetingUrl.style.setProperty('display', 'none', 'important'); // Force hide
                    console.log('DEBUG: Hiding meeting URL because hasExistingBooking = true');
                    console.log('DEBUG: meetingUrl display style =', meetingUrl.style.display);
                } else {
                    modalTitle.textContent = '新規予約';
                    confirmBtn.style.display = 'block';
                    deleteBtn.style.display = 'none';
                    saveUrlBtn.style.display = 'none';
                    editUrlBtn.style.display = 'none';
                    meetingUrl.style.display = 'block'; // Hiện meeting URL khi tạo lịch hẹn mới
                    meetingUrl.style.setProperty('display', 'block', 'important'); // Force show
                    textarea.disabled = false; // Enable for new booking
                    this.meetingUrl = ''; // Reset meeting URL cho lịch hẹn mới
                    console.log('DEBUG: Showing meeting URL because hasExistingBooking = false');
                    console.log('DEBUG: meetingUrl display style =', meetingUrl.style.display);
                }
            }

            modal.style.display = 'block';
        },

        async getBooking() {
            try {
                const res = await fetch(`/api/get_booking`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        res_id: this.$route.params.res_id,
                        opp_id: this.$route.params.opp_id
                    })
                });

                const result = await res.json();
                if (result.result.success) {
                    this.slot = result.result.data;

                    this.slot.forEach(element => {
                        if (element.resume_id == this.$route.params.res_id && element.opp_id == this.$route.params.opp_id) {
                            this.contractTypes = element.contract_types ? element.contract_types.split(',') : [];
                        }
                    });

                    // Cập nhật events cho calendar nếu calendar đã được khởi tạo
                    if (this.calendar) {
                        this.calendar.removeAllEvents();
                        this.calendar.addEventSource(this.slot);
                    }

                    console.log(this.slot);
                } else {
                    console.error(result.result.message);
                }
            } catch (error) {
                console.log(error);
            }
        },

        async UpdateBooking() {
            this.closeModal();
            try {
                // Lưu lại ngày đang xem hiện tại
                let currentDate = this.calendar ? this.calendar.getDate() : null;

                const res = await fetch('/api/update_booking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        opp_id: this.$route.params.opp_id,
                        resume_id: this.$route.params.res_id,
                        contract_types: this.contractTypes.join(','),
                        interview_start: this.selectedStartTime,
                        interview_end: this.selectedEndTime,
                        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                        updated_by: this.UserId,
                        meeting_url: this.meetingUrl,
                    })
                });
                const result = await res.json();
                if (result.result.success) {
                    console.log('更新しました');
                    this.slot = [];
                    await this.getBooking();
                    this.loadFullCalendar();

                    // Sau khi render lại calendar, set lại ngày đang xem
                    if (currentDate) {
                        setTimeout(() => {
                            this.calendar.gotoDate(currentDate);
                        }, 100); // Đợi calendar render xong
                    }
                } else {
                    console.error(result.result.message);
                    window.toastr.error(result.result.message);
                }
            } catch (err) {
                console.error(err);
            }
        },

        async deleteBooking() {
            this.closeModal();
            try {
                // Lưu lại ngày đang xem hiện tại
                let currentDate = this.calendar ? this.calendar.getDate() : null;

                // Sử dụng API update_booking với logic toggle - khi is_booking = true, API sẽ set thành false
                const res = await fetch('/api/update_booking', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        opp_id: this.$route.params.opp_id,
                        resume_id: this.$route.params.res_id,
                        contract_types: this.contractTypes.join(','),
                        interview_start: this.selectedStartTime,
                        interview_end: this.selectedEndTime,
                        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                        updated_by: this.UserId,
                        meeting_url: '', // Empty URL for deletion
                        action: 'delete' // Flag để backend biết đây là delete action
                    })
                });

                const result = await res.json();
                if (result.result.success) {
                    console.log('予約をキャンセルしました');
                    window.toastr.success('予約をキャンセルしました');
                    this.slot = [];
                    await this.getBooking();
                    this.loadFullCalendar();

                    // Sau khi render lại calendar, set lại ngày đang xem
                    if (currentDate) {
                        setTimeout(() => {
                            this.calendar.gotoDate(currentDate);
                        }, 100); // Đợi calendar render xong
                    }
                } else {
                    console.error(result.result.message);
                    window.toastr.error(result.result.message);
                }
            } catch (err) {
                console.error(err);
                window.toastr.error('キャンセル中にエラーが発生しました');
            }
        },

        async getOpp() {
            try {
                const res = await fetch(`/api/opp_edit?id=${this.$route.params.opp_id}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                })
                const result = await res.json();
                if (result.success) {
                    this.opp = result.data;
                    console.log('Opportunity data loaded:', this.opp);
                    console.log('Interview time range:', {
                        start_time: this.opp.interview_start_time,
                        end_time: this.opp.interview_end_time
                    });

                    // Refresh calendar để áp dụng weekly availability mới
                    if (this.calendar) {
                        this.calendar.render();
                    }
                } else {
                    console.error(result.result.message);
                }
            } catch (error) {
                console.log(error);
            }
        },

        async getRes() {
            try {
                const res = await fetch(`/api/resume_detail?id=${this.$route.params.res_id}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                })
                const result = await res.json();
                if (result.success) {
                    this.res = result.data;
                    this.res.initial_name = this.res.initial_name.split(" ").join(".")
                    console.log(this.res);
                    console.log(result.data);
                 } else {
                    console.error(result.result.message);
                }
            } catch (error) {
                console.log(error);
            }
        },

        async checkScout() {
            try {
                const res = await fetch('/api/check_scout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        resume_id: this.id2,
                        opportunity_id: this.id1,
                        user_id: this.UserId
                    })
                });

                const result = await res.json();

                if (result.result && result.result.success) {
                    if (result.result.type === 'scout') {
                        this.userAccess = 'scout';
                        this.contractTypes = ['scout'];
                    } else if (result.result.type === 'hiring') {
                        this.userAccess = 'hiring';
                        if (!this.contractTypes.includes('case')) {
                            this.contractTypes.push('case');
                        }
                    } else {
                        this.userAccess = 'none';
                        this.contractTypes = [];
                    }
                    return true;
                } else {
                    if (result.result && result.result.redirect) {
                        window.location.href = result.result.redirect;
                    } else {
                        window.location.href = '/notfound';
                    }
                    return false;
                }
            } catch (error) {
                console.error("Error checking scout:", error);
                window.location.href = '/notfound';
                return false;
            }
        },
        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        isSlotInRange(date, viewType) {
            if (viewType === 'dayGridMonth') return true;
            if (!this.opp.interview_start_time || !this.opp.interview_end_time) return true;
            const start = this.opp.interview_start_time;
            const end = this.opp.interview_end_time;
            const hour = date.getHours();
            const minute = date.getMinutes();
            const slotTime = hour * 60 + minute;

            const [startHour, startMinute] = start.split(':').map(Number);
            const [endHour, endMinute] = end.split(':').map(Number);
            const startTime = startHour * 60 + startMinute;
            const endTime = endHour * 60 + endMinute;

            return slotTime >= startTime && slotTime < endTime;
        },

        isDayAvailable(date) {
            const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

            // Chủ nhật (Sunday) không bao giờ cho phép đặt lịch
            if (dayOfWeek === 0) {
                console.log('Sunday - not available');
                return false;
            }

            // Nếu không có thông tin về ngày khả dụng, mặc định là tất cả ngày đều khả dụng (trừ Chủ nhật)
            if (!this.opp.available_monday && !this.opp.available_tuesday && !this.opp.available_wednesday &&
                !this.opp.available_thursday && !this.opp.available_friday && !this.opp.available_saturday) {
                console.log('No weekly availability set - all days available except Sunday');
                return true; // Tất cả ngày từ thứ 2-7 đều khả dụng
            }

            // Kiểm tra từng ngày cụ thể
            let result = false;
            switch (dayOfWeek) {
                case 1: result = this.opp.available_monday || false; break;    // 月曜日
                case 2: result = this.opp.available_tuesday || false; break;   // 火曜日
                case 3: result = this.opp.available_wednesday || false; break; // 水曜日
                case 4: result = this.opp.available_thursday || false; break;  // 木曜日
                case 5: result = this.opp.available_friday || false; break;    // 金曜日
                case 6: result = this.opp.available_saturday || false; break;  // 土曜日
                default: result = false;
            }

            console.log(`Day ${dayOfWeek} availability:`, {
                dayOfWeek: dayOfWeek,
                available_monday: this.opp.available_monday,
                available_tuesday: this.opp.available_tuesday,
                available_wednesday: this.opp.available_wednesday,
                available_thursday: this.opp.available_thursday,
                available_friday: this.opp.available_friday,
                available_saturday: this.opp.available_saturday,
                result: result
            });

            return result;
        },

        isBeforeApplicationDeadline(date) {
            // Nếu không có deadline thì luôn cho phép
            if (!this.opp.expired_at) {
                console.log('No application deadline set - always available');
                return true;
            }

            // Chuyển đổi expired_at thành Date object
            const deadline = new Date(this.opp.expired_at);
            const checkDate = new Date(date);

            // So sánh chỉ ngày (không tính giờ)
            const deadlineDate = new Date(deadline.getFullYear(), deadline.getMonth(), deadline.getDate());
            const checkDateOnly = new Date(checkDate.getFullYear(), checkDate.getMonth(), checkDate.getDate());

            const result = checkDateOnly <= deadlineDate;

            console.log('Application deadline check:', {
                expired_at: this.opp.expired_at,
                deadline: deadline,
                checkDate: checkDate,
                deadlineDate: deadlineDate,
                checkDateOnly: checkDateOnly,
                result: result
            });

            return result;
        },

        isPastDate(date) {
            const today = new Date();
            const checkDate = new Date(date);

            // So sánh chỉ ngày (không tính giờ)
            const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const checkDateOnly = new Date(checkDate.getFullYear(), checkDate.getMonth(), checkDate.getDate());

            const result = checkDateOnly < todayDate;

            console.log('Past date check:', {
                today: today,
                checkDate: checkDate,
                todayDate: todayDate,
                checkDateOnly: checkDateOnly,
                result: result
            });

            return result;
        },
    }
}

export default Schedule;