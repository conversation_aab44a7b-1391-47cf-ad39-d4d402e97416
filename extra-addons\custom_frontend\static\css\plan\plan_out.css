@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .pb-4 {
        padding-bottom: 1.5rem !important;
    }

    .text-right {
        text-align: right !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .mt-3 {
        margin-top: 1rem !important;
    }

    .pb-4 {
        padding-bottom: 1.5rem !important;
    }

    body a {
        color: #1072e9;
    }


    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-blue-grey {
        color: #546e7a;
    }

    .material-icons.md-36 {
        font-size: 36px;
    }

    :focus {
        outline: 0;
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}







.container{
    min-height: 400px;
    text-align: center;
}

.container p{
    font-size: 20px;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
    margin: 3em 0;
}

.note{
    max-width: 600px;
    margin: auto;
}

.note-content{
    font-size: 16px;
    font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
    padding: 4px;
    text-align: left;
}

.btn-out {
    display: block;
    margin: 40px auto;
    padding: 10px 30px;
    background-color: #1072e9;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

