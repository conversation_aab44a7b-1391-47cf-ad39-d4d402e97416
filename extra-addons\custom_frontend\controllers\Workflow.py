from odoo import http
from odoo.http import request, Response
import json
import logging
from datetime import datetime, timedelta
import pytz
from . import common
from .common import send_email_from_template

_logger = logging.getLogger(__name__)
class CriteriaController(http.Controller):

    @http.route('/api/get_workflowbyid', type='http', auth="public", methods=['GET'], csrf=False)
    def getWorkflow(self, **kwargs):
        workflow_id = kwargs.get("id")

        if not workflow_id:
            return Response(json.dumps({'success': False, 'message': 'Missing id'}), content_type='application/json', status=400)

        workflow = request.env['vit.workflow'].sudo().browse(int(workflow_id))
        workflow_evaluation = request.env['vit.workflow_evaluation'].sudo().search([('workflow_id', '=', int(workflow_id))])

        if not workflow.exists():
            return Response(json.dumps({'success': False, 'message': 'Workflow not found'}), content_type='application/json', status=404)

        evaluation = [];
        for e in workflow_evaluation:
            evaluation.append({
                'id': e.id,
                'criteria_id': e.criteria_id.id,
                'criteria_name': e.criteria_id.name,
                'score': e.score
            })

        data = {
            'success': True,
            'id': workflow.id,
            'opportunities_id': workflow.opportunities_id.id if workflow.opportunities_id else None,
            'opportunities_name': workflow.opportunities_id.subject if workflow.opportunities_id else None,
            'resumes_id': workflow.resumes_id.id if workflow.resumes_id else None,
            'resumes_name': workflow.resumes_id.user_name if workflow.resumes_id else None,
            'status': workflow.status,
            'expire_date': workflow.expire_date.isoformat() if workflow.expire_date else None,
            'evaluation': evaluation
            #'created_at': workflow.created_at.strftime('%Y-%m-%d %H:%M:%S') if workflow.created_at else None,
            #'created_by': workflow.created_by,
            #'updated_at': workflow.updated_at.strftime('%Y-%m-%d %H:%M:%S') if workflow.updated_at else None,
            #'updated_by': workflow.updated_by
        }

        return Response(json.dumps(data), content_type='application/json', status=200)

    @http.route('/api/get_evaluation_workflows_by_res', type='json', auth="public", methods=['POST'], csrf=False)
    def get_evaluation_workflows_by_res(self):
        data = request.httprequest.get_json(silent=True)

        user_id = data.get('user_id')
        res_id = data.get('resume_id')

        # Tìm tất cả các opp_id do user tạo
        opp_ids = request.env['vit.opportunities'].sudo().search([('created_by', '=', user_id)]).ids
        if not opp_ids:
            return {'success': False, 'message': 'No opportunities found'}

        _logger.info('Opportunities: %s', opp_ids)
        _logger.info('Resume id: %s', res_id)

        # Tìm workflow có res_id và opp_id tương ứng
        workflow_id = request.env['vit.workflow'].sudo().search([
            ('resumes_id', '=', res_id),
            ('opportunities_id', 'in', opp_ids)
        ], order='created_at desc', limit=1)
        if not workflow_id:
            return {'success': False, 'message': 'No workflows found'}

        # Lấy đánh giá mới nhất
        evaluations = request.env['vit.workflow_evaluation'].sudo().search([
            ('workflow_id', '=', workflow_id.id)
        ])

        if not evaluations:
            return {'success': False, 'message': 'No evaluations found'}

        # Trả về dữ liệu đánh giá
        return {
            'success': True,
            'data': {
                'workflow_id': workflow_id.id,
                'opp_name': workflow_id.opportunities_id.subject,
                'evaluations': [
                    {
                        'id': ev.id,
                        'criteria_id': ev.criteria_id.id,
                        'name': ev.criteria_id.name,
                        'score': ev.score,
                        'reason': ev.reason,
                        'created_by': ev.created_by,
                        'create_date': ev.create_date,
                        'write_date': ev.write_date
                    } for ev in evaluations
                ]
            }
        }


    @http.route('/api/create_workflow_evaluation', type='json', auth="public", methods=['POST'], csrf=False)
    def create_workflow_evaluation(self):
        try:
            data = request.httprequest.get_json(silent=True)
            if not data:
                return {"success": False, "message": "Invalid or missing JSON data"}

            workflow_id = data.get("workflow_id")
            created_by = data.get("created_by")
            evaluations = data.get("evaluations")

            if not workflow_id or not evaluations:
                return {"success": False, "message": "Missing workflow_id or evaluations"}

            workflow = request.env['vit.workflow'].sudo().browse(int(workflow_id))
            if not workflow.exists():
                return {"success": False, "message": "Workflow not found"}

            for evaluation in evaluations:
                criteria_id = evaluation.get("criteria_id")
                score = evaluation.get("score")
                reason = evaluation.get("reason", "")

                criteria = request.env['vit.evaluation_criteria'].sudo().browse(int(criteria_id))
                if not criteria.exists():
                    continue

                request.env['vit.workflow_evaluation'].sudo().create({
                    'workflow_id': int(workflow_id),
                    'criteria_id': int(criteria_id),
                    'score': int(score),
                    'reason': reason,
                    'created_by': created_by,
                    'created_at': datetime.now()
                })
            workflow.write({'status': 5})
            return {"success": True, "message": "Evaluations created successfully"}

        except Exception as e:
            return {"success": False, "message": str(e)}

    @http.route('/api/apply', type='json', auth='public', methods=['POST'], csrf=False)
    def apply_workflow(self):
        data = request.httprequest.get_json(silent=True)

        opportunities_id = data.get('opportunities_id')
        resume_ids = data.get('resumes_apply', [])
        status = data.get('status')
        created_by = data.get('created_by')
        created_at = data.get('created_at')

        if not opportunities_id or not resume_ids:
            return {'success': False, 'message': 'opportunities_id and resumes_apply are required'}

        # Lấy danh sách resume_id đã tồn tại với opportunities_id
        existing_workflows = request.env['vit.workflow'].sudo().search([
            ('opportunities_id', '=', int(opportunities_id))
        ])
        existing_resume_ids = {wf.resumes_id.id for wf in existing_workflows}

        # Lấy thông tin opportunity để tính toán expire_date
        opportunity = request.env['vit.opportunities'].sudo().browse(opportunities_id)

        # Tính toán expire_date dựa trên logic mới cho status = 0
        if status == 0:
            # Ngày ứng tuyển + 7 ngày
            application_plus_7_days = created_at + timedelta(days=7)

            # So sánh với ngày hết hạn ứng tuyển (expired_at)
            if opportunity.expired_at and opportunity.expired_at < application_plus_7_days:
                # Nếu expired_at sớm hơn, dùng expired_at
                calculated_expire_date = opportunity.expired_at
            else:
                # Nếu expired_at muộn hơn hoặc không có, dùng ngày ứng tuyển + 7 ngày
                calculated_expire_date = application_plus_7_days
        else:
            # Với các status khác, giữ logic cũ
            calculated_expire_date = datetime.now() + timedelta(days=7)

        # Lọc ra những resume_id chưa có để thêm mới
        new_records = [{
            'opportunities_id': opportunities_id,
            'resumes_id': resume_id,
            'expire_date': calculated_expire_date,
            'status': status,
            'created_by': created_by,
            'created_at': created_at,
        } for resume_id in resume_ids if resume_id not in existing_resume_ids]

        if new_records:
            request.env['vit.workflow'].sudo().create(new_records)

            # Sử dụng template từ database
            
            opportunities = request.env['vit.opportunities'].sudo().browse(int(opportunities_id))
            opportunities_name = opportunities.subject if opportunities else 'なし'
            link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}opportunities/{opportunities_id}/detail"

            user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
            company = user.company_id.name if user.company_id else 'なし'
            username = user.username if user.username else 'なし'
            user_email = user.email if user.email else 'なし'
            
            context_data = {
                'company_name': company,
                'user_name': username,
                'opp_name': opportunities_name,
                'status_text': '<p>▼ステータス：応募あり</p>',
                'interview_info': '',
                'link': link,
                'user_email': user_email
            }
            
            result = send_email_from_template('Workflow: Opportunity Status Change', context_data)
            
            if not result['success']:
                _logger.warning(f"Failed to send workflow apply email: {result['message']}")
            else:
                _logger.info(f"Sent workflow apply email to {user_email}")

            return {'success': True, 'message': 'Successfully created', 'added_resume_ids': [r['resumes_id'] for r in new_records], 'email_sent': user_email}

        return {
            'success': False,
            'message': 'No new records created (all resumes already exist)',
            'existing_resume_ids': list(existing_resume_ids)
        }

    @http.route('/api/get_opp_workflow', type='http', auth='public', methods=['GET'], csrf=False)
    def get_opp_workflow(self, **kwargs):
        opportunities_id = kwargs.get('opp_id')

        if not opportunities_id:
            return Response(
                json.dumps({'success': False,'message': 'Missing opportunities_id'}),
                content_type='application/json',
                status=400
            )

        workflows = request.env['vit.workflow'].sudo().search([
            ('opportunities_id', '=', int(opportunities_id))
        ])

        if not workflows:
            return Response(
                json.dumps({'success': False,'message': 'No workflow found for this opportunity'}),
                content_type='application/json',
                status=404
            )

        workflow_data = [wf.resumes_id.id for wf in workflows]

        return Response(
            json.dumps({'success': True, 'data': workflow_data}),
            content_type='application/json',
            status=200
        )

    @http.route('/api/get_workflow_by_opp', type='http', auth='public', methods=['GET'], csrf=False)
    def get_workflow_by_opp(self, **kwargs):
        try:
            company_id = kwargs.get('id')
            status_filter = kwargs.get('filter')

            if not company_id:
                return Response(
                    json.dumps({'success': False, 'message': 'Missing company_id'}),
                    content_type='application/json',
                    status=400
                )

            try:
                company_id = int(company_id)
            except ValueError:
                return Response(
                    json.dumps({'success': False, 'message': 'Invalid company_id'}),
                    content_type='application/json',
                    status=400
                )

            status_mapping = {
                "awaiting_schedule": 0,
                "scheduled": 1,
                "awaiting_interview_input": 2,
                "interview_completed": 3,
                "contract_completed_pending_review": 4,
                "contract_completed": 5
            }

            status_value = status_mapping.get(status_filter)

            query = """
                SELECT
                    w.id,
                    w.opportunities_id,
                    o.subject AS opportunities_name,
                    w.resumes_id,
                    p.initial_name AS resumes_name,
                    w.status,
                    w.expire_date,
                    w.created_at,
                    w.created_by
                FROM vit_workflow w
                JOIN vit_opportunities o ON w.opportunities_id = o.id
                JOIN vit_partner p ON w.resumes_id = p.id
                WHERE o.company_id = %s
            """
            params = [company_id]

            # Nếu có status_filter hợp lệ, thêm điều kiện lọc vào query
            if status_value is not None and status_filter != "all":
                query += " AND w.status = %s"
                params.append(status_value)

            request.env.cr.execute(query, tuple(params))
            workflows = request.env.cr.dictfetchall()

            for wf in workflows:
                if isinstance(wf.get('expire_date'), datetime):
                    wf['expire_date'] = wf['expire_date'].isoformat()
                if isinstance(wf.get('created_at'), datetime):
                    wf['created_at'] = wf['created_at'].isoformat()

            return Response(
                json.dumps({'success': True, 'message': 'Workflows retrieved successfully', 'data': workflows}),
                content_type='application/json',
                status=200
            )

        except Exception as e:
            return Response(
                json.dumps({'success': False, 'message': str(e)}),
                content_type='application/json',
                status=500
            )


    @http.route('/api/get_workflow_by_resume', type='http', auth='public', methods=['GET'], csrf=False)
    def get_workflow_by_resume(self, **kwargs):
        try:
            company_id = kwargs.get('id')
            status_filter = kwargs.get('filter')
            _logger.info(f"Received company_id: {company_id}")

            if not company_id:
                return Response(
                    json.dumps({'success': False, 'message': 'Missing company_id'}),
                    content_type='application/json',
                    status=400
                )

            try:
                company_id = int(company_id)
            except ValueError:
                return Response(
                    json.dumps({'success': False, 'message': 'Invalid company_id'}),
                    content_type='application/json',
                    status=400
                )
            status_mapping = {
                "awaiting_schedule": 0,
                "scheduled": 1,
                "awaiting_interview_input": 2,
                "interview_completed": 3,
                "contract_completed_pending_review": 4,
                "contract_completed": 5
            }

            status_value = status_mapping.get(status_filter)

            query = """
                SELECT
                    w.id,
                    w.opportunities_id,
                    o.subject AS opportunities_name,
                    w.resumes_id,
                    p.initial_name AS resumes_name,
                    w.status,
                    w.expire_date,
                    w.created_at,
                    w.created_by
                FROM vit_workflow w
                JOIN vit_opportunities o ON w.opportunities_id = o.id
                JOIN vit_partner p ON w.resumes_id = p.id
                WHERE p.company_id = %s
            """
            params = [company_id]
            if status_value is not None and status_filter != "all":
                query += " AND w.status = %s"
                params.append(status_value)

            request.env.cr.execute(query, tuple(params))
            workflows = request.env.cr.dictfetchall()

            for wf in workflows:
                if isinstance(wf.get('expire_date'), datetime):
                    wf['expire_date'] = wf['expire_date'].isoformat()
                if isinstance(wf.get('created_at'), datetime):
                    wf['created_at'] = wf['created_at'].isoformat()

            _logger.info(f"Workflows found: {workflows}")

            return Response(
                json.dumps({'success': True, 'message': 'Workflows retrieved successfully', 'data': workflows}),
                content_type='application/json',
                status=200
            )

        except Exception as e:
            _logger.error(f"Error in apply_workflow: {str(e)}")
            return Response(
                json.dumps({'success': False, 'message': str(e)}),
                content_type='application/json',
                status=500
            )

    @http.route('/api/check_expire', type='json', auth='public', methods=['POST'], csrf=False)
    def check_expire(self):
        jp_tz = pytz.timezone('Asia/Tokyo')
        now = datetime.now()
        _logger.info(f"Now: {now}")
        expired_workflows = request.env['vit.workflow'].sudo().search([])

        if not expired_workflows:
            return {'success': False, 'message': 'No expired workflows found'}

        for workflow in expired_workflows:
            contract_enddate_at = workflow.opportunities_id.contract_enddate_at if workflow.opportunities_id.contract_enddate_at else None
            expire_date = workflow.expire_date if workflow.expire_date else None
            _logger.info(f"Workflow: {workflow.id}")
            _logger.info(f"Start contract: {contract_enddate_at}")
            _logger.info(f"expire date: {expire_date}")

            if contract_enddate_at:
                if contract_enddate_at < now and workflow.status == 3:
                    new_expire_date = expire_date + timedelta(days=7) if expire_date else now + timedelta(days=7)
                    workflow.sudo().write({
                        'status': 4,
                        'expire_date': new_expire_date
                    })

                    opportunities = request.env['vit.opportunities'].sudo().browse(int(workflow.opportunities_id.id))
                    opportunities_name = opportunities.subject if opportunities else 'なし'
                    user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
                    company_name = user.company_id.name if user.company_id else 'なし'
                    username = user.username if user.username else 'なし'
                    user_email = user.email if user.email else 'なし'

                    partner = request.env['vit.partner'].sudo().browse(int(workflow.resumes_id.id))
                    partner_name = partner.initial_name if partner else 'なし'
                    user = request.env['vit.users'].sudo().browse(int(partner.created_by))
                    company_name_res = user.company_id.name if user.company_id else 'なし'
                    user_name_res = user.username if user.username else 'なし'
                    user_email_res = user.email if user.email else 'なし'

                    common.sendMailWorkflowOppChange(company_name, username, opportunities_name, workflow.status, user_email)
                    common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, workflow.status, user_email_res)

                    _logger.info(f"Updated workflow {workflow.id} to status 4 and extended expire date by 7 days from the old expire date")

            if expire_date:
                if expire_date < now and workflow.status == 4:
                    workflow.sudo().write({'status': 5})

                    opportunities = request.env['vit.opportunities'].sudo().browse(int(workflow.opportunities_id.id))
                    opportunities_name = opportunities.subject if opportunities else 'なし'
                    user = request.env['vit.users'].sudo().browse(int(opportunities.created_by))
                    company_name = user.company_id.name if user.company_id else 'なし'
                    username = user.username if user.username else 'なし'
                    user_email = user.email if user.email else 'なし'

                    partner = request.env['vit.partner'].sudo().browse(int(workflow.resumes_id.id))
                    partner_name = partner.initial_name if partner else 'なし'
                    user = request.env['vit.users'].sudo().browse(int(partner.created_by))
                    company_name_res = user.company_id.name if user.company_id else 'なし'
                    user_name_res = user.username if user.username else 'なし'
                    user_email_res = user.email if user.email else 'なし'

                    common.sendMailWorkflowOppChange(company_name, username, opportunities_name, workflow.status, user_email)
                    common.sendMailWorkflowResChange(company_name_res, user_name_res, partner_name, workflow.status, user_email_res)

                    _logger.info(f"Updated workflow {workflow.id} to status 5")

        return {
            'success': True,
            'message': 'Updated status for expired workflows',
            'updated_workflows': expired_workflows.mapped('id')
        }

