# common
APP_NAME = "MI-52"
CREAT_TOAST_ERROR_MESSAGE = "入力内容に誤りがあります。"
CREAT_TOAST_SUCCESS_MESSAGE = "登録しました。"

# login
LOGIN_TOAST_ERROR_MESSAGE = "メールアドレスまたはパスワードが不正です。"

# signup
SIGNUP_EMAIL_ERROR_MESSAGE = "メールアドレスは不正な値です"
SIGNUP_EMAIL_EXIST_ERROR_MESSAGE = "Email already exists"

from odoo.http import request
from datetime import datetime
import json
import logging
import hashlib
import uuid
import re

_logger = logging.getLogger(__name__)

# Dictionary chuyển đổi status cho Opportunity
OPP_STATUS_MAPPING = {
    0: '日程調整待ち',
    1: '日程調整済',
    2: '面談結果待ち',
    3: '面談済',
    4: '成約済',
    5: '成約済',
    6: 'キャンセル済'
}

# Dictionary chuyển đổi status cho Resume
RES_STATUS_MAPPING = {
    0: '日程調整待ち',
    1: '日程調整済',
    2: '面談結果待ち',
    3: '面談済',
    4: '成約済',
    5: '成約済',
    6: 'キャンセル済'
}

def convert_opp_status_to_text(status):
    """
    Chuyển đổi status từ số sang text tiếng Nhật cho Opportunity

    Args:
        status (int): Số status cần chuyển đổi

    Returns:
        str: Text tiếng Nhật tương ứng với status
    """
    return OPP_STATUS_MAPPING.get(status, '不明なステータス')

def convert_res_status_to_text(status):
    """
    Chuyển đổi status từ số sang text tiếng Nhật cho Resume

    Args:
        status (int): Số status cần chuyển đổi

    Returns:
        str: Text tiếng Nhật tương ứng với status
    """
    return RES_STATUS_MAPPING.get(status, '不明なステータス')

def get_japanese_weekday(date_str):
    """
    Chuyển đổi ngày thành thứ trong tuần bằng tiếng Nhật

    Args:
        date_str (str): Ngày theo format 'YYYY/MM/DD'

    Returns:
        str: Thứ trong tuần bằng tiếng Nhật (月、火、水、木、金、土、日)
    """
    try:
        # Chuyển đổi string thành datetime object
        date_obj = datetime.strptime(date_str, '%Y/%m/%d')

        # Mapping thứ trong tuần (0=Monday, 6=Sunday)
        weekday_mapping = {
            0: '月',  # Thứ 2
            1: '火',  # Thứ 3
            2: '水',  # Thứ 4
            3: '木',  # Thứ 5
            4: '金',  # Thứ 6
            5: '土',  # Thứ 7
            6: '日'   # Chủ nhật
        }

        return weekday_mapping.get(date_obj.weekday(), '曜')
    except:
        return '曜'

def get_email_template(template_name):
    """
    Lấy email template từ database theo tên template
    
    Args:
        template_name (str): Tên template (ví dụ: 'Workflow: Opportunity Status Change')
    
    Returns:
        record: Template record hoặc None nếu không tìm thấy
    """
    template = request.env['vit.email_template'].sudo().search([
        ('name', '=', template_name),
        ('is_active', '=', True)
    ], limit=1)
    return template

def send_email_from_template(template_name, context_data):
    """
    Gửi email sử dụng template từ database
    
    Args:
        template_name (str): Tên template
        context_data (dict): Dữ liệu để thay thế placeholder trong template
    
    Returns:
        dict: Kết quả gửi email
    """
    mail_server = request.env['ir.mail_server'].sudo().search([], limit=1)
    if not mail_server:
        return {'success': False, 'message': 'No mail server configured'}

    template = get_email_template(template_name)
    if not template:
        return {'success': False, 'message': f'Template "{template_name}" not found'}

    # Thay thế placeholder trong subject và body
    subject = template.subject
    body_html = template.body_html
    
    for key, value in context_data.items():
        placeholder = f"{{{key}}}"
        subject = subject.replace(placeholder, str(value) if value else '')
        body_html = body_html.replace(placeholder, str(value) if value else '')

    mail_values = {
        'subject': subject,
        'body_html': body_html,
        'email_from': mail_server.smtp_user,
        'email_to': context_data.get('user_email', ''),
    }

    try:
        mail = request.env['mail.mail'].sudo().create(mail_values)
        mail.sudo().send()
        return {'success': True, 'message': 'Sent mail successfully', 'email_sent': context_data.get('user_email', '')}
    except Exception as e:
        _logger.error(f"Failed to send email using template {template_name}: {str(e)}")
        return {'success': False, 'message': f'Failed to send email: {str(e)}'}

def sendMailWorkflowOppChange(company_name, user_name, opp_name, status, user_email, url=None, date=None, time_start=None, time_end=None, remind=False, is_link=False, interview_result=None, message=None):
    """
    Gửi email thông báo khi có thay đổi trong workflow cơ hội.

    Args:
        company_name (str): Tên công ty
        user_name (str): Tên người dùng
        opp_name (str): Tên cơ hội
        status (str): Trạng thái
        user_email (str): Email người dùng
        url (str): URL phỏng vấn

    Returns:
        dict: Kết quả gửi email với các thông tin:
            - success (bool): Trạng thái gửi email
            - message (str): Thông báo kết quả
            - email_sent (str, optional): Email đã gửi nếu thành công
    """
    # Chuyển đổi status sang text sử dụng hàm cho Opportunity
    status_text = convert_opp_status_to_text(status)
    
    # Xây dựng interview_info nếu có thông tin phỏng vấn
    interview_info = ""
    if status == 1 and date and time_start and time_end:
        weekday = get_japanese_weekday(date)
        interview_info = f'<p>▼面談日程：{date} ({weekday}) {time_start}～{time_end}</p>'
        if url:
            interview_info += f'<p>{"▼面談URL" if is_link else "▼面談メッセージ"}：{url}</p>'
    
    # Xây dựng status_text cho template
    status_text_html = f'<p>▼ステータス：{status_text}</p>' if status != 1 else ''
    
    link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}mypage"
    
    context_data = {
        'company_name': company_name,
        'user_name': user_name,
        'opp_name': opp_name,
        'status_text': status_text_html,
        'interview_info': interview_info,
        'link': link,
        'user_email': user_email
    }
    
    return send_email_from_template('Workflow: Opportunity Status Change', context_data)

def sendMailWorkflowResChange(company_name, user_name, resume_name, status, user_email, url=None, date=None, time_start=None, time_end=None, remind=False, is_link=False, interview_result=None, message=None, opp_name=None):
    """
    Gửi email thông báo khi có thay đổi trong workflow cơ hội.

    Args:
        company_name (str): Tên công ty
        user_name (str): Tên người dùng
        resume_name (str): Tên resume
        status (str): Trạng thái
        user_email (str): Email người dùng
        url (str): URL phỏng vấn
        interview_result (str): Kết quả phỏng vấn
        message (str): Thông điệp
        opp_name (str): Tên cơ hội

    Returns:
        dict: Kết quả gửi email với các thông tin:
            - success (bool): Trạng thái gửi email
            - message (str): Thông báo kết quả
            - email_sent (str, optional): Email đã gửi nếu thành công
    """
    # Chuyển đổi status sang text sử dụng hàm cho Resume
    status_text = convert_res_status_to_text(status)
    
    link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}mypage"
    
    # Template đặc biệt cho status 3 (面談済 - OK) và status 6 (NG) với kết quả phỏng vấn
    if (status == 3 or status == 6) and interview_result:
        result_text = "OK" if interview_result == "Pass" else "NG"
        message_block = f'<p>▼メッセージ：{message}</p>' if message else ''
        
        context_data = {
            'company_name': company_name,
            'user_name': user_name,
            'resume_name': resume_name,
            'opp_name': opp_name,
            'result_text': result_text,
            'message_block': message_block,
            'link': link,
            'user_email': user_email
        }
        
        return send_email_from_template('Workflow: Resume Interview Result', context_data)
    else:
        # Template thông thường cho các status khác
        # Xây dựng interview_info nếu có thông tin phỏng vấn
        interview_info = ""
        if status == 1 and date and time_start and time_end:
            weekday = get_japanese_weekday(date)
            interview_info = f'<p>▼面談日程：{date} ({weekday}) {time_start}～{time_end}</p>'
            if url:
                interview_info += f'<p>{"▼面談URL" if is_link else "▼面談メッセージ"}：{url}</p>'
        
        # Xây dựng status_text cho template
        status_text_html = f'<p>▼ステータス：{status_text}</p>' if status != 1 else ''
        
        context_data = {
            'company_name': company_name,
            'user_name': user_name,
            'resume_name': resume_name,
            'status_text': status_text_html,
            'interview_info': interview_info,
            'link': link,
            'user_email': user_email
        }
        
        return send_email_from_template('Workflow: Resume Status Change', context_data)

def sendMailStandardPlan(userid):
    """
    Gửi email thông báo đăng ký plan Standard thành công
    
    Args:
        userid (int): ID của user
        
    Returns:
        dict: Kết quả gửi email
    """
    user = request.env['vit.users'].sudo().search([('id', '=', userid)], limit=1)
    if not user:
        return {'success': False, 'message': 'User not found'}
    
    context_data = {
        'company_name': user.company_id.name if user.company_id else '',
        'user_name': user.username,
        'user_email': user.email
    }
    
    return send_email_from_template('Plan: Standard Registration', context_data)

def sendMailPremiumPlan(userid):
    """
    Gửi email thông báo đăng ký plan Premium thành công
    
    Args:
        userid (int): ID của user
        
    Returns:
        dict: Kết quả gửi email
    """
    user = request.env['vit.users'].sudo().search([('id', '=', userid)], limit=1)
    if not user:
        return {'success': False, 'message': 'User not found'}
    
    context_data = {
        'company_name': user.company_id.name if user.company_id else '',
        'user_name': user.username,
        'user_email': user.email
    }
    
    return send_email_from_template('Plan: Premium Registration', context_data)