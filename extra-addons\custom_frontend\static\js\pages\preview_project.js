import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const PreviewProject = {
    'template': `
<main class="margin-header sp_fluid" id="vue-app" style="background-color: white">
    <div class="container-fluid d-none d-md-block">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '探す', link: null },
            { text: '案件・人財を探す', link: null },
            { text: '案件を探す', link: '/opportunities/active' },
            { text: '{{subject}}', link: null, current: true }
        ], 'container-fluid', '')}
    </div>
    <div class="container-fluid grabient pt-3">
        <div aria-labelledby="nothing_resume_modal" class="modal" data-backdrop="true" id="nothing_resume" tabindex="-1"
             style="display: none;" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header"><h4 class="modal-title w-100">人財が登録されていません</h4></div>
                    <div class="modal-body"><p class="text-center font-extralarge">人財を作成していない場合</p>
                        <div class="row justify-content-md-center">
                            <div class="col-12"><a class="btn btn-default btn-lg btn-block waves-effect waves-light"
                                                   href="/resumes/manage/new">人財を新規登録</a>
                            </div>
                        </div>
                        <p class="text-center font-extralarge mt-5">人財を作成している場合</p>
                        <div class="row justify-content-md-center">
                            <div class="col-12"><a class="btn btn-default btn-lg btn-block waves-effect waves-light"
                                                   href="/resumes/manage/index">人財管理一覧</a>
                            </div>
                            <div class="col-12 text-center">
                                <button aria-label="Close"
                                        class="btn btn-outline-blue-grey mt-5 waves-effect waves-light"
                                        data-dismiss="modal">閉じる
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-md-10 col-lg-8 mx-auto">
                <div class="mb-4 card">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="d-inline-block nowrap" data-toggle="tooltip" title="" data-original-title="ブックマーク">
                                    <a id="anchor-bookmark125885" class="z-2 py-2" @click="toggleBookmark">
                                        <i class="material-icons ml-3" style="color: #1072e9;">
                                            {{ isBookmarked ? 'bookmark' : 'bookmark_border' }}
                                        </i>
                                    </a>
                                    <span class="pl-1 font-middle z-2 py-2 pr-2">{{ numberReact }}</span>
                                </div>
                                <div class="ml-3">
                                    <h2 class="font-extralarge mb-1">{{ subject }}</h2>
                                    <span class="small">
                                        <span v-if="updated_at" class="mr-2">更新：{{ updated_at }}</span><span>登録：{{ created_at }}</span>
                                    </span>
                                </div>
                            </div>
                            <div class="d-inline-block nowrap" data-toggle="tooltip" title="" data-original-title="テキストで表示">
                                <a class="mdb-modal-form" data-target="#display_text_format" data-toggle="modal" href="">
                                    <i class="material-icons pl-1 align-text-bottom">file_copy</i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body px-md-4">
                        <div class="mx-auto">
                            <div class="row">
                                <div class="col-12">
                                    <div class="card my-4 bg-grey-1 z-depth-0">
                                        <div class="card-body px-md-4" style="background-color: #f4f4f4;">
                                            <div class="w-100">
                                                <div class="d-md-flex align-items-start">
                                                    <div class="flex-shrink-0 w-60px mr-md-8 pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block"
                                                         style="width: 69px; margin-right:50px !important;">
                                                        単価
                                                    </div>
                                                    <div class="pt-1 pb-3 pb-md-1 font-ll custom-sub-text d-block">
                                                        <div v-if="!skill_matching_flg">{{unit_price_min}}万円 〜 {{unit_price_max}}万円 / 月</div>
                                                        <div v-else style="color: #000;">スキル見合い</div>
                                                    </div>
                                                </div>
                                                <div class="d-md-flex align-items-start">
                                                    <div class="flex-shrink-0 w-60px mr-md-8 pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block">
                                                        稼働率
                                                    </div>
                                                    <div class="pt-1 pb-3 pb-md-1 font-middle d-block">
                                                        <div>{{Array.isArray(utilization_rate) ? utilization_rate.map(rate => categoryMap[rate] || '').join('/') : ''}}</div>
                                                    </div>
                                                </div>
                                                <div class="d-md-flex align-items-start">
                                                    <div class="flex-shrink-0 w-60px mr-md-8 pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block">
                                                        出社頻度
                                                    </div>
                                                    <div class="pt-1 pb-3 pb-md-1 font-middle d-block">
                                                        <div>{{Array.isArray(work_frequencies) ? work_frequencies.map(fr => categoryMap[fr] || '').join('/') : ''}}</div>
                                                    </div>
                                                </div>
                                                <div class="d-md-flex align-items-start">
                                                    <div class="flex-shrink-0 w-60px mr-md-8 pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block">
                                                        就業場所
                                                    </div>
                                                    <div class="pt-1 pb-3 pb-md-1 font-middle d-block">
                                                        <div>{{Array.isArray(specifies_workplaces) ? specifies_workplaces.join("/") : ''}}</div>
                                                    </div>
                                                </div>
                                                <div class="d-md-flex align-items-start">
                                                    <div class="flex-shrink-0 w-60px mr-md-8 pt-1 pb-md-1 custom-grey-6-text ex-bold th-sm d-block">
                                                        契約期間
                                                    </div>
                                                    <div class="pt-1 pb-md-1 font-middle d-block">
                                                        <div>{{contract_startdate_at}} 〜 {{contract_enddate_at}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="truncate-information position-relative overflow-hidden">
                            <div class="mx-auto"><h3 class="font-middle mb-3 heading-label">案件領域</h3>
                                <div class="pl-3 font-middle mb-5">
                                    <div v-show="categories_design.length > 0" class="mb-3 d-md-flex">
                                        <span class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">設計</span>
                                        <div>
            <span v-for="(category, index) in categories_design" :key="index" class="d-inline-block">
                {{ categoryMap[category] || category }}
                <span v-if="index < categories_design.length - 1"> / </span>
            </span>
                                        </div>
                                    </div>
                                    <div v-show="categories_development.length > 0" class="mb-3 d-md-flex">
                                        <span class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">開発</span>
                                        <div>
            <span v-for="(category, index) in categories_development" :key="index" class="d-inline-block">
                {{ categoryMap[category] || category }}
                <span v-if="index < categories_development.length - 1"> / </span>
            </span>
                                        </div>
                                    </div>
                                    <div v-show="categories_infrastructure.length > 0" class="mb-3 d-md-flex">
                                        <span class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">インフラ</span>
                                        <div>
            <span v-for="(category, index) in categories_infrastructure" :key="index" class="d-inline-block">
                {{ categoryMap[category] || category }}
                <span v-if="index < categories_infrastructure.length - 1"> / </span>
            </span>
                                        </div>
                                    </div>
                                    <div v-show="categories_operation_maintenance.length > 0" class="mb-3 d-md-flex">
                                        <span class="px-2 mr-2 font-small custom-grey-6-text border-grey-3 text-nowrap mb-auto">運用・保守</span>
                                        <div>
            <span v-for="(category, index) in categories_operation_maintenance" :key="index" class="d-inline-block">
                {{ categoryMap[category] || category }}
                <span v-if="index < categories_operation_maintenance.length - 1"> / </span>
            </span>
                                        </div>
                                    </div>
                                </div>

                                <h3 class="font-middle mb-3 heading-label">案件内容</h3>
                                <div class="pl-3 font-middle mb-5" style="white-space: pre-line;"><p>
                                    {{requirements}}</p></div>
                                <h3 class="font-middle mb-3 heading-label">人財要件</h3>
                                <div class="pl-3 font-middle mb-5" style="white-space: pre-line;"><p>{{skill_requirements}}</p></div>
                            </div>
                        </div>
                        <div class="mx-auto">
                            <div class="row">
                                <div class="col-12"><h3 class="font-middle mb-3 heading-label">詳細情報</h3></div>
                            </div>
                        </div>
                        <div class="pl-3">
                            <div class="row px-3">
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row align-items-center py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold"><span
                                                class="d-block">案件確度</span></div>
                                        <div class="col-8 font-middle">{{categoryMap[order_accuracy_ids]}}</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row align-items-center py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold">契約形態</div>
                                        <div class="col-8 font-middle">{{Array.isArray(contract_types) ? contract_types.map(type => categoryMap['contract_' + type] || type).join(", ") : ''}}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold">商流への関与</div>
                                        <div class="col-8 font-middle">{{categoryMap[involvements]}}<br><a
                                                id="display_involvement_description_btn"
                                                data-toggle="modal" data-target="#involvement_description_modal"
                                                class="default-main-color"
                                                href="javascript:void(0);">商流への関与の図解を表示</a></div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold">案件の商流</div>
                                        <div class="col-8 font-middle">{{categoryMap[opp_type_id] || opp_type_id}}<br><a
                                                id="trading_flow_btn"
                                                data-toggle="modal" data-target="#trading_flow_modal"
                                                class="default-main-color"
                                                href="javascript:void(0);">案件の商流の図解を表示</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row align-items-center py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold">応募期限</div>
                                        <div class="col-8 font-middle">{{expired_at}}</div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row align-items-center py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold">募集人数</div>
                                        <div class="col-8 font-middle">{{participants}}人</div>
                                    </div>
                                </div>
                                <div class="col-12 border-bottom pl-0 py-1">
                                    <div class="row align-items-center py-2">
                                        <div class="col-4 col-md-2 custom-grey-5-text ex-bold">募集対象</div>
                                        <div class="col font-middle">{{selectedRestrictions.map(value => categoryMap[value] || value).join(", ")}}<br><a
                                                    id="display_trading_restriction_description_btn"
                                                    data-toggle="modal" data-target="#trading_restriction_description_modal"
                                                    class="default-main-color"
                                                    href="javascript:void(0);">募集対象の図解を表示</a></div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 border-bottom pl-0 py-1">
                                    <div class="row align-items-center py-2">
                                        <div class="col-4 custom-grey-5-text ex-bold">業種</div>
                                        <div class="col-8 font-middle">{{categoryMap[business_field] || business_field}}</div>
                                    </div>
                                </div>


                            </div>
                        </div>
                        <div aria-hidden="true" aria-labelledby="involvement_description_modal" class="modal"
                             id="involvement_description_modal" role="dialog" tabindex="-1">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title w-100"></h4>
                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                    <span aria-hidden="true">
                      <i class="material-icons md-dark mb-36">
                        clear
                      </i>
                    </span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div>
                                            <picture>
                                                <source srcset="/assets/commercial_distribution_detail_sp-3fbdfc2899b18566d6c4963bb399499a9ccb62026772b7ef2187bfeb1b337757.png"
                                                        media="(max-width: 767px)">
                                                <img src="/custom_frontend/static/img/commercial_distribution_detai.png">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <div class="col-6">
                                            <button class="btn btn-default btn-block waves-effect px-0 waves-light btn-blue-grey"
                                                    data-dismiss="modal"
                                                    type="button">閉じる
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div aria-hidden="true" aria-labelledby="trading_flow_modal" class="modal"
                             id="trading_flow_modal" role="dialog" tabindex="-1">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title w-100"></h4>
                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                    <span aria-hidden="true">
                      <i class="material-icons md-dark mb-36">
                        clear
                      </i>
                    </span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div>
                                            <picture>
                                                <source srcset="/assets/trading_flow_subc_sp-a5910c75c064365401a9aae7cd7c259e4940b89e9b3c7fe428f467305529d542.png"
                                                        media="(max-width: 767px)">
                                                <img src="/custom_frontend/static/img/trading_flow_msubc.png">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <div class="col-6">
                                            <button class="btn btn-default btn-block waves-effect px-0 waves-light btn-blue-grey"
                                                    data-dismiss="modal"
                                                    type="button">閉じる
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div aria-hidden="true" aria-labelledby="trading_restriction_description_modal" class="modal"
                             id="trading_restriction_description_modal" role="dialog" tabindex="-1">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title w-100"></h4>
                                        <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                    <span aria-hidden="true">
                      <i class="material-icons md-dark mb-36">
                        clear
                      </i>
                    </span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div>
                                            <picture>
                                                <source srcset="/assets/commercial_trading_restriction_detail_sp-3dc48d568db922a75fc68973071b580d22d3ca942e3380cf22d600c82a4b1f68.png"
                                                        media="(max-width: 767px)">
                                                <img src="/custom_frontend/static/img/commercial_trading_restriction_detail.png">
                                            </picture>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <div class="col-6">
                                            <button class="btn btn-default btn-block waves-effect px-0 waves-light btn-blue-grey"
                                                    data-dismiss="modal"
                                                    type="button">閉じる
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="publish_company_name_status_id !== 'private'" class="card mt-4">
                    <div class="card-body py-md-4">
                        <div class="mx-auto">
                            <div class="row">
                                <div class="col-12"><h3 class="font-middle mb-3 heading-label">掲載企業情報</h3>
                                    <div class="col-12 pl-0 py-1 ml-3 mb-3">
                                        <div class="align-items-center py-2">
                                            <div class="ex-bold">
                                                <a :href="'/company/' + company_id + '/detail'" style="text-decoration: none;">会社詳細</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-center flex-wrap">
                    <div v-if="prevOpportunity" class="col-12 col-md-6 px-0 pr-md-2">
                        <h6 class="mt-sm-3 mt-4 mb-3 text-center">前の案件情報</h6>
                        <div class="card hoverable font-middle mb-2">
                            <a :href="'/opportunities/' + prevOpportunity.id + '/detail?prev_next_display=display'">
                                <div class="p-3 bg-light-green text-default card-header" style="height: 80px;">
                                    {{ prevOpportunity.subject }}
                                </div>
                                <div class="card-content prev_next_class default-color-text" style="height: 184.028px;">
                                    <span class="custom-grey-5-text">案件内容</span>
                                    <p class="mb-2 shorten-into-2-line">{{ prevOpportunity.requirements }}</p>
                                    <span class="custom-grey-5-text">人財要件</span>
                                    <p class="mb-0 shorten-into-2-line">{{ prevOpportunity.skill_requirements }}</p>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div v-if="nextOpportunity" class="col-12 col-md-6 px-0 pl-md-2 mb-3 mb-md-0">
                        <h6 class="my-3 text-center">次の案件情報</h6>
                        <div class="card hoverable font-middle text-md-left mb-2">
                            <a :href="'/opportunities/' + nextOpportunity.id + '/detail?prev_next_display=display'">
                                <div class="p-3 bg-light-green text-default card-header" style="height: 80px;">
                                    {{ nextOpportunity.subject }}
                                </div>
                                <div class="card-content prev_next_class default-color-text" style="height: 184.028px;">
                                    <span class="custom-grey-5-text">案件内容</span>
                                    <p class="mb-2 shorten-into-2-line">{{ nextOpportunity.requirements }}</p>
                                    <span class="custom-grey-5-text">人財要件</span>
                                    <p class="mb-0 shorten-into-2-line">{{ nextOpportunity.skill_requirements }}</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12 mt-3 mb-4"><a
                            class="btn btn-blue-grey d-block font-default d-md-inline-block waves-effect waves-light m-0"
                            href="/opportunities/active">案件検索に戻る</a></div>
                </div>

                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>
                                ◆案件名: {{subject}}<br>
                                ◆案件領域: {{[].concat(
                                    Array.isArray(categories_design) ? categories_design : [],
                                    Array.isArray(categories_development) ? categories_development : [],
                                    Array.isArray(categories_infrastructure) ? categories_infrastructure : [],
                                    Array.isArray(categories_operation_maintenance) ? categories_operation_maintenance : []
                                ).map(cat => categoryMap[cat] || cat).join(", ")}}<br>
                                ◆就業場所: {{Array.isArray(specifies_workplaces) ? specifies_workplaces.join("/") : ''}}<br>
                                ◆業種: {{categoryMap[business_field] || business_field}}<br>
                                ◆単価: {{unit_price_min}}万円 〜 {{unit_price_max}}万円 / 月<br>
                                ◆契約期間: {{contract_startdate_at}} 〜 {{contract_enddate_at}}<br>
                                ◆募集人数: {{participants}}人<br>
                                ◆募集対象: {{selectedRestrictions.map(value => categoryMap[value] || value).join(", ")}}<br>
                                ◆稼働率: {{Array.isArray(utilization_rate) ? utilization_rate.join(",") : ''}}<br>
                                ◆出社頻度: {{Array.isArray(work_frequencies) ? work_frequencies.map(fr => categoryMap[fr] || fr).join("／") : ''}}<br>
                                ◆案件確度: {{categoryMap[order_accuracy_ids]}}<br>
                                ◆商流への関与: {{categoryMap[involvements]}}<br>
                                ◆案件の商流: {{categoryMap[opp_type_id] || opp_type_id}}<br>
                                ◆契約形態: {{Array.isArray(contract_types) ? contract_types.map(type => categoryMap[type] || type).join(", ") : ''}}<br>
                                ◆面談回数: {{ (interview_count_id || '').split(',').map(it => categoryMap[it] || '').join('／') || '' }}<br>
                                ◆案件内容: <br>{{requirements}}<br>
                                ◆人財要件: <br>{{skill_requirements}}<br>
                                **********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="!isOwner"
             class="fix_right_buttom d-md-flex justify-content-end align-items-center px-3 py-4 text-right w-100">
           <span class="vertical-baseline"><a class="btn btn-default font-middle m-0 vertical-baseline waves-effect waves-light" @click="checkResume">応募<i class="material-icons md-18 pl-1 vertical-sub" style="line-height: 3vh !important;">send</i></a></span>
        </div>
        <div v-else
             class="fix_right_buttom d-md-flex justify-content-end align-items-center px-3 py-4 text-right w-100">
            <div class="alert alert-warning w-100">ご自身または自社の案件です</div>
        </div>
    </div>
</main>
<link rel="stylesheet" href="/custom_frontend/static/css/preview_project.css"/>
<link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            textareaContent: '',
            subject: "",
            categories_design: [],
            categories_development: [],
            categories_infrastructure: [],
            categories_operation_maintenance: [],
            utilization_rate: [],
            unit_price_min: "",
            unit_price_min: "",
            skill_matching_flg: "",
            work_frequencies: [],
            specifies_workplaces: [],
            possible_continue_flg: "",
            requirements: "",
            skill_requirements: "",
            order_accuracy_ids: "",
            involvements: "",
            opp_type_id: "",
            contract_types: [],
            expired_at: "",
            participants: "",
            interview_count_id: "",
            trading_restriction: [],
            opp_qualities: [],
            public_status_id: "",
            publish_company_name_status_id: "",
            created_at: "",
            updated_at: "",
            selectedRestrictions: [],
            company_id: "",
            userID: "",
            isOwner: false,
            isBookmarked: false,
            numberReact: 0,
            prevOpportunity: null,
            nextOpportunity: null,
            opp_id: "",
            business_field: "",
        }
    },
    mounted() {
        this.$nextTick(() => {
            $('[data-toggle="tooltip"]').each(function () {
                $(this).tooltip({
                    container: $(this).parent(),
                    placement: 'top'
                });
            });
        });
        this.userID = userInfo ? userInfo.user_id : null;
        this.opp_id = parseInt(this.$route.params.id);
        this.GetOpp();
        this.getNextPrev();
        this.getReaction();

    },
    computed: {
        remainingText() {
            const remaining = 1000 - this.textareaContent.length;
            return `あと${remaining}文字`;
        },
        categoryMap() {
            return {
                // categories
                "design_pmo": "PMO",
                "design_pmpl": "PM・PL",
                "design_DX": "DX",
                "design_cloud": "クラウド",
                "design_strategy": "モダナイゼション",
                "design_work": "セキュリティ",
                "design_it": "ITインフラ",
                "design_ai": "AI",

                // categoriesDev
                "dev_pmo": "PMO",
                "dev_pmpl": "PM・PL",
                "dev_web": "Webシステム",
                "dev_ios": "IOS",
                "dev_android": "Android",
                "dev_control": "制御",
                "dev_embedded": "組込",
                "dev_ai": "AI・DL・ML",
                "dev_test": "テスト",
                "dev_cloud": "クラウド",
                "dev_architect": "サーバ",
                "dev_bridge_se": "データベース",
                "dev_network": "ネットワーク",
                "dev_mainframe": "メインフレーム",

                // categoriesInfra
                "infra_pmo": "PMO",
                "infra_pmpl": "PM・PL",
                "infra_server": "サーバー",
                "infra_network": "ネットワーク",
                "infra_db": "データベース",
                "infra_cloud": "クラウド",
                "infra_virtualized": "仮想化",
                "infra_mainframe": "メインフレーム",

                // categories3
                "operation_pmo": "業務システム",
                "operation_pmpl": "オープン",
                "operation_DX": "クラウド",
                "operation_mainframe": "メインフレーム",
                "operation_strategy": "ヘルプデスク",

                // contract type
                "contract_quas": "業務委託（準委任）",
                "contract_subc": "業務委託（請負）",
                "contract_temp": "派遣契約",

                //invol
                "enter_sales_channels": "商流に入る",
                "agent_only": "仲介のみ",

                //opp_qualities
                "700thousand_or_more": "70万円以上",
                "1million_or_more": "100万円以上",
                "less_1year_ok": "1年未満OK",
                "newcomer_ok": "新人でもOK",
                "over_50years_old_ok": "50代以上OK",
                "foreign_nationality_ok": "外国籍OK",
                "leader_recruitment": "リーダー募集",
                "english_skill": "英語力",
                "interview_once": "面談1回",
                "take_home_project": "持ち帰りOK",
                "team_proposal_ok": "チーム提案OK",
                "web_interview_ok": "ウェブ面談可能",
                "remote__location_ok": "案件場所から遠隔地居住でもOK",
                "long_term": "日本以外の居住者OK",
                "overseas_resident_ok": "長期（２年以上継続可能性）",

                //restriction
                "own_employee": "自社社員",
                "one_subcontract_employee": "協力会社社員（一社先）",
                "more_subcontract_employee": "協力会社社員（二社先以降）",
                "freelance_person": "フリーランス（本人）",
                "subcontract_freelance": "フリーランス（一社先）",
                "more_subcontract_freelance": "フリーランス（二社先以降）",

                //order_accuracy_ids
                "afte": "確定済み",
                "befo": "確定前",

                //opp_type_id
                "clnt": "エンド　　自社プロジェクトの案件",
                "prim": "元請　　エンド企業から直接依頼を受けた場合",
                "subc": "一次請　　元請企業から依頼を受けた場合",
                "msubc": "二次請以降　　一次請以降の企業から依頼を受けた場合",

                //utilization_rate
                '100': '100%（フル稼働）',
                '75': '75%',
                '50': '50%',
                '25': '25%',

                //work_frequencies
                '5days': '週5日出社',
                '2to4days': '週4 〜 2日出社',
                'less_than_1day': '週1日未満出社',
                'full_remote': 'フルリモート',
                '1day': '週1日出社',

                //interview_count_id
                "once": "1回",
                "few": "1〜2回",
                "twice": "2回",
                "over_three_times": "3回以上",

                //business_field
                "it_telecom_internet": "情報通信・インターネット",
                "automobile_machinery": "自動車・機械",
                "electronics": "エレクトロニクス機器",
                "resources_energy_materials": "資源・エネルギー・素材",
                "finance_corporate_services": "金融・法人サービス",
                "food_agriculture": "食品・農業",
                "consumer_goods_pharmaceuticals": "生活用品・嗜好品・薬",
                "entertainment_media": "娯楽・エンタメ・メディア",
                "construction_real_estate": "建設・不動産",
                "logistics_transport": "運輸・物流",
                "retail_dining": "流通・外食",
                "public_services": "生活・公共サービス"
            };
        }
    },
    methods: {
        updateCharCount() {
            const textarea = document.getElementById('body_field');
            const countDisplay = document.querySelector('.show-count');
            const maxDisplay = document.querySelector('.show-count-wrapper .custom-grey-text:nth-child(2)');
            const remaining = 1000 - textarea.value.length;
            countDisplay.textContent = `あと${remaining}文字`;
            if (remaining < 0) {
                countDisplay.style.color = 'red';
                maxDisplay.style.color = 'red';
            } else {
                countDisplay.style.color = ''; // Trả về màu mặc định
                maxDisplay.style.color = '';
            }
        },
        convertJapaneseDate(jpDate) {
            let parts = jpDate.match(/(\d{4})年(\d{2})月(\d{2})日/);
            if (!parts) return null;

            let year = parts[1];
            let month = parts[2];
            let day = parts[3];

            return `${year}-${month}-${day}`;
        },
        convertDateToJapanese(date) {
            if (!date) return null;
            const year = date.getFullYear();
            const month = ('0' + (date.getMonth() + 1)).slice(-2);  // Đảm bảo tháng luôn 2 chữ số
            const day = ('0' + date.getDate()).slice(-2);  // Đảm bảo ngày luôn 2 chữ số
            return `${year}年${month}月${day}日`;
        },
        async GetOpp() {
            let response = await fetch(`/api/opp_edit?id=${this.opp_id}`);

            if (!response.ok) {
                console.error("Error fetching opportunity:", response.statusText);
                return;
            }

            let result = await response.json();
            console.log("data: ", result.data);
            if (result.success) {
                this.subject = result.data.subject;

                // Sử dụng filter để loại bỏ các phần tử trống sau khi split
                this.categories_design = result.data.categories_design ? result.data.categories_design.split(",").map(item => item.trim()).filter(item => item && Object.keys(this.categoryMap).includes(item)) : [];
                this.categories_development = result.data.categories_development ? result.data.categories_development.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.categories_infrastructure = result.data.categories_infrastructure ? result.data.categories_infrastructure.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.categories_operation_maintenance = result.data.categories_operation_maintenance ? result.data.categories_operation_maintenance.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.utilization_rate = result.data.utilization_rate ? result.data.utilization_rate.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.unit_price_min = String(result.data.unit_price_min ?? 0);
                this.unit_price_max = String(result.data.unit_price_max ?? 0);
                this.skill_matching_flg = result.data.skill_matching_flg;
                this.work_frequencies = result.data.work_frequencies ? result.data.work_frequencies.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.specifies_workplaces = result.data.specifies_workplaces ? result.data.specifies_workplaces.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.possible_continue_flg = result.data.possible_continue_flg;
                this.requirements = result.data.requirements;
                this.skill_requirements = result.data.skill_requirements;
                this.order_accuracy_ids = result.data.order_accuracy_ids;
                this.involvements = result.data.involvements;
                this.opp_type_id = result.data.opp_type_id;
                this.contract_types = result.data.contract_types ? result.data.contract_types.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.participants = String(result.data.participants ?? "");
                this.interview_count_id = result.data.interview_count_id;
                this.selectedRestrictions = result.data.trading_restriction ? result.data.trading_restriction.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.opp_qualities = result.data.opp_qualities ? result.data.opp_qualities.split(",").map(item => item.trim()).filter(item => item !== "") : [];
                this.public_status_id = result.data.public_status_id;
                this.publish_company_name_status_id = result.data.publish_company_name_status_id;
                this.business_field = result.data.business_field || "";
                this.created_at = result.data.created_at ? this.convertDateToJapanese(new Date(result.data.created_at)) : '';
                this.updated_at = result.data.updated_at ? this.convertDateToJapanese(new Date(result.data.updated_at)) : '';
                this.contract_startdate_at = result.data.contract_startdate_at ? this.convertDateToJapanese(new Date(result.data.contract_startdate_at)) : '';
                this.contract_enddate_at = result.data.contract_enddate_at ? this.convertDateToJapanese(new Date(result.data.contract_enddate_at)) : '';
                this.expired_at = result.data.expired_at ? this.convertDateToJapanese(new Date(result.data.expired_at)) : '';
                this.company_id = result.data.company_id;
                this.isOwner = this.userID === parseInt(result.data.created_by);

                let status = result.data.status;
                if (status !== null) {
                    this.isDisabled = status === "0" ? true : false;
                }
                console.log("rate: ", this.utilization_rate);
                // console.log("convert rate: ", (this.utilization_rate || '').map(rate => this.categoryMap[rate] || '').join('/') || '');
            }
        },
        async getNextPrev() {
            try {
                const response = await fetch(`/api/opp_prev_next?id=${this.opp_id}`);
                const result = await response.json();
                if (result.success) {
                    this.prevOpportunity = result.data.prev || null;
                    this.nextOpportunity = result.data.next || null;
                    console.log("prev/next: ", this.prevOpportunity, this.nextOpportunity);
                } else {
                    console.error("Error fetching previous/next opportunity:", result.message);
                }
            } catch (error) {
                console.error("Request failed:", error);
            }
        },
        async checkResume() {
            try {
                const response = await fetch(`/api/resumes?user_id=${this.userID}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();
                console.log("Resumes:", result);
                const hasResume = result.resumes.length > 0;

                if (!hasResume) {
                    $('#nothing_resume').modal('show');
                } else {
                    window.location.href = `/messages/${this.opp_id}/select_resumes_for_apply`;
                }
            } catch (error) {
                console.error("Lỗi khi gọi API:", error);
            }
        },
        async toggleBookmark() {
            try {
                const response = await fetch('/api/react_opp', {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        user_id: this.userID,  // ID người dùng
                        opportunities_id: this.opp_id,  // ID cơ hội
                    }),
                });

                const result = await response.json();
                if (result.result.success) {
                    this.getReaction();
                }
            } catch (error) {
                console.error("Bookmark failed:", error);
            }
        },
        async getReaction() {
            const response = await fetch("/api/react_count_opp", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ user_id: this.userID, opportunities_id: this.opp_id })
            });

            const result = await response.json();
            if (result.result.success) {
                this.numberReact = result.result.likes;
                this.isBookmarked = result.result.liked;
            }
        }
    },
}
export default PreviewProject;