import random
import string
import hashlib
import time
from datetime import datetime, timedelta
import base64
from io import BytesIO
import logging

_logger = logging.getLogger(__name__)

class SimpleCaptcha:
    def __init__(self):
        # Store CAPTCHA challenges temporarily
        self.active_challenges = {}  # session_id -> challenge_info
        
        # Configuration
        self.config = {
            'challenge_length': 5,
            'expiry_minutes': 10,
            'max_attempts': 3
        }
    
    def generate_challenge(self, session_id):
        """
        Generate a simple math CAPTCHA challenge
        Returns: (challenge_text: str, challenge_id: str)
        """
        # Generate simple math problem
        num1 = random.randint(1, 10)
        num2 = random.randint(1, 10)
        operation = random.choice(['+', '-'])
        
        if operation == '+':
            answer = num1 + num2
            challenge_text = f"{num1} + {num2} = ?"
        else:
            # Ensure positive result for subtraction
            if num1 < num2:
                num1, num2 = num2, num1
            answer = num1 - num2
            challenge_text = f"{num1} - {num2} = ?"
        
        # Generate challenge ID
        challenge_id = self._generate_challenge_id()
        
        # Store challenge
        expiry_time = datetime.now() + timedelta(minutes=self.config['expiry_minutes'])
        
        self.active_challenges[session_id] = {
            'challenge_id': challenge_id,
            'answer': str(answer),
            'created_at': datetime.now(),
            'expiry_time': expiry_time,
            'attempts': 0
        }
        
        # Clean expired challenges
        self._clean_expired_challenges()
        
        _logger.info(f"Generated CAPTCHA challenge for session {session_id}: {challenge_text}")
        
        return challenge_text, challenge_id
    
    def verify_challenge(self, session_id, challenge_id, user_answer):
        """
        Verify CAPTCHA challenge
        Returns: (is_valid: bool, error_message: str)
        """
        if session_id not in self.active_challenges:
            return False, "No active challenge found"
        
        challenge_info = self.active_challenges[session_id]
        
        # Check challenge ID
        if challenge_info['challenge_id'] != challenge_id:
            return False, "Invalid challenge ID"
        
        # Check expiry
        if datetime.now() > challenge_info['expiry_time']:
            del self.active_challenges[session_id]
            return False, "Challenge expired"
        
        # Increment attempts
        challenge_info['attempts'] += 1
        
        # Check max attempts
        if challenge_info['attempts'] > self.config['max_attempts']:
            del self.active_challenges[session_id]
            return False, "Too many attempts"
        
        # Verify answer
        if str(user_answer).strip() == challenge_info['answer']:
            # Success - remove challenge
            del self.active_challenges[session_id]
            _logger.info(f"CAPTCHA verified successfully for session {session_id}")
            return True, "Success"
        else:
            _logger.warning(f"CAPTCHA verification failed for session {session_id}")
            return False, "Incorrect answer"
    
    def has_active_challenge(self, session_id):
        """Check if session has active challenge"""
        if session_id not in self.active_challenges:
            return False
        
        challenge_info = self.active_challenges[session_id]
        
        # Check if expired
        if datetime.now() > challenge_info['expiry_time']:
            del self.active_challenges[session_id]
            return False
        
        return True
    
    def get_challenge_info(self, session_id):
        """Get challenge info for session"""
        if session_id not in self.active_challenges:
            return None
        
        challenge_info = self.active_challenges[session_id]
        
        return {
            'challenge_id': challenge_info['challenge_id'],
            'attempts_remaining': self.config['max_attempts'] - challenge_info['attempts'],
            'expires_at': challenge_info['expiry_time'].isoformat()
        }
    
    def _generate_challenge_id(self):
        """Generate unique challenge ID"""
        timestamp = str(int(time.time()))
        random_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        
        # Create hash
        hash_input = f"{timestamp}_{random_str}".encode('utf-8')
        challenge_id = hashlib.md5(hash_input).hexdigest()[:16]
        
        return challenge_id
    
    def _clean_expired_challenges(self):
        """Clean expired challenges"""
        now = datetime.now()
        expired_sessions = [
            session_id for session_id, challenge_info in self.active_challenges.items()
            if now > challenge_info['expiry_time']
        ]
        
        for session_id in expired_sessions:
            del self.active_challenges[session_id]
        
        if expired_sessions:
            _logger.info(f"Cleaned {len(expired_sessions)} expired CAPTCHA challenges")
    
    def get_statistics(self):
        """Get CAPTCHA statistics"""
        self._clean_expired_challenges()
        
        return {
            'active_challenges': len(self.active_challenges),
            'config': self.config
        }

# Singleton instance
simple_captcha = SimpleCaptcha()
