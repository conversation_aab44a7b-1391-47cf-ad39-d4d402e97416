/*! CSS Used from: http://localhost:8069/custom_frontend/static/css/layout.css */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    nav {
        display: block;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .float-right {
        float: right !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mr-1 {
        margin-right: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .ml-1 {
        margin-left: 0.25rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .p-2 {
        padding: 0.5rem !important;
    }

    .pl-2 {
        padding-left: 0.5rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    .text-right {
        text-align: right !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        p {
            orphans: 3;
            widows: 3;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-sm {
        padding: .5rem 1.6rem;
        font-size: .64rem;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn.btn-sm[class*=btn-outline-] {
        padding-top: .38rem;
        padding-bottom: .38rem;
    }

    .btn-default {
        color: #fff !important;
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    }

    .btn-default:hover {
        color: #fff;
        background-color: #61b8f7;
    }

    .btn-default:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:focus,
    .btn-default:active {
        background-color: #005650;
    }

    .btn-default:not([disabled]):not(.disabled):active {
        background-color: #005650 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #1072e9 !important;
    }

    .btn-outline-default:hover,
    .btn-outline-default:focus,
    .btn-outline-default:active,
    .btn-outline-default:active:focus {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
    }

    .btn-outline-default:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9;
    }

    .p-0 {
        padding: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mr-1 {
        margin-right: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .ml-1 {
        margin-left: .25rem !important;
    }

    .p-2 {
        padding: .5rem !important;
    }

    .pl-2 {
        padding-left: .5rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .btn.btn-sm {
        padding: .5rem 1rem;
        font-size: .875rem;
    }

    body #app .title {
        background: linear-gradient(135deg, #00B0F0, #5B9BD5, #4472C4, #002060) !important;
    }




    /* Breadcrumb styles moved to layout.css */

    :focus {
        outline: 0;
    }

    ul {
        list-style: none;
        padding: 0;
    }

    .accordion_close {
        cursor: pointer;
    }

    @media (max-width: 767px) {
        .btn-outline-default:hover {
            border-color: #1072e9 !important;
            background-color: inherit !important;
            color: #1072e9 !important;
        }
    }
}

@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    :focus {
        outline: 0;
    }
}

@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .py-3 {
        padding-bottom: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .py-3 {
        padding-bottom: 1rem !important;
    }

    .px-3 {
        padding-left: 1rem !important;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    :focus {
        outline: 0;
    }

    ul {
        list-style: none;
        padding: 0;
    }
}

@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12,
    .col-lg-3 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        p {
            orphans: 3;
            widows: 3;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    :focus {
        outline: 0;
    }
}

.picker__box .picker__table .picker__day--outfocus {
    color: #fff !important;
}

input:focus,
select:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: #ccc !important;
}

.noUi-connect {
    background: #1072e9 !important;
}

/*! CSS Used from: http://localhost:8069/custom_frontend/static/css/resumes/active.css */
@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    main,
    nav {
        display: block;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h1,
    h4,
    h5 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none !important;
    }

    table {
        border-collapse: collapse;
    }

    th {
        text-align: inherit;
        text-align: -webkit-match-parent;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button,
    input,
    select {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button,
    input {
        overflow: visible;
    }

    button,
    select {
        text-transform: none;
    }

    [role=button] {
        cursor: pointer;
    }

    select {
        word-wrap: normal;
    }

    [type=button],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    input[type=checkbox] {
        box-sizing: border-box;
        padding: 0;
    }

    h1,
    h4,
    h5 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    h5 {
        font-size: 1.25rem;
    }

    .container,
    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    @media (min-width: 576px) {
        .container {
            max-width: 540px;
        }
    }

    @media (min-width: 768px) {
        .container {
            max-width: 720px;
        }
    }

    @media (min-width: 992px) {
        .container {
            max-width: 960px;
        }
    }

    @media (min-width: 1200px) {
        .container {
            max-width: 1140px;
        }
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-1,
    .col-12,
    .col-3,
    .col-4,
    .col-6,
    .col-7,
    .col-9,
    .col-lg-3,
    .col-lg-6,
    .col-lg-9,
    .col-md-12,
    .col-md-4,
    .col-md-8,
    .col-sm-2,
    .col-xl-2 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-1 {
        flex: 0 0 8.333333%;
        max-width: 8.333333%;
    }

    .col-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-7 {
        flex: 0 0 58.333333%;
        max-width: 58.333333%;
    }

    .col-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 576px) {
        .col-sm-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
    }

    @media (min-width: 768px) {
        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }

        .col-md-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-lg-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-lg-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }
    }

    @media (min-width: 1200px) {
        .col-xl-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }
    }

    .form-control {
        display: block;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        padding: .375rem .75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-control {
            transition: none;
        }
    }

    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .form-control:disabled,
    .form-control[readonly] {
        background-color: #e9ecef;
        opacity: 1;
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .custom-control {
        position: relative;
        z-index: 1;
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5rem;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .custom-control-input {
        position: absolute;
        left: 0;
        z-index: -1;
        width: 1rem;
        height: 1.25rem;
        opacity: 0;
    }

    .custom-control-input:checked~.custom-control-label::before {
        color: #fff;
        border-color: #007bff;
        background-color: #007bff;
    }

    .custom-control-input:focus~.custom-control-label::before {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #80bdff;
    }

    .custom-control-input:not(:disabled):active~.custom-control-label::before {
        color: #fff;
        background-color: #b3d7ff;
        border-color: #b3d7ff;
    }

    .custom-control-input:disabled~.custom-control-label {
        color: #6c757d;
    }

    .custom-control-input:disabled~.custom-control-label::before {
        background-color: #e9ecef;
    }

    .custom-control-label {
        position: relative;
        margin-bottom: 0;
        vertical-align: top;
    }

    .custom-control-label::before {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        pointer-events: none;
        content: "";
        background-color: #fff;
        border: #adb5bd solid 1px;
    }

    .custom-control-label::after {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        content: "";
        background: 50%/50% 50% no-repeat;
    }

    .custom-checkbox .custom-control-label::before {
        border-radius: .25rem;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
    }

    .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
        background-color: rgba(0, 123, 255, 0.5);
    }

    .custom-control-label::before {
        transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .custom-control-label::before {
            transition: none;
        }
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .card-header {
        padding: .75rem 1.25rem;
        margin-bottom: 0;
        background-color: rgba(0, 0, 0, 0.03);
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-header:first-child {
        border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0;
    }

    .pagination {
        display: flex;
        padding-left: 0;
        list-style: none;
        border-radius: .25rem;
    }

    .page-link {
        position: relative;
        display: block;
        padding: .5rem .75rem;
        margin-left: -1px;
        line-height: 1.25;
        color: #007bff;
        background-color: #fff;
        border: 1px solid #dee2e6;
    }

    .page-link:hover {
        z-index: 2;
        color: #0056b3;
        text-decoration: none;
        background-color: #e9ecef;
        border-color: #dee2e6;
    }

    .page-link:focus {
        z-index: 3;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .page-item:first-child .page-link {
        margin-left: 0;
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem;
    }

    .page-item:last-child .page-link {
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem;
    }

    .page-item.active .page-link {
        z-index: 3;
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }

    .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        cursor: auto;
        background-color: #fff;
        border-color: #dee2e6;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }
    }

    @media (min-width: 992px) {
        .modal-lg {
            max-width: 800px;
        }
    }

    .align-middle {
        vertical-align: middle !important;
    }

    .align-text-bottom {
        vertical-align: text-bottom !important;
    }

    .bg-white {
        background-color: #fff !important;
    }

    .border-top {
        border-top: 1px solid #dee2e6 !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-none {
            display: none !important;
        }

        .d-md-inline-block {
            display: inline-block !important;
        }

        .d-md-block {
            display: block !important;
        }

        .d-md-flex {
            display: flex !important;
        }
    }

    .justify-content-start {
        justify-content: flex-start !important;
    }

    .justify-content-end {
        justify-content: flex-end !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-start {
        align-items: flex-start !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    .float-left {
        float: left !important;
    }

    .float-right {
        float: right !important;
    }

    .position-relative {
        position: relative !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mr-0,
    .mx-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .ml-0,
    .mx-0 {
        margin-left: 0 !important;
    }

    .mt-1 {
        margin-top: 0.25rem !important;
    }

    .mr-1 {
        margin-right: 0.25rem !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .ml-1 {
        margin-left: 0.25rem !important;
    }

    .my-2 {
        margin-top: 0.5rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2,
    .my-2 {
        margin-bottom: 0.5rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3 {
        margin-left: 1rem !important;
    }

    .mr-4,
    .mx-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mx-4 {
        margin-left: 1.5rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .p-1 {
        padding: 0.25rem !important;
    }

    .py-1 {
        padding-top: 0.25rem !important;
    }

    .px-1 {
        padding-right: 0.25rem !important;
    }

    .py-1 {
        padding-bottom: 0.25rem !important;
    }

    .pl-1,
    .px-1 {
        padding-left: 0.25rem !important;
    }

    .p-2 {
        padding: 0.5rem !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
    }

    .pr-2,
    .px-2 {
        padding-right: 0.5rem !important;
    }

    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .pl-2,
    .px-2 {
        padding-left: 0.5rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 768px) {
        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .ml-md-3 {
            margin-left: 1rem !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        thead {
            display: table-header-group;
        }

        tr {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }

        .container {
            min-width: 992px !important;
        }
    }

    .hoverable {
        box-shadow: none;
        transition: all .55s ease-in-out;
    }

    .hoverable:hover {
        box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        transition: all .55s ease-in-out;
    }

    .disabled,
    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    a:not([href]):not([tabindex]),
    a:not([href]):not([tabindex]):focus,
    a:not([href]):not([tabindex]):hover {
        color: inherit;
        text-decoration: none;
    }

    h1,
    h4,
    h5 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn.btn-sm {
        padding: .5rem 1.6rem;
        font-size: .64rem;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn.btn-sm[class*=btn-outline-] {
        padding-top: .38rem;
        padding-bottom: .38rem;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    a.btn:not([href]):not([tabindex]),
    a.btn:not([href]):not([tabindex]):focus,
    a.btn:not([href]):not([tabindex]):hover {
        color: #fff;
    }

    .btn-outline-default {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #1072e9 !important;
    }

    .btn-outline-default:hover,
    .btn-outline-default:focus,
    .btn-outline-default:active,
    .btn-outline-default:active:focus {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
    }

    .btn-outline-default:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey {
        color: #fff !important;
        background-color: #78909c !important;
    }

    .btn-blue-grey:hover {
        color: #fff;
        background-color: #879ca7;
    }

    .btn-blue-grey:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:focus,
    .btn-blue-grey:active {
        background-color: #4a5b64;
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active {
        background-color: #4a5b64 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #4285f4;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: all .2s linear;
    }

    .pagination .page-item.active .page-link:hover {
        background-color: #4285f4;
    }

    .pagination .page-item.disabled .page-link {
        color: #868e96;
    }

    .pagination .page-item .page-link {
        font-size: .9rem;
        color: #212529;
        background-color: rgba(0, 0, 0, 0);
        border: 0;
        outline: 0;
        transition: all .3s linear;
    }

    .pagination .page-item .page-link:hover {
        background-color: #eee;
        border-radius: .25rem;
        transition: all .3s linear;
    }

    .pagination .page-item .page-link:focus {
        background-color: rgba(0, 0, 0, 0);
        box-shadow: none;
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    table th {
        font-size: .9rem;
        font-weight: 400;
    }

    table td {
        font-size: .9rem;
        font-weight: 300;
    }

    button,
    html [type=button] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    [type=checkbox]:not(:checked),
    [type=checkbox]:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    .select-wrapper .select-dropdown {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .select-wrapper {
        position: relative;
    }

    .select-wrapper:not(.md-outline) .select-dropdown:focus {
        border-bottom: 1px solid #4285f4;
        box-shadow: 0 1px 0 0 #4285f4;
    }

    .select-wrapper input.select-dropdown {
        position: relative;
        z-index: 2;
        display: block;
        width: 100%;
        height: 40px;
        padding: 0;
        margin: 0 0 .94rem 0;
        font-size: 1rem;
        line-height: 2.9rem;
        text-overflow: ellipsis;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0);
        border: none;
        border-bottom: 1px solid #ced4da;
        outline: none;
    }

    .select-wrapper input.select-dropdown:disabled {
        color: rgba(0, 0, 0, 0.3);
        cursor: default;
        border-bottom-color: rgba(0, 0, 0, 0.2);
    }

    .select-wrapper span.caret {
        position: absolute;
        top: .8rem;
        right: 0;
        font-size: .63rem;
        color: #495057;
    }

    .select-wrapper ul {
        padding-left: 0;
        list-style-type: none;
    }

    select {
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    }

    select:disabled {
        color: rgba(0, 0, 0, 0.3);
    }

    .dropdown-content {
        position: absolute;
        top: 0;
        z-index: 1021;
        display: none;
        min-width: 6.25rem;
        max-height: 40.625rem;
        margin: 0;
        overflow-y: auto;
        background-color: #fff;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        opacity: 0;
        will-change: width, height;
    }

    .dropdown-content li {
        width: 100%;
        clear: both;
        line-height: 1.3rem;
        color: #000;
        text-align: left;
        text-transform: none;
        cursor: pointer;
    }

    .dropdown-content li:hover,
    .dropdown-content li.active {
        background-color: #eee;
    }

    .dropdown-content li>span {
        display: block;
        padding: .5rem;
        font-size: .9rem;
        color: #4285f4;
    }

    button:focus {
        outline: 0 !important;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-extralarge {
        font-size: 1.25rem !important;
    }

    .font-ll {
        font-size: 1.5rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9 !important;
    }

    .p-0 {
        padding: 0 !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mr-0,
    .mx-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .ml-0,
    .mx-0 {
        margin-left: 0 !important;
    }

    .p-1 {
        padding: .25rem !important;
    }

    .py-1 {
        padding-top: .25rem !important;
    }

    .px-1 {
        padding-right: .25rem !important;
    }

    .py-1 {
        padding-bottom: .25rem !important;
    }

    .pl-1,
    .px-1 {
        padding-left: .25rem !important;
    }

    .mt-1 {
        margin-top: .25rem !important;
    }

    .mr-1 {
        margin-right: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .ml-1 {
        margin-left: .25rem !important;
    }

    .p-2 {
        padding: .5rem !important;
    }

    .py-2 {
        padding-top: .5rem !important;
    }

    .pr-2,
    .px-2 {
        padding-right: .5rem !important;
    }

    .py-2 {
        padding-bottom: .5rem !important;
    }

    .pl-2,
    .px-2 {
        padding-left: .5rem !important;
    }

    .my-2 {
        margin-top: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2,
    .my-2 {
        margin-bottom: .5rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .mb-3 {
        margin-bottom: 1rem !important;
    }

    .ml-3 {
        margin-left: 1rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pb-4,
    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .mr-4,
    .mx-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mx-4 {
        margin-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .pr-7 {
        padding-right: 3rem !important;
    }

    @media (min-width: 768px) {
        .mb-md-0 {
            margin-bottom: 0 !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .ml-md-3 {
            margin-left: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: rgba(0, 0, 0, .87);
    }

    body a:not([href]):not([tabindex]):focus,
    body a:not([href]):not([tabindex]):hover {
        color: #1072e9;
    }

    .bg-grey-1 {
        background-color: #f4f4f4;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-dark {
        color: rgba(0, 0, 0, 0.54);
    }

    .material-icons.md-grey {
        color: rgba(0, 0, 0, 0.38);
    }

    .material-icons.md-18 {
        font-size: 18px;
    }

    .calender-icon {
        cursor: default;
    }

    .vertical-top {
        vertical-align: top;
    }

    .nowrap {
        white-space: nowrap;
    }

    .z-2 {
        z-index: 2;
    }

    .clear:after {
        clear: both;
        content: "";
        display: block;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .btn.btn-sm {
        padding: .5rem 1rem;
        font-size: .875rem;
    }

    .dropdown-content li>span {
        color: #595959 !important;
    }

    .dropdown-content li.active,
    .dropdown-content li:hover {
        background-color: #1072e9;
    }

    .dropdown-content li.active>span,
    .dropdown-content li:hover>span {
        color: #fff;
    }

    .inline-unit-label,
    .inline-unit-icon {
        position: absolute;
        top: 1.2em;
    }

    .inline-unit-icon {
        line-height: .6rem !important;
    }







    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }
    }

    .vertical-line-label {
        position: relative;
        line-height: 1.8;
        margin-left: 1.285rem;
    }

    .vertical-line-label:before {
        content: "";
        background-color: #455965;
        height: 100%;
        width: .428rem;
        position: absolute;
        left: -1.285rem;
    }

    .title {
        background-color: #1072e9 !important;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    :focus {
        outline: 0;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #1072e9;
    }

    input.form-control[type=text] {
        border: none;
        border-bottom: 1px solid #1072e9;
        border-radius: 0;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        height: inherit;
        padding: .75rem .75rem;
        cursor: text;
    }

    input.form-control.picker__input {
        padding: .75rem .5rem;
        letter-spacing: -0.045rem;
        color: inherit !important;
    }

    .custom-checkbox label[class*=custom-control-label] {
        cursor: pointer;
    }

    .custom-control-label::before {
        width: 1.125rem;
        height: 1.125rem;
        background-color: rgba(0, 0, 0, 0);
        border: 2px solid #d5d9db;
        top: 3px;
        box-shadow: none !important;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
        background-color: #1072e9;
        border-color: #1072e9;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background: url(https://assign-navi.jp/assets/img/common/check_white.png) !important;
        background-size: contain !important;
        top: 3px;
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #d5d9db;
    }

    .select-wrapper.anavi-select {
        background-color: #f4f4f4;
        border-radius: 3px 3px 0 0;
    }

    .select-wrapper.anavi-select input.select-dropdown {
        padding-right: 0;
        height: 3rem;
        border-color: #1072e9;
        background-color: rgba(0, 0, 0, 0);
        font-size: 1rem;
        text-indent: 1rem;
    }

    .select-wrapper.anavi-select span.caret {
        top: 1rem;
        right: 1rem;
    }

    .select-wrapper.anavi-select span.caret.material-icons {
        font-size: 16px;
    }

    .picker__nav--prev:before {
        content: "";
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 0 8px 12px;
        border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: rotate(180deg);
    }

    .picker__nav--next:before {
        content: "";
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 0 8px 12px;
        border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767;
        position: absolute;
        top: 50%;
        right: 40%;
    }

    .card-header {
        padding: 1rem;
        background: #1072e9;
        border-bottom: none;
        color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    a .card-header:hover {
        opacity: .7;
    }

    .modal {
        opacity: 1;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none !important;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul {
        list-style: none;
        padding: 0;
    }

    .pagination {
        margin-left: auto;
        margin-right: auto;
        overflow: overlay;
    }

    .pagination .page-item {
        padding: .5rem;
    }

    .pagination .page-item .page-link {
        font-size: 1rem;
        border-radius: 50%;
        width: 3rem;
        height: 3rem;
        padding: .875rem 0;
        text-align: center;
    }

    .pagination .page-item .page-link:hover {
        border-radius: 50%;
        background-color: #cfd8dc;
    }

    .pagination .page-item .page-link:focus {
        box-shadow: none;
    }

    .pagination .page-item.active .page-link {
        border-radius: 50%;
        background-color: #546e7a;
    }

    .pagination .page-item.active .page-link:hover {
        background-color: #546e7a;
    }

    .accordion_open,
    .accordion_close,
    .accordion-close-area {
        cursor: pointer;
    }

    @media (max-width: 767px) {
        .btn-outline-default:hover {
            border-color: #1072e9 !important;
            background-color: inherit !important;
            color: #1072e9 !important;
        }
    }

    .font-size-middle {
        font-size: custom-(1rem) !important;
        line-height: 28.8px !important;
    }

    .new-flag-pc {
        position: absolute;
        top: 0;
        left: 0;
        padding: 2px 4px;
    }

    .new-flag-pc span {
        position: relative;
        z-index: 10;
        color: #fff;
    }

    .new-flag-pc::before {
        -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
        clip-path: polygon(0 0, 100% 0, 0 100%);
        border-radius: 4px 0 0 0;
        content: "";
        display: block;
        position: absolute;
        z-index: 0;
        top: 0;
        left: 0;
        width: 63px;
        height: 49.5px;
    }

    .new-flag-pc::before {
        background-color: #1072e9;
    }

    .search-area {
        background-color: rgba(255, 255, 255, 0.9);
        position: -webkit-sticky;
        position: sticky;
        bottom: 0;
        left: 0;
        z-index: 10;
    }

    .search-close-btn {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    #slider-handles {
        height: 4px;
    }

    #slider-handles .noUi-handle {
        height: 16px;
        width: 16px;
        top: -6px;
        right: -8px;
        border-radius: 8px;
        background-color: #1072e9;
        box-shadow: none;
        border: none;
    }

    #slider-handles .noUi-handle:before,
    #slider-handles .noUi-handle:after {
        content: none;
    }

    .noUi-target {
        background: #eee !important;
        border-radius: 2px;
        border: none !important;
    }
}

@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

.noUi-target,
.noUi-target * {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -ms-touch-action: none;
    touch-action: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.noUi-target {
    position: relative;
}

.noUi-base,
.noUi-connects {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}

.noUi-connects {
    overflow: hidden;
    z-index: 0;
}

.noUi-connect,
.noUi-origin {
    will-change: transform;
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    height: 100%;
    width: 100%;
    -ms-transform-origin: 0 0;
    -webkit-transform-origin: 0 0;
    -webkit-transform-style: preserve-3d;
    transform-origin: 0 0;
    transform-style: flat;
}

.noUi-horizontal .noUi-origin {
    height: 0;
}

.noUi-handle {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: absolute;
}

.noUi-touch-area {
    height: 100%;
    width: 100%;
}

.noUi-horizontal {
    height: 18px;
}

.noUi-horizontal .noUi-handle {
    width: 34px;
    height: 28px;
    right: -17px;
    top: -6px;
}

.noUi-target {
    background: #FAFAFA;
    border-radius: 4px;
    border: 1px solid #D3D3D3;
    box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;
}

.noUi-connects {
    border-radius: 3px;
}

.noUi-connect {
    background: #3FB8AF;
}

.noUi-handle {
    border: 1px solid #D9D9D9;
    border-radius: 3px;
    background: #FFF;
    cursor: default;
    box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
}

.noUi-handle:after,
.noUi-handle:before {
    content: "";
    display: block;
    position: absolute;
    height: 14px;
    width: 1px;
    background: #E8E7E6;
    left: 14px;
    top: 6px;
}

.noUi-handle:after {
    left: 17px;
}

.sort-display-area {
    cursor: pointer;
}

.sort-display-area:hover {
    opacity: .3;
}

.sort-display-area label {
    cursor: pointer;
}

.sort-options-area {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
    position: absolute;
    z-index: 5;
    right: 0;
    width: max-content;
    cursor: pointer;
}

.sort-options-area li {
    position: relative;
    padding: 8px 44px 8px 16px;
}

.sort-options-area li:hover {
    opacity: .3;
    background-color: #e6eaec;
}

.sort-options-area li i {
    position: absolute;
    top: 6px;
    right: 10px;
}

.default-bg-color-opacity-10 {
    background-color: rgba(0, 188, 174, .1);
}

.default-bg-color-opacity-10:hover {
    opacity: 1;
}

a.card {
    color: rgba(0, 0, 0, .87);
    box-shadow: none;
}

a.card:hover {
    box-shadow: 0 8px 17px 0 rgba(0, 0, 0, .2), 0 6px 20px 0 rgba(0, 0, 0, .19);
}

.form-check-input:not(:checked),
.form-check-input:checked {
    position: absolute;
    pointer-events: none;
    opacity: 0;
}

.form-check-input[type=radio]:not(:checked)+label,
.form-check-input[type=radio]:checked+label {
    position: relative;
    display: inline-block;
    height: 1.5625rem;
    padding-left: 28px;
    line-height: 1.5625rem;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: .28s ease;
}

.form-check-input[type=radio]+label:before,
.form-check-input[type=radio]+label:after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 18px;
    height: 18px;
    margin: 4px;
    content: "";
    transition: .28s ease;
}

.form-check-input[type=radio]:not(:checked)+label:before,
.form-check-input[type=radio]:not(:checked)+label:after,
.form-check-input[type=radio]:checked+label:before,
.form-check-input[type=radio]:checked+label:after {
    border-radius: 50%;
}

.form-check-input[type=radio]:not(:checked)+label:before,
.form-check-input[type=radio]:not(:checked)+label:after {
    border: 2px solid #d5d9db;
}

.form-check-input[type=radio]:not(:checked)+label:after {
    transform: scale(0);
}

.form-check-input[type=radio]:checked+label:before {
    border: 2px solid rgba(0, 0, 0, 0);
}

.form-check-input[type=radio]:checked+label:after {
    border: 2px solid #1072e9;
}

.form-check-input[type=radio]:checked+label:after {
    background-color: #1072e9;
}

.form-check-input[type=radio]:checked+label:after {
    transform: scale(1.02);
}

.form-check-input[type=radio]:disabled:not(:checked)+label:before,
.form-check-input[type=radio]:disabled:checked+label:before {
    background-color: rgba(0, 0, 0, 0);
    border-color: rgba(0, 0, 0, 0.46);
}

.form-check-input[type=radio]:focus+label:before {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#radio-options {
    padding-left: 0 !important;
    margin-left: .6rem !important;
    margin-bottom: 16px !important;
}

hr {
    border-top: 1px solid rgba(0, 0, 0, 0.2) !important;
}

@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    .bg-white {
        background-color: #fff !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    .px-2 {
        padding-right: 0.5rem !important;
    }

    .px-2 {
        padding-left: 0.5rem !important;
    }

    .pl-3 {
        padding-left: 1rem !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    .px-2 {
        padding-right: .5rem !important;
    }

    .px-2 {
        padding-left: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .pl-3 {
        padding-left: 1rem !important;
    }

    .mr-3 {
        margin-right: 1rem !important;
    }

    :focus {
        outline: 0;
    }
}

/*! CSS Used from: https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css */
*,
::after,
::before {
    box-sizing: border-box;
}

main,
nav {
    display: block;
}

[tabindex="-1"]:focus {
    outline: 0 !important;
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
}

h1,
h4,
h5 {
    margin-top: 0;
    margin-bottom: .5rem;
}

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

ul {
    margin-top: 0;
    margin-bottom: 1rem;
}

a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

a:not([href]):not([tabindex]) {
    color: inherit;
    text-decoration: none;
}

a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none;
}

a:not([href]):not([tabindex]):focus {
    outline: 0;
}

table {
    border-collapse: collapse;
}

th {
    text-align: inherit;
}

label {
    display: inline-block;
    margin-bottom: .5rem;
}

button {
    border-radius: 0;
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color;
}

button,
input,
select {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

button,
input {
    overflow: visible;
}

button,
select {
    text-transform: none;
}

select {
    word-wrap: normal;
}

[type=button],
button {
    -webkit-appearance: button;
}

[type=button]:not(:disabled),
button:not(:disabled) {
    cursor: pointer;
}

input[type=checkbox],
input[type=radio] {
    box-sizing: border-box;
    padding: 0;
}

h1,
h4,
h5 {
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, .1);
}

.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

@media (min-width:576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width:768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width:992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width:1200px) {
    .container {
        max-width: 1140px;
    }
}

.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-1,
.col-12,
.col-3,
.col-4,
.col-6,
.col-7,
.col-9,
.col-lg-3,
.col-lg-6,
.col-lg-9,
.col-md-12,
.col-md-4,
.col-md-8,
.col-sm-2,
.col-xl-2 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
}

.col-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
}

.col-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}

.col-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
}

.col-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
}

.col-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
}

@media (min-width:576px) {
    .col-sm-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }
}

@media (min-width:768px) {
    .col-md-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-md-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-md-12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (min-width:992px) {
    .col-lg-3 {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%;
    }

    .col-lg-6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-lg-9 {
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%;
    }
}

@media (min-width:1200px) {
    .col-xl-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%;
    }
}

.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

@media (prefers-reduced-motion:reduce) {
    .form-control {
        transition: none;
    }
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1;
}

.form-check-input {
    position: absolute;
    margin-top: .3rem;
    margin-left: -1.25rem;
}

.form-check-input:disabled~.form-check-label {
    color: #6c757d;
}

.form-check-label {
    margin-bottom: 0;
}

.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

@media (prefers-reduced-motion:reduce) {
    .btn {
        transition: none;
    }
}

.btn:hover {
    color: #212529;
    text-decoration: none;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.btn:disabled {
    opacity: .65;
}

.btn-sm {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.custom-control {
    position: relative;
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5rem;
}

.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}

.custom-control-input:checked~.custom-control-label::before {
    color: #fff;
    border-color: #007bff;
    background-color: #007bff;
}

.custom-control-input:focus~.custom-control-label::before {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.custom-control-input:focus:not(:checked)~.custom-control-label::before {
    border-color: #80bdff;
}

.custom-control-input:not(:disabled):active~.custom-control-label::before {
    color: #fff;
    background-color: #b3d7ff;
    border-color: #b3d7ff;
}

.custom-control-input:disabled~.custom-control-label {
    color: #6c757d;
}

.custom-control-input:disabled~.custom-control-label::before {
    background-color: #e9ecef;
}

.custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
}

.custom-control-label::before {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    background-color: #fff;
    border: #adb5bd solid 1px;
}

.custom-control-label::after {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background: no-repeat 50%/50% 50%;
}

.custom-checkbox .custom-control-label::before {
    border-radius: .25rem;
}

.custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
}

.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(0, 123, 255, .5);
}

.custom-control-label::before {
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

@media (prefers-reduced-motion:reduce) {
    .custom-control-label::before {
        transition: none;
    }
}

.card {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem;
}

.card-header {
    padding: .75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, .03);
    border-bottom: 1px solid rgba(0, 0, 0, .125);
}

.card-header:first-child {
    border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0;
}

.pagination {
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem;
}

.page-link {
    position: relative;
    display: block;
    padding: .5rem .75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-link:focus {
    z-index: 2;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25);
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
}

.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

.close:hover {
    color: #000;
    text-decoration: none;
}

.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
    opacity: .75;
}

button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: .5rem;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    outline: 0;
}

.modal-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem;
}

.modal-header .close {
    padding: 1rem 1rem;
    margin: -1rem -1rem -1rem auto;
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
}

.modal-body {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1rem;
}

@media (min-width:576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
}

@media (min-width:992px) {
    .modal-lg {
        max-width: 800px;
    }
}

.align-middle {
    vertical-align: middle !important;
}

.align-text-bottom {
    vertical-align: text-bottom !important;
}

.bg-white {
    background-color: #fff !important;
}

.border-top {
    border-top: 1px solid #dee2e6 !important;
}

.d-none {
    display: none !important;
}

.d-inline-block {
    display: inline-block !important;
}

.d-block {
    display: block !important;
}

.d-flex {
    display: -ms-flexbox !important;
    display: flex !important;
}

@media (min-width:768px) {
    .d-md-none {
        display: none !important;
    }

    .d-md-inline-block {
        display: inline-block !important;
    }

    .d-md-block {
        display: block !important;
    }

    .d-md-flex {
        display: -ms-flexbox !important;
        display: flex !important;
    }
}

.justify-content-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
}

.justify-content-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
}

.justify-content-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
}

.justify-content-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
}

.align-items-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
}

.align-items-center {
    -ms-flex-align: center !important;
    align-items: center !important;
}

.float-left {
    float: left !important;
}

.float-right {
    float: right !important;
}

.position-relative {
    position: relative !important;
}

.w-100 {
    width: 100% !important;
}

.m-0 {
    margin: 0 !important;
}

.mt-0 {
    margin-top: 0 !important;
}

.mr-0,
.mx-0 {
    margin-right: 0 !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
    margin-left: 0 !important;
}

.mt-1 {
    margin-top: .25rem !important;
}

.mr-1 {
    margin-right: .25rem !important;
}

.mb-1 {
    margin-bottom: .25rem !important;
}

.ml-1 {
    margin-left: .25rem !important;
}

.my-2 {
    margin-top: .5rem !important;
}

.mr-2 {
    margin-right: .5rem !important;
}

.mb-2,
.my-2 {
    margin-bottom: .5rem !important;
}

.mr-3 {
    margin-right: 1rem !important;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

.ml-3 {
    margin-left: 1rem !important;
}

.mr-4,
.mx-4 {
    margin-right: 1.5rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mx-4 {
    margin-left: 1.5rem !important;
}

.mb-5 {
    margin-bottom: 3rem !important;
}

.p-0 {
    padding: 0 !important;
}

.px-0 {
    padding-right: 0 !important;
}

.pl-0,
.px-0 {
    padding-left: 0 !important;
}

.p-1 {
    padding: .25rem !important;
}

.py-1 {
    padding-top: .25rem !important;
}

.px-1 {
    padding-right: .25rem !important;
}

.py-1 {
    padding-bottom: .25rem !important;
}

.pl-1,
.px-1 {
    padding-left: .25rem !important;
}

.p-2 {
    padding: .5rem !important;
}

.py-2 {
    padding-top: .5rem !important;
}

.pr-2,
.px-2 {
    padding-right: .5rem !important;
}

.py-2 {
    padding-bottom: .5rem !important;
}

.pl-2,
.px-2 {
    padding-left: .5rem !important;
}

.pt-3,
.py-3 {
    padding-top: 1rem !important;
}

.px-3 {
    padding-right: 1rem !important;
}

.pb-3,
.py-3 {
    padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
    padding-left: 1rem !important;
}

.py-4 {
    padding-top: 1.5rem !important;
}

.pb-4,
.py-4 {
    padding-bottom: 1.5rem !important;
}

.pl-4 {
    padding-left: 1.5rem !important;
}

.pt-5 {
    padding-top: 3rem !important;
}

.mx-auto {
    margin-right: auto !important;
}

.ml-auto,
.mx-auto {
    margin-left: auto !important;
}

@media (min-width:768px) {
    .mb-md-0 {
        margin-bottom: 0 !important;
    }

    .ml-md-3 {
        margin-left: 1rem !important;
    }

    .px-md-3 {
        padding-right: 1rem !important;
    }

    .px-md-3 {
        padding-left: 1rem !important;
    }

    .py-md-4 {
        padding-top: 1.5rem !important;
    }

    .px-md-4 {
        padding-right: 1.5rem !important;
    }

    .py-md-4 {
        padding-bottom: 1.5rem !important;
    }

    .px-md-4 {
        padding-left: 1.5rem !important;
    }
}

.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}

@media print {

    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important;
    }

    a:not(.btn) {
        text-decoration: underline;
    }

    thead {
        display: table-header-group;
    }

    tr {
        page-break-inside: avoid;
    }

    p {
        orphans: 3;
        widows: 3;
    }

    .container {
        min-width: 992px !important;
    }
}

/*! CSS Used from: https://cdnjs.cloudflare.com/ajax/libs/pickadate.js/3.6.3/themes/default.css */
.picker {
    font-size: 16px;
    text-align: left;
    line-height: 1.2;
    color: #000;
    position: absolute;
    z-index: 10000;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none;
}

.picker__input {
    cursor: default;
}

.picker__holder {
    width: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.picker__holder,
.picker__frame {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    -ms-transform: translateY(100%);
    transform: translateY(100%);
}

.picker__holder {
    position: fixed;
    transition: background 0.15s ease-out, transform 0s 0.15s;
    -webkit-backface-visibility: hidden;
}

.picker__frame {
    position: absolute;
    margin: 0 auto;
    min-width: 256px;
    max-width: 666px;
    width: 100%;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    opacity: 0;
    transition: all 0.15s ease-out;
}

@media (min-height: 33.875em) {
    .picker__frame {
        overflow: visible;
        top: auto;
        bottom: -100%;
        max-height: 80%;
    }
}

@media (min-height: 40.125em) {
    .picker__frame {
        margin-bottom: 7.5%;
    }
}

.picker__wrap {
    display: table;
    width: 100%;
    height: 100%;
}

@media (min-height: 33.875em) {
    .picker__wrap {
        display: block;
    }
}

.picker__box {
    background: #fff;
    display: table-cell;
    vertical-align: middle;
}

@media (min-height: 26.5em) {
    .picker__box {
        font-size: 1.25em;
    }
}

@media (min-height: 33.875em) {
    .picker__box {
        display: block;
        font-size: 1.33em;
        border: 1px solid #777;
        border-top-color: #898989;
        border-bottom-width: 0;
        border-radius: 5px 5px 0 0;
        box-shadow: 0 12px 36px 16px rgba(0, 0, 0, 0.24);
    }
}

@media (min-height: 40.125em) {
    .picker__box {
        font-size: 1.5em;
        border-bottom-width: 1px;
        border-radius: 5px;
    }
}

/*! CSS Used from: https://cdnjs.cloudflare.com/ajax/libs/pickadate.js/3.6.3/themes/default.date.css */
.picker__box {
    padding: 0 1em;
}

.picker__header {
    text-align: center;
    position: relative;
    margin-top: .75em;
}

.picker__select--month,
.picker__select--year {
    border: 1px solid #b7b7b7;
    height: 2em;
    padding: .5em;
    margin-left: .25em;
    margin-right: .25em;
}

@media (min-width: 24.5em) {

    .picker__select--month,
    .picker__select--year {
        margin-top: -0.5em;
    }
}

.picker__select--month {
    width: 35%;
}

.picker__select--year {
    width: 22.5%;
}

.picker__select--month:focus,
.picker__select--year:focus {
    border-color: #0089ec;
}

.picker__nav--prev,
.picker__nav--next {
    position: absolute;
    padding: .5em 1.25em;
    width: 1em;
    height: 1em;
    box-sizing: content-box;
    top: -0.25em;
}

@media (min-width: 24.5em) {

    .picker__nav--prev,
    .picker__nav--next {
        top: -0.33em;
    }
}

.picker__nav--prev {
    left: -1em;
    padding-right: 1.25em;
}

@media (min-width: 24.5em) {
    .picker__nav--prev {
        padding-right: 1.5em;
    }
}

.picker__nav--next {
    right: -1em;
    padding-left: 1.25em;
}

@media (min-width: 24.5em) {
    .picker__nav--next {
        padding-left: 1.5em;
    }
}

.picker__nav--prev:before,
.picker__nav--next:before {
    content: " ";
    border-top: .5em solid transparent;
    border-bottom: .5em solid transparent;
    border-right: 0.75em solid #000;
    width: 0;
    height: 0;
    display: block;
    margin: 0 auto;
}

.picker__nav--next:before {
    border-right: 0;
    border-left: 0.75em solid #000;
}

.picker__nav--prev:hover,
.picker__nav--next:hover {
    cursor: pointer;
    color: #000;
    background: #b1dcfb;
}

.picker__table {
    text-align: center;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
    font-size: inherit;
    width: 100%;
    margin-top: .75em;
    margin-bottom: .5em;
}

@media (min-height: 33.875em) {
    .picker__table {
        margin-bottom: .75em;
    }
}

.picker__table td {
    margin: 0;
    padding: 0;
}

.picker__weekday {
    width: 14.285714286%;
    font-size: .75em;
    padding-bottom: .25em;
    color: #999;
    font-weight: 500;
}

@media (min-height: 33.875em) {
    .picker__weekday {
        padding-bottom: .5em;
    }
}

.picker__day {
    padding: .3125em 0;
    font-weight: 200;
    border: 1px solid transparent;
}

.picker__day--today {
    position: relative;
}

.picker__day--today:before {
    content: " ";
    position: absolute;
    top: 2px;
    right: 2px;
    width: 0;
    height: 0;
    border-top: 0.5em solid #0059bc;
    border-left: .5em solid transparent;
}

.picker__day--outfocus {
    color: #ddd;
}

.picker__day--infocus:hover,
.picker__day--outfocus:hover {
    cursor: pointer;
    color: #000;
    background: #b1dcfb;
}

.picker__day--highlighted {
    border-color: #0089ec;
}

.picker__day--highlighted:hover {
    cursor: pointer;
    color: #000;
    background: #b1dcfb;
}

.picker__footer {
    text-align: center;
}

.picker__button--today,
.picker__button--clear,
.picker__button--close {
    border: 1px solid #fff;
    background: #fff;
    font-size: .8em;
    padding: .66em 0;
    font-weight: bold;
    width: 33%;
    display: inline-block;
    vertical-align: bottom;
}

.picker__button--today:hover,
.picker__button--clear:hover,
.picker__button--close:hover {
    cursor: pointer;
    color: #000;
    background: #b1dcfb;
    border-bottom-color: #b1dcfb;
}

.picker__button--today:focus,
.picker__button--clear:focus,
.picker__button--close:focus {
    background: #b1dcfb;
    border-color: #0089ec;
    outline: none;
}

.picker__button--today:before,
.picker__button--clear:before,
.picker__button--close:before {
    position: relative;
    display: inline-block;
    height: 0;
}

.picker__button--today:before,
.picker__button--clear:before {
    content: " ";
    margin-right: .45em;
}

.picker__button--today:before {
    top: -0.05em;
    width: 0;
    border-top: 0.66em solid #0059bc;
    border-left: .66em solid transparent;
}

.picker__button--clear:before {
    top: -0.25em;
    width: .66em;
    border-top: 3px solid #e20;
}

.picker__button--close:before {
    content: "\D7";
    top: -0.1em;
    vertical-align: top;
    font-size: 1.1em;
    margin-right: .35em;
    color: #777;
}

.picker__button--today[disabled],
.picker__button--today[disabled]:hover {
    background: #f5f5f5;
    border-color: #f5f5f5;
    color: #ddd;
    cursor: default;
}

.picker__button--today[disabled]:before {
    border-top-color: #aaa;
}

/*! CSS Used from: http://localhost:8069/custom_frontend/static/css/pickadate.css */
.picker__input {
    cursor: default;
}

.picker {
    position: absolute;
    z-index: 10000;
    font-size: 1rem;
    line-height: 1.2;
    color: #000;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.picker .picker__holder {
    position: fixed;
    width: 100%;
    top: 0 !important;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    transition: background .15s ease-out, top 0s .15s;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

.picker .picker__holder,
.picker .picker__frame {
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
}

.picker .picker__frame {
    position: absolute;
    width: 18.75rem;
    min-width: 16rem;
    max-width: 20.3125rem;
    max-height: 21.875rem;
    margin: 0 auto;
    filter: alpha(opacity=0);
    opacity: 1;
    transition: none;
    transform: none;
}

@media (min-height: 28.875em) {
    .picker .picker__frame {
        top: auto;
        bottom: -100%;
        max-height: 80%;
        overflow: visible;
    }
}

@media (min-height: 40.125em) {
    .picker .picker__frame {
        margin-bottom: 7.5%;
    }
}

.picker .picker__frame .picker__wrap {
    display: table;
    width: 100%;
    height: 100%;
}

@media (min-height: 28.875em) {
    .picker .picker__frame .picker__wrap {
        display: block;
    }
}

.picker .picker__box {
    display: table-cell;
    vertical-align: middle;
    background: #fff;
    padding: 0 !important;
}

@media (min-height: 28.875em) {
    .picker .picker__box {
        display: block;
        border: 1px solid #777;
        border-top-color: #898989;
        border-bottom-width: 0;
        border-radius: 5px 5px 0 0;
        box-shadow: 0 0.75rem 2.25rem 1rem rgba(0, 0, 0, 0.24);
    }
}

.picker__footer {
    width: 100%;
}

.picker__box {
    padding: 0;
    overflow: hidden;
    border-radius: .125rem;
}

.picker__box .picker__header {
    position: relative;
    margin-bottom: 1.25rem;
    text-align: center;
    margin-top: 0 !important;
}

.picker__box .picker__header select {
    display: inline-block !important;
}

@media screen {

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }
    }

    :disabled {
        pointer-events: none !important;
    }

    :focus {
        outline: 0;
    }
}

.picker__box .picker__header .picker__select--month,
.picker__box .picker__header .picker__select--year {
    display: inline-block;
    height: 2em;
    padding: 0;
    margin-right: .25em;
    margin-left: .25em;
    background: rgba(0, 0, 0, 0);
    border: none;
    border-bottom: 1px solid #ced4da;
    outline: 0;
}

.picker__box .picker__header .picker__select--month:focus,
.picker__box .picker__header .picker__select--year:focus {
    border-color: rgba(0, 0, 0, 0.05);
}

.picker__box .picker__header .picker__select--year {
    width: 30%;
}

.picker__box .picker__header .picker__nav--prev,
.picker__box .picker__header .picker__nav--next {
    position: absolute;
    top: 95px !important;
    box-sizing: content-box;
    padding: .1875rem .625rem;
}

.picker__box .picker__header .picker__nav--prev:hover,
.picker__box .picker__header .picker__nav--next:hover {
    color: #000;
    cursor: pointer;
    background-color: transparent;
}

.picker__box .picker__header .picker__nav--prev:before,
.picker__box .picker__header .picker__nav--next:before {
    display: block;
    font-family: "Font Awesome 5 Free", sans-serif;
    font-weight: 900;
}

.picker__box .picker__header .picker__nav--prev {
    left: -0.5em;
    padding-right: 1.25em;
}

.picker__box .picker__header .picker__nav--next {
    right: -0.2em;
    padding-left: 1.25em;
}

.picker__box .picker__table {
    width: 100%;
    margin-top: .75em;
    margin-bottom: .5em;
    font-size: 1rem;
    text-align: center;
    table-layout: fixed;
    border-spacing: 0;
    border-collapse: collapse;
}

.picker__box .picker__table th,
.picker__box .picker__table td {
    text-align: center;
}

.picker__box .picker__table td {
    padding: 0;
    margin: 0;
}

.picker__box .picker__table .picker__weekday {
    width: 14%;
    padding-bottom: .25em;
    font-size: .9em;
    font-weight: 500;
    color: #999;
}

@media (min-height: 33.875em) {
    .picker__box .picker__table .picker__weekday {
        padding-bottom: .25em;
    }
}

.picker__box .picker__table .picker__day--today {
    position: relative;
    padding: .75rem 0;
    font-weight: 400;
    letter-spacing: -0.3;
    border: 1px solid rgba(0, 0, 0, 0);
    background-color: transparent;
}

.picker__box .picker__table .picker__day.picker__day--today {
    color: #1072e9;
}

.picker__box .picker__table .picker__day--infocus {
    padding: .75rem 0;
    font-weight: 400;
    color: #595959;
    letter-spacing: -0.3;
    border: #595959 rgba(0, 0, 0, 0);
    background-color: transparent;
}

.picker__box .picker__table .picker__day--infocus:hover {
    color: #000;
    cursor: pointer;
    background-color: transparent;
}

.picker__box .picker__table .picker__day--outfocus {
    display: none;
    padding: .75rem 0;
}

.picker__box .picker__table .picker__day--outfocus:hover {
    font-weight: 500;
    color: #ddd;
    cursor: pointer;
}

.picker__box .picker__table .picker__day--highlighted:hover {
    cursor: pointer;
}

.picker__box .picker__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: .3125rem .625rem;
    text-align: right;
}

.picker__box .picker__footer .picker__button--today,
.picker__box .picker__footer .picker__button--clear,
.picker__box .picker__footer .picker__button--close {
    display: inline-block;
    width: 33%;
    padding: 1rem 0 .7rem 0;
    font-size: .8em;
    font-weight: 700;
    text-transform: uppercase;
    vertical-align: bottom;
    background: #fff;
    border: 1px solid #fff;
}

.picker__box .picker__footer .picker__button--today:hover,
.picker__box .picker__footer .picker__button--clear:hover,
.picker__box .picker__footer .picker__button--close:hover {
    color: #000;
    cursor: pointer;
    background: #b1dcfb;
    border-bottom-color: #b1dcfb;
}

.picker__box .picker__footer .picker__button--today:focus,
.picker__box .picker__footer .picker__button--clear:focus,
.picker__box .picker__footer .picker__button--close:focus {
    background: #b1dcfb;
    border-color: rgba(0, 0, 0, 0.05);
    outline: none;
}

.picker__box .picker__footer .picker__button--today:before,
.picker__box .picker__footer .picker__button--clear:before,
.picker__box .picker__footer .picker__button--close:before {
    position: relative;
    display: inline-block;
    height: 0;
}

.picker__box .picker__footer .picker__button--today:before,
.picker__box .picker__footer .picker__button--clear:before {
    margin-right: .45em;
    content: " ";
}

.picker__box .picker__footer .picker__button--today:before {
    top: -0.05em;
    width: 0;
    border-top: .66em solid #0059bc;
    border-left: 0.66em solid rgba(0, 0, 0, 0);
}

.picker__box .picker__footer .picker__button--clear:before {
    top: -0.25em;
    width: .66em;
    border-top: 3px solid #e20;
}

.picker__box .picker__footer .picker__button--close:before {
    top: -0.1em;
    margin-right: .35em;
    font-size: 1.1em;
    color: #777;
    vertical-align: top;
    content: "×";
}

.picker__box .picker__footer .picker__button--today[disabled],
.picker__box .picker__footer .picker__button--today[disabled]:hover {
    color: #ddd;
    cursor: default;
    background: #f5f5f5;
    border-color: #f5f5f5;
}

.picker__box .picker__footer .picker__button--today[disabled]:before {
    border-top-color: #aaa;
}

.picker__box {
    font-size: 1em !important;
}

.picker__nav--prev:before,
.picker__nav--next:before {
    border-right: none !important;
}

.picker__nav--prev:before {
    border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #000000 !important;
}

.picker__day--today:before {
    border-left: 0 !important;
}

.picker__day--highlighted:hover {
    border: 0;
}

.modal-header {
    padding: 2rem !important;
}

.modal-body {
    padding: 0 2rem 2rem !important;
}

.picker__nav--prev:before {
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 0 8px 12px;
    border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: rotate(180deg);
}

.picker__nav--next:before {
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 0 8px 12px;
    border-color: rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #676767;
    position: absolute;
    top: 50%;
    right: 40%;
}

/*! CSS Used from: https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css */
.noUi-target,
.noUi-target * {
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -ms-touch-action: none;
    touch-action: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.noUi-target {
    position: relative;
}

.noUi-base,
.noUi-connects {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}

.noUi-connects {
    overflow: hidden;
    z-index: 0;
}

.noUi-connect,
.noUi-origin {
    will-change: transform;
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    height: 100%;
    width: 100%;
    -ms-transform-origin: 0 0;
    -webkit-transform-origin: 0 0;
    -webkit-transform-style: preserve-3d;
    transform-origin: 0 0;
    transform-style: flat;
}

.noUi-horizontal .noUi-origin {
    height: 0;
}

.noUi-handle {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    position: absolute;
}

.noUi-touch-area {
    height: 100%;
    width: 100%;
}

.noUi-horizontal {
    height: 18px;
}

.noUi-horizontal .noUi-handle {
    width: 34px;
    height: 28px;
    right: -17px;
    top: -6px;
}

.noUi-target {
    background: #FAFAFA;
    border-radius: 4px;
    border: 1px solid #D3D3D3;
    box-shadow: inset 0 1px 1px #F0F0F0, 0 3px 6px -5px #BBB;
}

.noUi-connects {
    border-radius: 3px;
}

.noUi-connect {
    background: #3FB8AF;
}

.noUi-handle {
    border: 1px solid #D9D9D9;
    border-radius: 3px;
    background: #FFF;
    cursor: default;
    box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
}

.noUi-handle:after,
.noUi-handle:before {
    content: "";
    display: block;
    position: absolute;
    height: 14px;
    width: 1px;
    background: #E8E7E6;
    left: 14px;
    top: 6px;
}

.noUi-handle:after {
    left: 17px;
}

/*! CSS Used fontfaces */
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

@font-face {
    font-family: "Font Awesome 5 Free";
    font-display: block;
    font-weight: 900;
    src: url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2) format("woff2"), url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.ttf) format("truetype");
}

@font-face {
    font-family: "Font Awesome 5 Free";
    font-display: block;
    font-weight: 400;
    src: url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-regular-400.woff2) format("woff2"), url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-regular-400.ttf) format("truetype");
}


.new-flag-pc {
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 4px;
}

.new-flag-pc span {
    position: relative;
    z-index: 10;
    color: #fff;
}

.new-flag-pc::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 4px 0 0 0;
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 80px;
    height: 60px;
}

.new-flag-pc::before {
    background-color: #ff0000;
}

.viewed-flag-pc {
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 4px;
}

.viewed-flag-pc span {
    position: relative;
    z-index: 10;
    color: #fff;
}

.viewed-flag-pc::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 4px 0 0 0;
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 80px;
    height: 60px;
}

.viewed-flag-pc::before {
    background-color: #9da9b2;
}

.exp-flag-pc {
    position: absolute;
    top: 0;
    left: 0;
    padding: 2px 4px;
}

.exp-flag-pc span {
    position: relative;
    z-index: 10;
    color: #fff;
}

.exp-flag-pc::before {
    -webkit-clip-path: polygon(0 0, 100% 0, 0 100%);
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 4px 0 0 0;
    content: "";
    display: block;
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;
    width: 80px;
    height: 60px;
}

.exp-flag-pc::before {
    background-color: #1072e9;
}
