import re
import requests
import urllib.parse
from odoo import http
from odoo.http import request, Response
import json
from datetime import datetime
from . import common as global_common
from collections import defaultdict
import logging
import base64
from ..utils import AntivirusScanner, security_logger


_logger = logging.getLogger(__name__)
class ResumesNewController(http.Controller):

    @http.route('/api/resume_new', type='http', auth='public', methods=['POST'], csrf=False)
    def create_resume(self, **kwargs):
        try:
            content_json = request.httprequest.form.get('content')
            if not content_json:
                return request.make_response(
                    json.dumps({
                        'success': False,
                        'message': 'コンテンツが提供されていません。'
                    }),
                    headers=[('Content-Type', 'application/json')],
                    status=400
                )

            try:
                content = json.loads(content_json)
            except json.JSONDecodeError:
                return request.make_response(
                    json.dumps({
                        'success': False,
                        'message': '無効なJSONフォーマットです。'
                    }),
                    headers=[('Content-Type', 'application/json')],
                    status=400
                )

            # <PERSON><PERSON>m tra file trước
            file = request.httprequest.files.get('file')
            if not file:
                return request.make_response(
                    json.dumps({
                        'success': False,
                        'message': global_common.CREAT_TOAST_ERROR_MESSAGE
                    }),
                    headers=[('Content-Type', 'application/json')],
                    status=400
                )

            # Validate và scan file trước khi tạo bản ghi
            try:
                file_content_bytes = file.read()
                file_name = file.filename

                # 1. Kiểm tra file type
                if not AntivirusScanner.is_allowed_file_type(file_name):
                    security_logger.log_security_violation(
                        'invalid_file_type',
                        f'File type not allowed: {file_name}',
                        content.get('user_id')
                    )
                    return request.make_response(
                        json.dumps({
                            "success": False,
                            "message": "ファイル形式が許可されていません。PDF、DOC、DOCXファイルのみ許可されています。"
                        }),
                        headers=[('Content-Type', 'application/json')],
                        status=400
                    )

                # 2. Kiểm tra file size
                if not AntivirusScanner.is_allowed_file_size(len(file_content_bytes)):
                    security_logger.log_security_violation(
                        'file_too_large',
                        f'File too large: {file_name}, size: {len(file_content_bytes)} bytes',
                        content.get('user_id')
                    )
                    return request.make_response(
                        json.dumps({
                            "success": False,
                            "message": "ファイルサイズが大きすぎます。最大10MBまで許可されています。"
                        }),
                        headers=[('Content-Type', 'application/json')],
                        status=400
                    )

                # 3. Antivirus scan
                is_clean, scan_result = AntivirusScanner.scan_file(file_content_bytes, file_name)
                security_logger.log_antivirus_scan(
                    file_name,
                    scan_result,
                    content.get('user_id'),
                    len(file_content_bytes)
                )

                if not is_clean:
                    security_logger.log_security_violation(
                        'virus_detected',
                        f'Virus scan failed: {file_name}, result: {scan_result}',
                        content.get('user_id')
                    )
                    return request.make_response(
                        json.dumps({
                            "success": False,
                            "message": "セキュリティスキャンによりファイルが拒否されました。"
                        }),
                        headers=[('Content-Type', 'application/json')],
                        status=400
                    )

                _logger.info(f"File passed security validation: {file_name}")

            except Exception as e:
                _logger.error(f"Security validation error: {str(e)}", exc_info=True)
                security_logger.log_security_violation(
                    'security_scan_error',
                    f'Security scan error: {str(e)}',
                    content.get('user_id')
                )
                return request.make_response(
                    json.dumps({
                        "success": False,
                        "message": "ファイルセキュリティスキャンに失敗しました。"
                    }),
                    headers=[('Content-Type', 'application/json')],
                    status=500
                )

            # Reset file pointer
            file.seek(0)

            # Sau khi file đã được validate và scan thành công, tạo bản ghi
            try:
                # Tạo bản ghi resume
                db_resume = request.env['vit.partner'].sudo().create({
                    'resume_visibility': content.get('resume_visibility'),
                    'status': content.get('status'),
                    'company_publish': content.get('company_publish'),
                    'human_publish': content.get('human_publish'),
                    'date_period': content.get('date_period'),
                    'user_id': content.get('user_id'),
                    'opp_id': content.get('opp_id'),
                    'user_name': content.get('user_name'),
                    'initial_name': content.get('initial_name'),
                    'gender': content.get('gender'),
                    'birthday': content.get('birthday'),
                    'nationality': content.get('nationality'),
                    'resident': content.get('resident'),
                    'categories_consultation': content.get('categories_consultation'),
                    'categories_development': content.get('categories_development'),
                    'categories_infrastructure': content.get('categories_infrastructure'),
                    'categories_design': content.get('categories_design'),
                    'experience_pr': content.get('experience_pr'),
                    'qualification': content.get('qualification'),
                    'characteristic': content.get('characteristic'),
                    'utilization_rate': content.get('utilization_rate'),
                    'unit_price_min': content.get('unit_price_min'),
                    'unit_price_max': content.get('unit_price_max'),
                    'region': content.get('region'),
                    'working_frequency': content.get('working_frequency'),
                    'working_location': content.get('working_location'),
                    'working_hope': content.get('working_hope'),
                    'company_settings': content.get('company_settings'),
                    'created_at': content.get('created_at'),
                    'created_by': content.get('created_by'),
                    'updated_at': content.get('updated_at'),
                    'updated_by': content.get('updated_by'),
                    'contract_types': content.get('contract_types'),
                    'company_id': content.get('company_id'),
                    'partner_types': content.get('partner_types')
                })

                # Tạo ir.attachment public trước
                attachment = None
                try:
                    attachment = request.env['ir.attachment'].sudo().create({
                        'name': file_name,
                        'datas': base64.b64encode(file_content_bytes),
                        'res_model': 'vit.curriculum_vitae',  # sẽ cập nhật res_id sau
                        'type': 'binary',
                        'mimetype': file.content_type,
                        'public': True,
                    })
                    _logger.info(f"Created attachment: id={attachment.id}, name={file_name}, public=True")
                except Exception as e:
                    _logger.error(f"Failed to create attachment: {str(e)}", exc_info=True)

                # Tạo bản ghi CV, gán file_attachment_id
                cv = request.env["vit.curriculum_vitae"].sudo().create({
                    "name": file_name,
                    "resume_id": db_resume.id,
                    "file_attachment_id": attachment.id if attachment else False
                })
                # Cập nhật lại res_id cho attachment (nếu cần)
                if attachment:
                    attachment.sudo().write({'res_id': cv.id})
                db_resume.sudo().write({"cv_id": cv.id})
                security_logger.log_file_upload(
                    file_name,
                    len(file_content_bytes),
                    db_resume.id,
                    success=True,
                    reason="Upload successful"
                )

                return request.make_response(
                    json.dumps({
                        'success': True,
                        'message': global_common.CREAT_TOAST_SUCCESS_MESSAGE,
                        'id': db_resume.id
                    }),
                    headers=[('Content-Type', 'application/json')]
                )

            except Exception as e:
                _logger.error(f"Database error: {str(e)}", exc_info=True)
                return request.make_response(
                    json.dumps({
                        'success': False,
                        'message': 'データベースエラーが発生しました。'
                    }),
                    headers=[('Content-Type', 'application/json')],
                    status=500
                )

        except Exception as e:
            _logger.error(f"Unexpected error: {str(e)}", exc_info=True)
            return request.make_response(
                json.dumps({
                    'success': False,
                    'message': '予期せぬエラーが発生しました。'
                }),
                headers=[('Content-Type', 'application/json')],
                status=500
            )

    @http.route('/api/resumes_active', type='http', auth='public', methods=['GET'])
    def get_all_partner(self, **kwargs):
        user_id = request.httprequest.headers.get('X-User-ID')  # Lấy user_id từ headers
        company_id = kwargs.get('company_id')
        if not user_id:
            return request.make_response(
                json.dumps({'success': False, 'message': 'Missing X-User-ID in headers'}),
                headers={'Content-Type': 'application/json'},
                status=400
            )

        page = int(request.httprequest.args.get('page', 1))
        limit = 20
        sort_type = request.httprequest.args.get('sort_type', 'created_at')
        offset = (page - 1) * limit
        valid_sort_fields = ["created_at", "updated_at", "unit_price_max"]
        sort_column = sort_type if sort_type in valid_sort_fields else "created_at"

        # Thêm điều kiện lọc để ẩn resume có visibility = 'private'
        domain = [('resume_visibility', '!=', 'private')]

        # Lấy danh sách partners
        if not company_id:
            partners = request.env['vit.partner'].sudo().search_read(domain, limit=limit, offset=offset, order=f"{sort_column} desc")
        else:
            domain.append(('company_id', '=', int(company_id)))
            partners = request.env['vit.partner'].sudo().search_read(domain, limit=limit, offset=offset, order=f"{sort_column} desc")

        # Tính tổng số bản ghi với điều kiện lọc
        total_count = request.env['vit.partner'].sudo().search_count(domain)
        total_pages = (total_count + limit - 1) // limit

        # Lấy danh sách ID của partners để query react
        partner_ids = [pt['id'] for pt in partners]

        # Query bảng vit.reaction để lấy số lượng react theo resumes_id với status = 1
        reactions = request.env['vit.reaction'].sudo().read_group(
            [('resumes_id', 'in', partner_ids), ('status', '=', 1)],  # Chỉ tính những reaction có status = 1
            ['resumes_id'],
            ['resumes_id']
        )
        _logger.info(f"Reaction Map: {reactions}")

        # Xử lý dữ liệu react
        reaction_map = {r['resumes_id'][0]: r['resumes_id_count'] for r in reactions}

        # Kiểm tra user_id có trong vit.reaction không (chỉ lấy status = 1)
        user_reacts = request.env['vit.reaction'].sudo().search([
            ('resumes_id', 'in', partner_ids), ('user_id', '=', int(user_id)), ('status', '=', 1)
        ])
        user_react_map = {ur.resumes_id.id: True for ur in user_reacts}

        # Chuyển dữ liệu sang JSON
        data = []
        for pt in partners:
            opp_id = pt['id']
            data.append({
                'id': opp_id,
                'resume_visibility': pt['resume_visibility'],
                'status': pt['status'],
                'user_id': pt['user_id'][0] if pt['user_id'] else None,
                'cv_id': pt['cv_id'],
                'opp_id': pt['opp_id'],
                'user_name': pt['user_name'],
                'initial_name': pt['initial_name'],
                'gender': pt['gender'],
                'birthday': pt['birthday'].strftime('%Y/%m/%d') if pt['birthday'] else None,
                'nationality': pt['nationality'] if pt['nationality'] else None,
                'resident': pt['resident'],
                'categories_consultation': pt['categories_consultation'],
                'categories_development': pt['categories_development'],
                'categories_infrastructure': pt['categories_infrastructure'],
                'categories_design': pt['categories_design'],
                'experience_pr': pt['experience_pr'],
                'qualification': pt['qualification'],
                'characteristic': pt['characteristic'],
                'utilization_rate': pt['utilization_rate'] if pt['utilization_rate'] else None,
                'unit_price_min': pt['unit_price_min'],
                'unit_price_max': pt['unit_price_max'],
                'region': pt['region'],
                'partner_types': pt['partner_types'] if pt['partner_types'] else None,
                'working_frequency': pt['working_frequency'] if pt['working_frequency'] else None,
                'working_location': pt['working_location'] if pt['working_location'] else None,
                'working_hope': pt['working_hope'],
                'company_settings': pt['company_settings'],
                'contract_types': pt['contract_types'],
                'rating_flag': pt['rating_flag'],
                'internal_comment': pt['internal_comment'],
                'created_at': pt['created_at'].strftime('%Y/%m/%d') if pt['created_at'] else None,
                'created_by': pt['created_by'],
                'updated_at': pt['updated_at'].strftime('%Y/%m/%d') if pt['updated_at'] else None,
                'updated_by': pt['updated_by'],
                'viewer_id': pt['viewer_id'] if pt['viewer_id'] else None,
                'react_number': reaction_map.get(pt['id'], 0),  # Đếm số lượng react theo resumes_id có status = 1
                'is_react': user_react_map.get(pt['id'], False),  # Kiểm tra user đã react (status = 1) hay chưa
                'company_name': pt['company_id'][1] if pt.get('company_id') else None
            })

        return request.make_response(
            json.dumps({
                'success': True,
                'data': data,
                'current_page': page,
                'total_pages': total_pages,
                'total_records': total_count
            }),
            headers={'Content-Type': 'application/json'}
        )

    @http.route('/api/resume_search_condition/search', type='http', auth='public', methods=['GET'], csrf=False)
    def search_resumes(self):
        args = []
        user_id = request.httprequest.headers.get('X-User-ID')
        page = int(request.httprequest.args.get('page', 1))
        limit = 20
        offset = (page - 1) * limit
        sort_type = request.httprequest.args.get('sort_type', 'created_at')
        valid_sort_fields = ["created_at", "updated_at", "unit_price_max"]
        sort_column = sort_type if sort_type in valid_sort_fields else "created_at"

        # Thêm điều kiện lọc để ẩn resume có visibility = 'private'
        args.append(('resume_visibility', '!=', 'private'))

        # Filter by unit price range
        unit_price_min = request.httprequest.args.get('resume_search_condition[asking_unit_price_min]')
        unit_price_max = request.httprequest.args.get('resume_search_condition[asking_unit_price_max]')
        if unit_price_min:
            args.append(('unit_price_min', '>=', int(unit_price_min)))
        if unit_price_max:
            args.append(('unit_price_max', '<=', int(unit_price_max)))

        # Free text search in name and experience
        free_keyword = request.httprequest.args.get('resume_search_condition[free_keyword]')
        if free_keyword:
            args.append('|')
            args.append(('user_name', 'ilike', free_keyword))
            args.append(('experience_pr', 'ilike', free_keyword))

        # Exclude keywords from search
        negative_keyword = request.httprequest.args.get('resume_search_condition[negative_keyword]')
        if negative_keyword:
            args.append(('user_name', 'not ilike', negative_keyword))
            args.append(('experience_pr', 'not ilike', negative_keyword))

        # Filter by availability date
        date_period = request.httprequest.args.get('resume_search_condition[available_time_at_earliest]')
        if date_period:
            try:
                date_period = datetime.strptime(date_period, "%Y年%m月%d日").strftime("%Y-%m-%d")
                args += ['|', ('date_period', '>=', date_period), ('date_period', '=', False)]
            except ValueError:
                pass

        # Filter by nationality
        nationality = request.httprequest.args.get('resume_search_condition[nationality]')
        if nationality == 'japan':
            args.append(('nationality', 'ilike', 'japan'))


        # Filter by resident status
        resident = request.httprequest.args.get('resume_search_condition[resident]')
        if resident == 'yes':
            args.append(('resident', '=', 'yes'))
        elif resident == 'no':
            args.append(('resident', '=', 'no'))
        elif resident == 'other':
            args += ['&', ('resident', '!=', 'yes'), ('resident', '!=', 'no')]

        # Category mapping for different skill types
        prefix_mapping = {
            "consul": "categories_consultation",
            "dev": "categories_development",
            "infra": "categories_infrastructure",
            "design": "categories_design"
        }

        # Parameters that can have multiple values
        multi_value_params = [
            'partner_types',
            'utilization_rate', 'working_frequency'
        ]
        single_value_params = ['gender']

        # Process experience categories
        selected_categories = request.httprequest.args.getlist('resume_search_condition[exp_categories]')
        category_groups = defaultdict(list)

        for category in selected_categories:
            prefix = category.split("_")[0]
            if prefix in prefix_mapping:
                category_groups[prefix_mapping[prefix]].append(category)

        for field_name, values in category_groups.items():
            if values:
                domain = []
                if len(values) > 1:
                    domain.extend(['|'] * (len(values) - 1))
                domain.extend([(field_name, 'ilike', f'%{v}%') for v in values])

                if domain:
                    if len(domain) > 1:
                        args.append('&')
                    args.extend(domain)
        # status
        status = request.httprequest.args.get('resume_search_condition[status]')
        if status:
            args.append(('status', '=', status))

        # contract_types
        contract_types = request.httprequest.args.get('resume_search_condition[contract_types]')
        if contract_types:
            args.append(('contract_types', '=', contract_types))

        # Handle utilization_rate range
        utilization_rate_min = request.httprequest.args.get('resume_search_condition[utilization_rate_min]')
        utilization_rate_max = request.httprequest.args.get('resume_search_condition[utilization_rate_max]')
        if utilization_rate_min or utilization_rate_max:
            # Range search for utilization rate
            try:
                if utilization_rate_min and utilization_rate_max:
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
                elif utilization_rate_min:
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                elif utilization_rate_max:
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
            except (ValueError, TypeError):
                # If conversion fails, skip utilization rate filtering
                pass

        # Handle working_frequency range
        working_frequency_min = request.httprequest.args.get('resume_search_condition[working_frequency_min]')
        working_frequency_max = request.httprequest.args.get('resume_search_condition[working_frequency_max]')
        if working_frequency_min or working_frequency_max:
            # Range search for working frequency
            freq_order = {
                'full_remote': 0,
                'less_than_1day': 1,
                '1day': 2,
                '2days': 3,
                '3days': 4,
                '4days': 5,
                '5days': 6,
                '2to4days': 3.5  # Legacy value
            }

            min_val = freq_order.get(working_frequency_min, 0) if working_frequency_min else 0
            max_val = freq_order.get(working_frequency_max, 6) if working_frequency_max else 6

            # Build OR condition for all frequencies in the range
            freq_domain = []
            matching_freqs = [key for key, val in freq_order.items() if min_val <= val <= max_val]

            if matching_freqs:
                if len(matching_freqs) > 1:
                    freq_domain.extend(['|'] * (len(matching_freqs) - 1))
                freq_domain.extend([('working_frequency', 'ilike', f'%{freq}%') for freq in matching_freqs])
                args.extend(freq_domain)

        # Process remaining multi-value parameters (excluding utilization_rate and working_frequency)
        remaining_params = [param for param in multi_value_params if param not in ['utilization_rate', 'working_frequency']]
        for param in remaining_params:
            values = request.httprequest.args.getlist(f'resume_search_condition[{param}][]')
            if values:
                domain = []
                if len(values) > 1:
                    domain.extend(['|'] * (len(values) - 1))
                domain.extend([(param, 'ilike', f'%{value}%') for value in values])
                args.extend(domain)

        # Fallback to old array-based search for backward compatibility
        if not (utilization_rate_min or utilization_rate_max):
            utilization_rates = request.httprequest.args.getlist('resume_search_condition[utilization_rate][]')
            if utilization_rates:
                domain = []
                if len(utilization_rates) > 1:
                    domain.extend(['|'] * (len(utilization_rates) - 1))
                domain.extend([('utilization_rate', 'ilike', f'%{rate}%') for rate in utilization_rates])
                args.extend(domain)

        if not (working_frequency_min or working_frequency_max):
            working_frequencies = request.httprequest.args.getlist('resume_search_condition[working_frequency][]')
            if working_frequencies:
                domain = []
                if len(working_frequencies) > 1:
                    domain.extend(['|'] * (len(working_frequencies) - 1))
                domain.extend([('working_frequency', 'ilike', f'%{freq}%') for freq in working_frequencies])
                args.extend(domain)

        # Filter by working location
        working_location = request.httprequest.args.getlist('resume_search_condition[workplaces][]')
        if working_location:
            domain = []
            if len(working_location) > 1:
                domain.extend(['|'] * (len(working_location) - 1))
            domain.extend([('working_location', 'ilike', f'%{workplace.strip()}%') for workplace in working_location])
            args.extend(domain)

        # Process single-value parameters
        for param in single_value_params:
            if (value := request.httprequest.args.get(f'resume_search_condition[{param}]')):
                args.append((param, '=', value))

        # Filter by age range
        current_year = datetime.today().year
        age_min = request.httprequest.args.get('resume_search_condition[age_min]')
        age_max = request.httprequest.args.get('resume_search_condition[age_max]')

        if age_min:
            try:
                birth_min = f'{current_year - int(age_min)-1}-12-31'
                args.append(('birthday', '<=', birth_min))
            except ValueError:
                return json.dumps({'success': False, 'error': 'Giá trị tuổi tối thiểu không hợp lệ'})

        if age_max:
            try:
                birth_max = f'{current_year - int(age_max)+1}-01-01'
                args.append(('birthday', '>=', birth_max))
            except ValueError:
                return json.dumps({'success': False, 'error': 'Giá trị tuổi tối đa không hợp lệ'})

        # Remove leading '&' if present
        if args and args[0] == '&':
            args.pop(0)

        # Filter resumes with internal comments
        with_internal_comment = request.httprequest.args.get('resume_search_condition[with_internal_comment]')
        if with_internal_comment == 'true':
            try:
                user_id = request.httprequest.headers.get('X-User-ID')
                company_id = request.httprequest.headers.get('X-Company-ID')
                if user_id and company_id:
                    try:
                        user_id = int(user_id)
                        company_id = int(company_id)
                    except ValueError:
                        return json.dumps({'success': False, 'error': 'Định dạng user_id hoặc company_id không hợp lệ'})

                    workflow_evaluations = request.env['vit.workflow_evaluation'].sudo().search([
                        ('created_by', '=', user_id)
                    ])
                    workflow_ids = workflow_evaluations.mapped('workflow_id.id')
                    workflow_records = request.env['vit.workflow'].sudo().search([
                        ('id', 'in', workflow_ids)
                    ])
                    resume_ids = workflow_records.mapped('resumes_id.id')

                    args.append(('id', 'in', resume_ids))
            except Exception as e:
                return json.dumps({'success': False, 'error': f'Lỗi xử lý internal comments: {str(e)}'})

        try:
            _logger.info("🔍 ORM Query Args: %s", args)
            partners = request.env['vit.partner'].sudo().search(
                args,
                limit=limit,
                offset=offset,
                order=f"{sort_column} desc"
            )
            total_count = request.env['vit.partner'].sudo().search_count(args)

            partner_ids = [pt['id'] for pt in partners]

            # Query bảng vit.reaction để lấy số lượng react theo resumes_id với status = 1
            reactions = request.env['vit.reaction'].sudo().read_group(
                [('resumes_id', 'in', partner_ids), ('status', '=', 1)],  # Chỉ tính những reaction có status = 1
                ['resumes_id'],
                ['resumes_id']
            )
            _logger.info(f"Reaction Map: {reactions}")

            # Xử lý dữ liệu react
            reaction_map = {r['resumes_id'][0]: r['resumes_id_count'] for r in reactions}

            # Kiểm tra user_id có trong vit.reaction không (chỉ lấy status = 1)
            user_reacts = request.env['vit.reaction'].sudo().search([
                ('resumes_id', 'in', partner_ids), ('user_id', '=', int(user_id)), ('status', '=', 1)
            ])
            user_react_map = {ur.resumes_id.id: True for ur in user_reacts}

            results = [
                {
                    "id": partner.id,
                    "resume_visibility": partner.resume_visibility,
                    "status": partner.status,
                    "user_id": partner.user_id.id if partner.user_id else None,
                    "cv_id": partner.cv_id.id if partner.cv_id else None,
                    "user_name": partner.user_name,
                    "initial_name": partner.initial_name,
                    "gender": partner.gender,
                    "birthday": partner.birthday.strftime('%Y/%m/%d') if partner.birthday else None,
                    "nationality": partner.nationality,
                    "resident": partner.resident,
                    "categories_consultation": partner.categories_consultation,
                    "categories_development": partner.categories_development,
                    "categories_infrastructure": partner.categories_infrastructure,
                    "categories_design": partner.categories_design,
                    "experience_pr": partner.experience_pr,
                    "qualification": partner.qualification,
                    "characteristic": partner.characteristic,
                    "utilization_rate": partner.utilization_rate,
                    "unit_price_min": partner.unit_price_min,
                    "unit_price_max": partner.unit_price_max,
                    "region": partner.region,
                    "working_frequency": partner.working_frequency,
                    "working_location": partner.working_location,
                    "working_hope": partner.working_hope,
                    "company_settings": partner.company_settings,
                    "contract_types": partner.contract_types,
                    "rating_flag": partner.rating_flag,
                    "internal_comment": partner.internal_comment,
                    "created_at": partner.create_date.strftime('%Y/%m/%d') if partner.create_date else None,
                    "created_by": partner.create_uid.id if partner.create_uid else None,
                    "updated_at": partner.write_date.strftime('%Y/%m/%d') if partner.write_date else None,
                    "updated_by": partner.write_uid.id if partner.write_uid else None,
                    "viewer_id": partner.viewer_id,
                    "company_name": partner.company_id.name if partner.company_id else None,
                    'react_number': reaction_map.get(partner.id, 0),
                    'is_react': user_react_map.get(partner.id, False),
                }
                for partner in partners
            ]

            return json.dumps({
                'success': True,
                'data': results,
                'current_page': page,
                'total_pages': (total_count + limit - 1) // limit,
                'total_records': total_count
            })
        except Exception as e:
            _logger.error("❌ Error counting resumes: %s", str(e))
            return json.dumps({'success': False, 'error': f'Lỗi đếm số lượng resumes: {str(e)}'})

        except Exception as e:
            _logger.error("❌ Unexpected error: %s", str(e))
            return json.dumps({'success': False, 'error': f'Lỗi không mong muốn: {str(e)}'})

    @http.route('/api/view_partner', type='http', auth='public', methods=['POST'], csrf=False)
    def add_viewer_to_partner(self, **kwargs):
        user_id = kwargs.get('user_id')
        partner_id = kwargs.get('partner_id')
        _logger.info("👀 User %s viewed partner %s", user_id, partner_id)
        # Nếu user_id hợp lệ, thêm bản ghi vào vit.viewresumes
        if user_id:
            request.env['vit.viewresumes'].sudo().create({
                'user_id': int(user_id),
                'resumes_id': int(partner_id),
                'created_at': datetime.now(),
            })

        return json.dumps({
            'success': True,
            'message': f'User (ID: {user_id if user_id else "Anonymous"}) viewed partner {partner_id}',
            'partner_id': partner_id,
        })

    @http.route('/api/check_view', type='http', auth='public', methods=['GET'])
    def check_view(self, **kwargs):
        user_id = kwargs.get('user_id')
        partner_id = kwargs.get('partner_id')

        if not user_id or not partner_id:
            return json.dumps({'success': False, 'message': 'Missing user_id or partner_id'})

        # Ép kiểu dữ liệu để tránh lỗi so sánh chuỗi và số nguyên
        try:
            user_id = int(user_id)
            partner_id = int(partner_id)
        except ValueError:
            return json.dumps({'success': False, 'message': 'Invalid user_id or partner_id'})

        # Truy vấn lấy danh sách kết quả thay vì chỉ đếm số lượng
        viewed_records = request.env['vit.viewresumes'].sudo().search([
            ('user_id', '=', user_id),
            ('resumes_id', '=', partner_id)
        ])

        # Ghi log danh sách bản ghi tìm thấy
        _logger.info("🔍 Checking views for user %s on partner %s: Found %s records -> %s",
                     user_id, partner_id, len(viewed_records), viewed_records)

        return json.dumps({
            'success': True,
            'viewed': bool(viewed_records)  # Trả về True nếu có ít nhất một bản ghi
        })

    @http.route('/api/resume_count', type='http', auth='public', methods=['GET'])
    def count_resumes(self):
        args = []

        # Thêm điều kiện lọc để ẩn resume có visibility = 'private'
        args.append(('resume_visibility', '!=', 'private'))

        # Filter by unit price range
        unit_price_min = request.httprequest.args.get('resume_search_condition[asking_unit_price_min]')
        unit_price_max = request.httprequest.args.get('resume_search_condition[asking_unit_price_max]')
        if unit_price_min:
            args.append(('unit_price_min', '>=', int(unit_price_min)))
        if unit_price_max:
            args.append(('unit_price_max', '<=', int(unit_price_max)))

        # Free text search in name and experience
        free_keyword = request.httprequest.args.get('resume_search_condition[free_keyword]')
        if free_keyword:
            args.append('|')
            args.append(('user_name', 'ilike', free_keyword))
            args.append(('experience_pr', 'ilike', free_keyword))

        # Exclude keywords from search
        negative_keyword = request.httprequest.args.get('resume_search_condition[negative_keyword]')
        if negative_keyword:
            args.append(('user_name', 'not ilike', negative_keyword))
            args.append(('experience_pr', 'not ilike', negative_keyword))

        # Filter by availability date
        date_period = request.httprequest.args.get('resume_search_condition[available_time_at_earliest]')
        if date_period:
            try:
                date_period = datetime.strptime(date_period, "%Y年%m月%d日").strftime("%Y-%m-%d")
                args += ['|', ('date_period', '>=', date_period), ('date_period', '=', False)]
            except ValueError:
                pass

        # Filter by nationality
        nationality = request.httprequest.args.get('resume_search_condition[nationality]')
        if nationality == 'japan':
            args.append(('nationality', 'ilike', 'japan'))


        # Filter by resident status
        resident = request.httprequest.args.get('resume_search_condition[resident]')
        if resident == 'yes':
            args.append(('resident', '=', 'yes'))
        elif resident == 'no':
            args.append(('resident', '=', 'no'))
        elif resident == 'other':
            args += ['&', ('resident', '!=', 'yes'), ('resident', '!=', 'no')]

        # Category mapping for different skill types
        prefix_mapping = {
            "consul": "categories_consultation",
            "dev": "categories_development",
            "infra": "categories_infrastructure",
            "design": "categories_design"
        }

        # Parameters that can have multiple values
        multi_value_params = [
            'partner_types',
            'utilization_rate', 'working_frequency'
        ]
        single_value_params = ['gender']

        # Process experience categories
        selected_categories = request.httprequest.args.getlist('resume_search_condition[exp_categories]')
        category_groups = defaultdict(list)

        for category in selected_categories:
            prefix = category.split("_")[0]
            if prefix in prefix_mapping:
                category_groups[prefix_mapping[prefix]].append(category)

        for field_name, values in category_groups.items():
            if values:
                domain = []
                if len(values) > 1:
                    domain.extend(['|'] * (len(values) - 1))
                domain.extend([(field_name, 'ilike', f'%{v}%') for v in values])

                if domain:
                    if len(domain) > 1:
                        args.append('&')
                    args.extend(domain)
        # status
        status = request.httprequest.args.get('resume_search_condition[status]')
        if status:
            args.append(('status', '=', status))

        # contract_types
        contract_types = request.httprequest.args.get('resume_search_condition[contract_types]')
        if contract_types:
            args.append(('contract_types', '=', contract_types))

        # Handle utilization_rate range
        utilization_rate_min = request.httprequest.args.get('resume_search_condition[utilization_rate_min]')
        utilization_rate_max = request.httprequest.args.get('resume_search_condition[utilization_rate_max]')
        if utilization_rate_min or utilization_rate_max:
            # Range search for utilization rate
            try:
                if utilization_rate_min and utilization_rate_max:
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
                elif utilization_rate_min:
                    args.append(('utilization_rate', '>=', int(utilization_rate_min)))
                elif utilization_rate_max:
                    args.append(('utilization_rate', '<=', int(utilization_rate_max)))
            except (ValueError, TypeError):
                # If conversion fails, skip utilization rate filtering
                pass

        # Handle working_frequency range
        working_frequency_min = request.httprequest.args.get('resume_search_condition[working_frequency_min]')
        working_frequency_max = request.httprequest.args.get('resume_search_condition[working_frequency_max]')
        if working_frequency_min or working_frequency_max:
            # Range search for working frequency
            freq_order = {
                'full_remote': 0,
                'less_than_1day': 1,
                '1day': 2,
                '2days': 3,
                '3days': 4,
                '4days': 5,
                '5days': 6,
                '2to4days': 3.5  # Legacy value
            }

            min_val = freq_order.get(working_frequency_min, 0) if working_frequency_min else 0
            max_val = freq_order.get(working_frequency_max, 6) if working_frequency_max else 6

            # Build OR condition for all frequencies in the range
            freq_domain = []
            matching_freqs = [key for key, val in freq_order.items() if min_val <= val <= max_val]

            if matching_freqs:
                if len(matching_freqs) > 1:
                    freq_domain.extend(['|'] * (len(matching_freqs) - 1))
                freq_domain.extend([('working_frequency', 'ilike', f'%{freq}%') for freq in matching_freqs])
                args.extend(freq_domain)

        # Process remaining multi-value parameters (excluding utilization_rate and working_frequency)
        remaining_params = [param for param in multi_value_params if param not in ['utilization_rate', 'working_frequency']]
        for param in remaining_params:
            values = request.httprequest.args.getlist(f'resume_search_condition[{param}][]')
            if values:
                domain = []
                if len(values) > 1:
                    domain.extend(['|'] * (len(values) - 1))
                domain.extend([(param, 'ilike', f'%{value}%') for value in values])
                args.extend(domain)

        # Fallback to old array-based search for backward compatibility
        if not (utilization_rate_min or utilization_rate_max):
            utilization_rates = request.httprequest.args.getlist('resume_search_condition[utilization_rate][]')
            if utilization_rates:
                domain = []
                if len(utilization_rates) > 1:
                    domain.extend(['|'] * (len(utilization_rates) - 1))
                domain.extend([('utilization_rate', 'ilike', f'%{rate}%') for rate in utilization_rates])
                args.extend(domain)

        if not (working_frequency_min or working_frequency_max):
            working_frequencies = request.httprequest.args.getlist('resume_search_condition[working_frequency][]')
            if working_frequencies:
                domain = []
                if len(working_frequencies) > 1:
                    domain.extend(['|'] * (len(working_frequencies) - 1))
                domain.extend([('working_frequency', 'ilike', f'%{freq}%') for freq in working_frequencies])
                args.extend(domain)

        # Filter by working location
        working_location = request.httprequest.args.getlist('resume_search_condition[workplaces][]')
        if working_location:
            domain = []
            if len(working_location) > 1:
                domain.extend(['|'] * (len(working_location) - 1))
            domain.extend([('working_location', 'ilike', f'%{workplace.strip()}%') for workplace in working_location])
            args.extend(domain)

        # Process single-value parameters
        for param in single_value_params:
            if (value := request.httprequest.args.get(f'resume_search_condition[{param}]')):
                args.append((param, '=', value))

        # Filter by age range
        current_year = datetime.today().year
        age_min = request.httprequest.args.get('resume_search_condition[age_min]')
        age_max = request.httprequest.args.get('resume_search_condition[age_max]')

        if age_min:
            try:
                birth_min = f'{current_year - int(age_min)-1}-12-31'
                args.append(('birthday', '<=', birth_min))
            except ValueError:
                return json.dumps({'success': False, 'error': 'Giá trị tuổi tối thiểu không hợp lệ'})

        if age_max:
            try:
                birth_max = f'{current_year - int(age_max)+1}-01-01'
                args.append(('birthday', '>=', birth_max))
            except ValueError:
                return json.dumps({'success': False, 'error': 'Giá trị tuổi tối đa không hợp lệ'})

        # Remove leading '&' if present
        if args and args[0] == '&':
            args.pop(0)

        # Filter resumes with internal comments
        with_internal_comment = request.httprequest.args.get('resume_search_condition[with_internal_comment]')
        if with_internal_comment == 'true':
            try:
                user_id = request.httprequest.headers.get('X-User-ID')
                company_id = request.httprequest.headers.get('X-Company-ID')
                if user_id and company_id:
                    try:
                        user_id = int(user_id)
                        company_id = int(company_id)
                    except ValueError:
                        return json.dumps({'success': False, 'error': 'Định dạng user_id hoặc company_id không hợp lệ'})

                    workflow_evaluations = request.env['vit.workflow_evaluation'].sudo().search([
                        ('created_by', '=', user_id)
                    ])
                    workflow_ids = workflow_evaluations.mapped('workflow_id.id')
                    workflow_records = request.env['vit.workflow'].sudo().search([
                        ('id', 'in', workflow_ids)
                    ])
                    resume_ids = workflow_records.mapped('resumes_id.id')

                    args.append(('id', 'in', resume_ids))
            except Exception as e:
                return json.dumps({'success': False, 'error': f'Lỗi xử lý internal comments: {str(e)}'})

        try:
            _logger.info("🔍 ORM Query Args: %s", args)
            total_count = request.env['vit.partner'].sudo().search_count(args)
            _logger.info("🔍 ORM Count: %s", total_count)

            return json.dumps({
                'success': True,
                'total_records': total_count,
                'args': args
            })
        except Exception as e:
            _logger.error("❌ Error counting resumes: %s", str(e))
            return json.dumps({'success': False, 'error': f'Lỗi đếm số lượng resumes: {str(e)}'})

        except Exception as e:
            _logger.error("❌ Unexpected error: %s", str(e))
            return json.dumps({'success': False, 'error': f'Lỗi không mong muốn: {str(e)}'})

    @http.route('/api/resumes/<filter_value>', type='http', auth='public', methods=['GET'])
    def resumes(filter_value):
        page = int(request.httprequest.args.get('page', 1))
        limit = int(request.httprequest.args.get('limit', 24))  # Mặc định là 24 nếu không có tham số limit
        sort_type = request.httprequest.args.get('sort_by', 'created_at')  # Thay sort_type thành sort_by
        offset = (page - 1) * limit
        valid_sort_fields = ["created_at", "updated_at", "unit_price_max"]
        sort_column = sort_type if sort_type in valid_sort_fields else "created_at"

        # Tạo domain lọc cho filter_value (tên hoặc kỹ năng liên quan đến 'Java')
        domain = []
        if filter_value:
            # Giả sử filter_value là một từ khóa tìm kiếm trong tên người dùng hoặc kỹ năng
            domain.append(('user_name', 'ilike', filter_value))  # Tìm kiếm tên người dùng chứa từ khóa Java
            # Nếu bạn muốn lọc theo kỹ năng (giả sử có trường experience_pr)
            domain.append(('experience_pr', 'ilike', filter_value))  # Lọc theo chuyên môn nếu cần

        # Lấy dữ liệu từ model
        partners = request.env['vit.partner'].sudo().search_read(domain, limit=limit, offset=offset, order=f"{sort_column} desc")

        # Tính tổng số bản ghi
        total_count = len(partners) if len(partners) < limit else request.env['vit.partner'].sudo().search_count(domain)
        total_pages = (total_count + limit - 1) // limit

        # Chuẩn bị dữ liệu trả về
        data = []
        for pt in partners:
            data.append({
                'id': pt['id'],
                'resume_visibility': pt['resume_visibility'],
                'status': pt['status'],
                'user_id': pt['user_id'][0] if pt['user_id'] else None,
                'cv_id': pt['cv_id'],
                'opp_id': pt['opp_id'],
                'user_name': pt['user_name'],
                'initial_name': pt['initial_name'],
                'gender': pt['gender'],
                'birthday': pt['birthday'].strftime('%Y/%m/%d') if pt['birthday'] else None,
                'nationality': pt['nationality'] if pt['nationality'] else None,
                'resident': pt['resident'],
                'categories_consultation': pt['categories_consultation'],
                'categories_development': pt['categories_development'],
                'categories_infrastructure': pt['categories_infrastructure'],
                'categories_design': pt['categories_design'],
                'experience_pr': pt['experience_pr'],
                'qualification': pt['qualification'],
                'characteristic': pt['characteristic'],
                'utilization_rate': pt['utilization_rate'] if pt['utilization_rate'] else None,
                'unit_price_min': pt['unit_price_min'],
                'unit_price_max': pt['unit_price_max'],
                'region': pt['region'],
                'working_frequency': pt['working_frequency'] if pt['working_frequency'] else None,
                'working_location': pt['working_location'] if pt['working_location'] else None,
                'working_hope': pt['working_hope'],
                'company_settings': pt['company_settings'],
                'contract_types': pt['contract_types'],
                'rating_flag': pt['rating_flag'],
                'internal_comment': pt['internal_comment'],
                'created_at': pt['created_at'].strftime('%Y/%m/%d') if pt['created_at'] else None,
                'created_by': pt['created_by'],
                'updated_at': pt['updated_at'].strftime('%Y/%m/%d') if pt['updated_at'] else None,
                'updated_by': pt['updated_by'],
                'viewer_id': pt['viewer_id'] if pt['viewer_id'] else None
            })

        # Trả về dữ liệu dưới dạng JSON
        return request.make_response(
            json.dumps({
                'success': True,
                'data': data,
                'current_page': page,
                'total_pages': total_pages,
                'total_records': total_count
            }),
            headers={'Content-Type': 'application/json'}
        )

