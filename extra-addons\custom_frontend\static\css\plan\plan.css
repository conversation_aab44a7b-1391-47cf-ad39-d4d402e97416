.table {
    display: flex;
    flex-direction: column;
  }
  .table thead, .table tbody {
    display: block;
  }
  .table tr {
    display: flex;
    flex: 1;
  }
  .table th, .table td {
    flex: 1;
    padding: 8px;
    align-items: center;
    vertical-align: middle !important;
  }

@media screen and (min-width: 375px) and (max-width: 390px) {
  .custom-plan-table th, .custom-plan-table td {
      border: 2px solid #ffffff;
      padding: 4px;
  }

  .margin-header {
    margin-top: 10rem !important;
  }
}
  