import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const ERP = {
    'template': `
<div style="margin-top: 79px;">
${createBreadcrumb([
    { text: 'Mi52TOP', link: '/' },
    { text: '案件を探す', link: null, current: true }
])}
<div class="container-fluid title py-2 py-md-4"><h1 class="mb-0">案件を探す</h1></div>
<div class="container-fluid grabient pt-5">
    <div class="row">
        <div class="d-none d-md-block col-12 col-md-4 col-lg-3" id="side-search">
            <div class="card py-4 side-card"><i
                    class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
                <div class="mb-3">
                    <div class="reset_link_area-pc mx-3 d-none"><a
                            class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light"
                            href="/opportunities/active"><span>保存済み条件で検索</span></a></div>
                    <div class="reset_link_area-sp d-none"><a class="reset_search_condition mx-0"
                                                              href="/opportunities/active"><span>保存条件で検索</span></a>
                    </div>
                </div>
                <form class="new_opportunity_search_condition" id="opportunity_search_condition_form" novalidate=""
                      action="/opportunity_search_conditions/search" accept-charset="UTF-8" method="get"><input
                        name="utf8" type="hidden" autocomplete="off" value="✓"><input id="hidden_unit_price_min"
                                                                                      autocomplete="off" type="hidden"
                                                                                      name="opportunity_search_condition[unit_price_min]"
                                                                                      value=""><input
                        id="hidden_unit_price_max" autocomplete="off" type="hidden"
                        name="opportunity_search_condition[unit_price_max]" value="">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 px-0 px-md-3"><label
                                    class="font-middle mb-3 ex-bold">フリーワード</label>
                                <div class="mb-4">
                                    <div class="vertical-top d-inline-block w-100" data-html="true"
                                                           data-toggle="tooltip" title=""
                                                           data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
                                        <div class="mb-1"><input placeholder="キーワード" class="form-control"
                                                                 autocomplete="off" id="free_keyword_field" type="text"
                                                                 name="opportunity_search_condition[free_keyword]">
                                        </div>
                                    </div>
                                    <div class="mb-3"><span class="font-middle">を含む</span></div>
                                    <div class="mb-1"><input placeholder="除外キーワード" class="form-control"
                                                             autocomplete="off" id="negative_keyword_field" type="text"
                                                             name="opportunity_search_condition[negative_keyword]">
                                    </div>
                                    <div class="mb-3"><span class="font-middle">を除く</span></div>
                                </div>
                                <div class="mb-5"><label class="font-middle mb-3 ex-bold">得意領域</label>
                                    <div class="consul_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">コンサル</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_up</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_consul]"
                                           id="opportunity_search_condition_accordion_open_consul" value="yes">
                                    <div class="accordion_contents_consul" style="display: block;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul0" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_pmo"><label id="opp_categories_field_label_0"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul1" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_pmpl"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="consul1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul2" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_strategy"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="consul2">戦略</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul3" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_work"><label id="opp_categories_field_label_3"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="consul3">業務</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul4" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_it"><label id="opp_categories_field_label_4"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="consul4">IT</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul5" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_rpa"><label id="opp_categories_field_label_5"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul5">RPA</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul6" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_erp"><label id="opp_categories_field_label_6"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="consul6">ERP・PKG</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="consul7" id_params="consul"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="consul_security"><label
                                                        id="opp_categories_field_label_7"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="consul7">セキュリティ</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="dev_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear"><span
                                            class="font-size-middle ex-bold">開発</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_dev]"
                                           id="opportunity_search_condition_accordion_open_dev" value="no">
                                    <div class="accordion_contents_dev" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev0" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_pmo"><label id="opp_categories_field_label_0"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev1" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_pmpl"><label id="opp_categories_field_label_1"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev2" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_architect"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev2">アーキテクト</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev3" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_bridge_se"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev3">ブリッジSE</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev4" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_work"><label id="opp_categories_field_label_4"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev4">業務システム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev5" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_web"><label id="opp_categories_field_label_5"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev5">Webシステム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev6" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_ios"><label id="opp_categories_field_label_6"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev6">iOS</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev7" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_android"><label id="opp_categories_field_label_7"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev7">Android</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev8" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_mainframe"><label
                                                        id="opp_categories_field_label_8"
                                                        class="custom-control-label anavi-select-label mb-3" for="dev8">メインフレーム</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev9" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_control"><label id="opp_categories_field_label_9"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev9">制御</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev10" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_embedded"><label
                                                        id="opp_categories_field_label_10"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev10">組込</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev11" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_iot"><label id="opp_categories_field_label_11"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev11">IoT</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev12" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_ai_dl"><label id="opp_categories_field_label_12"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="dev12">AI・DL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev13" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_frontend"><label
                                                        id="opp_categories_field_label_13"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev13">フロントエンド</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev14" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_backend"><label id="opp_categories_field_label_14"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="dev14">バックエンド</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev15" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_game"><label id="opp_categories_field_label_15"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev15">ゲーム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev16" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_rpa"><label id="opp_categories_field_label_16"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev16">RPA</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev17" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_erp"><label id="opp_categories_field_label_17"
                                                                                  class="custom-control-label anavi-select-label mb-3"
                                                                                  for="dev17">ERP</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev18" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_operation"><label
                                                        id="opp_categories_field_label_18"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="dev18">運用・保守</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="dev19" id_params="dev"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="dev_test"><label id="opp_categories_field_label_19"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="dev19">テスト</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="infra_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">インフラ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_infra]"
                                           id="opportunity_search_condition_accordion_open_infra" value="no">
                                    <div class="accordion_contents_infra" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra0" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_pmo"><label id="opp_categories_field_label_0"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="infra0">PMO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra1" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_pmpl"><label id="opp_categories_field_label_1"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="infra1">PM・PL</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra2" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_server"><label id="opp_categories_field_label_2"
                                                                                       class="custom-control-label anavi-select-label mb-3"
                                                                                       for="infra2">サーバー</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra3" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_network"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra3">ネットワーク</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra4" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_db"><label id="opp_categories_field_label_4"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="infra4">データベース</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra5" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_cloud"><label id="opp_categories_field_label_5"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="infra5">クラウド</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra6" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_virtualized"><label
                                                        id="opp_categories_field_label_6"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra6">仮想化</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra7" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_mainframe"><label
                                                        id="opp_categories_field_label_7"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra7">メインフレーム</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="infra8" id_params="infra"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="infra_operation"><label
                                                        id="opp_categories_field_label_8"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="infra8">運用・保守</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="helpdesk_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">ヘルプデスク</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_helpdesk]"
                                           id="opportunity_search_condition_accordion_open_helpdesk" value="no">
                                    <div class="accordion_contents_helpdesk" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk0"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_self"><label
                                                        id="opp_categories_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="helpdesk0">ヘルプデスク</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk1"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_se"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="helpdesk1">社内SE</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="helpdesk2"
                                                           id_params="helpdesk" type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="helpdesk_support"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="helpdesk2">PMOサポート</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="marke_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">Webマーケ</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_marke]"
                                           id="opportunity_search_condition_accordion_open_marke" value="no">
                                    <div class="accordion_contents_marke" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke0" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_direction"><label
                                                        id="opp_categories_field_label_0"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke0">ディレクション</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke1" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_seo"><label id="opp_categories_field_label_1"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="marke1">SEO</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke2" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_email"><label id="opp_categories_field_label_2"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="marke2">Eメール</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke3" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_sns"><label id="opp_categories_field_label_3"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="marke3">SNS</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke4" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_listing"><label
                                                        id="opp_categories_field_label_4"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke4">リスティング</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke5" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_affiliate"><label
                                                        id="opp_categories_field_label_5"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="marke5">アフィリエイト</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="marke6" id_params="marke"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="marke_video"><label id="opp_categories_field_label_6"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="marke6">動画</label></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="design_details accordion_close py-1 bg-grey-1 pl-3 mb-2 d-flex clear">
                                        <span class="font-size-middle ex-bold">デザイン</span><i
                                            class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">keyboard_arrow_down</i>
                                    </div>
                                    <input autocomplete="off" type="hidden"
                                           name="opportunity_search_condition[accordion_open_design]"
                                           id="opportunity_search_condition_accordion_open_design" value="no">
                                    <div class="accordion_contents_design" style="display: none;">
                                        <div class="mx-auto py-3 pl-3">
                                            <div class="selecting-form row px-3"><input type="hidden"
                                                                                        name="opportunity_search_condition[opp_categories][]">
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design0" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_web"><label id="opp_categories_field_label_0"
                                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                                     for="design0">Webシステム</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design1" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_uiux"><label id="opp_categories_field_label_1"
                                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                                      for="design1">UI/UX</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design2" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_character"><label
                                                        id="opp_categories_field_label_2"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="design2">キャラクター</label></div>
                                                <div class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                    <input class="custom-control-input" id="design3" id_params="design"
                                                           type="checkbox"
                                                           name="opportunity_search_condition[opp_categories][]"
                                                           value="design_graphic"><label
                                                        id="opp_categories_field_label_3"
                                                        class="custom-control-label anavi-select-label mb-3"
                                                        for="design3">グラフィック</label></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <label class="font-middle mb-3 ex-bold">単価</label>
                                <div class="mb-5"><p class="d-flex justify-content-center" id="price-range">
                                    下限なし〜上限なし</p>
                                    <div class="slider-styled mx-4 noUi-target noUi-ltr noUi-horizontal noUi-txt-dir-ltr"
                                         id="slider-handles">
                                        <div class="noUi-base">
                                            <div class="noUi-connects">
                                                <div class="noUi-connect"
                                                     style="transform: translate(0%, 0px) scale(1, 1);"></div>
                                            </div>
                                            <div class="noUi-origin"
                                                 style="transform: translate(-100%, 0px); z-index: 5;">
                                                <div class="noUi-handle noUi-handle-lower" data-handle="0" tabindex="0"
                                                     role="slider" aria-orientation="horizontal" aria-valuemin="35.0"
                                                     aria-valuemax="200.0" aria-valuenow="35.0" aria-valuetext="35.00">
                                                    <div class="noUi-touch-area"></div>
                                                </div>
                                            </div>
                                            <div class="noUi-origin" style="transform: translate(0%, 0px); z-index: 4;">
                                                <div class="noUi-handle noUi-handle-upper" data-handle="1" tabindex="0"
                                                     role="slider" aria-orientation="horizontal" aria-valuemin="40.0"
                                                     aria-valuemax="205.0" aria-valuenow="205.0"
                                                     aria-valuetext="205.00">
                                                    <div class="noUi-touch-area"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between font-small custom-grey-text my-2"><span>下限なし</span><span>上限なし</span>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold" for="">稼働率</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[utilization_rate][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_0"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="full_utilization"><label id="utilization_rate_field_label_0"
                                                                                class="custom-control-label anavi-select-label mb-3"
                                                                                for="utilization_rate_field_opportunity_search_condition_0">100%（フル稼働）</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_1"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="80to99"><label id="utilization_rate_field_label_1"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_1">80
                                            〜 99%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_2"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="60to79"><label id="utilization_rate_field_label_2"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_2">60
                                            〜 79%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_3"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="40to59"><label id="utilization_rate_field_label_3"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_3">40
                                            〜 59%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_4"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="20to39"><label id="utilization_rate_field_label_4"
                                                                      class="custom-control-label anavi-select-label mb-3"
                                                                      for="utilization_rate_field_opportunity_search_condition_4">20
                                            〜 39%</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="utilization_rate_field_opportunity_search_condition_5"
                                                type="checkbox" name="opportunity_search_condition[utilization_rate][]"
                                                value="less_than_20"><label id="utilization_rate_field_label_5"
                                                                            class="custom-control-label anavi-select-label mb-3"
                                                                            for="utilization_rate_field_opportunity_search_condition_5">20%未満</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">案件の商流</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[opp_type_id][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_0" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="clnt"><label
                                                id="opp_type_id_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_0">エンド</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_1" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="prim"><label
                                                id="opp_type_id_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_1">元請</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_2" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="subc"><label
                                                id="opp_type_id_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_2">一次請</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="opp_type_id_field_opportunity_search_condition_3" type="checkbox"
                                                name="opportunity_search_condition[opp_type_id][]" value="msubc"><label
                                                id="opp_type_id_field_label_3"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="opp_type_id_field_opportunity_search_condition_3">二次請以降</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">商流への関与</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[involvement][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="involvement_field_opportunity_search_condition_0" type="checkbox"
                                                name="opportunity_search_condition[involvement][]"
                                                value="enter_sales_channels"><label id="involvement_field_label_0"
                                                                                    class="custom-control-label anavi-select-label mb-3"
                                                                                    for="involvement_field_opportunity_search_condition_0">掲載企業が商流に入る案件のみ表示する</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">出社頻度</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[work_frequencies][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_0"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="5days"><label id="work_frequencies_field_label_0"
                                                                     class="custom-control-label anavi-select-label mb-3"
                                                                     for="work_frequencies_field_opportunity_search_condition_0">週5日出社</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_1"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="2to4days"><label id="work_frequencies_field_label_1"
                                                                        class="custom-control-label anavi-select-label mb-3"
                                                                        for="work_frequencies_field_opportunity_search_condition_1">週4
                                            〜 2日出社</label></div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_2"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="less_than_1day"><label id="work_frequencies_field_label_2"
                                                                              class="custom-control-label anavi-select-label mb-3"
                                                                              for="work_frequencies_field_opportunity_search_condition_2">週1日以下出社</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="work_frequencies_field_opportunity_search_condition_3"
                                                type="checkbox" name="opportunity_search_condition[work_frequencies][]"
                                                value="full_remote"><label id="work_frequencies_field_label_3"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="work_frequencies_field_opportunity_search_condition_3">フルリモート</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto hbVMQlS mb-5">
                                    <div class="row ml-0 mr-0"><label class="font-middle mb-3 ex-bold"
                                                                      for="">就業場所</label>
                                        <button class="mdb-modal-form btn btn-outline-default btn-sm mb-3 mt-0 ml-3 waves-effect waves-light"
                                                type="button" data-toggle="modal"
                                                data-reference="#specifies_workplaces_base">選択
                                        </button>
                                        <p class="pref-select p-0 m-0 w-100" id="check_item">選択されていません</p>
                                        <input id="specifies_workplaces" multiple="" autocomplete="off" type="hidden"
                                               name="opportunity_search_condition[specifies_workplaces][]">
                                        <div class="modal" id="specifies_workplaces_base" tabindex="-1" role="dialog"
                                             aria-labelledby="specifies_workplaces_modal" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header"><h4 class="modal-title w-100">
                                                        就業場所を選択</h4>
                                                        <button class="close btn-modal-close" aria-label="Close"
                                                                type="button"
                                                                data-reference="#specifies_workplaces_base"><i
                                                                class="material-icons md-dark mb-36" aria-hidden="true">clear</i>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row multiple-prefecture-select">
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">北海道</a><input
                                                                    type="hidden" value="1"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">青森県</a><input
                                                                    type="hidden" value="2"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岩手県</a><input
                                                                    type="hidden" value="3"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">宮城県</a><input
                                                                    type="hidden" value="4"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">秋田県</a><input
                                                                    type="hidden" value="5"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山形県</a><input
                                                                    type="hidden" value="6"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福島県</a><input
                                                                    type="hidden" value="7"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">茨城県</a><input
                                                                    type="hidden" value="8"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">栃木県</a><input
                                                                    type="hidden" value="9"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">群馬県</a><input
                                                                    type="hidden" value="10"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">埼玉県</a><input
                                                                    type="hidden" value="11"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">千葉県</a><input
                                                                    type="hidden" value="12"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">東京都</a><input
                                                                    type="hidden" value="13"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">神奈川県</a><input
                                                                    type="hidden" value="14"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">新潟県</a><input
                                                                    type="hidden" value="15"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">富山県</a><input
                                                                    type="hidden" value="16"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">石川県</a><input
                                                                    type="hidden" value="17"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福井県</a><input
                                                                    type="hidden" value="18"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山梨県</a><input
                                                                    type="hidden" value="19"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">長野県</a><input
                                                                    type="hidden" value="20"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岐阜県</a><input
                                                                    type="hidden" value="21"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">静岡県</a><input
                                                                    type="hidden" value="22"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">愛知県</a><input
                                                                    type="hidden" value="23"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">三重県</a><input
                                                                    type="hidden" value="24"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">滋賀県</a><input
                                                                    type="hidden" value="25"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">京都府</a><input
                                                                    type="hidden" value="26"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">大阪府</a><input
                                                                    type="hidden" value="27"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">兵庫県</a><input
                                                                    type="hidden" value="28"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">奈良県</a><input
                                                                    type="hidden" value="29"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">和歌山県</a><input
                                                                    type="hidden" value="30"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">鳥取県</a><input
                                                                    type="hidden" value="31"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">島根県</a><input
                                                                    type="hidden" value="32"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">岡山県</a><input
                                                                    type="hidden" value="33"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">広島県</a><input
                                                                    type="hidden" value="34"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">山口県</a><input
                                                                    type="hidden" value="35"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">徳島県</a><input
                                                                    type="hidden" value="36"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">香川県</a><input
                                                                    type="hidden" value="37"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">愛媛県</a><input
                                                                    type="hidden" value="38"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">高知県</a><input
                                                                    type="hidden" value="39"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">福岡県</a><input
                                                                    type="hidden" value="40"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">佐賀県</a><input
                                                                    type="hidden" value="41"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">長崎県</a><input
                                                                    type="hidden" value="42"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">熊本県</a><input
                                                                    type="hidden" value="43"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">大分県</a><input
                                                                    type="hidden" value="44"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">宮崎県</a><input
                                                                    type="hidden" value="45"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">鹿児島県</a><input
                                                                    type="hidden" value="46"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">沖縄県</a><input
                                                                    type="hidden" value="47"></div>
                                                            <div class="col-4 col-sm-2 p-1"><a
                                                                    class="btn btn-outline-default btn-block px-0 pref waves-effect waves-light">海外</a><input
                                                                    type="hidden" value="48"></div>
                                                        </div>
                                                        <div class="row pt-5">
                                                            <button class="btn btn-blue-grey mx-auto btn-modal-close waves-effect waves-light"
                                                                    aria-label="Close"
                                                                    data-reference="#specifies_workplaces_base">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">契約形態</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_search_condition[contract_types][]">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_0"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="quas"><label id="contract_types_field_label_0"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_0">業務委託（準委任）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_1"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="subc"><label id="contract_types_field_label_1"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_1">業務委託（請負）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="contract_types_field_opportunity_search_condition_2"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[contract_types][]"
                                                       value="temp"><label id="contract_types_field_label_2"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="contract_types_field_opportunity_search_condition_2">派遣契約</label>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">案件の特徴</label>
                                    <div class="pl-3 mb-5"><p id="opp_quality_text">選択されていません</p><input
                                            multiple="" autocomplete="off" type="hidden"
                                            name="opportunity_search_condition[opp_qualities][]"
                                            id="opportunity_search_condition_opp_qualities">
                                        <button class="mdb-modal-form btn btn-outline-default m-0 waves-effect waves-light"
                                                data-reference="#quality" data-toggle="modal" href="" type="button">
                                            案件の特徴を選択
                                        </button>
                                        <div aria-hidden="true" aria-labelledby="quality_modal" class="modal"
                                             id="quality" role="dialog" tabindex="-1">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header"><h4 class="modal-title w-100">
                                                        案件の特徴を選択</h4>
                                                        <button aria-label="Close" class="close" data-dismiss="modal"
                                                                type="button"><span aria-hidden="true"><i
                                                                class="material-icons md-dark mb-36">clear</i></span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row opp-quality-select">
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">70万円以上</a><input
                                                                    type="hidden" value="700thousand_or_more"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">100万円以上</a><input
                                                                    type="hidden" value="1million_or_more"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">1年未満OK</a><input
                                                                    type="hidden" value="less_1year_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">新人でもOK</a><input
                                                                    type="hidden" value="newcomer_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">50代以上OK</a><input
                                                                    type="hidden" value="over_50years_old_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">外国籍OK</a><input
                                                                    type="hidden" value="foreign_nationality_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">リーダー募集</a><input
                                                                    type="hidden" value="leader_recruitment"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">英語力</a><input
                                                                    type="hidden" value="english_skill"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">面談1回</a><input
                                                                    type="hidden" value="interview_once"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">持ち帰りOK</a><input
                                                                    type="hidden" value="take_home_project"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">チーム提案OK</a><input
                                                                    type="hidden" value="team_proposal_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">ウェブ面談可能</a><input
                                                                    type="hidden" value="web_interview_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">案件場所から遠隔地居住でもOK</a><input
                                                                    type="hidden" value="remote__location_ok"></div>
                                                            <div class="col-12 col-sm-6"><a
                                                                    class="btn btn-outline-default btn-block px-0 mb-3 quality waves-effect waves-light">日本以外の居住者OK</a><input
                                                                    type="hidden" value="overseas_resident_ok"></div>
                                                        </div>
                                                        <div class="row mt-3">
                                                            <button aria-label="Close"
                                                                    class="btn btn-blue-grey mx-auto waves-effect waves-light"
                                                                    data-dismiss="modal">閉じる
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                 for="">募集対象</label>
                                    <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                           name="opportunity_search_condition[allowable_trading_restriction][]">
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_0"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_own_employee"><label
                                                id="allowable_trading_restriction_field_label_0"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_0">自社社員</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_1"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_via_another_company"><label
                                                id="allowable_trading_restriction_field_label_1"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_1">協力会社社員</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_2"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_freelance_self"><label
                                                id="allowable_trading_restriction_field_label_2"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_2">フリーランス（本人）</label>
                                        </div>
                                        <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3"><input
                                                class="custom-control-input"
                                                id="allowable_trading_restriction_field_opportunity_search_condition_3"
                                                type="checkbox"
                                                name="opportunity_search_condition[allowable_trading_restriction][]"
                                                value="allow_freelance"><label
                                                id="allowable_trading_restriction_field_label_3"
                                                class="custom-control-label anavi-select-label mb-3"
                                                for="allowable_trading_restriction_field_opportunity_search_condition_3">フリーランス（企業登録）</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion_details accordion_close py-3 px-2 clear border-top"><span
                                        class="float-left pr-7">詳細検索条件</span><i
                                        class="material-icons md-dark float-right d-block">keyboard_arrow_down</i></div>
                                <input autocomplete="off" type="hidden"
                                       name="opportunity_search_condition[accordion_open]"
                                       id="opportunity_search_condition_accordion_open" value="no">
                                <div class="accordion_contents_details mb-4 pt-3" style="display: none;">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">案件内容の確定状況</label>
                                        <div class="selecting-form row px-3 with-title"><input type="hidden"
                                                                                               name="opportunity_search_condition[order_accuracy_id][]">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="order_accuracy_id_field_opportunity_search_condition_0"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[order_accuracy_id][]"
                                                       value="afte"><label id="order_accuracy_id_field_label_0"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="order_accuracy_id_field_opportunity_search_condition_0">確定済み</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input class="custom-control-input"
                                                       id="order_accuracy_id_field_opportunity_search_condition_1"
                                                       type="checkbox"
                                                       name="opportunity_search_condition[order_accuracy_id][]"
                                                       value="befo"><label id="order_accuracy_id_field_label_1"
                                                                           class="custom-control-label anavi-select-label mb-3"
                                                                           for="order_accuracy_id_field_opportunity_search_condition_1">確定前</label>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">契約開始日</label>
                                    <div class="row pl-3">
                                        <div class="col-9">
                                            <div class="mx-auto mb-3"><input class="form-control picker__input"
                                                                             autocomplete="off"
                                                                             id="contract_startdate_at_field"
                                                                             type="text"
                                                                             name="opportunity_search_condition[contract_startdate_at]"
                                                                             readonly="" aria-haspopup="true"
                                                                             aria-expanded="false" aria-readonly="false"
                                                                             aria-owns="contract_startdate_at_field_root">
                                                <div class="picker" id="contract_startdate_at_field_root"
                                                     aria-hidden="true">
                                                    <div class="picker__holder" tabindex="-1">
                                                        <div class="picker__frame">
                                                            <div class="picker__wrap">
                                                                <div class="picker__box">
                                                                    <div class="picker__header">
                                                                        <div class="picker__date-display">
                                                                            <div class="picker__weekday-display">
                                                                                木曜日,
                                                                            </div>
                                                                            <div class="picker__month-display">
                                                                                <div>2月</div>
                                                                            </div>
                                                                            <div class="picker__day-display">
                                                                                <div>13</div>
                                                                            </div>
                                                                            <div class="picker__year-display">
                                                                                <div>2025</div>
                                                                            </div>
                                                                        </div>
                                                                        <select class="picker__select--year" disabled=""
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Select a year">
                                                                            <option value="2018">2018</option>
                                                                            <option value="2019">2019</option>
                                                                            <option value="2020">2020</option>
                                                                            <option value="2021">2021</option>
                                                                            <option value="2022">2022</option>
                                                                            <option value="2023">2023</option>
                                                                            <option value="2024">2024</option>
                                                                            <option value="2025" selected="">2025
                                                                            </option>
                                                                            <option value="2026">2026</option>
                                                                            <option value="2027">2027</option>
                                                                            <option value="2028">2028</option>
                                                                            <option value="2029">2029</option>
                                                                            <option value="2030">2030</option>
                                                                            <option value="2031">2031</option>
                                                                            <option value="2032">2032</option>
                                                                        </select><select class="picker__select--month"
                                                                                         disabled=""
                                                                                         aria-controls="contract_startdate_at_field_table"
                                                                                         title="Select a month">
                                                                        <option value="0">1月</option>
                                                                        <option value="1" selected="">2月</option>
                                                                        <option value="2">3月</option>
                                                                        <option value="3">4月</option>
                                                                        <option value="4">5月</option>
                                                                        <option value="5">6月</option>
                                                                        <option value="6">7月</option>
                                                                        <option value="7">8月</option>
                                                                        <option value="8">9月</option>
                                                                        <option value="9">10月</option>
                                                                        <option value="10">11月</option>
                                                                        <option value="11">12月</option>
                                                                    </select>
                                                                        <button class="picker__nav--prev btn btn-flat"
                                                                                data-nav="-1" role="button"
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Previous month"></button>
                                                                        <button class="picker__nav--next btn btn-flat"
                                                                                data-nav="1" role="button"
                                                                                aria-controls="contract_startdate_at_field_table"
                                                                                title="Next month"></button>
                                                                    </div>
                                                                    <table class="picker__table"
                                                                           id="contract_startdate_at_field_table"
                                                                           role="grid"
                                                                           aria-controls="contract_startdate_at_field"
                                                                           aria-readonly="true">
                                                                        <thead>
                                                                        <tr>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="月曜日">月
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="火曜日">火
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="水曜日">水
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="木曜日">木
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="金曜日">金
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="土曜日">土
                                                                            </th>
                                                                            <th class="picker__weekday" scope="col"
                                                                                title="日曜日">日
                                                                            </th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1737910800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月27日">27
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1737997200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月28日">28
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738083600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月29日">29
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738170000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月30日">30
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1738256400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年01月31日">31
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738342800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月01日">1
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738429200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月02日">2
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738515600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月03日">3
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738602000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月04日">4
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738688400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月05日">5
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738774800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月06日">6
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738861200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月07日">7
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1738947600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月08日">8
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739034000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月09日">9
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739120400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月10日">10
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739206800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月11日">11
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739293200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月12日">12
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus picker__day--today picker__day--highlighted"
                                                                                     data-pick="1739379600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月13日"
                                                                                     aria-activedescendant="true">13
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739466000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月14日">14
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739552400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月15日">15
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739638800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月16日">16
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739725200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月17日">17
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739811600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月18日">18
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739898000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月19日">19
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1739984400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月20日">20
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740070800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月21日">21
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740157200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月22日">22
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740243600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月23日">23
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740330000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月24日">24
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740416400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月25日">25
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740502800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月26日">26
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740589200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月27日">27
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--infocus"
                                                                                     data-pick="1740675600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年02月28日">28
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740762000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月01日">1
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740848400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月02日">2
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1740934800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月03日">3
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741021200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月04日">4
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741107600000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月05日">5
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741194000000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月06日">6
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741280400000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月07日">7
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741366800000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月08日">8
                                                                                </div>
                                                                            </td>
                                                                            <td role="presentation">
                                                                                <div class="picker__day picker__day--outfocus"
                                                                                     data-pick="1741453200000"
                                                                                     role="gridcell"
                                                                                     aria-label="2025年03月09日">9
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                        </tbody>
                                                                    </table>
                                                                    <div class="picker__footer">
                                                                        <button class="picker__button--today"
                                                                                type="button" data-pick="1739379600000"
                                                                                disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            今日
                                                                        </button>
                                                                        <button class="picker__button--clear"
                                                                                type="button" data-clear="1" disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            消去
                                                                        </button>
                                                                        <button class="picker__button--close"
                                                                                type="button" data-close="true"
                                                                                disabled=""
                                                                                aria-controls="contract_startdate_at_field">
                                                                            Close
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <input type="hidden"
                                                       name="opportunity_search_condition[contract_startdate_at]"></div>
                                        </div>
                                        <div class="col-1 p-0"><i
                                                class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">募集人数</label>
                                    <div class="row pl-3">
                                        <div class="col-8">
                                            <div class="mx-auto mb-5"><input type="number" min="0" class="form-control"
                                                                             autocomplete="off" id="participants_field"
                                                                             name="opportunity_search_condition[participants]">
                                            </div>
                                        </div>
                                        <div class="col pl-0"><label class="inline-unit-label">人</label></div>
                                    </div>
                                    <div class="mx-auto mb-5"><label class="font-middle mb-3 ex-bold">面談回数</label>
                                        <div class="select-wrapper mdb-select anavi-select mb-5"><span
                                                class="caret material-icons">keyboard_arrow_down</span><input
                                                type="text" class="select-dropdown form-control" readonly="true"
                                                required="false"
                                                data-activates="select-options-interview_count_id_field" value=""
                                                role="listbox" aria-multiselectable="false" aria-disabled="false"
                                                aria-required="false" aria-haspopup="true" aria-expanded="false">
                                            <ul id="select-options-interview_count_id_field"
                                                class="dropdown-content select-dropdown w-100 " style="display: none;">
                                                <li class=" active " role="option" aria-selected="true"
                                                    aria-disabled="false"><span class="filtrable "> 未選択    </span>
                                                </li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 1回    </span></li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 1〜2回    </span>
                                                </li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 2回    </span></li>
                                                <li class="  " role="option" aria-selected="false"
                                                    aria-disabled="false"><span class="filtrable "> 3回以上    </span>
                                                </li>
                                            </ul>
                                            <select class="mdb-select anavi-select mb-5 initialized"
                                                    id="interview_count_id_field"
                                                    name="opportunity_search_condition[interview_count_id]">
                                                <option value="">未選択</option>
                                                <option value="once">1回</option>
                                                <option value="few">1〜2回</option>
                                                <option value="twice">2回</option>
                                                <option value="over_three_times">3回以上</option>
                                            </select></div>
                                    </div>
                                    <div class="accordion-close-area text-center">閉じる<i
                                            class="material-icons md-dark md-18">close</i></div>
                                </div>
                                <div class="border-bottom mb-5"></div>
                                <input autocomplete="off" type="hidden" name="opportunity_search_condition[switch_type]"
                                       id="opportunity_search_condition_switch_type" value="button">
                                <div class="text-center d-none d-md-block search-area py-2">
                                    <button name="button" type="submit"
                                            class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light"
                                            id="opp-search-btn" data-disable-with="検索中">
                                        <div class="py-2 d-none" id="loader">
                                            <div class="loader"></div>
                                        </div>
                                        <div id="btn-text"><span class="font-extralarge" id="search-count">75</span>
                                            件<br>この条件で検索
                                        </div>
                                    </button>
                                    <div class="py-2"><a
                                            href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="container-fluid d-block d-md-none search-fixed-btn search-area-sm">
                    <div class="row py-3">
                        <div class="col-12">
                            <button name="button" type="submit"
                                    class="btn btn-default btn-block font-middle mb-3 submit-btn waves-effect waves-light"
                                    form="opportunity_search_condition_form" data-disable-with="検索中">
                                <div class="py-2 d-none" id="loader-sm">
                                    <div class="loader"></div>
                                </div>
                                <div id="btn-text-sm"><span class="font-extralarge" id="search-count-sm">75</span>
                                    件<br>この条件で検索
                                </div>
                            </button>
                            <div class="text-center"><a
                                    href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-8 col-lg-9">
            <div class="row">
                <div class="col-12 d-none d-md-block mb-4">
                    <div class="d-flex"><span class="mr-3 nowrap">よく検索されるキーワード</span>
                        <div><a class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/107"><span>SAP</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/106"><span>PMO</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/102"><span>コンサル</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/105"><span>Java</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/110"><span>PHP</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/142"><span>RPA</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/140"><span>AI</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/44"><span>Python</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/144"><span>セキュリティ</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/145"><span>Salesforce</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/101"><span>PM</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/146"><span>React</span></a><a
                                class="mr-4 default-main-color font-middle d-inline-block"
                                href="/opportunities/keyword/147"><span>テスト</span></a></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="d-flex justify-content-between search-toggle d-md-none bg-white w-100 p-3 mt-4 mx-3"><a
                        class="font-middle custom-grey-6-text m-0" href="javascript:void(0);">検索条件指定</a><i
                        class="material-icons custom-grey-6-text">keyboard_arrow_down</i></div>
            </div>
            <div class="row mr-0 ml-0">
                <div class="col-12 mt-3 border border-light p-3 px-md-4 bg-grey-2 rounded-sm">
                    <div class="position-relative">
                        <div class="d-sm-flex search-condition-area line-height-180">
                            <div class="text-nowrap mb-2 mb-sm-n2 ml-3 ml-sm-0">検索条件</div>
                            <div class="pl-3">
                                <div class="d-sm-inline boder-left"><span
                                        class="mr-3 custom-grey-5-text">得意領域</span>
                                    <div class="d-sm-inline mb-2 mb-sm-0"><span
                                            class="border-grey-3 bg-white mr-2 px-2">コンサル</span>
                                        <div class="d-sm-inline"><span class="mr-3">ERP・PKG</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion_search_condition_btn"><a
                                class="accordion_search_condition accordion_close text-right btn-block pr-3 bg-grey-2 pl-5"
                                href="javascript:void(0);" style="display: none;"><span
                                class="vertical-middle font-middle">すべて表示</span><i class="material-icons">keyboard_arrow_down</i></a>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="font-middle"><span class="font-ll ex-bold" id="total-count">75</span> 件中 1〜24件</div>
                <div data-v-7d20fe15="" class="position-relative pl-3"><!-- ソート中の項目を表示 -->
                    <div data-v-7d20fe15="" class="d-flex justify-content-end sort-display-area p-2"><label
                            data-v-7d20fe15="" class="pr-2 mb-0">新着（降順）</label><i data-v-7d20fe15=""
                                                                                      class="material-icons custom-grey-6-text pl-1">keyboard_arrow_down</i>
                    </div>
                    <ul data-v-7d20fe15="" class="bg-white sort-options-area text-right" style="display: none;">
                        <li data-v-7d20fe15="" class="sort-active">新着（降順） <i data-v-7d20fe15=""
                                                                                 class="material-icons custom-grey-6-text">done</i>
                        </li>
                        <li data-v-7d20fe15="" class="">更新（降順） <!--v-if--></li>
                        <li data-v-7d20fe15="" class="">単価（降順） <!--v-if--></li>
                    </ul>
                </div>
            </div>
            <div class="row">
                <div data-v-1c51a755="" id="jsonld-125545">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【ハイブリ/ 調査・評価】元請支援_SAP×人給Success Factors領域におけるIT×業務コンサル募集_008038",
                            "value": 125545
                        },
                        "datePosted": "2025/02/13 11:19:49",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 150,
                                "maxValue": 180,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125545/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【ハイブリ/ 調査・評価】元請支援_SAP×人給Success
                                    Factors領域におけるIT×業務コンサル募集_008038
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/24 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            人事・給与業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、ワークショップの準備、ファシリテーション、成果物作成などの担当を想定
                            関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。
                            【場所】
                            ハイブリッド/ 有楽町駅周辺
                            詳細な場所は面談時に元請から共有予定。
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">150万円 〜 180万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125545" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    541413182556<br>◆案件名: 【ハイブリ/ 調査・評価】元請支援_SAP×人給Success
                                    Factors領域におけるIT×業務コンサル募集_008038<br>◆案件への関わり: 商流に入る<br>◆案件の商流:
                                    一次請<br>◆案件内容: <br>人事・給与業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、ワークショップの準備、ファシリテーション、成果物作成などの担当を想定<br>関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。<br>【場所】<br>ハイブリッド/
                                    有楽町駅周辺<br>詳細な場所は面談時に元請から共有予定。<br>※沖縄への出張対応の可能性あり。
                                    （1週間程度を想定）<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ： 当月末締め<br>稼働日数 ： 週5日<br>【備考】<br>勤務時間 ： 9:00～18:00<br>年齢制限 ：
                                    50歳まで<br>商流制限 ： 原則として貴社所属の方を優先致します。フリーランスの方もご相談ください。<br>国籍制限
                                    ： 日本籍の方を優先させていただきます。（日本語がネイティブレベルが求められるため）<br>服装指定
                                    ： スーツ or ビジネスカジュアル<br>リモート対応 ： 在宅業務での環境は貴社 or
                                    稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・SAP
                                    HCM（給与計算含む）とSuccessFactorsの専門知識<br>・一般的な人事領域のビジネスプロセスに精通している<br>・HRプロセスのワークショップの準備、実施、文書化の経験がある<br>・日本語：ネイティブレベル<br>・英語：ビジネスレベル（相当も可）<br>◆単価:
                                    150万円 〜 180万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年02月24日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125530">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【SAP】導入に伴う経理業務支援_ワークショップ担当",
                            "value": 125530
                        },
                        "datePosted": "2025/02/12 21:36:17",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125530/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【SAP】導入に伴う経理業務支援_ワークショップ担当
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/17 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">カスタマー：半導体装置メーカー
                            案件概要：SAP導入に伴う経理業務支援（要件定義フェーズ～）
                            　　　　　ワークショップの準備、ファシリテーション、成果物作成などの担当者を募集します。
                            勤務地：弊社日比谷オフィス、クライアントオフィス（港区）、リモートワーク
                            ※山梨、岩手への出張あり。数か月に1度、日帰りや1週間程度の出張を予定しています
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 150万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125530" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    822363771034<br>◆案件名: 【SAP】導入に伴う経理業務支援_ワークショップ担当<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>カスタマー：半導体装置メーカー<br>案件概要：SAP導入に伴う経理業務支援（要件定義フェーズ～）<br>　　　　　ワークショップの準備、ファシリテーション、成果物作成などの担当者を募集します。<br>勤務地：弊社日比谷オフィス、クライアントオフィス（港区）、リモートワーク<br>※山梨、岩手への出張あり。数か月に1度、日帰りや1週間程度の出張を予定しています<br>契約開始時期：2025年3月後半～4月初旬（契約期間は3か月を予定。延長可能性あり）<br>単価：スキル見合い<br>募集人数：1名<br>◆人財要件:
                                    <br>必須スキル<br>・SAP COの専門知識<br>・一般的な経理領域のビジネスプロセスに精通している<br>・FI/CO領域のワークショップの成果物準備、ファシリテーションの経験がある<br>・日本語が流暢であること。英語力は不問。<br>尚可スキル<br>・SAP
                                    FIの専門知識<br>・経理領域への業務支援経験<br><br>◆単価: 100万円 〜 150万円 / 月
                                    ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1〜2回<br>◆契約期間: 2025年03月17日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125498">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "製薬会社向け、製薬事業 (特に製造工程) および基幹システム統合支援",
                            "value": 125498
                        },
                        "datePosted": "2025/02/12 16:02:26",
                        "validThrough": "2025/02/28 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 190,
                                "maxValue": 200,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125498/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">製薬会社向け、製薬事業 (特に製造工程)
                                    および基幹システム統合支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/17 〜 2025/05/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■概要
                            SAPのグローバルテンプレート展開。
                            生産管理(MES)や会社間取引においてカスタマイズを多用している。
                            ヨーロッパ⇒アメリカ⇒日本の順でロールアウトを検討しており、すでにヨーロッパでは検討を本格化済み。
                            日本への導入に関する検討を2025年4~5月頃に本格化予定。その事前に導入のインパクト調査を実施したい。


                        </p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/27</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">190万円 〜 200万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125498" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    676343741509<br>◆案件名: 製薬会社向け、製薬事業 (特に製造工程) および基幹システム統合支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>■概要<br>SAPのグローバルテンプレート展開。<br>生産管理(MES)や会社間取引においてカスタマイズを多用している。<br>ヨーロッパ⇒アメリカ⇒日本の順でロールアウトを検討しており、すでにヨーロッパでは検討を本格化済み。<br>日本への導入に関する検討を2025年4~5月頃に本格化予定。その事前に導入のインパクト調査を実施したい。<br><br><br><br>◆人財要件:
                                    <br>■必須スキル<br>・MESや生産現場業務に詳しい<br>・製薬事業に詳しい<br>・ITシステムに係る一定の理解
                                    (SAPに関わらず)<br>・構想策定など上流経験<br>・英語 (ドキュメントが英語のため／スピーキング不要)<br><br>勤務地：リモート+東京オフィス出社可能性あり<br>◆単価:
                                    190万円 〜 200万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1〜2回<br>◆契約期間:
                                    2025年02月17日 〜 2025年05月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125481">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "SAP導入に伴う経理業務支援（要件定義フェーズ～）",
                            "value": 125481
                        },
                        "datePosted": "2025/02/12 11:02:29",
                        "validThrough": "2025/03/09 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 160,
                                "maxValue": 180,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125481/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    SAP導入に伴う経理業務支援（要件定義フェーズ～）
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/24 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">カスタマー：半導体装置メーカー
                            案件概要：SAP導入に伴う経理業務支援（要件定義フェーズ～）
                            勤務地 ：日比谷オフィス、クライアントオフィス（港区）、リモートワーク(出社頻度応相談)
                            ※山梨、岩手への出張あり。数か月に1度、日帰りや1週間程度の出張を予定しています

                            &lt;募集ポジション&gt;＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            ワークショッ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/8</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">160万円 〜 180万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125481" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    105325746274<br>◆案件名: SAP導入に伴う経理業務支援（要件定義フェーズ～）<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>カスタマー：半導体装置メーカー<br>案件概要：SAP導入に伴う経理業務支援（要件定義フェーズ～）<br>勤務地
                                    ：日比谷オフィス、クライアントオフィス（港区）、リモートワーク(出社頻度応相談)<br>※山梨、岩手への出張あり。数か月に1度、日帰りや1週間程度の出張を予定しています<br><br>&lt;募集ポジション&gt;＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>ワークショップの準備、ファシリテーション、成果物作成などの担当者、1名。<br>■契約開始時期<br>2025年3月後半～4月初旬（契約期間は3か月を予定。延長可能性あり）<br><br>◆人財要件:
                                    <br>■必要なスキル・知見<br>・SAP COの専門知識<br>・一般的な経理領域のビジネスプロセスに精通している<br>・FI/CO領域のワークショップの成果物準備、ファシリテーションの経験がある<br>・日本語が流暢であること。英語力は不問。<br>・（あれば尚可）SAP
                                    FIの専門知識<br>・（あれば尚可）経理領域への業務支援経験<br><br>◆単価: 160万円 〜
                                    180万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1〜2回<br>◆契約期間:
                                    2025年03月24日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125477">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【高単価】IFS導入プロジェクト（ERP構想及び要件定義）",
                            "value": 125477
                        },
                        "datePosted": "2025/02/12 09:54:23",
                        "validThrough": "2025/03/10 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 180,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125477/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755="">
                            <div data-v-1c51a755="" class="new-flag-pc"><span data-v-1c51a755=""
                                                                              class="font-small">NEW!</span></div>
                        </div><!--v-if-->
                        <h5 data-v-1c51a755="" class="ml-5 mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【高単価】IFS導入プロジェクト（ERP構想及び要件定義）
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/11 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">1. クライアント：製造業
                            2. 案件名称：IFS導入プロジェクト（ERP構想及び要件定義）
                            3. 案件概要：ERP導入に向けた構想及び要件定義でIFS主体
                            4. 期待役割：構想検討及び要件定義メンバー、提案活動に参画可能性あり
                            5. 参画期間：2025/03/11～2025/06/30
                            6. 勤務場所： オンサイト・リモート併用
                            7...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/9</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 180万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 80 〜 99%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755=""
                                    class="">週4 〜 2日出社 / 週1日出社 / 週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125477" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    377321666057<br>◆案件名: 【高単価】IFS導入プロジェクト（ERP構想及び要件定義）<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>1. クライアント：製造業<br>2.
                                    案件名称：IFS導入プロジェクト（ERP構想及び要件定義）<br>3. 案件概要：ERP導入に向けた構想及び要件定義でIFS主体<br>4.
                                    期待役割：構想検討及び要件定義メンバー、提案活動に参画可能性あり<br>5.
                                    参画期間：2025/03/11～2025/06/30<br>6. 勤務場所： オンサイト・リモート併用<br>7.
                                    想定単価：120-180万円　※スキル・役割によって決定<br><br>【選考方法】<br>1.
                                    人財（職務経歴書）による書類選考。<br>2. 案件責任者 or 現場担当との面談。*1回若しくは2回を想定。<br>◆人財要件:
                                    <br>1．IFSコンサルとして構想策定及び要件定義を実施した経験がある方。提案活動をしたことがある方は特に優遇。<br>2．クライアントと打合せに向けて検討資料を作成するスキル、及び直接折衝/QAができる日本語能力が必須。<br>3．ERP導入時にSAPとIFSの比較を行うことが多々あり、SAP/IFSの特性を理解した提案や資料作成ができるスキルがあると非常に良い。<br>4．少人数で動くPJの為、自走可能な人財を求めている。<br>◆単価:
                                    120万円 〜 180万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働） / 80 〜 99%<br>◆出社頻度:
                                    週4 〜 2日出社 / 週1日出社 / 週1日未満出社 / フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    3人<br>◆面談回数: 2回<br>◆契約期間: 2025年03月11日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） /
                                    フリーランス（一社先） / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125417">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【ハイブリ/ 調査・評価】元請支援_SAP会計領域におけるIT×業務コンサル募集_008031",
                            "value": 125417
                        },
                        "datePosted": "2025/02/10 11:26:29",
                        "validThrough": "2025/03/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 180,
                                "maxValue": 190,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125417/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【ハイブリ/
                                    調査・評価】元請支援_SAP会計領域におけるIT×業務コンサル募集_008031
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月12日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/24 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            会計業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、改修に合ったってのシステム要件定義も行う想定。
                            関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。
                            【場所】
                            ハイブリッド/ 有楽町駅周辺
                            詳細な場所は面談時に元請から共有予定。
                            ※沖縄への出張対応の可能性...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">180万円 〜 190万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125417" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    847154384302<br>◆案件名: 【ハイブリ/ 調査・評価】元請支援_SAP会計領域におけるIT×業務コンサル募集_008031<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>会計業務に関するビジネスプロセスの調査～評価。運用している基幹システムはSAPとなるので、改修に合ったってのシステム要件定義も行う想定。<br>関連して、ワークショップの開催やその会議体のファシリテーションも作業スコープに盛り込まれる。<br>【場所】<br>ハイブリッド/
                                    有楽町駅周辺<br>詳細な場所は面談時に元請から共有予定。<br>※沖縄への出張対応の可能性あり。
                                    （1週間程度を想定）<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ： 当月末締め<br>稼働日数 ： 週5日<br>【備考】<br>ただし弊社未面談の方は事前にお会いさせていただきます。（計2回）<br>勤務時間
                                    ： 9:00～18:00<br>年齢制限 ： 50歳まで<br>商流制限 ： 貴社所属の方を優先致します<br>国籍制限
                                    ： 日本籍の方を優先させていただきます<br>服装指定 ： スーツ or ビジネスカジュアル<br>リモート対応
                                    ： 在宅業務での環境は貴社 or 稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・SAPシステム会計領域の知見と、企業財務の専門知識<br>・一般的なビジネスプロセス（AP、AR、GL、トレジャリーなど）<br>・財務プロセスのワークショップの準備、実施、文書化の経験がある<br>・英語：ビジネスレベル<br>◆単価:
                                    180万円 〜 190万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年02月24日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先）
                                    / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125412">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【要員募集：ヘルプデスク】ERP導入/知見/リモート",
                            "value": 125412
                        },
                        "datePosted": "2025/02/10 10:31:32",
                        "validThrough": "2025/03/12 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 50,
                                "maxValue": 70,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125412/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【要員募集：ヘルプデスク】ERP導入/知見/リモート
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2026/02/28
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝
                            ■ERPパッケージシステムヘルプデスク
                            〈作業内容〉
                            　ERPパッケージ（Microsoft Dynamic365BC）導入済の顧客からの
                            　問合せを受付け、開発チームの支援を受けながら対応する。

                            ■必須要件：
                            ・ERPパッケージ（何でも可）導入経験またはそれに準ずる知...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/11</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">50万円 〜 70万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社 / 週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125412" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    458151095871<br>◆案件名: 【要員募集：ヘルプデスク】ERP導入/知見/リモート<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容:
                                    <br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>■ERPパッケージシステムヘルプデスク<br>〈作業内容〉<br>　ERPパッケージ（Microsoft
                                    Dynamic365BC）導入済の顧客からの<br> 　問合せを受付け、開発チームの支援を受けながら対応する。<br><br>■必須要件：<br>・ERPパッケージ（何でも可）導入経験またはそれに準ずる知見<br>→参画後にレクチャーはあるが、一定の知識は必要<br>・コミュニケーション能力<br>・フットワークの軽さ<br><br>■尚可要件：<br>開発経験<br>英語力（対応可だと有難いです。）<br><br>■条件：<br>期間：4月～<br>稼働：100％<br>人数：1名<br>金額：50万～70万ほど<br>面談：2回※WEB<br>場所：基本リモート（必要に応じ天王洲アイル）<br>支払い：45日<br>勤務：フレックス・拘束時間なし<br><br>■備考：<br>55歳ぐらいまで<br>外国籍不可<br>弊社から1社先まで<br>地方も可だが、初日出社や毎月1回程度の出社にかかる交通費は実費でお願いします。<br>＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝＝<br>◆人財要件:
                                    <br>■必須要件：<br>・ERPパッケージ（何でも可）導入経験またはそれに準ずる知見<br>→参画後にレクチャーはあるが、一定の知識は必要<br>・コミュニケーション能力<br>・フットワークの軽さ<br><br>■尚可要件：<br>開発経験<br>英語力（対応可だと有難いです。）<br>◆単価:
                                    50万円 〜 70万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日出社
                                    / 週1日未満出社 / フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年04月01日 〜 2026年02月28日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125405">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【SAP】（FI、CO）財務プロセスワークショップ担当",
                            "value": 125405
                        },
                        "datePosted": "2025/02/07 20:35:35",
                        "validThrough": "2025/03/09 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都 ",
                                    "addressCountry": "JP"
                                },
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": " 沖縄県",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125405/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【SAP】（FI、CO）財務プロセスワークショップ担当
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/17 〜 2025/04/30</div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">カスタマー：高等教育機関
                            案件概要：ビジネスプロセス評価、システム要求事項の整理
                            　　　　　ワークショップの準備、ファシリテーション、成果物作成などの担当者を募集します。
                            勤務地：日比谷オフィス、リモートワーク
                            　　　　※沖縄への出張あり。 1週間程度の出張を数回予定しています。
                            契約開始時期：2025年2月末～3月初旬（契約期間は８～...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/8</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 150万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都 / 沖縄県</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125405" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    280928137295<br>◆案件名: 【SAP】（FI、CO）財務プロセスワークショップ担当<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>カスタマー：高等教育機関<br>案件概要：ビジネスプロセス評価、システム要求事項の整理<br>　　　　　ワークショップの準備、ファシリテーション、成果物作成などの担当者を募集します。<br>勤務地：日比谷オフィス、リモートワーク<br>　　　　※沖縄への出張あり。
                                    1週間程度の出張を数回予定しています。<br>契約開始時期：2025年2月末～3月初旬（契約期間は８～10週間を予定）<br>単価：スキル見合い<br>募集人数：1名<br>◆人財要件:
                                    <br>必須スキル・知見：<br>・SAP S/4HANA（FI、COなど）に関する財務の専門知識<br>・一般的なビジネスプロセス（AP、AR、GL、トレジャリーなど）に精通している<br>・財務プロセスのワークショップの準備、実施、文書化の経験がある<br>・日本語が流暢であること、ビジネスレベルの英語力を有すること<br><br>尚可スキル：<br>・高等教育機関における業務経験<br>◆単価:
                                    100万円 〜 150万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都 / 沖縄県<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1〜2回<br>◆契約期間: 2025年02月17日 〜 2025年04月30日<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125329">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【50％稼働可】SAPロールアウト支援（FI＆CO有識者）",
                            "value": 125329
                        },
                        "datePosted": "2025/02/07 09:11:18",
                        "validThrough": "2025/03/08 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 170,
                                "maxValue": 200,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125329/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【50％稼働可】SAPロールアウト支援（FI＆CO有識者）
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/20 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            ・大手SIerグループ企業のPJ体制に入り、SAPのFI・COの知見を活かし、海外拠点（インド）含めた自動車メーカー企業向けのSAPロールアウトプロジェクト支援を行う。
                            （開発部隊が海外※インド等）
                        </p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/7</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">170万円 〜 200万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働） / 80 〜 99% / 60 〜 79% / 40 〜 59%</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125329" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    856815473078<br>◆案件名: 【50％稼働可】SAPロールアウト支援（FI＆CO有識者）<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>・大手SIerグループ企業のPJ体制に入り、SAPのFI・COの知見を活かし、海外拠点（インド）含めた自動車メーカー企業向けのSAPロールアウトプロジェクト支援を行う。<br>（開発部隊が海外※インド等）<br><br>◆人財要件:
                                    <br>[必須スキル]<br>・SAP導入プロジェクト経験（グローバルロールアウト：EMEA経験）<br>・SAPの知見（FI＆CO※特にCOの有識者）<br>・システム導入プロジェクトのチェンジマネジメント経験<br>・プロジェクトマネジメントスキル（課題管理・進捗管理・各種報告）<br>・1人称で業務推進が可能<br>・海外法人との英語MTGでの討議経験（上級ビジネス英語必要※会話・読み書き）<br>・日本国籍の方のみ<br><br>[尚可スキル]<br>・製造業（自動車）向けのPJ経験・知見<br>◆単価:
                                    170万円 〜 200万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働） / 80 〜 99% / 60 〜
                                    79% / 40 〜 59%<br>◆出社頻度: 週5日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年02月20日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125363">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "急募！複数ポジション募集PJ/PM/アーキテクト/インフラ/コンサル",
                            "value": 125363
                        },
                        "datePosted": "2025/02/07 02:17:54",
                        "validThrough": "2025/03/09 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 125,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125363/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    急募！複数ポジション募集PJ/PM/アーキテクト/インフラ/コンサル
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/12/31</div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■案件概要
                            ・現在エンドクライアント内にて複数PJが走っている中、大規模な体制変更を推進することに
                            ・アプリの組織、インフラの組織に集約
                            ・データマネジメントはインフラ組織に集約
                            ・専門職の部門を新設し、PM、BA, ITAのリソースプールに

                            ■課題
                            ・基幹システム刷新においてPM,BA,ITAのリソースが足りていない
                            ・セキュ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/8</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 125万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125363" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    372862279406<br>◆案件名: 急募！複数ポジション募集PJ/PM/アーキテクト/インフラ/コンサル<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>■案件概要<br>・現在エンドクライアント内にて複数PJが走っている中、大規模な体制変更を推進することに<br>・アプリの組織、インフラの組織に集約<br>・データマネジメントはインフラ組織に集約<br>・専門職の部門を新設し、PM、BA,
                                    ITAのリソースプールに<br><br>■課題<br>・基幹システム刷新においてPM,BA,ITAのリソースが足りていない<br>・セキュリティ観点ではUS/ASIAで強力なガバナンスが必要<br>・海外のITをサポートしている部隊を吸収する
                                    (海外ITもミッションになる)<br>・M&amp;A案件も進行中<br><br>■募集ポジション<br>・発注側のPM/5名程度<br>・社内アプリ全般を担当するアーキテクト人財：1名<br>・PJのインフラ全般を担当するアーキテクト：1名<br>・セキュリティアーキテクト（実装観点）：1名<br>・Azureに知見が深いアーキテクト：1名<br>・BCPに知見のあるセキュリティコンサルタント：1名→急募<br>・海外ITサポートチーム責任者（海外支社のIT投資が適切か否か判断できる方）：1名→急募<br><br>■スキル要件<br>全ポジションにおける共通必須スキル：<br>・ユーザー/ベンダーコントロールが必要な為、柔軟にコミュニケーションを取ることができる事<br>・ユーザーサイド/ベンダーサイドどちらも経験している事<br>・小回りがきく事<br><br>■作業期間<br>3月〜<br><br>■作業場所<br>都内23区内<br>週4オンサイト、週1リモート<br><br>■単価目安<br>～125万円／月<br><br>■商流<br>プライム直<br><br>■その他<br>・人財とあわせて、上記の業務内容とスキル要件に対する知見・経験等について具体的に補足コメントをいただけると提案する際にプッシュできます。<br>・年齢：〜50歳<br>・面談２回<br>◆人財要件:
                                    <br>■募集ポジション<br>・発注側のPM/5名程度<br>・社内アプリ全般を担当するアーキテクト人財：1名<br>・PJのインフラ全般を担当するアーキテクト：1名<br>・セキュリティアーキテクト（実装観点）：1名<br>・Azureに知見が深いアーキテクト：1名<br>・BCPに知見のあるセキュリティコンサルタント：1名→急募<br>・海外ITサポートチーム責任者（海外支社のIT投資が適切か否か判断できる方）：1名→急募<br><br>■スキル要件<br>全ポジションにおける共通必須スキル：<br>・ユーザー/ベンダーコントロールが必要な為、柔軟にコミュニケーションを取ることができる事<br>・ユーザーサイド/ベンダーサイドどちらも経験している事<br>・小回りがきく事<br>◆単価:
                                    100万円 〜 125万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 5人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年03月01日 〜 2025年12月31日<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125362">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "グローバル小売業/SAP S/4HANA の実装/展開(OTC/SD)",
                            "value": 125362
                        },
                        "datePosted": "2025/02/07 02:11:07",
                        "validThrough": "2025/03/09 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125362/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">グローバル小売業/SAP S/4HANA
                                    の実装/展開(OTC/SD)
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/11 〜 2025/12/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■案件概要
                            SAP S/4HANA の実装/ロールアウト

                            ■業務内容
                            • 実装/展開プロジェクトのSAP SD機能リードコンサルタント
                            • SAP OTC/SD の実務経験が10年以上

                            ■スキル要件
                            • SAP SDに関する深い知識
                            • SAP Distribution機能が外部の倉庫管理システムと統合する方法に関する知識。...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/8</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 150万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125362" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    137861869833<br>◆案件名: グローバル小売業/SAP S/4HANA の実装/展開(OTC/SD)<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>■案件概要<br>SAP S/4HANA
                                    の実装/ロールアウト<br><br>■業務内容<br>• 実装/展開プロジェクトのSAP SD機能リードコンサルタント<br>•
                                    SAP OTC/SD の実務経験が10年以上<br><br>■スキル要件<br>• SAP SDに関する深い知識<br>•
                                    SAP Distribution機能が外部の倉庫管理システムと統合する方法に関する知識。<br>•
                                    実務経験があり、一人称で主体的に動ける人財。<br>• 優れた技術、論理、問題解決スキル。<br>•
                                    日本語と英語の両方でビジネス レベルの流暢さを持つバイリンガル。<br><br>■作業期間<br>ASAP<br><br>■作業場所<br>東京
                                    (渋谷区)<br>※ハイブリッドも検討可能、オンサイト可能日数をご提示ください。<br>※フルリモートはNG<br><br>■単価目安<br>～150万円／月（スキル見合いで高単価の人でも提案可能）<br><br>■商流<br>プライム直<br><br>■その他<br>人財とあわせて、上記の業務内容とスキル要件に対する知見・経験等について具体的に補足コメントをいただけると提案する際にプッシュできます。<br>◆人財要件:
                                    <br>■スキル要件<br>• SAP SDに関する深い知識<br>• SAP
                                    Distribution機能が外部の倉庫管理システムと統合する方法に関する知識。<br>•
                                    実務経験があり、一人称で主体的に動ける人財。<br>• 優れた技術、論理、問題解決スキル。<br>•
                                    日本語と英語の両方でビジネス レベルの流暢さを持つバイリンガル。<br>◆単価: 120万円
                                    〜 150万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    1回<br>◆契約期間: 2025年02月11日 〜 2025年12月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125346">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "ECC6.0からS/4HANAへのグリーンフィールド移行案件（新規構築）におけるFIコンサルタント、基本設計者",
                            "value": 125346
                        },
                        "datePosted": "2025/02/06 16:09:13",
                        "validThrough": "2025/03/08 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 200,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125346/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    ECC6.0からS/4HANAへのグリーンフィールド移行案件（新規構築）におけるFIコンサルタント、基本設計者
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/05/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">ロジ・会計領域をS/4HANAへ刷新に向け、会計領域の要件定義フェーズ作業

                            ①FIコンサル：GL担当者1名、AR担当者１名

                            （メイン作業）
                            ・現行アドオン調査・整理
                            ・ToBeアドオンへの置き換え時の実現ポイント調査・整理
                            （サブ作業）
                            ・現行SAPの調査支援（標準機能の含む）
                            ・ToBe詳細業務フロー作成支援
                            ・BPマス...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/7</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 200万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125346" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    211825758247<br>◆案件名: ECC6.0からS/4HANAへのグリーンフィールド移行案件（新規構築）におけるFIコンサルタント、基本設計者<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>ロジ・会計領域をS/4HANAへ刷新に向け、会計領域の要件定義フェーズ作業<br><br>①FIコンサル：GL担当者1名、AR担当者１名<br><br>（メイン作業）<br>・現行アドオン調査・整理<br>・ToBeアドオンへの置き換え時の実現ポイント調査・整理<br>（サブ作業）<br>・現行SAPの調査支援（標準機能の含む）<br>・ToBe詳細業務フロー作成支援<br>・BPマスタへの統合作業支援<br><br>②FI基本設計者<br>FI領域の基本設計<br>◆人財要件:
                                    <br>①<br>・FI／GL、AR領域の要件定義書から基本設計を１から作成し、後続作業も出来る方。<br>・顧客との調整や内容説明、SAP標準機能に対する知識<br><br>②<br>・FI基本設計の十分な経験<br>◆単価:
                                    120万円 〜 200万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 3人<br>◆面談回数:
                                    1〜2回<br>◆契約期間: 2025年03月01日 〜 2025年05月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125333">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "機械業様向け SAP S/4 HANA新規導入案件",
                            "value": 125333
                        },
                        "datePosted": "2025/02/06 14:00:19",
                        "validThrough": "2025/02/28 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125333/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">機械業様向け SAP S/4
                                    HANA新規導入案件
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月07日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2027/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【顧客　　】
                            機械業

                            【作業内容】
                            　要件定義（業務ヒアリング、S/4機能説明、プロトタイプ検証構築）作業

                            【作業場所】大手町または東陽町

                            【モジュール】
                            FI, CO, SD, MM, PS</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/27</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 150万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125333" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    875817740839<br>◆案件名: 機械業様向け SAP S/4 HANA新規導入案件<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>【顧客　　】<br>機械業<br><br>【作業内容】<br>　要件定義（業務ヒアリング、S/4機能説明、プロトタイプ検証構築）作業<br><br>【作業場所】大手町または東陽町<br><br>【モジュール】<br>FI,
                                    CO, SD, MM, PS<br>◆人財要件: <br>【スキル】<br>・ERP導入案件における要件定義の経験<br>・対象モジュール（FI,
                                    CO, SD, MM, PS, PP）いずれかでの要件定義経験<br><br>【商流】<br>貴社社員または直個人事業主<br><br>【年齢】<br>60歳未満<br><br>【国籍】<br>外国籍不可<br>◆単価:
                                    120万円 〜 150万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 6人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年04月01日 〜 2027年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125332">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "食品業様向け SAP S/4 HANA 新規導入（計画フェーズ）案件",
                            "value": 125332
                        },
                        "datePosted": "2025/02/06 13:39:41",
                        "validThrough": "2025/02/28 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 150,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125332/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">食品業様向け SAP S/4 HANA
                                    新規導入（計画フェーズ）案件
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2025/07/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【顧客　　】食品業
                            【作業内容】計画フェーズ（現状調査、新業務検討、標準化整理、S/4標準説明）をご対応頂ける方
                            【作業場所】大手町、客先（都内）、リモート可（割合は応相談）
                            【モジュール】FI, CO, SD, MM, PP, PS, QM
                        </p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/27</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 150万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125332" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    158816783121<br>◆案件名: 食品業様向け SAP S/4 HANA 新規導入（計画フェーズ）案件<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>【顧客　　】食品業<br>【作業内容】計画フェーズ（現状調査、新業務検討、標準化整理、S/4標準説明）をご対応頂ける方<br>【作業場所】大手町、客先（都内）、リモート可（割合は応相談）<br>【モジュール】FI,
                                    CO, SD, MM, PP, PS, QM<br><br>◆人財要件: <br>【募集スキル】<br>①（対象モジュールのいずれかで）
                                    要件定義などのERP上流フェーズのコンサル経験<br>② SAP カスタマイズ設定、システム要件定義、Fi＆Gap検討などの経験<br><br>【商流】<br>貴社社員または直フリーランスまで<br><br>【年齢】<br>～６０歳まで<br><br>【外国籍】<br>不可<br><br><br>◆単価:
                                    120万円 〜 150万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 6人<br>◆面談回数: 2回<br>◆契約期間:
                                    2025年04月01日 〜 2025年07月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125331">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【急募/元請け直】ヘルスケア企業でのPMサポート",
                            "value": 125331
                        },
                        "datePosted": "2025/02/06 13:36:59",
                        "validThrough": "2025/03/08 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 120,
                                "maxValue": 140,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125331/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【急募/元請け直】ヘルスケア企業でのPMサポート
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/04/01 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■案件名：ヘルスケア企業でのPMサポート
                            ■案件概要：ヘルスケア企業内でPJが先行して走っており、エンドクライアントの社員PMのサポートを実施想定。
                            ■業務内容
                            ・PMのサポート全般
                            ・PJ推進のためのドキュメンテーション、ファシリテーション
                            ・関係部門横断での各種調整
                            ■時期：2025年4月～
                            ■稼働率：100％
                            ■働き方：ハイ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/7</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">120万円 〜 140万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125331" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    585816615287<br>◆案件名: 【急募/元請け直】ヘルスケア企業でのPMサポート<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>■案件名：ヘルスケア企業でのPMサポート<br>■案件概要：ヘルスケア企業内でPJが先行して走っており、エンドクライアントの社員PMのサポートを実施想定。<br>■業務内容<br>・PMのサポート全般<br>・PJ推進のためのドキュメンテーション、ファシリテーション<br>・関係部門横断での各種調整<br>■時期：2025年4月～<br>■稼働率：100％<br>■働き方：ハイブリッド（オンサイト：渋谷）※週2～3出社※<br>■単価：～140万（※スキル見合いで多少の上振れ可）<br>■面談：2回想定<br>■商流：元請け→弊社<br>■商流制限：貴社社員、貴社1社先社員、貴社直フリーランスを優先<br>◆人財要件:
                                    <br>■人財要件<br>□Must<br>・コンサルファーム出身で最終職位がマネージャー相当<br>・能動的、且つ自立的に動ける方<br>・業務システムの知見あり<br>・コミュニケーション能力が高い方<br>□Nice
                                    to have<br>・業務システムの中でも特に小売り業知見がある方<br>◆単価: 120万円 〜
                                    140万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜
                                    2日出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    1〜2回<br>◆契約期間: 2025年04月01日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125319">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "S/4HANAロールアウト導入支援プロジェクト",
                            "value": 125319
                        },
                        "datePosted": "2025/02/06 10:51:25",
                        "validThrough": "2025/02/28 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 200,
                                "maxValue": 220,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125319/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    S/4HANAロールアウト導入支援プロジェクト
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/15 〜 2025/06/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【お客様】製造業
                            【概要】S/4HANAロールアウト導入支援プロジェクト
                            【勤務地】週２，３田町　リモート
                            【導入モジュール】会計(FI)
                            【参加フェーズ】要件定義/実現化～
                            　SAP　FIチームリーダー
                            【参画時期】即日
                            【募集人数】1名</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/27</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">200万円 〜 220万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125319" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    804806684536<br>◆案件名: S/4HANAロールアウト導入支援プロジェクト<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>【お客様】製造業<br>【概要】S/4HANAロールアウト導入支援プロジェクト<br>【勤務地】週２，３田町　リモート<br>【導入モジュール】会計(FI)<br>【参加フェーズ】要件定義/実現化～<br>　SAP　FIチームリーダー<br>【参画時期】即日<br>【募集人数】1名<br>◆人財要件:
                                    <br>SAP　FI導入経験<br>SAP導入リーダー経験<br>英語ビジネスレベル<br>◆単価: 200万円
                                    〜 220万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 2回<br>◆契約期間:
                                    2025年02月15日 〜 2025年06月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先）
                                    / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125317">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【基本リモート/ 地方在住も相談OK】製造（元請支援）_SAPプロジェクト販売チームの移行PMO募集_008024",
                            "value": 125317
                        },
                        "datePosted": "2025/02/06 10:50:48",
                        "validThrough": "2025/03/08 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": []
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 145,
                                "maxValue": 160,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125317/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【基本リモート/
                                    地方在住も相談OK】製造（元請支援）_SAPプロジェクト販売チームの移行PMO募集_008024
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月06日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/05/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">
                            ・移行統括チームと販売チーム（7つの販売サブチームあり）の間に立って、移行トライアル/本番移行の作業計画策定、進捗管理、課題管理を支援
                            ・販売チームの各移行担当者と移行統括チームメンバーとの橋渡し的役割として、支援を行う
                            【場所】
                            基本はリモート
                            詳細な場所は面談時に元請から共有予定。
                            【商流】
                            元請＞弊社
                            【精算条件】
                            精算条件...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/7</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">145万円 〜 160万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125317" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    554806649870<br>◆案件名: 【基本リモート/
                                    地方在住も相談OK】製造（元請支援）_SAPプロジェクト販売チームの移行PMO募集_008024<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>・移行統括チームと販売チーム（7つの販売サブチームあり）の間に立って、移行トライアル/本番移行の作業計画策定、進捗管理、課題管理を支援<br>・販売チームの各移行担当者と移行統括チームメンバーとの橋渡し的役割として、支援を行う<br>【場所】<br>基本はリモート<br>詳細な場所は面談時に元請から共有予定。<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ：<br>稼働日数 ： 週5日<br>【備考】<br>勤務時間 ： 9:00～18:00<br>年齢制限 ： 55歳まで<br>商流制限
                                    ： 貴社所属の方を優先致します<br>国籍制限 ： 日本籍の方を優先させていただきます<br>服装指定
                                    ： スーツ or ビジネスカジュアル<br>リモート対応 ： 在宅業務での環境は貴社 or
                                    稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・サブチームリードができるレベル、担当者として自走できクライアントをリードできる<br>　‐SAPの販売管理、購買管理に関わるマスタの知識<br>　‐SAP導入プロジェクトにおける、データ移行の経験者<br>・複数のステークホルダーとのコミュニケーション力、調整力<br>【尚可要件】<br>・製造業の販売、購買業務<br>◆単価:
                                    145万円 〜 160万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: フルリモート<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1回<br>◆契約期間: 2025年03月01日 〜 2025年05月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） /
                                    フリーランス（一社先） / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125306">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "自動車会社向けのSAP S/4HANA移行プロジェクト",
                            "value": 125306
                        },
                        "datePosted": "2025/02/05 18:04:51",
                        "validThrough": "2025/03/07 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 170,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125306/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">自動車会社向けのSAP
                                    S/4HANA移行プロジェクト
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月05日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月05日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/05 〜 2025/08/31</div>
                            <span data-v-1c51a755=""
                                  class="badge-pill font-small ml-md-2 badge-pill font-small badge-blue">元請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">【ポジション】SAP COコンサル
                            【業務内容】
                            - 原価センタ計画実績関連レポートに関する以下資料の作成およびセッション実施
                            - アドオン要件定義書
                            - 移行計画書
                            - テスト計画書、テスト仕様書
                            - 教育計画書
                            - 基本設計書レビュー、アドオン受入
                            - 結合テストシナリオ作成、実施

                            【働き方】基本リモート、場合により六本...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/6</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 170万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日出社 / 週1日未満出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125306" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    783746299202<br>◆案件名: 自動車会社向けのSAP S/4HANA移行プロジェクト<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 元請<br>◆案件内容: <br>【ポジション】SAP COコンサル<br>【業務内容】<br>-
                                    原価センタ計画実績関連レポートに関する以下資料の作成およびセッション実施<br>-
                                    アドオン要件定義書<br>- 移行計画書<br>- テスト計画書、テスト仕様書<br>-
                                    教育計画書<br>- 基本設計書レビュー、アドオン受入<br>- 結合テストシナリオ作成、実施<br><br>【働き方】基本リモート、場合により六本木出社<br>【稼働率】100％<br>【期間】3月　〜　8月末（継続可能性あり）<br><br><br><br>◆人財要件:
                                    <br>【必須スキル】<br>・クエリ、レポートペインタの知見<br>・顧客との要件定義セッション経験<br>・基本設計レビュー、受入のご経験<br>・結合テストシナリオ作成、実施のご経験<br>・移行/テスト/権限/教育計画書策定のご経験<br><br>【あれば良いスキル】<br>・償却シミュレーション関連の知見<br>・CO計画関連のカスタマイズ知見<br><br>【言語】<br>・日本語スキル：ネイティブ<br>・英語スキル：不要<br>◆単価:
                                    100万円 〜 170万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日出社
                                    / 週1日未満出社<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 1〜2回<br>◆契約期間: 2025年02月05日 〜 2025年08月31日<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125260">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【弊社独占/超急募/面談1回のみ】SAPユーザートレーニング推進支援",
                            "value": 125260
                        },
                        "datePosted": "2025/02/04 19:30:33",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 120,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125260/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">
                                    【弊社独占/超急募/面談1回のみ】SAPユーザートレーニング推進支援
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月10日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/11 〜 2025/05/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■案件名
                            SAPのユーザートレーニング推進支援

                            ■エンドクライアント
                            外資系スポーツウェア

                            ■元請け企業
                            ブティック系総合コンサルティングファーム

                            ■案件概要
                            元請けは、全社的なチェンジマネジメントで既に参画しており、チェンジマネジメントの一環でユーザートレーニングがあります。
                            本案件に参画していた元請け人財がリプレイ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 120万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">60 〜 79%</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週4 〜 2日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755=""
                                    class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125260" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    034665039554<br>◆案件名: 【弊社独占/超急募/面談1回のみ】SAPユーザートレーニング推進支援<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>■案件名<br>SAPのユーザートレーニング推進支援<br><br>■エンドクライアント<br>外資系スポーツウェア<br><br>■元請け企業<br>ブティック系総合コンサルティングファーム<br><br>■案件概要<br>元請けは、全社的なチェンジマネジメントで既に参画しており、チェンジマネジメントの一環でユーザートレーニングがあります。<br>本案件に参画していた元請け人財がリプレイスすることとなったため、そこの推進支援ができるリプレイス人財を募集。<br>参画開始～4月中までに、ユーザートレーニング計画とトレーニングマニュアルを作成する。<br>その後、5月にユーザートレーニングを実施する。<br><br>■業務内容<br>・トレーニングマニュアルの作成<br>・トレーニングの全体計画作成<br>・ユーザートレーニングの実施<br>※上記進める中でエンドクライアントからの相談があれば、適時相談にも乗る※<br><br>■時期<br>ASAP～5月末<br>※PJの状況によっては、6月末までになる可能性あり※<br><br>■稼働率<br>70％<br><br>■単価<br>～120万（100%稼働時）<br>※スキル見合い※<br><br>■働き方<br>ハイブリッド（オンサイト：新宿）<br>※週2～3出社※<br><br>■商流<br>元請け→弊社<br><br>■面談回数<br>1回（元請け代表と面談）<br><br>■その他補足<br>・外国籍も可（ただし、日本語が流暢で英語の読み書きができる方）※マニュアルは日本語で作成※<br>◆人財要件:
                                    <br>■人財要件<br>□Must<br>・SAPの知識や経験がある方<br>・計画策定やマニュアル作成が出来る方<br>・ユーザートレーニングの経験がある方<br><br>□Nice
                                    to have<br>・コンサルティングファーム出身またはコンサルティングファーム案件での稼働実績のある方<br>・周囲と関係性を築き、能動的に推進できる方<br><br>◆単価:
                                    100万円 〜 120万円 / 月 ※スキル見合い<br>◆稼働率: 60 〜 79%<br>◆出社頻度: 週4 〜 2日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 1回<br>◆契約期間:
                                    2025年02月11日 〜 2025年05月31日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125204">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "SAPロールアウト(EMEA)PMO支援",
                            "value": 125204
                        },
                        "datePosted": "2025/02/04 17:53:28",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都 ",
                                    "addressCountry": "JP"
                                },
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": " 静岡県 ",
                                    "addressCountry": "JP"
                                },
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": " 海外",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 170,
                                "maxValue": 200,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125204/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">SAPロールアウト(EMEA)PMO支援</div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/24 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">[作業内容]
                            ・大手SIerグループ企業のPJ体制に入り、海外拠点（インド）含めた自動車メーカー企業向けのSAPロールアウトプロジェクトのPMO支援を行う。
                            （開発部隊が海外※インド等）

                            [補足事項]
                            ・インドへの出張可能性あり</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">170万円 〜 200万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都 / 静岡県 / 海外</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125204" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    852631881955<br>◆案件名: SAPロールアウト(EMEA)PMO支援<br>◆案件への関わり: 商流に入る<br>◆案件の商流:
                                    二次請以降<br>◆案件内容: <br>[作業内容]<br>・大手SIerグループ企業のPJ体制に入り、海外拠点（インド）含めた自動車メーカー企業向けのSAPロールアウトプロジェクトのPMO支援を行う。<br>（開発部隊が海外※インド等）<br><br>[補足事項]<br>・インドへの出張可能性あり<br>◆人財要件:
                                    <br>[必須スキル]<br>・SAP導入プロジェクトでのPMO経験（グローバルロールアウト※EMEA経験）<br>・SAPの知見（PP,
                                    QM, PM, FI, CO）<br>・システム導入プロジェクトのチェンジマネジメント経験<br>・プロジェクトマネジメントスキル（課題管理・進捗管理・各種報告）<br>・1人称で業務推進が可能<br>・海外法人との英語MTGでの討議経験（上級ビジネス英語必要※会話・読み書き）<br>・日本国籍の方のみ<br><br>[尚可スキル]<br>・製造業（自動車）向けのPJ経験・知見<br>◆単価:
                                    170万円 〜 200万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社<br>◆就業場所:
                                    東京都 / 静岡県 / 海外<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年02月24日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125213">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【フルリモート】SAP S/4HANA  クラウド導入支援 / 3月～ / ～160万",
                            "value": 125213
                        },
                        "datePosted": "2025/02/04 11:31:22",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": []
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 150,
                                "maxValue": 160,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125213/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【フルリモート】SAP S/4HANA クラウド導入支援
                                    / 3月～ / ～160万
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/03/01 〜 2025/05/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">二次請以降</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■案件名
                            SAP S/4HANA クラウド導入支援

                            ■期間
                            3月～

                            ■勤務形態
                            フルリモート

                            ■商流制限
                            貴社社員・個人事業主または貴社１社先社員まで

                            ■年齢制限
                            ～50代後半

                            ■外国籍
                            不可

                            ■稼働率
                            100％

                            ■単価
                            ～160万（スキル見合い）
                            ※上振れ検討可

                            ■精算有無
                            ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">150万円 〜 160万円 / 月 ※応相談</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125213" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    328636282103<br>◆案件名: 【フルリモート】SAP S/4HANA クラウド導入支援 / 3月～ /
                                    ～160万<br>◆案件への関わり: 商流に入る<br>◆案件の商流: 二次請以降<br>◆案件内容: <br>■案件名<br>SAP
                                    S/4HANA
                                    クラウド導入支援<br><br>■期間<br>3月～<br><br>■勤務形態<br>フルリモート<br><br>■商流制限<br>貴社社員・個人事業主または貴社１社先社員まで<br><br>■年齢制限<br>～50代後半<br><br>■外国籍<br>不可<br><br>■稼働率<br>100％<br><br>■単価<br>～160万（スキル見合い）<br>※上振れ検討可<br><br>■精算有無<br>固定<br><br>■支払いサイト<br>月末締め翌々月5日（35日）<br><br>■面談と方法<br>2回（Web）<br><br>■募集人数<br>1名<br><br>■勤務時間<br>9:00～18:00想定<br><br>■案件内容<br>某大手SIer企業の社内システム開発案件として、<br>クラウド上にSAP
                                    S/4HANA for Professional Servicesの導入に向けて<br>開発を進める予定です。<br><br>2025年1月まで設計フェーズを対応しておりましたが、<br>実装フェーズが近づいてきた為、作業者
                                    兼 アドバイザーとして<br>ご支援いただける方を募集いたします。<br>◆人財要件: <br>■必須スキル<br>下記すべて必須経験となります。各項目へスキルコメントをお願いいたします。<br>・SAP
                                    S/4HANA for Professional Services導入コンサルのご経験<br>　またはPSモジュールが得意でS/4HANAの経験豊富な方<br>・クラウドのSAPに抵抗のない方<br><br>■尚可スキル<br>※各項目へスキルコメントをお願いいたします。<br>・S/4HANAのPublicCloudEdtion経験<br>◆単価:
                                    150万円 〜 160万円 / 月 ※スキル見合い<br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: フルリモート<br>◆契約形態:業務委託（準委任）<br>◆募集人数:
                                    1人<br>◆面談回数: 2回<br>◆契約期間: 2025年03月01日 〜 2025年05月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125206">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【原則リモート/ グローバル企業】製薬_ビジネスレポーティング領域支援_007956",
                            "value": 125206
                        },
                        "datePosted": "2025/02/04 10:47:01",
                        "validThrough": "2025/03/15 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 165,
                                "maxValue": 175,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125206/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【原則リモート/
                                    グローバル企業】製薬_ビジネスレポーティング領域支援_007956
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月13日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/13 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">エンドクライアントの親会社で使用中のSAP
                            S4と標準化された業務プロセスをグローバルテンプレートとして、エンドクライアントの国内/海外の子社へロールインを行うプロジェクトが実行中。
                            ----------------------------
                            本ポジションは、レポーティング領域全般のファシリテーション（タスク管理、遂行支援、ドキュメンテーション...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/14</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">165万円 〜 175万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / フリーランス（本人）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125206" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    376633625809<br>◆案件名: 【原則リモート/ グローバル企業】製薬_ビジネスレポーティング領域支援_007956<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>エンドクライアントの親会社で使用中のSAP
                                    S4と標準化された業務プロセスをグローバルテンプレートとして、エンドクライアントの国内/海外の子社へロールインを行うプロジェクトが実行中。<br>----------------------------<br>本ポジションは、レポーティング領域全般のファシリテーション（タスク管理、遂行支援、ドキュメンテーション
                                    etc.）の支援をお願いします。事業会社（エンドクライアント）のIT部門支援の立ち位置となります。（システムベンダーは別に存在）<br>・レポーティング領域におけるAsIs→ToBeをグローバルメンバとディスカッション<br>・決定事項をドキュメントとして取りまとめ、国内メンバに展開、必要なQA対応を実施。<br>といった作業を想定しています。<br>以下は想定となるが、下記のような作業を過去に行ったことがある方をイメージしている。<br>1：事業会社における商品の製造～会計（売上・販売）に関するデータ（SAPトランザクション・マスタデータなど）を活用し、ビジネスレポーティングを実施。<br>2：AsIs→Tobeにおける上記レポーティング業務と基づくデーター群の取り扱い。<br>システム開発が必要な場合には、その改修の要件定義と以降のフェーズの対応<br>【場所】<br>原則はリモート多めのハイブリッド/
                                    ただし、クライアント要望に応じたオンサイトに対応していただける方を優先<br>海外メンバの来日による変動の可能性もあり・詳細な場所は面談時に元請から共有予定。<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ： 当月末締め翌月末日<br>稼働日数 ： 週5日<br>【備考】<br>面談回数 ： 2回( オンライン
                                    ）<br>勤務時間 ： 9:00～18:00<br>年齢制限 ： 44歳まで<br>商流制限 ：
                                    あり。弊社からの再委託まで。（貴社プロパまで）<br>フリーランスの方は応相談<br>国籍制限 ：
                                    日本籍の方を優先させていただきます<br>服装指定 ： スーツ or ビジネスカジュアル<br>リモート対応
                                    ： 在宅業務での環境は貴社 or 稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・一般的なプロジェクト管理スキル（タスク整理、進捗・課題管理、ドキュメンテーション）<br>・SAP導入プロジェクトにコンサルタントとして参画し、導入作業を実施した経験＋それに伴うSAP知識<br>　‐コンサルタントとしての実務レベルでSAPの内容が理解しているレベルが必要<br>・英語：ビジネスレベル<br>　‐会議に出席し、トークテーマ・内容が理解できる。資料の読み書き（※スピーキングは尚可）<br>・100％稼働可能な方（協働するコンサル（元請）がパーシャルでのアサインのため）<br>◆単価:
                                    165万円 〜 175万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日未満出社 /
                                    フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    2回<br>◆契約期間: 2025年02月13日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / フリーランス（本人）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125170">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "【基本リモート/ TMS】金融_人事領域ITコンサル募集_008020",
                            "value": 125170
                        },
                        "datePosted": "2025/02/03 15:02:34",
                        "validThrough": "2025/03/06 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 120,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125170/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">【基本リモート/
                                    TMS】金融_人事領域ITコンサル募集_008020
                                </div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月03日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/04 〜 2025/04/30
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">Company導入、運用、業務設計に関してエンド（金融系事業会社）支援にあたっていただきます。
                            【場所】
                            基本、稼働者様自宅にてリモート
                            詳細な場所は面談時に共有予定。
                            【商流】
                            元請＞弊社
                            【精算条件】
                            精算条件：固定
                            支払サイト ： 当月末締め翌々月5日
                            稼働日数 ： 週5日
                            【備考】
                            勤務時間 ： 9:00～18:00...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/3/5</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">非公開 <!--v-if--></span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 120万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週5日出社</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先） / フリーランス（二社先以降）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125170" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    327562553825<br>◆案件名: 【基本リモート/ TMS】金融_人事領域ITコンサル募集_008020<br>◆案件への関わり:
                                    商流に入る<br>◆案件の商流: 一次請<br>◆案件内容: <br>Company導入、運用、業務設計に関してエンド（金融系事業会社）支援にあたっていただきます。<br>【場所】<br>基本、稼働者様自宅にてリモート<br>詳細な場所は面談時に共有予定。<br>【商流】<br>元請＞弊社<br>【精算条件】<br>精算条件：固定<br>支払サイト
                                    ： 当月末締め翌々月5日<br>稼働日数 ： 週5日<br>【備考】<br>勤務時間 ： 9:00～18:00<br>年齢制限
                                    ： 48歳まで<br>商流制限 ： 貴社所属の方を優先致します<br>国籍制限 ： 日本籍の方を優先させていただきます<br>服装指定
                                    ： スーツ or ビジネスカジュアル<br>リモート対応 ： 在宅業務での環境は貴社 or
                                    稼働者様にてご準備ください。<br>◆人財要件: <br>【必須要件】<br>・対象システムの機能群についての知見<br>・対象システムのプロジェクト経験（3年～以上の方を優先）<br>◆単価:
                                    100万円 〜 120万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週5日出社<br>◆就業場所:
                                    東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数: 2回<br>◆契約期間:
                                    2025年02月04日 〜 2025年04月30日 ※継続の可能性あり<br>◆募集対象: 自社社員 /
                                    協力会社社員（一社先） / 協力会社社員（二社先以降） / フリーランス（本人） / フリーランス（一社先）
                                    / フリーランス（二社先以降）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-1c51a755="" id="jsonld-125142">
                    <script type="application/ld+json">{
                        "@context": "http://schema.org/",
                        "@type": "JobPosting",
                        "hiringOrganization": {
                            "@type": "Organization",
                            "name": "assign navi(Mi52)",
                            "logo": "https://assign-navi.jp/assets/img/common/h_logo.svg"
                        },
                        "identifier": {
                            "@type": "PropertyValue",
                            "name": "SAP_FIコンサルタント募集",
                            "value": 125142
                        },
                        "datePosted": "2025/02/03 05:11:51",
                        "validThrough": "2025/02/14 00:00:00",
                        "jobLocation": {
                            "@type": "Place",
                            "address": [
                                {
                                    "@type": "PostalAddress",
                                    "addressRegion": "東京都",
                                    "addressCountry": "JP"
                                }
                            ]
                        },
                        "baseSalary": {
                            "@type": "MonetaryAmount",
                            "currency": "JPY",
                            "value": {
                                "@type": "QuantitativeValue",
                                "minValue": 100,
                                "maxValue": 200,
                                "unitText": "MONTH"
                            }
                        }
                    }</script>
                </div>
                <div data-v-1c51a755="" class="col-12"><a data-v-1c51a755="" class="card mb-4 w-100 hoverable d-block"
                                                          href="/opportunities/125142/detail?prev_next_display=display">
                    <!-- PC時 card-header -->
                    <div data-v-1c51a755=""
                         class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start" style="">
                        <div data-v-1c51a755=""><!--v-if--></div><!--v-if-->
                        <h5 data-v-1c51a755="" class="mb-0">
                            <div data-v-1c51a755=""
                                 class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                <div data-v-1c51a755="" class="default-main-color">SAP_FIコンサルタント募集</div>
                            </div>
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 font-small custom-grey-6-text"><span
                                    data-v-1c51a755="">更新</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月04日</span></div>
                            <div data-v-1c51a755="" class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                <span data-v-1c51a755="">登録</span><span data-v-1c51a755="" class="px-1">:</span><span
                                    data-v-1c51a755="">2025年02月03日</span></div>
                        </h5><!--v-if--></div>
                    <div data-v-1c51a755="" class="pt-3 pb-4 px-3 px-md-4">
                        <div data-v-1c51a755="" class="mb-2">
                            <div data-v-1c51a755="" class="d-md-inline-block mt-1 ex-bold">2025/02/03 〜 2025/03/31
                                ※継続の可能性あり
                            </div>
                            <span data-v-1c51a755="" class="badge-pill font-small ml-md-2 badge-pill font-small grey">一次請</span>
                            <!--v-if-->
                            <div data-v-1c51a755="" class="d-md-inline-block ml-2 mt-2 mt-md-0"><!--v-if--></div>
                        </div>
                        <p data-v-1c51a755="" class="mt-1">■案件概要：
                            SAPにレポートペインタという機能があり、要件の取りまとめと受け入れテストができるFIコンサルタントを探しております。
                            主に会計領域で使用されるレポート類(財務諸表や管理会計レポート)を作成する仕組みで、ベトナム国籍の方が開発し、コンサルタントとして要件と受け入れ支援を行います。
                            ■業務内容：
                            ・要件の取りまとめ
                            ・受入テ...</p>
                        <div data-v-1c51a755="" class="row mb-2">
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label><span
                                    data-v-1c51a755="">2025/2/13</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                <!-- 会社名の表示条件 --><span data-v-1c51a755="">ログイン後に表示</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 mb-2"><label data-v-1c51a755=""
                                                                                        class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label><span
                                    data-v-1c51a755="">100万円 〜 200万円 / 月 </span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label><span
                                    data-v-1c51a755="" class="">100%（フル稼働）</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label><span
                                    data-v-1c51a755="" class="">週1日未満出社 / フルリモート</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label><span
                                    data-v-1c51a755="">東京都</span></div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex align-items-start mb-2"><label
                                    data-v-1c51a755=""
                                    class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">募集対象</label><span
                                    data-v-1c51a755="" class="">自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）</span>
                            </div>
                            <div data-v-1c51a755="" class="col-12 col-lg-6 d-flex mb-2"><label data-v-1c51a755=""
                                                                                               class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">契約形態</label><span
                                    data-v-1c51a755="">業務委託（準委任）</span></div><!--v-if--></div><!--v-if-->
                        <!--v-if--></div>
                </a></div>
                <div aria-hidden="true" aria-labelledby="display_text_format_modal" class="modal"
                     id="display_text_format_125142" role="dialog" tabindex="-1">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header"><h4 class="modal-title w-100">案件詳細</h4>
                                <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span
                                        aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-4">**********************************************************<br>◆案件ID:
                                    447527110181<br>◆案件名: SAP_FIコンサルタント募集<br>◆案件への関わり: 仲介のみ<br>◆案件の商流:
                                    一次請（仲介案件）<br>◆案件内容: <br>■案件概要：<br>SAPにレポートペインタという機能があり、要件の取りまとめと受け入れテストができるFIコンサルタントを探しております。<br>主に会計領域で使用されるレポート類(財務諸表や管理会計レポート)を作成する仕組みで、ベトナム国籍の方が開発し、コンサルタントとして要件と受け入れ支援を行います。<br>■業務内容：<br>・要件の取りまとめ<br>・受入テストの対応<br>■稼働率：100%<br>■募集：2名<br>■働き方：基本テレワーク<br>　　　　　全国各地からでも参画可能。<br>　　　　　出張の場合は実費精算有り。<br>■単価：ご提案ください。<br>■開始時期：ASAP<br>■商流：エンド→プライム→上位→弊社
                                    <br>■商流制限：なし<br>■年齢制限：なし<br>■国籍：日本のみ<br>■面談回数：1回<br>◆人財要件:
                                    <br>■必須スキル：<br>・SAP　S/4HANA経験者でレポートペインターやクエリーの経験者で、要件定義や受入作業ができる方<br>※レポートペインター：主に会計領域で使用するレポート類（財務諸表や管理会計レポート）を作成するツール<br><br>◆単価:
                                    100万円 〜 200万円 / 月 <br>◆稼働率: 100%（フル稼働）<br>◆出社頻度: 週1日未満出社 /
                                    フルリモート<br>◆就業場所: 東京都<br>◆契約形態:業務委託（準委任）<br>◆募集人数: 1人<br>◆面談回数:
                                    1回<br>◆契約期間: 2025年02月03日 〜 2025年03月31日 ※継続の可能性あり<br>◆募集対象:
                                    自社社員 / 協力会社社員（一社先） / フリーランス（本人） / フリーランス（一社先）<br>**********************************************************
                                </div>
                                <div class="text-center"><a aria-label="Close"
                                                            class="btn btn-blue-grey waves-effect waves-light"
                                                            data-dismiss="modal">閉じる</a></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <nav aria-label="pagination example">
                <nav role="navigation" class="pagination">
                    <ul class="pagination">
                        <li class="prev previous_page disabled page-item"><a class="page-link waves-effect" href="#">←
                            前</a></li>
                        <li class="active page-item"><a class="page-link waves-effect"
                                                        href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_erp&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=1">1</a>
                        </li>
                        <li class="page-item"><a rel="next" class="page-link waves-effect"
                                                 href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_erp&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=2">2</a>
                        </li>
                        <li class="page-item"><a class="page-link waves-effect"
                                                 href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_erp&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=3">3</a>
                        </li>
                        <li class="page-item"><a class="page-link waves-effect"
                                                 href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_erp&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=4">4</a>
                        </li>
                        <li class="next next_page page-item"><a class="page-link waves-effect" rel="next"
                                                                href="/opportunity_search_conditions/search?opportunity_search_condition%5Baccordion_open_consul%5D=yes&amp;opportunity_search_condition%5Bopp_categories%5D%5B%5D=consul_erp&amp;opportunity_search_condition%5Bswitch_type%5D=button&amp;page=2">次
                            →</a></li>
                    </ul>
                </nav>
            </nav>
        </div>
    </div>
</div>
<div style="margin-top: 79px;">
<link rel="stylesheet" href="/custom_frontend/static/css/ERP1.css"/>
    `,
    data() {
        return {
        }
    }
}

export default ERP