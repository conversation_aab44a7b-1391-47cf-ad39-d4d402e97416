/* Tab navigation styles for data management pages */
.tab-container {
    display: flex;
    width: 100%;
    margin: 48px auto 0;
    overflow: hidden;
    gap: 0.5%;
    justify-content: center;

}

.tab-button {
    height: 5vh;
    width: 15.8%;
    border: 2px solid #4472C4;
    border-top-left-radius: 0.4rem;
    border-top-right-radius: 0.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2%;
    cursor: pointer;
    margin-top: 9px;
}

.tab-button.active {
    background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    color: white;
    border-color: #4472C4 !important;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .tab-container {
        width: 100%;
        gap: 2% !important;
    }
    
    .tab-button {
        padding: 10px 5px;
        font-size: 14px;
    }
}
