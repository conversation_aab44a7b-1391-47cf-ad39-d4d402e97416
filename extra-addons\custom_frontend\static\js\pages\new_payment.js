import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const NewPayment = {
    'template': `
        <main class="pb-3 margin-header" id="vue-app">
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '登録・管理', link: null },
                { text: '会社データ管理', link: null },
                { text: 'プラン変更', link: null, current: true }
            ])}
            <div class="container-fluid">
                <div class="d-flex justify-content-end align-items-center">
                    <button class="mobile-menu-btn d-md-none" @click="toggleMobileMenu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
                <div class="mobile-menu" :class="{ 'active': isMobileMenuOpen }">
                    <div class="mobile-menu-content">
                        <button class="mobile-menu-close" @click="closeMobileMenu">
                            <span></span>
                        </button>
                        <ul>
                            <li style="font-size: 24px;font-weight: bold;">会社データ管理</li>
                            <li><a href="/companies/manage/edit">会社データ</a></li>
                            <li><a href="/users/edit">プロフィール</a></li>
                            <li><a href="/users/profile/edit_email">メールアドレス</a></li>
                            <li><a href="/users/profile/edit_password">パスワード</a></li>
                            <li><a href="/setting_gmail">メール受信設定</a></li>
                            <li><a class="active" href="/mypage/plan">プラン</a></li>
                            <li><a href="/plan/plant_out">退会</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="container-fluid grabient pt-5 position-relative">
                <div class="row mb-4 mb-md-0">
                    <div class="d-none d-md-block col-md-4 col-lg-3 side-menu-contents">
                        <div class="card px-3 pb-3 side-card collapsible">
                            <ul class="collapsible mb-0">
                                <div class="d-none d-md-block font-large border-bottom mb-3 py-3"><span class="pl-3 custom-grey-5-text">会社データ管理</span></div>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/companies/manage/edit"><span class="pl-3 font-middle">会社データ</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/edit"><span class="pl-3 font-middle">プロフィール</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_email"><span class="pl-3 font-middle">メールアドレス</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/users/profile/edit_password"><span class="pl-3 font-middle">パスワード</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/setting_gmail"><span class="pl-3 font-middle">メール受信設定</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3 active" aria-current="page" href="/mypage/plan"><span class="pl-3 font-middle">プラン</span></a></li>
                                <li class="my-md-1"><a class="d-block py-1 px-3" href="/plan/plant_out"><span class="pl-3 font-middle">退会</span></a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-12 col-md-8 col-lg-9 mb-4 mb-md-0">
                        <div class="text-center mb-5">
                            <h3 class="mb-4">変更するプランを選択してください。</h3>
                        </div>
                        <div class="row justify-content-center">
                            <div class="col-12 col-md-8 col-lg-6">
                                <form class="new_credit_payment" id="credit_payments_regist_cc_form" novalidate="novalidate" autocomplete="off" action="/mypage/plan" accept-charset="UTF-8" method="post">
                                    <input name="utf8" type="hidden" value="✓" autocomplete="off" />
                                    <input type="hidden" name="authenticity_token" value="0Mt5kUDki+3cMCHw1CQbmddn1bJnH33cSjNvTVzUlUE49C1FJK++b/dFVmWz3M03+jZUaMXdHOJjjsNP8dTe1Q==" autocomplete="off" />
                                    <input value="72327" class="form-control" autocomplete="off" type="hidden" name="credit_payment[merchant_id]" id="credit_payment_merchant_id" />
                                    <input value="001" class="form-control" autocomplete="off" type="hidden" name="credit_payment[service_id]" id="credit_payment_service_id" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[token]" id="credit_payment_token" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[token_key]" id="credit_payment_token_key" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[masked_cc_number]" id="credit_payment_masked_cc_number" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[card_brand_code]" id="credit_payment_card_brand_code" />
                                    <input value="" class="form-control" autocomplete="off" type="hidden" name="credit_payment[cc_expiration]" id="credit_payment_cc_expiration" />
                                    <div class="card px-3 px-md-4 form-card mt-2 pt-5">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mx-auto mb-5">
                                                    <label class="font-middle mb-3" for="">プラン <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                                            </label>
                                                    <div v-dropdown="{modelValue: selectedPlan, listType: 'planOptions'}" @selected="selectPlan">
                                                        <input type="hidden" v-model="selectedPlan" name="credit_payment[plan]" />
                                                    </div>
                                                </div>
                                                <div class="mb-3 d-flex align-items-center">
                                                    <span class="card-icon">
                                                        <img src="/custom_frontend/static/img/cards/visa.png" alt="VISA" />
                                                    </span>
                                                    <span class="card-icon ml-2">
                                                        <img src="/custom_frontend/static/img/cards/mastercard.png" alt="MasterCard" />
                                                    </span>
                                                    <span class="card-icon ml-2">
                                                        <img src="/custom_frontend/static/img/cards/jcb.png" alt="JCB" />
                                                    </span>
                                                    <span class="card-icon ml-2">
                                                        <img src="/custom_frontend/static/img/cards/amex.png" alt="American Express" />
                                                    </span>
                                                    <a href="https://www.cardservice.co.jp/service/creditcard/card.html" target="_blank" class="ml-3 card-info-link">
                                                        対応カードの詳細を確認する。
                                                    </a>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3" for="">カード番号 <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                                            </label>
                                                            <input class="form-control" autocomplete="off" id="credit_card_number_field" type="text" name="credit_payment[credit_card_number]" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3" for="">カード名義人 <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                                            </label>
                                                            <input class="form-control" autocomplete="off" id="card_holder_name_field" type="text" name="credit_payment[card_holder_name]" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <label class="font-middle mb-3" for="credit_card_expire_year">カード有効期限 <span class="badge-pill badge-danger pink lighten-2 font-small ml-3">必須</span>
                                                </label>
                                                <div class="row">
                                                    <div class="col-6">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3 custom-grey-text active" for="">月</label>
                                                            <input maxlength="2" class="form-control" autocomplete="off" id="credit_card_expire_month_field" size="2" type="text" name="credit_payment[credit_card_expire_month]" />
                                                        </div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3 custom-grey-text active" for="">年</label>
                                                            <input maxlength="2" class="form-control" autocomplete="off" id="credit_card_expire_year_field" size="2" type="text" name="credit_payment[credit_card_expire_year]" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="mx-auto mb-5">
                                                            <label class="font-middle mb-3 active" for="">セキュリティコード <span class="badge-pill badge-danger pink lighten-2 font-small ml-2 d-inline-block">必須</span>
                                                            </label>
                                                            <input class="form-control" autocomplete="off" id="security_code_field" type="text" name="credit_payment[security_code]" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="mx-auto mb-5">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" class="custom-control-input" id="auto_renew" name="credit_payment[auto_renew]">
                                                                <label class="custom-control-label font-middle" for="auto_renew">自動更新する</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row justify-content-center mt-5">
                                        <div class="col-6 col-md-4 mt-2">
                                            <button style = "width: 300px; margin-left: -64px;"
                                                name="button" type="button"  class="btn btn-default btn-block btn-lg font-middle waves-effect waves-light" onclick="generateToken(this)" style="color: white">申し込み</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/new_payment.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/users/profile/profile.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/mobile_menu.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/dropdown.css"/>
        <script src="https://linkpt.cardservice.co.jp/api/token/1.0/zeus_token.js"></script>
        <script src="/custom_frontend/static/js/zeus_payment.js"></script>
    `,
    data() {
        return {
            isMobileMenuOpen: false,
            selectedPlan: '未選択'
        }
    },
    methods: {
        toggleMobileMenu() {
            this.isMobileMenuOpen = !this.isMobileMenuOpen;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                if (this.isMobileMenuOpen) {
                    mobileMenu.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Ngăn scroll khi menu mở
                } else {
                    mobileMenu.classList.remove('active');
                    document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
                }
            }
        },
        closeMobileMenu() {
            this.isMobileMenuOpen = false;
            const mobileMenu = document.querySelector('.mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.remove('active');
                document.body.style.overflow = ''; // Cho phép scroll khi menu đóng
            }
        },
        selectPlan(value) {
            this.selectedPlan = value;
            console.log('Selected plan:', value);
            // Xử lý logic khi chọn plan ở đây
        }
    },
    async mounted() {
        // Thêm event listener để đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            const mobileMenu = document.querySelector('.mobile-menu');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu && mobileMenuBtn &&
                !mobileMenu.contains(e.target) &&
                !mobileMenuBtn.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // Thêm event listener để đóng menu khi resize màn hình lớn hơn 767px
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                this.closeMobileMenu();
            }
        });
    }
}
export default NewPayment