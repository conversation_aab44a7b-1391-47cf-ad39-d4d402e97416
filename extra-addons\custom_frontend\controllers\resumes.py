import re
import requests
import urllib.parse
from odoo import http
from odoo.http import request, Response
import json
import logging

_logger = logging.getLogger(__name__)
class ResumesManagerController(http.Controller):
    @http.route('/api/resumes', type='http', auth='public', cors='*', methods=['GET'])
    def get_resume(self):
        user_id = request.params.get('user_id')
        if user_id:
            db_resume = request.env['vit.partner'].sudo().search([('created_by', '=', int(user_id))])
        else:
            db_resume = request.env['vit.partner'].sudo().search([])

        if not db_resume:
            return request.make_response(
                json.dumps({'success': False, 'message': 'No resumes found', 'resumes': []})
            )

        company_id = None
        Users = request.env['vit.users'].sudo()
        Company = Users.search([('id', '=', user_id)], limit=1)
        if Company:
            company_id = Company.company_id.id
            _logger.info(f"company_id: {company_id}")
            
        View = request.env['vit.viewresumes'].sudo()
        Scout = request.env['vit.scout'].sudo()
        Workflow = request.env['vit.workflow'].sudo()
        
        resumes_list = []
        for resume in db_resume:
            if resume.del_flg != True:
                view_count = View.search_count([('resumes_id', '=', resume.id)])
                resumeViewUserId = View.search([('resumes_id', '=', resume.id)])
                for view in resumeViewUserId:
                    _logger.info(f"resumeViewUserId: {view.user_id}")
                    if not view.user_id:
                        continue  # bỏ qua nếu không có user
                    CompanySub = Users.search([('id', '=', view.user_id.id)], limit=1)
                    _logger.info(f"CompanySub: {CompanySub.company_id.id}")
                    if CompanySub and CompanySub.company_id.id == company_id:
                        view_count -= 1

                scout_count = Scout.search_count([('resumes_id', '=', resume.id)])
                workflow_count = Workflow.search_count([('resumes_id', '=', resume.id)])

                # Lấy memo hiện tại
                memo = request.env['vit.comments'].sudo().search([
                    ('resume_id', '=', resume.id),
                    ('status', '=', 1)  # status = 1 cho memo
                ], limit=1)
                memo_content = memo.content if memo else ''

                resumes_list.append({
                    'id': resume.id,
                    'resume_visibility': resume.resume_visibility,
                    'status': resume.status,
                    'date_period': str(resume.date_period) if resume.date_period else None,
                    'user_id': resume.user_id.id if resume.user_id else None,
                    'cv_id': resume.cv_id.id if resume.cv_id else None,
                    'opp_id': resume.opp_id.id if resume.opp_id else None,
                    'user_name': resume.user_name,
                    'initial_name': resume.initial_name,
                    'gender': resume.gender,
                    'birthday': str(resume.birthday) if resume.birthday else None,
                    'nationality': resume.nationality,
                    'resident': resume.resident,
                    'categories_consultation': resume.categories_consultation,
                    'categories_development': resume.categories_development,
                    'categories_infrastructure': resume.categories_infrastructure,
                    'categories_design': resume.categories_design,
                    'experience_pr': resume.experience_pr,
                    'qualification': resume.qualification,
                    'characteristic': resume.characteristic,
                    'utilization_rate': resume.utilization_rate,
                    'unit_price_min': resume.unit_price_min,
                    'unit_price_max': resume.unit_price_max,
                    'region': resume.region,
                    'working_frequency': resume.working_frequency,
                    'working_location': resume.working_location,
                    'working_hope': resume.working_hope,
                    'company_settings': resume.company_settings,
                    'created_at': str(resume.created_at) if resume.created_at else None,
                    'created_by': resume.created_by.id if resume.created_by else None,
                    'updated_at': str(resume.updated_at) if resume.updated_at else None,
                    'updated_by': resume.updated_by.id if resume.updated_by else None,
                    'view': view_count,
                    'scout_number': scout_count,
                    'apply_number': workflow_count - scout_count,
                    'memo': memo_content,
                })

        return request.make_response(
            json.dumps({'success': True, 'message': 'Get resumes success', 'resumes': resumes_list})
        )
        
    @http.route('/api/resumes_for_apply', type='http', auth='public', cors='*', methods=['GET'])
    def get_resume_for_apply(self):
        user_id = request.params.get('user_id')
        opp_id = request.params.get('opp_id')

        if user_id:
            db_resume = request.env['vit.partner'].sudo().search([('created_by', '=', user_id)])
        else:
            db_resume = request.env['vit.partner'].sudo().search([])

        if not db_resume:
            return request.make_response(
                json.dumps({'success': False, 'message': 'No resumes found', 'resumes': []})
            )

        booked_resumes = request.env['vit.workflow'].sudo().search([('opportunities_id', '=', opp_id)])
        booked_resume_ids = {booking.resumes_id.id for booking in booked_resumes if booking.resumes_id}
        
        _logger.error(f"Booked Resume IDs: {booked_resume_ids}")

        resumes_list = []
        for resume in db_resume:
            if resume.id not in booked_resume_ids:
                resumes_list.append({
                    'id': resume.id,
                    'resume_visibility': resume.resume_visibility,
                    'status': resume.status,
                    'date_period': str(resume.date_period) if resume.date_period else None,
                    'user_id': resume.user_id.id if resume.user_id else None,
                    'cv_id': resume.cv_id.id if resume.cv_id else None,
                    'opp_id': resume.opp_id.id if resume.opp_id else None,
                    'user_name': resume.user_name,
                    'initial_name': resume.initial_name,
                    'gender': resume.gender,
                    'birthday': str(resume.birthday) if resume.birthday else None,
                    'nationality': resume.nationality,
                    'resident': resume.resident,
                    'categories_consultation': resume.categories_consultation,
                    'categories_development': resume.categories_development,
                    'categories_infrastructure': resume.categories_infrastructure,
                    'categories_design': resume.categories_design,
                    'experience_pr': resume.experience_pr,
                    'qualification': resume.qualification,
                    'characteristic': resume.characteristic,
                    'utilization_rate': resume.utilization_rate,
                    'unit_price_min': resume.unit_price_min,
                    'unit_price_max': resume.unit_price_max,
                    'region': resume.region,
                    'working_frequency': resume.working_frequency,
                    'working_location': resume.working_location,
                    'working_hope': resume.working_hope,
                    'company_settings': resume.company_settings,
                    'created_at': str(resume.created_at) if resume.created_at else None,
                    'created_by': resume.created_by if resume.created_by else None,
                    'updated_at': str(resume.updated_at) if resume.updated_at else None,
                    'updated_by': resume.updated_by if resume.updated_by else None,
                    'view': resume.view,
                    'scout_number': resume.scout_number
                })

        return request.make_response(
            json.dumps({'success': True, 'message': 'Get resumes success', 'resumes': resumes_list})
        )
