/*! CSS Used from: https://assign-navi.jp/assets/application-a6ae88c5d81f7d4b8d78ca2206d85ea085a3ddf489452a0d157bd90a7f80aa90.css ; media=screen */
@media screen {
    :root {
        --blue: #007bff;
        --indigo: #6610f2;
        --purple: #6f42c1;
        --pink: #e83e8c;
        --red: #dc3545;
        --orange: #fd7e14;
        --yellow: #ffc107;
        --green: #28a745;
        --teal: #20c997;
        --cyan: #17a2b8;
        --white: #fff;
        --gray: #6c757d;
        --gray-dark: #343a40;
        --primary: rgb(0, 188, 174);
        --secondary: #6c757d;
        --success: #28a745;
        --info: #17a2b8;
        --warning: #ffc107;
        --danger: #dc3545;
        --light: #f8f9fa;
        --dark: #343a40;
        --breakpoint-xs: 0;
        --breakpoint-sm: 576px;
        --breakpoint-md: 768px;
        --breakpoint-lg: 992px;
        --breakpoint-xl: 1200px;
        --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        --primary-color: #1072e9;
    }

    *,
    ::after,
    ::before {
        box-sizing: border-box;
    }

    html {
        font-family: sans-serif;
        line-height: 1.15;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
    }

    footer,
    header,
    main,
    nav {
        display: block;
    }

    body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #212529;
        text-align: left;
        background-color: #fff;
    }

    [tabindex="-1"]:focus:not(:focus-visible) {
        outline: 0 !important;
    }

    h1,
    h4 {
        margin-top: 0;
        margin-bottom: .5rem;
    }

    p {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    dl,
    ul {
        margin-top: 0;
        margin-bottom: 1rem;
    }

    ul ul {
        margin-bottom: 0;
    }

    dt {
        font-weight: 700;
    }

    dd {
        margin-bottom: .5rem;
        margin-left: 0;
    }

    a {
        color: #007bff;
        text-decoration: none;
        background-color: transparent;
    }

    a:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    img {
        vertical-align: middle;
        border-style: none;
    }

    table {
        border-collapse: collapse;
    }

    th {
        text-align: inherit;
        text-align: -webkit-match-parent;
    }

    label {
        display: inline-block;
        margin-bottom: .5rem;
    }

    button {
        border-radius: 0;
    }

    button:focus:not(:focus-visible) {
        outline: 0;
    }

    button,
    input,
    textarea {
        margin: 0;
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
    }

    button,
    input {
        overflow: visible;
    }

    button {
        text-transform: none;
    }

    [type=button],
    [type=submit],
    button {
        -webkit-appearance: button;
    }

    [type=button]:not(:disabled),
    [type=submit]:not(:disabled),
    button:not(:disabled) {
        cursor: pointer;
    }

    input[type=checkbox],
    input[type=radio] {
        box-sizing: border-box;
        padding: 0;
    }

    textarea {
        overflow: auto;
        resize: vertical;
    }

    h1,
    h4 {
        margin-bottom: .5rem;
        font-weight: 500;
        line-height: 1.2;
    }

    h1 {
        font-size: 2.5rem;
    }

    h4 {
        font-size: 1.5rem;
    }

    .small {
        font-size: 80%;
        font-weight: 400;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
        margin-left: -15px;
    }

    .col-12,
    .col-4,
    .col-6,
    .col-8,
    .col-lg-3,
    .col-lg-8,
    .col-md-10,
    .col-md-3,
    .col-md-4,
    .col-md-6,
    .col-sm-2,
    .col-sm-4,
    .col-sm-6 {
        position: relative;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
    }

    .col-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .col-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
    }

    .col-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media (min-width: 576px) {
        .col-sm-2 {
            flex: 0 0 16.666667%;
            max-width: 16.666667%;
        }

        .col-sm-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-sm-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }
    }

    @media (min-width: 768px) {
        .col-md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-md-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .col-md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        .col-md-10 {
            flex: 0 0 83.333333%;
            max-width: 83.333333%;
        }
    }

    @media (min-width: 992px) {
        .col-lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        .col-lg-8 {
            flex: 0 0 66.666667%;
            max-width: 66.666667%;
        }
    }

    .form-control {
        display: block;
        width: 100%;
        height: calc(1.5em + .75rem + 2px);
        padding: .375rem .75rem;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: .25rem;
        transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .form-control {
            transition: none;
        }
    }

    .form-control:focus {
        color: #495057;
        background-color: #fff;
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .form-control:disabled,
    .form-control[readonly] {
        background-color: #e9ecef;
        opacity: 1;
    }

    textarea.form-control {
        height: auto;
    }

    .form-check {
        position: relative;
        display: block;
        padding-left: 1.25rem;
    }

    .form-check-input {
        position: absolute;
        margin-top: .3rem;
        margin-left: -1.25rem;
    }

    .form-check-input:disabled~.form-check-label {
        color: #6c757d;
    }

    .form-check-label {
        margin-bottom: 0;
    }

    .form-inline {
        display: flex;
        flex-flow: row wrap;
        align-items: center;
    }

    .form-inline .form-check {
        width: 100%;
    }

    @media (min-width: 576px) {
        .form-inline label {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
        }

        .form-inline .form-control {
            display: inline-block;
            width: auto;
            vertical-align: middle;
        }

        .form-inline .form-check {
            display: flex;
            align-items: center;
            justify-content: center;
            width: auto;
            padding-left: 0;
        }

        .form-inline .form-check-input {
            position: relative;
            flex-shrink: 0;
            margin-top: 0;
            margin-right: .25rem;
            margin-left: 0;
        }
    }

    .btn {
        display: inline-block;
        font-weight: 400;
        color: #212529;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-color: transparent;
        border: 1px solid transparent;
        padding: .375rem .75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: .25rem;
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .btn {
            transition: none;
        }
    }

    .btn:hover {
        color: #212529;
        text-decoration: none;
    }

    .btn:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn:disabled {
        opacity: .65;
    }

    .btn:not(:disabled):not(.disabled) {
        cursor: pointer;
    }

    .btn-lg {
        padding: .5rem 1rem;
        font-size: 1.25rem;
        line-height: 1.5;
        border-radius: .3rem;
    }

    .btn-sm {
        padding: .25rem .5rem;
        font-size: .875rem;
        line-height: 1.5;
        border-radius: .2rem;
    }

    .btn-block {
        display: block;
        width: 100%;
    }

    .custom-control {
        position: relative;
        z-index: 1;
        display: block;
        min-height: 1.5rem;
        padding-left: 1.5rem;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .custom-control-input {
        position: absolute;
        left: 0;
        z-index: -1;
        width: 1rem;
        height: 1.25rem;
        opacity: 0;
    }

    .custom-control-input:checked~.custom-control-label::before {
        color: #fff;
        border-color: #007bff;
        background-color: #007bff;
    }

    .custom-control-input:focus~.custom-control-label::before {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #80bdff;
    }

    .custom-control-input:not(:disabled):active~.custom-control-label::before {
        color: #fff;
        background-color: #b3d7ff;
        border-color: #b3d7ff;
    }

    .custom-control-input:disabled~.custom-control-label {
        color: #6c757d;
    }

    .custom-control-input:disabled~.custom-control-label::before {
        background-color: #e9ecef;
    }

    .custom-control-label {
        position: relative;
        margin-bottom: 0;
        vertical-align: top;
    }

    .custom-control-label::before {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        pointer-events: none;
        content: "";
        background-color: #fff;
        border: #adb5bd solid 1px;
    }

    .custom-control-label::after {
        position: absolute;
        top: .25rem;
        left: -1.5rem;
        display: block;
        width: 1rem;
        height: 1rem;
        content: "";
        background: 50%/50% 50% no-repeat;
    }

    .custom-checkbox .custom-control-label::before {
        border-radius: .25rem;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
    }

    .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
        background-color: rgba(0, 123, 255, 0.5);
    }

    .custom-control-label::before {
        transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    }

    @media (prefers-reduced-motion: reduce) {
        .custom-control-label::before {
            transition: none;
        }
    }

    .navbar {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        padding: .5rem 1rem;
    }

    .navbar-brand {
        display: inline-block;
        padding-top: .3125rem;
        padding-bottom: .3125rem;
        margin-right: 1rem;
        font-size: 1.25rem;
        line-height: inherit;
        white-space: nowrap;
    }

    .navbar-brand:focus,
    .navbar-brand:hover {
        text-decoration: none;
    }

    .navbar-nav {
        display: flex;
        flex-direction: column;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    @media (min-width: 768px) {
        .navbar-expand-md {
            flex-flow: row nowrap;
            justify-content: flex-start;
        }

        .navbar-expand-md .navbar-nav {
            flex-direction: row;
        }
    }

    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0, 0, 0, 0.125);
        border-radius: .25rem;
    }

    .badge-pill {
        padding-right: .6em;
        padding-left: .6em;
        border-radius: 10rem;
    }

    .badge-danger {
        color: #fff;
        background-color: #dc3545;
    }

    .progress {
        display: flex;
        height: 1rem;
        overflow: hidden;
        line-height: 0;
        font-size: .75rem;
        background-color: #e9ecef;
        border-radius: .25rem;
    }

    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: .5;
    }

    .close:hover {
        color: #000;
        text-decoration: none;
    }

    .close:not(:disabled):not(.disabled):focus,
    .close:not(:disabled):not(.disabled):hover {
        opacity: .75;
    }

    button.close {
        padding: 0;
        background-color: transparent;
        border: 0;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1050;
        display: none;
        width: 100%;
        height: 100%;
        overflow: hidden;
        outline: 0;
    }

    .modal-dialog {
        position: relative;
        width: auto;
        margin: .5rem;
        pointer-events: none;
    }

    .modal-dialog-centered {
        display: flex;
        align-items: center;
        min-height: calc(100% - 1rem);
    }

    .modal-dialog-centered::before {
        display: block;
        height: calc(100vh - 1rem);
        height: -webkit-min-content;
        height: -moz-min-content;
        height: min-content;
        content: "";
    }

    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        pointer-events: auto;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: .3rem;
        outline: 0;
    }

    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: calc(.3rem - 1px);
        border-top-right-radius: calc(.3rem - 1px);
    }

    .modal-header .close {
        padding: 1rem 1rem;
        margin: -1rem -1rem -1rem auto;
    }

    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }

    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }

    .modal-footer {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;
        padding: .75rem;
        border-top: 1px solid #dee2e6;
        border-bottom-right-radius: calc(.3rem - 1px);
        border-bottom-left-radius: calc(.3rem - 1px);
    }

    .modal-footer>* {
        margin: .25rem;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            max-width: 500px;
            margin: 1.75rem auto;
        }

        .modal-dialog-centered {
            min-height: calc(100% - 3.5rem);
        }

        .modal-dialog-centered::before {
            height: calc(100vh - 3.5rem);
            height: -webkit-min-content;
            height: -moz-min-content;
            height: min-content;
        }
    }

    @media (min-width: 992px) {
        .modal-lg {
            max-width: 800px;
        }
    }

    .align-baseline {
        vertical-align: baseline !important;
    }

    .align-middle {
        vertical-align: middle !important;
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .d-none {
        display: none !important;
    }

    .d-inline-block {
        display: inline-block !important;
    }

    .d-block {
        display: block !important;
    }

    .d-flex {
        display: flex !important;
    }

    @media (min-width: 768px) {
        .d-md-block {
            display: block !important;
        }

        .d-md-flex {
            display: flex !important;
        }
    }

    @media (min-width: 1200px) {
        .d-xl-none {
            display: none !important;
        }

        .d-xl-inline-block {
            display: inline-block !important;
        }

        .d-xl-block {
            display: block !important;
        }
    }

    .flex-column {
        flex-direction: column !important;
    }

    .justify-content-center {
        justify-content: center !important;
    }

    .justify-content-between {
        justify-content: space-between !important;
    }

    .align-items-start {
        align-items: flex-start !important;
    }

    .align-items-center {
        align-items: center !important;
    }

    @media (min-width: 768px) {
        .flex-md-row {
            flex-direction: row !important;
        }

        .justify-content-md-center {
            justify-content: center !important;
        }
    }

    .float-left {
        float: left !important;
    }

    .float-right {
        float: right !important;
    }

    .fixed-top {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        z-index: 1030;
    }

    .w-50 {
        width: 50% !important;
    }

    .w-100 {
        width: 100% !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mx-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mx-0 {
        margin-left: 0 !important;
    }

    .mb-1 {
        margin-bottom: 0.25rem !important;
    }

    .mt-2 {
        margin-top: 0.5rem !important;
    }

    .mr-2 {
        margin-right: 0.5rem !important;
    }

    .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .ml-2 {
        margin-left: 0.5rem !important;
    }

    .mt-3,
    .my-3 {
        margin-top: 1rem !important;
    }

    .mr-3,
    .mx-3 {
        margin-right: 1rem !important;
    }

    .mb-3,
    .my-3 {
        margin-bottom: 1rem !important;
    }

    .mx-3 {
        margin-left: 1rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .mt-5 {
        margin-top: 3rem !important;
    }

    .mb-5 {
        margin-bottom: 3rem !important;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .p-1 {
        padding: 0.25rem !important;
    }

    .pt-1,
    .py-1 {
        padding-top: 0.25rem !important;
    }

    .px-1 {
        padding-right: 0.25rem !important;
    }

    .py-1 {
        padding-bottom: 0.25rem !important;
    }

    .pl-1,
    .px-1 {
        padding-left: 0.25rem !important;
    }

    .pt-2,
    .py-2 {
        padding-top: 0.5rem !important;
    }

    .px-2 {
        padding-right: 0.5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: 0.5rem !important;
    }

    .pl-2,
    .px-2 {
        padding-left: 0.5rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pr-4 {
        padding-right: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 3rem !important;
    }

    .px-5 {
        padding-right: 3rem !important;
    }

    .px-5 {
        padding-left: 3rem !important;
    }

    .mt-n4 {
        margin-top: -1.5rem !important;
    }

    .mr-auto,
    .mx-auto {
        margin-right: auto !important;
    }

    .ml-auto,
    .mx-auto {
        margin-left: auto !important;
    }

    @media (min-width: 576px) {
        .mt-sm-0 {
            margin-top: 0 !important;
        }

        .mb-sm-0 {
            margin-bottom: 0 !important;
        }

        .ml-sm-4 {
            margin-left: 1.5rem !important;
        }

        .pl-sm-2 {
            padding-left: 0.5rem !important;
        }

        .px-sm-3 {
            padding-right: 1rem !important;
        }

        .pl-sm-3,
        .px-sm-3 {
            padding-left: 1rem !important;
        }
    }

    @media (min-width: 768px) {
        .pr-md-0 {
            padding-right: 0 !important;
        }

        .pl-md-0 {
            padding-left: 0 !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    .text-right {
        text-align: right !important;
    }

    .text-center {
        text-align: center !important;
    }

    @media (min-width: 768px) {
        .text-md-right {
            text-align: right !important;
        }
    }

    .font-weight-bold {
        font-weight: 700 !important;
    }

    @media print {

        *,
        ::after,
        ::before {
            text-shadow: none !important;
            box-shadow: none !important;
        }

        a:not(.btn) {
            text-decoration: underline;
        }

        img,
        tr {
            page-break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }

        body {
            min-width: 992px !important;
        }

        .navbar {
            display: none;
        }
    }

    .pink.lighten-2 {
        background-color: #ff6388 !important;
    }

    .pink {
        background-color: #ff285b !important;
    }

    .black-text {
        color: #000 !important;
    }

    .white {
        background-color: #fff !important;
    }

    .white-text {
        color: #fff !important;
    }

    .primary-color-dark {
        background-color: #0d47a1 !important;
    }

    :disabled {
        pointer-events: none !important;
    }

    a {
        color: #007bff;
        text-decoration: none;
        cursor: pointer;
        transition: all .2s ease-in-out;
    }

    a:hover {
        color: #0056b3;
        text-decoration: none;
        transition: all .2s ease-in-out;
    }

    a:disabled:hover {
        color: #007bff;
    }

    a:not([href]):not([tabindex]),
    a:not([href]):not([tabindex]):focus,
    a:not([href]):not([tabindex]):hover {
        color: inherit;
        text-decoration: none;
    }

    body {
        font-family: "Roboto", sans-serif;
        font-weight: 300;
    }

    h1,
    h4 {
        font-weight: 300;
    }

    .font-small {
        font-size: .9rem;
    }

    .waves-effect {
        position: relative;
        overflow: hidden;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    a.waves-effect,
    a.waves-light {
        display: inline-block;
    }

    .btn {
        margin: .375rem;
        color: inherit;
        text-transform: uppercase;
        word-wrap: break-word;
        white-space: normal;
        cursor: pointer;
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        padding: .84rem 2.14rem;
        font-size: .81rem;
    }

    .btn:hover,
    .btn:focus,
    .btn:active {
        outline: 0;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn.btn-block {
        margin: inherit;
    }

    .btn.btn-lg {
        padding: 1rem 2.4rem;
        font-size: .94rem;
    }

    .btn.btn-sm {
        padding: .5rem 1.6rem;
        font-size: .64rem;
    }

    .btn:disabled:hover,
    .btn:disabled:focus,
    .btn:disabled:active {
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    .btn[class*=btn-outline-] {
        padding-top: .7rem;
        padding-bottom: .7rem;
    }

    .btn.btn-sm[class*=btn-outline-] {
        padding-top: .38rem;
        padding-bottom: .38rem;
    }

    .btn-default {
        color: #fff !important;
        background: linear-gradient(to right, #61b8f7, #1072e9) !important;
    }

    .btn-default:hover {
        color: #fff;
        background-color: #61b8f7;
    }

    .btn-default:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:focus,
    .btn-default:active {
        background-color: #005650;
    }

    .btn-default:not([disabled]):not(.disabled):active {
        background-color: #005650 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #1072e9 !important;
    }

    .btn-outline-default:hover,
    .btn-outline-default:focus,
    .btn-outline-default:active,
    .btn-outline-default:active:focus {
        color: #1072e9 !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
    }

    .btn-outline-default:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #1072e9 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-default:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey {
        color: #fff;
        background-color: #78909c !important;
    }

    .btn-blue-grey:hover {
        color: #fff;
        background-color: #879ca7;
    }

    .btn-blue-grey:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:focus,
    .btn-blue-grey:active {
        background-color: #4a5b64;
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active {
        background-color: #4a5b64 !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border: 2px solid #78909c !important;
    }

    .btn-outline-blue-grey:hover,
    .btn-outline-blue-grey:focus,
    .btn-outline-blue-grey:active,
    .btn-outline-blue-grey:active:focus {
        color: #78909c !important;
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: #78909c !important;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .btn-outline-blue-grey:not([disabled]):not(.disabled):active:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .card {
        font-weight: 400;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    }

    @media (min-width: 600px) {
        .navbar.scrolling-navbar {
            padding-top: 12px;
            padding-bottom: 12px;
            transition: background .5s ease-in-out, padding .5s ease-in-out;
        }

        .navbar.scrolling-navbar .navbar-nav>li {
            transition-duration: 1s;
        }

        .navbar.scrolling-navbar.top-nav-collapse {
            padding-top: 5px;
            padding-bottom: 5px;
        }
    }

    .badge-pill {
        padding-right: .5rem;
        padding-left: .5rem;
        border-radius: 10rem;
    }

    .badge-danger {
        color: #fff !important;
        background-color: #ff285b !important;
    }

    .modal-dialog .modal-content {
        border: 0;
        border-radius: .25rem;
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
    }

    .modal-dialog .modal-content .modal-header {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }

    .modal {
        padding-right: 0 !important;
    }

    footer.page-footer {
        bottom: 0;
        color: #fff;
    }

    footer.page-footer .container-fluid {
        width: auto;
    }

    footer.page-footer a {
        color: #fff;
    }

    table th {
        font-size: .9rem;
        font-weight: 400;
    }

    table td {
        font-size: .9rem;
        font-weight: 300;
    }

    button,
    html [type=button],
    [type=submit] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    .form-check-input:not(:checked),
    .form-check-input:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    .form-check-input[type=radio]:not(:checked)+label,
    .form-check-input[type=radio]:checked+label {
        position: relative;
        display: inline-block;
        height: 1.5625rem;
        padding-left: 28px;
        line-height: 1.5625rem;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        transition: .28s ease;
    }

    .form-check-input[type=radio]+label:before,
    .form-check-input[type=radio]+label:after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 18px;
        height: 18px;
        margin: 4px;
        content: "";
        transition: .28s ease;
    }

    .form-check-input[type=radio]:not(:checked)+label:before,
    .form-check-input[type=radio]:not(:checked)+label:after,
    .form-check-input[type=radio]:checked+label:before,
    .form-check-input[type=radio]:checked+label:after {
        border-radius: 50%;
    }

    .form-check-input[type=radio]:not(:checked)+label:before,
    .form-check-input[type=radio]:not(:checked)+label:after {
        border: 2px solid #d5d9db;
    }

    .form-check-input[type=radio]:not(:checked)+label:after {
        transform: scale(0);
    }

    .form-check-input[type=radio]:checked+label:before {
        border: 2px solid rgba(0, 0, 0, 0);
    }

    .form-check-input[type=radio]:checked+label:after {
        border: 2px solid #1072e9;
    }

    .form-check-input[type=radio]:checked+label:after {
        background-color: #1072e9;
    }

    .form-check-input[type=radio]:checked+label:after {
        transform: scale(1.02);
    }

    .form-check-input[type=radio]:disabled:not(:checked)+label:before,
    .form-check-input[type=radio]:disabled:checked+label:before {
        background-color: rgba(0, 0, 0, 0);
        border-color: rgba(0, 0, 0, 0.46);
    }

    .form-check-input[type=radio]:focus+label:before {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    [type=checkbox]:not(:checked),
    [type=checkbox]:checked {
        position: absolute;
        pointer-events: none;
        opacity: 0;
    }

    .select-wrapper .select-dropdown {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .select-wrapper {
        position: relative;
    }

    .select-wrapper:not(.md-outline) .select-dropdown:focus {
        border-bottom: 1px solid #4285f4;
        box-shadow: 0 1px 0 0 #4285f4;
    }

    .select-wrapper input.select-dropdown {
        position: relative;
        z-index: 2;
        display: block;
        width: 100%;
        height: 40px;
        padding: 0;
        margin: 0 0 .94rem 0;
        font-size: 1rem;
        line-height: 2.9rem;
        text-overflow: ellipsis;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0);
        border: none;
        border-bottom: 1px solid #ced4da;
        outline: none;
    }

    .select-wrapper input.select-dropdown:disabled {
        color: rgba(0, 0, 0, 0.3);
        cursor: default;
        border-bottom-color: rgba(0, 0, 0, 0.2);
    }

    .select-wrapper span.caret {
        position: absolute;
        top: .8rem;
        right: 0;
        font-size: .63rem;
        color: #495057;
    }

    .select-wrapper ul {
        padding-left: 0;
        list-style-type: none;
    }

    .dropdown-content {
        top: 0;
        position: absolute;
        z-index: 1021;
        display: none;
        min-width: 6.25rem;
        max-height: 11.5rem;
        margin: 0;
        overflow-y: auto;
        background-color: #fff;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
        opacity: 0;
        will-change: width, height;
    }

    .dropdown-content li {
        width: 100%;
        clear: both;
        line-height: 1.3rem;
        color: #000;
        text-align: left;
        text-transform: none;
        cursor: pointer;
    }

    .dropdown-content li:hover,
    .dropdown-content li.active {
        background-color: #eee;
    }

    .dropdown-content li>span {
        display: block;
        padding: .5rem;
        font-size: .9rem;
        color: #4285f4;
    }

    button:focus {
        outline: 0 !important;
    }

    .drag-target {
        position: fixed;
        top: 0;
        z-index: 998;
        width: 10px;
        height: 100%;
    }

    .drag-target {
        position: fixed;
        top: 0;
        z-index: 998;
        width: 10px;
        height: 100%;
    }

    .md-progress {
        position: relative;
        display: block;
        width: 100%;
        height: .25rem;
        margin-bottom: 1rem;
        overflow: hidden;
        background-color: #eee;
        box-shadow: none;
    }

    .md-progress .indeterminate {
        background-color: #90caf9;
    }

    .md-progress .indeterminate:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        content: "";
        background-color: inherit;
        -webkit-animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
        animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
        will-change: left, right;
    }

    .md-progress .indeterminate:after {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        content: "";
        background-color: inherit;
        -webkit-animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        animation: indeterminate 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) infinite;
        -webkit-animation-delay: 1.15s;
        animation-delay: 1.15s;
        will-change: left, right;
    }

    .bold {
        font-weight: 500;
    }

    .ex-bold {
        font-weight: 700 !important;
    }

    .font-default {
        font-size: .875rem !important;
    }

    .font-middle {
        font-size: 1rem !important;
    }

    .font-large {
        font-size: 1.125rem !important;
    }

    .font-extralarge {
        font-size: 1.25rem !important;
    }

    .font-small {
        font-size: .75rem !important;
    }

    .white-text {
        color: #fff;
    }

    .default-color-text {
        color: rgba(0, 0, 0, 0.87);
    }

    .custom-grey-text {
        color: rgba(84, 110, 122, 0.87);
    }

    .custom-grey-3-text {
        color: #d5d9db;
    }

    .custom-grey-6-text {
        color: #455965;
    }

    .default-main-color {
        color: #1072e9;
    }

    .px-0 {
        padding-right: 0 !important;
    }

    .pb-0 {
        padding-bottom: 0 !important;
    }

    .pl-0,
    .px-0 {
        padding-left: 0 !important;
    }

    .m-0 {
        margin: 0 !important;
    }

    .mt-0 {
        margin-top: 0 !important;
    }

    .mx-0 {
        margin-right: 0 !important;
    }

    .mb-0 {
        margin-bottom: 0 !important;
    }

    .mx-0 {
        margin-left: 0 !important;
    }

    .p-1 {
        padding: .25rem !important;
    }

    .pt-1,
    .py-1 {
        padding-top: .25rem !important;
    }

    .px-1 {
        padding-right: .25rem !important;
    }

    .py-1 {
        padding-bottom: .25rem !important;
    }

    .pl-1,
    .px-1 {
        padding-left: .25rem !important;
    }

    .mb-1 {
        margin-bottom: .25rem !important;
    }

    .pt-2,
    .py-2 {
        padding-top: .5rem !important;
    }

    .px-2 {
        padding-right: .5rem !important;
    }

    .pb-2,
    .py-2 {
        padding-bottom: .5rem !important;
    }

    .pl-2,
    .px-2 {
        padding-left: .5rem !important;
    }

    .mt-2 {
        margin-top: .5rem !important;
    }

    .mr-2 {
        margin-right: .5rem !important;
    }

    .mb-2 {
        margin-bottom: .5rem !important;
    }

    .ml-2 {
        margin-left: .5rem !important;
    }

    .pt-3,
    .py-3 {
        padding-top: 1rem !important;
    }

    .pr-3,
    .px-3 {
        padding-right: 1rem !important;
    }

    .pb-3,
    .py-3 {
        padding-bottom: 1rem !important;
    }

    .pl-3,
    .px-3 {
        padding-left: 1rem !important;
    }

    .mt-3,
    .my-3 {
        margin-top: 1rem !important;
    }

    .mr-3,
    .mx-3 {
        margin-right: 1rem !important;
    }

    .mb-3,
    .my-3 {
        margin-bottom: 1rem !important;
    }

    .mx-3 {
        margin-left: 1rem !important;
    }

    .p-4 {
        padding: 1.5rem !important;
    }

    .py-4 {
        padding-top: 1.5rem !important;
    }

    .pr-4 {
        padding-right: 1.5rem !important;
    }

    .py-4 {
        padding-bottom: 1.5rem !important;
    }

    .pl-4 {
        padding-left: 1.5rem !important;
    }

    .mr-4 {
        margin-right: 1.5rem !important;
    }

    .mb-4 {
        margin-bottom: 1.5rem !important;
    }

    .pt-5 {
        padding-top: 2rem !important;
    }

    .px-5 {
        padding-right: 2rem !important;
    }

    .px-5 {
        padding-left: 2rem !important;
    }

    .mt-5 {
        margin-top: 2rem !important;
    }

    .mb-5 {
        margin-bottom: 2rem !important;
    }

    .mt-6 {
        margin-top: 2.5rem !important;
    }

    .pl-7 {
        padding-left: 3rem !important;
    }

    .mt-7 {
        margin-top: 3rem !important;
    }

    @media (min-width: 576px) {
        .mt-sm-0 {
            margin-top: 0 !important;
        }

        .mb-sm-0 {
            margin-bottom: 0 !important;
        }

        .pl-sm-2 {
            padding-left: .5rem !important;
        }

        .px-sm-3 {
            padding-right: 1rem !important;
        }

        .pl-sm-3,
        .px-sm-3 {
            padding-left: 1rem !important;
        }

        .ml-sm-4 {
            margin-left: 1.5rem !important;
        }
    }

    @media (min-width: 768px) {
        .pr-md-0 {
            padding-right: 0 !important;
        }

        .pl-md-0 {
            padding-left: 0 !important;
        }

        .px-md-3 {
            padding-right: 1rem !important;
        }

        .px-md-3 {
            padding-left: 1rem !important;
        }

        .py-md-4 {
            padding-top: 1.5rem !important;
        }

        .px-md-4 {
            padding-right: 1.5rem !important;
        }

        .py-md-4 {
            padding-bottom: 1.5rem !important;
        }

        .px-md-4 {
            padding-left: 1.5rem !important;
        }
    }

    .mt-n4 {
        margin-top: -1.5rem !important;
    }

    .gap-4 {
        -moz-column-gap: 1.5rem !important;
        column-gap: 1.5rem !important;
        row-gap: 1.5rem !important;
    }

    .width-200 {
        width: 200px;
    }

    @media (max-width: 768px) {
        .sp-width-150 {
            width: 150px !important;
        }
    }

    :root {
        font-family: "Noto Sans Japanese", sans-serif, "HiraginoCustom", MyYugo;
    }

    body {
        font-family: none;
        font-family: "Noto Sans Japanese", "MySansSerif", sans-serif, "HiraginoCustom", MyYugo;
        font-size: .875rem;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        word-wrap: break-word;
        word-break: break-all;
        transition: color .3s ease 0s;
        height: auto;
    }

    body a {
        color: #1072e9;
    }

    body a:hover {
        color: #1072e9;
    }

    body a:hover img {
        opacity: .7;
    }

    body a:not([href]):not([tabindex]):focus,
    body a:not([href]):not([tabindex]):hover {
        color: #1072e9;
    }

    .bg-default-green {
        background-color: #1072e9;
    }

    .bg-grey-1 {
        background-color: #f4f4f4;
    }

    .bg-grey-4 {
        background-color: #9da9b2;
    }

    .bg-deep-orange-1 {
        background-color: #1072e9;
    }

    .material-icons {
        vertical-align: bottom;
        cursor: pointer;
    }

    .material-icons.md-dark {
        color: rgba(0, 0, 0, 0.54);
    }

    .user-icon {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
    }

    .vertical-middle {
        vertical-align: middle !important;
    }

    .vertical-text-bottom {
        vertical-align: text-bottom;
    }

    .clear:after {
        clear: both;
        content: "";
        display: block;
    }

    .overflow-visible {
        overflow: visible !important;
    }

    .btn {
        line-height: 1;
        text-transform: none;
    }

    .btn:hover,
    .btn:active,
    .btn:focus {
        opacity: .7;
    }

    .btn[class*=btn-outline-]:hover,
    .btn[class*=btn-outline-]:active,
    .btn[class*=btn-outline-]:focus {
        box-shadow: 0 5px 11px 0 rgba(0, 0, 0, 0.18), 0 4px 15px 0 rgba(0, 0, 0, 0.15);
        outline: 0;
        opacity: 1;
    }

    .btn.btn-sm {
        padding: .5rem 1rem;
        font-size: .875rem;
    }

    @media (max-width: 767px) {
        .sp-w-100 {
            width: 100%;
        }
    }

    .badge-pill {
        padding: .1rem .5rem;
        color: #fff;
    }

    .dropdown-content li>span {
        color: #595959 !important;
    }

    .dropdown-content li.active,
    .dropdown-content li:hover {
        background-color: #1072e9;
    }

    .dropdown-content li.active>span,
    .dropdown-content li:hover>span {
        color: #fff;
    }

    body header a {
        color: rgba(0, 0, 0, 0.87);
        overflow: visible;
    }

    body header a:hover,
    body header a:hover .material-icons {
        opacity: .7;
        text-decoration: none;
    }

    body header .navbar.scrolling-navbar {
        padding: 28px 32px;
    }

    @media (min-width: 768px) {
        body header .navbar.scrolling-navbar {
            height: 80px;
        }
    }

    body header .navbar-brand img {
        width: 113px;
        vertical-align: baseline;
    }

    @media (min-width: 768px) {
        body header .navbar-brand img {
            width: 8.5rem;
            margin-right: 1.5rem;
            margin-bottom: 0;
            vertical-align: sub;
        }
    }

    body header .navbar-left>ul {
        margin: 0;
    }

    body header .navbar-left>ul li {
        display: inline-block;
        position: relative;
        line-height: 1.7;
    }

    body header .navbar-left>ul li span {
        cursor: pointer;
    }

    body header .navbar-left>ul li ul {
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        background-color: #fff;
        display: none;
        position: absolute;
        left: 15px;
    }

    body header .navbar-left>ul li ul li {
        display: block;
        margin: 0;
    }

    body header .navbar-left>ul li ul li a {
        padding: 0 20px;
        display: block;
        text-decoration: none;
        white-space: nowrap;
    }

    body header .navbar-left>ul li:hover ul {
        display: block;
    }

    body header .navbar-left>ul li:hover ul a:hover {
        opacity: .7;
    }

    body header .header-search-menu {
        font-weight: 500;
    }

    body header .header-search-menu a {
        color: #2a3942;
    }

    body header .header-search-menu span:hover {
        opacity: .7;
    }

    body header .navbar-nav {
        flex-direction: row;
    }

    body header .icon-wrapper {
        position: relative;
        float: left;
    }

    body header .icon-wrapper i {
        width: 28px;
        height: 28px;
        font-size: 28px;
        text-align: center;
        vertical-align: middle;
        color: #455965;
    }

    @media (min-width: 768px) {
        body header .icon-wrapper i {
            width: 32px;
            height: 32px;
            font-size: 32px;
        }
    }

    body header .link_area {
        font-size: 13px;
        line-height: 1;
    }

    body header .phone-area label {
        font-size: 18px;
    }

    body header .phone-icon {
        width: 17px;
        height: 17px;
        display: inline-block;
        margin: 0 4px 3px 0;
    }

    body header .phone-reception-time {
        font-weight: 500;
        line-height: normal;
    }

    body header .phone-outside-reception-hours {
        font-family: "YakuHanJP";
        font-feature-settings: "palt" 1;
    }

    body header .sp-phone-area {
        display: block;
        text-align: center;
        background-color: #eaf8f7;
    }

    body header .sp-phone-invitation-message {
        font-weight: 700;
        margin-bottom: 0;
        white-space: nowrap;
    }

    body header .sp-phone-icon {
        width: 20px;
        height: 20px;
        display: inline-block;
        margin: 0 4px 6px 0;
        vertical-align: bottom;
    }

    body header .sp-phone-number {
        margin-bottom: 0;
        font-size: 1.5rem;
        line-height: normal;
        white-space: nowrap;
    }

    body header .sp-phone-reception-time {
        color: #2a3942;
        font-weight: 500;
        line-height: normal;
    }

    body header .sp-phone-outside-reception-hours {
        color: #2a3942;
        font-family: "YakuHanJP";
        font-feature-settings: "palt" 1;
    }

    body header .sp-phone-area .tel-btn {
        font-size: 1.5rem;
        line-height: normal;
    }

    body header .line-height-normal {
        line-height: 1.6;
    }

    body header .line-height-mini {
        line-height: .7;
    }

    body header .roboto {
        font-family: "Roboto";
    }

    body header .header-color {
        color: #2a3942;
    }

    @media screen and (min-width: 481px) {
        body header .tel-btn {
            display: none;
        }
    }

    @media (max-width: 767px) {
        body header .navbar.scrolling-navbar {
            padding: 12px 16px;
            height: 64px;
        }
    }

    @media (max-width: 480px) {
        body header .text-phone-number {
            display: none;
        }
    }







    main {
        margin-top: 115px;
    }

    main .grabient {
        min-height: 50vh;
    }

    main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar) {
        position: relative;
        line-height: 1.8;
        font-weight: 700 !important;
    }

    main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
        content: "";
        background-color: #455965;
        display: block;
        height: 100%;
        width: .428rem;
        position: absolute;
        left: -1.285rem;
    }

    @media (max-width: 767px) {
        main {
            margin-top: 64px;
        }

        main .sp_sides_uniter {
            margin: 0 -15px;
        }

        main.sp_fluid label:not(.custom-control-label):not(.form-check-label):not(.no-label-bar):before {
            left: -0.8125rem !important;
        }
    }

    .title {

        background-color: #1072e9 !important;


        background-size: cover;
    }

    .title h1 {
        color: #fff;
    }

    @media (max-width: 767px) {
        .title h1 {
            font-size: 1.8rem;
        }
    }

    .fix_right_buttom {
        position: fixed;
        right: 0;
        bottom: 0;
        z-index: 99;
        background: rgba(0, 0, 0, 0.7);
    }

    @media screen and (max-width: 768px) {
        .fix_right_buttom {
            position: fixed;
            right: 0;
            bottom: 0;
            z-index: 99;
        }
    }

    footer {
        width: 100%;
        height: auto;
        background-color: #78909c;
    }

    :focus {
        outline: 0;
    }

    .form-control:focus {
        box-shadow: none;
        border-color: #1072e9;
    }

    input.form-control[type=text] {
        border: none;
        border-bottom: 1px solid #1072e9;
        border-radius: 0;
        background-color: #eee;
        color: rgba(0, 0, 0, 0.87);
        height: inherit;
        padding: .75rem .75rem;
        cursor: text;
    }

    textarea.form-control {
        border-color: #1072e9;
    }

    .custom-checkbox label[class*=custom-control-label] {
        cursor: pointer;
    }

    .custom-control-label::before {
        width: 1.125rem;
        height: 1.125rem;
        background-color: rgba(0, 0, 0, 0);
        border: 2px solid #d5d9db;
        top: 3px;
        box-shadow: none !important;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
        background-color: #1072e9;
        border-color: #1072e9;
    }

    .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
        background: url(https://assign-navi.jp/assets/img/common/check_white.png);
        background-size: contain;
        left: -1.4rem;
        top: 3px;
    }

    .custom-control-input:focus:not(:checked)~.custom-control-label::before {
        border-color: #d5d9db;
    }

    .select-wrapper.anavi-select {
        background-color: #f4f4f4;
        border-radius: 3px 3px 0 0;
    }

    .select-wrapper.anavi-select input.select-dropdown {
        padding-right: 0;
        height: 3rem;
        border-color: #1072e9;
        background-color: rgba(0, 0, 0, 0);
        font-size: 1rem;
        text-indent: 1rem;
    }

    .select-wrapper.anavi-select span.caret {
        top: 1rem;
        right: 1rem;
    }

    .select-wrapper.anavi-select span.caret.material-icons {
        font-size: 16px;
    }

    .card.select-card {
        color: rgba(0, 0, 0, 0.87);
        transition: all .3s ease;
        font-weight: 300;
        cursor: pointer;
    }

    .card.select-card:hover {
        opacity: .7;
    }

    .card.select-card td.select-arrow {
        width: 1rem;
        text-align: right;
    }

    .modal {
        opacity: 1;
    }

    .modal-header {
        padding: 2rem;
        border-bottom: none;
    }

    .modal-body {
        padding: 0 2rem 2rem;
    }

    .modal-footer {
        border-top: none;
        padding: 0 2rem 2rem;
        margin-top: -2rem;
        flex-wrap: initial;
    }

    @media (max-width: 767px) {
        .modal-header {
            padding: 2rem 1rem;
            border-bottom: none;
        }

        .modal-body {
            padding: 0 1rem 2rem;
        }
    }

    ul,
    dl {
        list-style: none;
        padding: 0;
    }

    .account-side-scrollbar {
        overflow-y: scroll;
        height: 100vh;
        border-left: 2px solid #9da9b2;
    }

    .custom-side-nav {
        color: #2a3942;
        display: none;
        position: fixed;
        right: 0;
        width: 318px !important;
        z-index: 10;
        height: 100%;
    }

    .custom-side-nav ul li .user-icon {
        width: 2.5rem;
        height: 2.5rem;
    }

    .custom-side-nav ul li .border-bottom {
        margin-bottom: 12px;
    }

    .custom-side-nav ul li .side-nav-title {
        padding-bottom: 12px;
    }

    .custom-side-nav ul li .side-nav-title,
    .custom-side-nav ul li .side-nav-contents {
        cursor: pointer;
    }

    .custom-side-nav ul li .side-nav-title a,
    .custom-side-nav ul li .side-nav-contents a,
    .custom-side-nav ul li .side-nav-contents div {
        color: #2a3942;
        padding: 0 0 12px 24px;
    }

    .custom-side-nav ul li .side-nav-title a:hover span,
    .custom-side-nav ul li .side-nav-contents a:hover span,
    .custom-side-nav ul li .side-nav-contents div:hover span {
        opacity: .7;
    }

    .custom-side-nav ul li .side-nav-title a span,
    .custom-side-nav ul li .side-nav-contents a span,
    .custom-side-nav ul li .side-nav-contents div span {
        font-size: 1rem;
        line-height: 32px;
    }

    .accordion_close {
        cursor: pointer;
    }

    .progress-zindex {
        z-index: 1050;
    }

    .progress {
        border-radius: 1.25rem;
    }

    @media (max-width: 768px) {
        .progress {
            width: 263px;
            margin: 0 auto;
        }
    }

    .border-bottom {
        border-bottom: 1px solid #e6eaec !important;
    }

    @media (max-width: 767px) {
        .btn-outline-default:hover {
            border-color: #1072e9 !important;
            background-color: inherit !important;
            color: #1072e9 !important;
        }
    }

    @media (max-width: 767px) {
        .btn-outline-blue-grey:hover {
            border-color: #78909c !important;
            background-color: inherit !important;
            color: #78909c !important;
        }
    }

    .font-size-middle {
        font-size: custom-(1rem) !important;
        line-height: 28.8px !important;
    }

    .form-check-input[type=radio]:focus+label:before {
        border-color: custom-(#d5d9db);
        box-shadow: none;
    }

    .form-check-input[type=radio]:not(:checked)+label,
    .form-check-input[type=radio]:checked+label {
        height: initial;
    }

    .message-validator {
        color: #dc3545;
        text-align: center;
    }
}

/*! CSS Used from: https://fonts.googleapis.com/icon?family=Material+Icons ; media=screen */
@media screen {
    .material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
    }
}

/*! CSS Used from: Embedded */
.dropdown-area[data-v-deab947c] {
    display: block;
    position: absolute;
    max-height: 183.984px;
    top: 0px;
    left: 0px;
    opacity: 1;
}

/*! CSS Used from: Embedded */
ul[data-v-6d56596f] {
    border-bottom: 1px solid #e6eaec;
}

ul li[data-v-6d56596f] {
    padding-bottom: 1rem;
    transition: opacity .2s ease-in-out;
}

ul li[data-v-6d56596f]:hover {
    cursor: pointer;
    opacity: .6;
    color: #1072e9;
    transition: opacity, color .2s ease-in-out;
}

.tab-active[data-v-6d56596f] {
    padding-bottom: calc(1rem - 2px);
    border-bottom: 2px solid #1072e9;
    color: #1072e9;
}

/*! CSS Used from: Embedded */
.separater[data-v-31c30a8b] {
    border-bottom: 1px solid #e6eaec;
}

.link-to-index[data-v-31c30a8b] {
    color: #1072e9;
}

.link-to-index[data-v-31c30a8b]:hover {
    color: #1072e9;
    background-color: #eaf8f7;
    opacity: .6;
}

.header-tab-menu-wrapper[data-v-31c30a8b] {
    position: absolute;
    background-color: #fff;
    width: 432px;
    right: 0;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, .12), 0px 2px 5px 0px rgba(0, 0, 0, .16);
    border-radius: 4px;
    z-index: 100;
}

@media screen and (max-width: 768px) {
    .header-tab-menu-wrapper[data-v-31c30a8b] {
        position: fixed;
        max-width: 364px;
        right: 4px;
    }
}

/*! CSS Used from: Embedded */
.mr-20px[data-v-4197fd32] {
    margin-right: 20px;
}

/*! CSS Used from: Embedded */
@media (min-width:576px) {

    label[for=public_status_id_field1],
    label[for=public_status_id_field2],
    label[for=available_status_id_field1],
    label[for=available_status_id_field2],
    label[for=available_status_id_field3],
    label[for=gender_id_field1],
    label[for=nationality_id_field1] {
        margin-left: 2.5rem;
    }
}

/*! CSS Used keyframes */
@-webkit-keyframes indeterminate {
    0% {
        right: 100%;
        left: -35%;
    }

    60% {
        right: -90%;
        left: 100%;
    }

    100% {
        right: -90%;
        left: 100%;
    }
}

@keyframes indeterminate {
    0% {
        right: 100%;
        left: -35%;
    }

    60% {
        right: -90%;
        left: 100%;
    }

    100% {
        right: -90%;
        left: 100%;
    }
}

/*! CSS Used fontfaces */
@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 300;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Light.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Medium.woff) format("woff");
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 700;
    src: url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/roboto/Roboto-Bold.woff) format("woff");
}

@font-face {
    font-family: "Noto Sans Japanese";
    font-style: normal;
    font-weight: 300;
    font-display: auto;
    src: local("Noto Sans CJK JP Regular"), local("NotoSansCJKjp-Regular"), local("NotoSansJP-Regular"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/SubNotoSansJP_regular.woff) format("woff");
}

@font-face {
    font-family: "Noto Sans Japanese";
    font-style: normal;
    font-weight: 700;
    src: local("Noto Sans CJK JP Bold"), local("NotoSansCJKjp-Bold"), local("NotoSansJP-Bold"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff2) format("woff2"), url(https://assign-navi.jp/assets/font/notosans/Subset-NotoSansCJKjp-Bold.woff) format("woff");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 100;
    font-display: auto;
    src: local("HiraginoSans-W1"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 200;
    font-display: auto;
    src: local("HiraginoSans-W2"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 300;
    font-display: auto;
    src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 400;
    font-display: auto;
    src: local("HiraginoSans-W3"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 500;
    font-display: auto;
    src: local("HiraginoSans-W5"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 600;
    src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 700;
    font-display: auto;
    src: local("HiraginoSans-W6"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 800;
    font-display: auto;
    src: local("HiraginoSans-W7"), local("Hiragino Sans");
}

@font-face {
    font-family: "HiraginoCustom";
    font-weight: 900;
    font-display: auto;
    src: local("HiraginoSans-W8"), local("Hiragino Sans");
}

@font-face {
    font-family: MyYugo;
    font-weight: normal;
    font-display: auto;
    src: local("YuGothic-Medium"), local("Yu Gothic Medium"), local("YuGothic-Regular");
}

@font-face {
    font-family: MyYugo;
    font-weight: bold;
    font-display: auto;
    src: local("YuGothic-Bold"), local("Yu Gothic");
}

@font-face {
    font-family: MySansSerif;
    font-weight: normal;
    font-display: auto;
    src: local("HelveticaNeue"), local("Helvetica Neue"), local("Helvetica"), local("Arial");
}

@font-face {
    font-family: MySansSerif;
    font-weight: 700;
    font-display: auto;
    src: local("HelveticaNeueBold"), local("HelveticaNeue-Bold"), local("Helvetica Neue Bold"), local("HelveticaBold"), local("Helvetica-Bold"), local("Helvetica Bold"), local("Arial Bold");
}

@font-face {
    font-family: MySansSerif;
    font-weight: 900;
    font-display: auto;
    src: local("HelveticaNeueBlack"), local("HelveticaNeue-Black"), local("Helvetica Neue Black"), local("HelveticaBlack"), local("Helvetica-Black"), local("Helvetica Black"), local("Arial Black");
}

@font-face {
    font-family: "YakuHanJP";
    font-style: normal;
    font-weight: 500;
    src: url(https://assign-navi.jp/assets/font/YakuHanJP-Medium.woff) format("woff");
}

@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');
}

.selected-btn {
    background-color: #1072e9 !important;
    color: white !important;
}

.selected-btn:hover {
    background-color: #1072e9 !important;
    /* Giữ nguyên màu khi hover */
    color: #fff !important;
    opacity: 0.7 !important;
}

.info-item {
    display: flex;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item a {
    color: #1072e9 !important;
}

.info-item a:hover {
    color: #1072e9 !important;
    text-decoration: underline !important;
}

.info-label {
    width: 80px;
    color: #666;
    font-size: 14px;
}

.info-value {
    flex: 1;
    padding-left: 10px;
}

.info-container {
    border-left: 4px solid var(--primary-color);
    padding-left: 15px;
    margin: 15px 0 25px 0;
}

.input-section {
    display: grid;
    grid-template-columns: 220px 1fr;
    gap: 20px;
}

.show-count-wrapper .show-count {
    margin-bottom: 0px !important;
}

.show-count-wrapper .custom-grey-text {
    margin-bottom: 0px !important;
}

.material-icons.md-grey {
    color: rgba(0, 0, 0, 0.38);
}

.calender-section {
    position: relative;
}

.calender-section input {
    padding-right: 40px;
    /* Để không bị icon che chữ */
}

.calender-icon {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: gray;
    /* Màu icon */
    font-size: 20px;
}

.checkbox-container {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #eee;
}

main {
    font-weight: 500 o !important;
}