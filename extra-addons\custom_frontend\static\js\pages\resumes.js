import { userInfo } from "../router/router.js";
import { createManagementBreadcrumb } from "../utils/breadcrumbHelper.js";

const Resumes = {
    template: `
        <main class="pb-3 margin-header" id="vue-app" data-v-app="" style="background-color: white;">
            ${createManagementBreadcrumb('人財管理一覧')}
            <div class="container-fluid">
                <div class="tab-container" style="display: flex; justify-content: flex-start;margin-top: 0px;">
                    <div class="tab-button" @click="navigateToOpportunities">案件管理</div>
                    <div class="tab-button active" style="margin-left: 0.2%;">人財管理</div>
                </div>
            </div>

            <div style="margin-top: -2.5%;">
                <div class="container-fluid grabient pt-5">
                <div class="row">
                    <div :class="['d-md-block', 'col-12', 'col-md-4', isSearchOpen ? 'active' : 'd-none']" id="side-search">
                        <div class="card py-4 side-card" style="height: fit-content; width: 101%;">
                                <input name="utf8" type="hidden" value="✓" autocomplete="off">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="mx-auto mb-5"><input class="form-control" placeholder="フリーキーワード"
                                                                            autocomplete="off" id="free_keyword_field"
                                                                            type="text"
                                                                            v-model="filterCriteria.searchQuery"
                                                                            name="resume_manage_condition[free_keyword]">
                                            </div>
                                            <div class="mx-auto mt-0 mb-3">
                                                <label class="font-middle mb-3" for="">案件ステータス</label>
                                                <div class="selecting-form row px-3 with-title">
                                                    <input type="hidden" name="resumes_manage_condition[resume_visibility][]" value="">
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input class="custom-control-input "
                                                            id="resume_visibility_field_condition_0"
                                                            type="checkbox" value="work_available"
                                                            v-model="filterCriteria.selectedStatus"
                                                            name="resumes_manage_condition[resume_visibility][]">
                                                        <label id="resume_visibility_field_label_0"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="resume_visibility_field_condition_0">即日可</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input class="custom-control-input "
                                                            id="resume_visibility_field_condition_1"
                                                            type="checkbox" value="will_be_available"
                                                            v-model="filterCriteria.selectedStatus"
                                                            name="resumes_manage_condition[resume_visibility][]">
                                                        <label
                                                            id="resume_visibility_field_label_0"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="resume_visibility_field_condition_1">今後可</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input class="custom-control-input "
                                                            id="resume_visibility_field_condition_2"
                                                            type="checkbox" value="depends_on_opportunities"
                                                            v-model="filterCriteria.selectedStatus"
                                                            name="resumes_manage_condition[resume_visibility][]">
                                                        <label
                                                            id="resume_visibility_field_label_2"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="resume_visibility_field_condition_2">要相談</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input class="custom-control-input "
                                                            id="resume_visibility_field_condition_3"
                                                            type="checkbox" value="not_corresponding"
                                                            v-model="filterCriteria.selectedStatus"
                                                            name="resumes_manage_condition[resume_visibility][]">
                                                        <label
                                                            id="resume_visibility_field_label_3"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            for="resume_visibility_field_condition_3">対応不可</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mx-auto mt-0 mb-3">
                                                <label class="font-middle mb-3"><span>人財の公開設定</span></label>
                                                <div class="selecting-form row px-3 with-title">
                                                    <input value="false" autocomplete="off" type="hidden"
                                                        name="opportunity_manage_condition[match_status]"
                                                        id="resumes_manage_condition_match_status">
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input
                                                            id="match_status_field_0" class="custom-control-input " type="checkbox"
                                                            value="public"
                                                            v-model="filterCriteria.selectedVisibility"
                                                            name="opportunity_manage_condition[match_status]">
                                                        <label
                                                            id="match_status_field_label" class="custom-control-label anavi-select-label mb-3"
                                                            for="match_status_field_0">公開</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input
                                                            id="match_status_field_1" class="custom-control-input " type="checkbox"
                                                            value="limited"
                                                            v-model="filterCriteria.selectedVisibility"
                                                            name="opportunity_manage_condition[match_status]">
                                                        <label
                                                            id="match_status_field_label" class="custom-control-label anavi-select-label mb-3"
                                                            for="match_status_field_1">ブックマーク先のみに公開</label>
                                                    </div>
                                                    <div class="custom-control custom-checkbox form-check pl-4 col-6 col-md-12 ml-md-3">
                                                        <input
                                                            id="match_status_field_2" class="custom-control-input " type="checkbox"
                                                            value="private"
                                                            v-model="filterCriteria.selectedVisibility"
                                                            name="opportunity_manage_condition[match_status]">
                                                        <label
                                                            id="match_status_field_label" class="custom-control-label anavi-select-label mb-3"
                                                            for="match_status_field_2">非公開</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-center d-none d-md-block">
                                        <button name="save_button" @click="applyFilters" class="btn btn-default font-middle waves-effect waves-light">検索</button>
                                    </div>
                                </div>
                                <div class="container-fluid blue-grey lighten-4" :class="['search-fixed-btn', isSearchOpen && isMobile ? 'd-block' : 'd-none']">
                                    <div class="row py-5">
                                        <div class="col-12">
                                            <button name="button"
                                                    class="btn btn-default btn-block font-middle mb-3 submit-btn waves-effect waves-light"
                                                    @click="applyFilters">検索
                                            </button>
                                        </div>
                                        <div class="col-12">
                                            <div
                                                class="btn btn-blue-grey btn-block font-middle search-toggle waves-effect waves-light" @click="closeSearchMenu">
                                                閉じる
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>
                    <div :class="['col-12', 'col-md-8', isSearchOpen ? 'd-none' : 'd-block']" style="margin-top: -94px;">
                        <div class="row">
                            <div class="col-12 d-block d-md-none mb-4"><a
                                class="btn btn-default search-toggle m-0 waves-effect waves-light" @click="openSearchMenu" href="javascript:void(0);">検索条件</a>
                            </div>
                        </div>
                        <div class="d-flex align-items-center my-4">
                            <div class="mr-auto">
                                <p class="font-middle mb-0">
                                    <span class="green-text">{{ filteredResumes.length }}件</span>ヒットしました
                                </p>
                            </div>
                            <div class="mb-n3">
                                <div v-dropdown="{ modelValue: '', listType: 'optionFilterResume'}" @selected="sortValue = $event.detail"></div>
                                <input type="hidden" v-model="sortValue"/>
                            </div>
                        </div>
                        <div class="row d-none d-md-block">
                            <div class="col-12">
                                <div aria-hidden="true" aria-labelledby="modal-49040_modal" class="modal" id="modal-49040" role="dialog" tabindex="-1">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title w-100">メモを保存</h4>
                                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                                    <span aria-hidden="true">
                                                        <i class="material-icons md-dark mb-36">clear</i>
                                                    </span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form class="edit_resume" id="edit_resume_49040" action="manage_update_memo_resumes_path" accept-charset="UTF-8" data-remote="true" method="post"></form>
                                                    <input name="utf8" type="hidden" autocomplete="off" value="✓">
                                                    <input type="hidden" name="_method" autocomplete="off" value="patch">
                                                <div class="input_memo_field">
                                                    <div class="mb-4">
                                                        <div class="mx-auto mb-5">
                                                            <div class="">
                                                                <label class="font-middle mb-3" for="">社内メモ</label>
                                                            </div>
                                                            <textarea rows="5" class="form-control" autocomplete="off" id="memo_field" name="resume[memo]"></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="text-center">
                                                        <a class="btn btn-default btn-lg font-middle memo-btn memo_save_btn waves-effect waves-light" data-dismiss="modal" id="49040" type="submit">保存</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <table class="table white d-less-than-md-none">
                                    <thead class="bg-grey-6 white-text">
                                        <tr>
                                            <th class="vertical-middle">人財</th>
                                            <th class="vertical-middle text-center">稼働可能状況</th>
                                            <th class="vertical-middle text-center">メモ</th>
                                            <th class="vertical-middle text-center">更新 / 登録</th>
                                        </tr>
                                    </thead>
                                    <tbody v-for="resume in paginatedSortedResumes" :key="resume.id" v-if="paginatedSortedResumes.length">
                                        <tr>
                                            <td class="th-lg vertical-middle">
                                                <a :href="'/resumes/' + resume.id + '/manage/edit'">
                                                    <h5 class="font-middle mb-2">
                                                        <span class="d-inline-block mr-3">{{resume.initial_name}}.（{{resume.user_name}}）さん</span>
                                                        <span>{{resume.gender === "male" ? "男" : "女"}}性　{{ calculateAge(resume.birthday) }}歳</span>
                                                    </h5>
                                                </a>
                                                <div>
                                                    <div class="d-inline-block mr-4">
                                                        <i class="material-icons vertical-middle custom-grey-5-text font-extralarge font-weight-bold mr-2">remove_red_eye</i>
                                                        <span>{{ resume.view }}</span>
                                                    </div>
                                                    <div class="d-inline-block mr-4">
                                                        <span class="custom-grey-5-text font-small font-weight-bold mr-2">申込み</span>
                                                        <span>{{ resume.apply_number }}</span>
                                                    </div>
                                                    <div class="d-inline-block">
                                                        <span class="custom-grey-5-text font-small font-weight-bold mr-2">スカウト受領</span>
                                                        <span>{{ resume.scout_number }}</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="th-lg vertical-middle text-center">
                                                <div class="text-center">
                                                    <span v-if="resume.status === 'work_available'" class="text-green">即日可</span>
                                                    <span v-else-if="resume.status === 'will_be_available'" class="text-blue">今後可 <br>
                                                         （{{ new Date(resume.date_period).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }}~）</span>
                                                    <span v-else-if="resume.status === 'depends_on_opportunities'" class="text-orange">要相談</span>
                                                    <span v-else-if="resume.status === 'not_corresponding'" class="text-red">対応不可</span>
                                                    <span v-else>不明</span> <br>
                                                </div>
                                                <div class="text-center">
                                                    <span class="d-inline-block badge-pill font-small"
                                                        :class="resume.resume_visibility === 'public' ? 'pink lighten-2'
                                                                : resume.resume_visibility === 'limited' ? 'yellow darken-3'
                                                                : 'blue-grey'">
                                                        {{ resume.resume_visibility === 'public' ? '公開'
                                                            : resume.resume_visibility === 'limited' ? 'ブックマーク先のみに公開'
                                                            : '非公開' }}
                                                        </span>
                                                </div>
                                            </td>
                                            <td class="th-extrasm vertical-middle text-center">
                                                <a class="mdb-modal-form" :data-target="'#modal-' + resume.id" data-toggle="modal" href="">
                                                    <i class="material-icons font-extralarge">sticky_note_2</i>
                                                </a>
                                                <span class="d-block" id="'memo_field_' + {{ resume.id}} + '_table'"></span>
                                            </td>
                                            <td class="th-lg vertical-middle text-center"> {{ new Date(resume.updated_at).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }} <br>
                                                <div class="font-small">（{{ new Date(resume.created_at).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }}）</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- Pagination -->
                                <nav aria-label="pagination example">
                                    <nav role="navigation" class="pagination">
                                        <ul class="pagination pageul">
                                            <li :class="['page-item', { disabled: currentPage === 1 }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)">← 前</a>
                                            </li>

                                            <li v-for="page in totalPages" :key="page" :class="['page-item', { active: currentPage === page }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                                            </li>

                                            <li :class="['page-item', { disabled: currentPage === totalPages }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)">次 →</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </nav>
                            </div>
                        </div>
                        <div class="d-md-none">
                            <div class="d-flex" v-for="resume in paginatedSortedResumes" :key="resume.id">
                                <div class="card mb-4 w-100 hoverable p-3">
                                    <div class="card-header d-block bg-white border-bottom p-0" style="height: 3em;">
                                        <a :href="'/resumes/' + resume.id + '/manage/edit/'">
                                            <h5 class="font-middle mb-2">
                                                <span class="d-inline-block mr-3">{{resume.initial_name}}.（{{resume.user_name}}）さん</span>
                                                <span>{{resume.gender === "male" ? "男" : "女"}}性　{{ calculateAge(resume.birthday) }}歳</span>
                                            </h5>
                                        </a>
                                        <div class="d-flex align-items-center">
                                            <div class="text-dark">
                                                <div class="d-inline-block mr-4">
                                                    <i class="material-icons vertical-middle custom-grey-5-text font-extralarge font-weight-bold mr-2">remove_red_eye</i>
                                                    <span>{{ resume.view }}</span>
                                                </div>
                                                <div class="d-inline-block mr-4">
                                                    <span class="custom-grey-5-text font-small font-weight-bold mr-2">申込み</span>
                                                    <span>0</span>
                                                </div>
                                                <div class="d-inline-block">
                                                    <span class="custom-grey-5-text font-small font-weight-bold mr-2">スカウト受領</span>
                                                    <span>{{ resume.scout_number }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-content opportunity_cell pt-3 px-0 pb-0" style="height: 10em;">
                                        <div class="d-flex align-items-center mb-2"><label
                                            class="custom-grey-text font-small font-weight-bold mb-0 mr-2">稼働可能状況</label>
                                            <div class="text-center">
                                                <span v-if="resume.status === 'work_available'" class="text-green">即日可</span>
                                                <span v-else-if="resume.status === 'will_be_available'" class="text-blue">今後可 <br>
                                                        （{{ new Date(resume.date_period).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }}~）</span>
                                                <span v-else-if="resume.status === 'depends_on_opportunities'" class="text-orange">要相談</span>
                                                <span v-else-if="resume.status === 'not_corresponding'" class="text-red">対応不可</span>
                                                <span v-else>不明</span> <br>
                                            </div>
                                            <div class="text-center">
                                                <span class="d-inline-block badge-pill font-small"
                                                    :class="resume.resume_visibility === 'public' ? 'pink lighten-2'
                                                            : resume.resume_visibility === 'limited' ? 'yellow darken-3'
                                                            : 'blue-grey'">
                                                    {{ resume.resume_visibility === 'public' ? '公開'
                                                        : resume.resume_visibility === 'limited' ? 'ブックマーク先のみに公開'
                                                        : '非公開' }}
                                                    </span>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center mb-2 mt-1"><label
                                            class="custom-grey-text font-small font-weight-bold mb-0 mr-2">メモ</label>
                                            <a class="mdb-modal-form" :data-target="'#modal-' + resume.id" data-toggle="modal" href="">
                                                <i class="material-icons font-extralarge">sticky_note_2</i>
                                            </a>
                                            <span class="d-block" id="'memo_field_' + {{ resume.id}} + '_table'"></span>
                                        </div>
                                        <div class="d-flex align-items-center mt-1"><label
                                            class="custom-grey-text font-small font-weight-bold mb-0 mr-2">更新（登録）</label><span>{{ new Date(resume.updated_at).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }}</span><span>（{{ new Date(resume.created_at).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }}）</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="">
                            <div class="d-flex" v-for="resume in paginatedSortedResumes" :key="resume.id" v-if="paginatedSortedResumes.length">
                                <div aria-hidden="true" aria-labelledby="modal-49040_modal" class="modal" :id="'modal-' + resume.id" role="dialog" tabindex="-1">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title w-100">メモを保存</h4>
                                                <button aria-label="Close" class="close" data-dismiss="modal" type="button">
                                                    <span aria-hidden="true">
                                                        <i class="material-icons md-dark mb-36">clear</i>
                                                    </span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form class="edit_resume" :id="'edit_resume_' + resume.id" action="manage_update_memo_resumes_path" accept-charset="UTF-8" data-remote="true" method="post">
                                                    <input name="utf8" type="hidden" autocomplete="off" value="✓">
                                                    <input type="hidden" name="_method" autocomplete="off" value="patch">
                                                    <div class="input_memo_field">
                                                        <div class="mb-4">
                                                            <div class="mx-auto mb-5">
                                                                <div class="">
                                                                    <label class="font-middle mb-3" for="">社内メモ</label>
                                                                </div>
                                                                <textarea rows="5" class="form-control" autocomplete="off" :id="'memo_field_' + resume.id" name="resume[memo]" :value="resume.memo || ''"></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="text-center">
                                                            <a class="btn btn-default btn-lg font-middle memo-btn memo_save_btn waves-effect waves-light" data-dismiss="modal" :id="resume.id" type="submit">保存</a>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mx-auto"></div>
                    </div>
                </div>
            </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/resumes.css"/>
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css">
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/data_tabs.css">
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/resumes/manage/index.css">
    `,
    data() {
        return {
            UserId: "",
            selectedText: "",
            Resumes: [],
            filteredResumes: [],
            errMsg: "",
            sortValue: "",
            filterCriteria: {
                searchQuery: "",
                selectedStatus: [],
                selectedVisibility: [],
            },
            sortMapping: {
                "更新日": "updated_at",      // Ngày cập nhật
                "登録日": "created_at",      // Ngày đăng ký
                "閲覧数": "view",           // Lượt xem
                "スカウト受領数": "scout_number" // Số lần nhận scout
            },
            currentPage: 1,
            itemsPerPage: 20,
            isSearchOpen: false,
            isMobile: window.innerWidth < 768
        };
    },

    mounted() {
        this.UserId = userInfo ? userInfo.user_id : null;
        this.get_resumes();
        window.addEventListener('resize', this.handleResize);

        // Add event listener for backdrop click to close search modal
        document.addEventListener('click', (event) => {
            const sideSearch = document.getElementById('side-search');
            if (sideSearch && sideSearch.classList.contains('active')) {
                // Check if click is on backdrop (not on the card content)
                if (event.target === sideSearch) {
                    this.closeSearchMenu();
                }
            }
        });

        // Add event listener for memo save buttons
        this.$nextTick(() => {
            document.addEventListener('click', (event) => {
                if (event.target.classList.contains('memo_save_btn')) {
                    event.preventDefault(); // Ngăn chặn form submit
                    event.stopPropagation(); // Ngăn chặn event bubbling

                    console.log('Memo save button clicked:', event.target.id);
                    const resumeId = event.target.id.replace('mobile_', ''); // Remove mobile_ prefix if exists
                    const memoField = document.getElementById(`memo_field_${resumeId}`) ||
                                    document.getElementById(`memo_field_mobile_${resumeId}`);

                    console.log('Memo field found:', memoField);
                    if (memoField) {
                        const memo = memoField.value;
                        console.log('Saving memo:', memo, 'for resume:', resumeId);
                        this.saveMemo(resumeId, memo);
                    }
                }
            });
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
    },

    computed: {
        totalPages() {
            return Math.ceil(this.filteredResumes.length / this.itemsPerPage);
        },

        sortedResumes() {
            const sortKey = this.sortMapping[this.sortValue] || "updated_at"; // Giá trị mặc định
            return [...this.filteredResumes].sort((a, b) => {
                const valA = sortKey.includes("_at") ? new Date(a[sortKey]) : a[sortKey] || 0;
                const valB = sortKey.includes("_at") ? new Date(b[sortKey]) : b[sortKey] || 0;
                return valB - valA; // Sắp xếp giảm dần (mới nhất trước)
            });
        },

        paginatedSortedResumes() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            return this.sortedResumes.slice(start, start + this.itemsPerPage);
        }
    },

    methods: {
        navigateToOpportunities() {
            window.location.href = '/opportunities/manage/index';
        },

        calculateAge(birthday) {
            if (!birthday) return "N/A"; // Nếu không có ngày sinh, trả về "N/A"

            // Xử lý chuỗi có dạng "YYYY-MM-DD HH:MM:SS"
            if (typeof birthday === "string") {
                birthday = birthday.split(" ")[0]; // Lấy phần "YYYY-MM-DD", bỏ giờ
            }

            const birthDate = new Date(birthday);
            if (isNaN(birthDate.getTime())) return "N/A"; // Kiểm tra nếu ngày không hợp lệ

            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();

            // Kiểm tra nếu chưa tới sinh nhật trong năm nay thì trừ đi 1 tuổi
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            return age;
        },

        async get_resumes() {
            const response = await fetch(`/api/resumes?user_id=${this.UserId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })


            if (!response.ok) {
                throw new Error('Failed to fetch resumes')
            }

            const data = await response.json()
            //console.log(data);
            if(data.success) {
                //console.log(`${data.message}: ${data.resumes}`)
                this.Resumes = data.resumes;
                this.filteredResumes = [...this.Resumes];
            } else {
                this.errMsg = data.message;
            }
        },

        getSortValue(resume) {
            const sortKey = this.sortMapping[this.sortValue] || "updated_at";
            return resume[sortKey];
        },

        applyFilters() {
            console.log("Search Query:", this.filterCriteria.searchQuery);
            console.log("Selected Status:", this.filterCriteria.selectedStatus);
            console.log("Selected Visibility:", this.filterCriteria.selectedVisibility);
            console.log("Resume Data:", this.Resumes);


            this.filteredResumes = this.Resumes.filter(resume => {
                // Lọc theo từ khóa tìm kiếm
                const matchesSearch = this.filterCriteria.searchQuery.trim() !== ""
                    ? (resume.name?.toLowerCase() || "").includes(this.filterCriteria.searchQuery.toLowerCase())
                    : true;

                // Kiểm tra Status (work_available, will_be_available, depends_on_opportunities, not_corresponding)
                const matchesStatus = this.filterCriteria.selectedStatus.length > 0
                    ? this.filterCriteria.selectedStatus.includes(String(resume.status)) // Chuyển status về dạng chuỗi để so sánh
                    : true;

                // Kiểm tra Resume Visibility (public, private, limited)
                const matchesVisibility = this.filterCriteria.selectedVisibility.length > 0
                    ? this.filterCriteria.selectedVisibility.includes(resume.resume_visibility) // So sánh trực tiếp với chuỗi
                    : true;

                const isMatch = matchesSearch && matchesStatus && matchesVisibility;

                return isMatch;
            });

            // Reset về trang đầu tiên khi lọc
            this.currentPage = 1;

            // Đóng search menu trên mobile sau khi search
            if (this.isMobile && this.isSearchOpen) {
                this.closeSearchMenu();
            }
        },

        changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
              this.currentPage = page;
            }
        },
        openSearchMenu() {
            this.isSearchOpen = true;
            const footer = document.querySelector('footer');
            if (footer) {
            footer.classList.add('d-none');
            }
        },
        closeSearchMenu() {
            this.isSearchOpen = false;
            const footer = document.querySelector('footer');
            if (footer) {
            footer.classList.remove('d-none');
            }
        },
        handleResize() {
            this.isMobile = window.innerWidth < 768;
            // Nếu resize lên desktop thì tự đóng menu
            if (!this.isMobile && this.isSearchOpen) {
              this.closeSearchMenu();
            }
        },

        async saveMemo(resumeId, memo) {
            try {
                const response = await fetch('/api/resume_memo_update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': odoo.csrf_token
                    },
                    body: JSON.stringify({
                        resume_id: resumeId,
                        memo: memo
                    })
                });

                const result = await response.json();
                if (result.success) {
                    // Update the memo in the local data
                    const resume = this.Resumes.find(res => res.id == resumeId);
                    if (resume) {
                        resume.memo = memo;
                    }

                    // Update the filtered data as well
                    const filteredResume = this.filteredResumes.find(res => res.id == resumeId);
                    if (filteredResume) {
                        filteredResume.memo = memo;
                    }

                    window.toastr.success('メモが保存されました。');
                } else {
                    window.toastr.error('メモの保存に失敗しました。');
                }
            } catch (error) {
                console.error('Error saving memo:', error);
                window.toastr.error('メモの保存に失敗しました。');
            }
        }
    }
};


export default Resumes;