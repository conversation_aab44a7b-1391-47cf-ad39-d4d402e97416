from odoo import http
from odoo.http import request
from .common import send_email_from_template

class ContactController(http.Controller):
    @http.route('/api/contact', type='json', auth='public', methods=['POST'], csrf=False)
    def contact_form(self):
        data = request.httprequest.get_json(silent=True)

        reference_code = data.get('reference_code')
        message = data.get('message')
        company = data.get('company')
        lastname = data.get('lastname')
        firstname = data.get('firstname')
        email = data.get('email')
        phone = data.get('phone')

        if not message or not lastname or not firstname or not email or not phone or not reference_code:
            return {'success': False, 'message': 'Missing required fields'}

        # Sử dụng template từ database
        context_data = {
            'company': company or 'なし',
            'lastname': lastname,
            'firstname': firstname,
            'email': email,
            'phone': phone,
            'message': message,
            'user_email': "<EMAIL>"  # Email admin
        }
        
        result = send_email_from_template('Contact: Form', context_data)
        
        if not result['success']:
            return {'success': False, 'message': result['message']}

        return {'success': True, 'message': '<PERSON><PERSON><PERSON> hệ đã được g<PERSON>i thành công'}

    @http.route('/api/create_contact', type='json', auth='public', methods=['POST'], csrf=False)
    def create_contact(self):
        data = request.httprequest.get_json(silent=True)

        referral_code = data.get('referral_code')
        content = data.get('content')

        if not referral_code or not content:
            return {'success': False, 'message': 'Missing required fields'}

        try:
            contact = request.env['vit.contact'].sudo().create({
                'referral_code': referral_code,
                'content': content,
                'status': 0
            })
            return {'success': True, 'message': 'Contact created successfully', 'data': contact.id}
        except Exception as e:
            return {'success': False, 'message': f'Error creating contact: {str(e)}'}