from odoo import http
from odoo.http import request, Response
import json

class GuidelineVideoController(http.Controller):

    @http.route('/guideline/video', type='http', auth='public', methods=['GET'])
    def get_video(self):
        video = request.env['vit.video'].sudo().search([], limit=1)
        if not video:
            return Response(
                json.dumps({'error': 'No video found'}),
                content_type='application/json'
            )

        video_url = f'/web/content/vit.video/{video.id}/video_file'
        
        data = {
            'name': video.name,
            'url': video_url,
            'mimetype': video.mimetype
        }
        return Response(
            json.dumps(data),
            content_type='application/json'
        )