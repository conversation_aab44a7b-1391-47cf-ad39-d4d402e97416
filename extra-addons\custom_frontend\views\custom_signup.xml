<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="custom_signup_inherit" inherit_id="auth_signup.signup">
            <xpath expr="//form" position="replace">

                <div class="bg-light pt-3 pt-lg-5 pb-0 pb-lg-5 px-3 bgCurve4" >
                    <div class="container-customer pb-4" style="padding-top:50px 0;">
                        <div class="registerCard card">
                            <div class="cardTabs">
                                <ul class="nav nav-tabs" id="registerTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <a href="/web/signup" class="nav-link active" id="register-tab">SIGN UP</a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a href="/web/login" class="nav-link" id="login-tab">LOGIN</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="tab-content" id="registerTabContent">
                                <div class="tab-pane fade show active" id="register-tab-pane" role="tabpanel" aria-labelledby="register-tab" tabindex="0">
                                    <div class="card-body px-1 px-lg-4">
                                        <div class="formBox px-lg-2 pb-lg-3">
                                            <form id="registerForm" action="/web/signup/submit" method="POST" class="p-0 m-0 needs-validation" novalidate="">
                                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"  />
                                                <input type="hidden" name="next" value="after-registration" />

                                                <div class="mb-3 ms-2">
                                                    Already have an account? <a href="/web/login" class="loginLink text-nowrap">LOGIN HERE</a>
                                                </div>

                                                <div class="row g-3 mb-3">
                                                    <div class="col-md">
                                                        <label for="name" class="form-label">Contact First Name*</label>
                                                        <input id="name" type="text" class="form-control" name="name" placeholder="Enter your first name" required="true" />
                                                    </div>
                                                    <div class="col-md">
                                                        <label for="lastname" class="form-label">Contact Last Name*</label>
                                                        <input id="lastname" type="text" class="form-control" name="lastname" placeholder="Enter your last name" required="true" />
                                                    </div>
                                                    <div class="col-md">
                                                        <label for="phone" class="form-label">Contact Phone*</label>
                                                        <input name="phone" id="phone" class="form-control" placeholder="************" required="true" />
                                                    </div>
                                                </div>

                                                <div class="row g-3 mb-3">
                                                    <div class="col-md-12">
                                                        <label for="email" class="form-label">Email*</label>
                                                        <input id="email" type="email" class="form-control" name="email" placeholder="<EMAIL>" required="true" />
                                                    </div>

                                                    <div class="col-md-12">
                                                        <label for="password" class="form-label">Select Password*</label>
                                                        <input id="password" type="password" class="form-control" name="password" placeholder="Type in your password" required="true" />
                                                    </div>

                                                    <div class="col-md-12">
                                                        <label for="password-confirm" class="form-label">Confirm Password*</label>
                                                        <input id="password-confirm" type="password" class="form-control" name="password_confirmation" placeholder="Retype the password you selected" required="true" />
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <div class="form-check pb-3 pt-2">
                                                        <input name="terms1" class="form-check-input" type="checkbox" id="termsCheck1" required="true" />
                                                        <label class="form-check-label" for="termsCheck1">
                                                            I understand that Second Opinions' services are subject to their <a href="https://www.secondopinions.com/terms-of-use.html" target="_blank">Terms of Use</a>
                                                        </label>
                                                        <div class="invalid-feedback">
                                                            You have to select the check box to continue
                                                        </div>
                                                    </div>

                                                    <div class="form-check">
                                                        <input name="terms2" class="form-check-input" type="checkbox" id="termsCheck2" required="true" />
                                                        <label class="form-check-label" for="termsCheck2">
                                                            I understand that my information will be handled in accordance with Second Opinion's <a href="https://www.secondopinions.com/privacy-policy.html" target="_blank">Privacy Policy</a>
                                                        </label>
                                                        <div class="invalid-feedback">
                                                            You have to select the check box to continue
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-12 text-end d-grid d-lg-flex align-items-end justify-content-lg-between">
                                                    <div class="pb-4 pb-lg-0">
                                                        <div class="g-recaptcha" data-sitekey="6LckNQETAAAAAIi1kjz8Q0QkwSrJLi17-751JUfy" data-size="normal" data-theme="light" id="recaptcha-element"></div>
                                                    </div>
                                                    <script>
                                                    async function fetchData() {
                                                                try {
                                                                    const response = await fetch('/api/clear', {
                                                                        method: 'GET',
                                                                    });

                                                                    if (response.ok) {
                                                                        console.log('Request successful!');
                                                                    } else {
                                                                        console.error('Request failed with status:', response.status);
                                                                    }
                                                                } catch (error) {
                                                                    console.error('An error occurred:', error);
                                                                }
                                                            }
                                                    </script>
                                                    <t t-if="request.session.get('signup_error')">
                                                        <script type="text/javascript">
                                                            alert('<t t-esc="request.session.get('signup_error')"/>');
                                                            fetchData(); 
                                                        </script>
                                                    </t>
                                                    <t t-if="request.session.get('signup_success')">
                                                        <script type="text/javascript">
                                                            alert('<t t-esc="request.session.get('signup_success')"/>');
                                                            fetchData();
                                                            window.location.href = "/web/login";
                                                        </script>
                                                    </t>
                                                    <button type="submit" class="btn btn-primary px-5">CONTINUE</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <link rel="stylesheet" href="/custom_frontend/static/css/signup.css" />
            </xpath>
        </template>
    </data>
</odoo>
