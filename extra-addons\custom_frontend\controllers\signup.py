from odoo import http
from odoo.http import request
import json
import re
from datetime import datetime, timedelta
from . import common as global_common
from .common import send_email_from_template
import random
import secrets
import os
import logging
import hashlib
import base64
_logger = logging.getLogger(__name__)

class SignupController(http.Controller):

    @http.route('/api/signup', type='json', auth='public', methods=['POST'])
    def signup(self):
        data = request.httprequest.get_json(silent=True)

        last_name  = data.get('last_name')
        first_name = data.get('first_name')
        display_name = data.get('display_name')
        # refer_number = data.get('refer_number')
        email = data.get('email')
        password = data.get('password')
        company_name = data.get('company_name')
        company_site_url = data.get('company_site_url')
        tel = data.get('tel')
        company_establishment = data.get('company_establishment')
        company_number_of_employees = data.get('company_number_of_employees')
        company_capital = data.get('company_capital')
        company_dispatch_license = data.get('company_dispatch_license')
        purpose_of_use = data.get('purpose_of_use')
        purpose_of_use_note = data.get('purpose_of_use_note')
        learn_about_this_site = data.get('learn_about_this_site')
        learn_about_this_site_note = data.get('learn_about_this_site_note')
        terms_of_service = data.get('terms_of_service')

        required_fields = [
            last_name, first_name, # display_name, refer_number,
            email, password, company_name, company_site_url, tel, company_establishment,
            company_number_of_employees, company_capital, company_dispatch_license,
            purpose_of_use, learn_about_this_site
        ]
        if any(field in [None, ""] for field in required_fields):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE,}

        if (learn_about_this_site == 'others' and learn_about_this_site_note == '') or ('other_purposes' in purpose_of_use and purpose_of_use_note == ''):
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}

        if terms_of_service == False:
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE}


        # validate email
        if self.is_valid_email(email) is False:
            return {'success': False, 'message': global_common.CREAT_TOAST_ERROR_MESSAGE,
                    'email_errorMessage': global_common.SIGNUP_EMAIL_ERROR_MESSAGE}

        # check if email already exists
        db_old_user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email)], limit=1)
        db_old_username = db_old_user.username

        if db_old_username:
            return {'success': False, 'message': global_common.SIGNUP_EMAIL_EXIST_ERROR_MESSAGE}

        # create new company
        code = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        db_company = request.env['vit.companies'].sudo().create({
            'name': company_name,
            'company_site_url': company_site_url,
            'established_year': int(company_establishment) if company_establishment else 0,
            'employee_count': int(company_number_of_employees) if company_number_of_employees else 0,
            'charter_capital': int(company_capital) if company_capital else 0,
            'referral_code': code,
            'email': email,
            'created_at': datetime.now(),
            'has_dispatch_license': company_dispatch_license == 'true',
            # TODO: update company attributes
        })

        if not db_company:
            return {'success': False, 'message': 'Company creation failed'}

        # create new user
        # Băm mật khẩu trước khi lưu vào cơ sở dữ liệu
        hashed_password = self._hash_password(password)
        db_user = request.env['vit.users'].with_context(active_test=False).sudo().create({
            'username': last_name + ' ' + first_name,
            'email': email,
            'password': hashed_password,
            'phone': tel,
            'purpose_of_use': purpose_of_use,
            'purpose_of_use_note': purpose_of_use_note,
            'learn_about_this_site': learn_about_this_site,
            'learn_about_this_site_note': learn_about_this_site_note,
            'company_id': db_company.id,
            'created_at': datetime.now(),
        })

        if db_user:
            try:
                db_company.sudo().write({
                    'created_by': db_user.id,
                })

                # Khởi tạo giá trị mặc định
                receive_opportunities = False
                receive_resumes = False

                # Kiểm tra purpose_of_use
                if db_user.purpose_of_use:
                    purpose_list = db_user.purpose_of_use.split(',')

                    # Kiểm tra các giá trị trong purpose_of_use
                    if 'find_opportunities' in purpose_list:
                        receive_opportunities = True

                    if 'find_project_staffs' in purpose_list:
                        receive_resumes = True

                    if 'other_purposes' in purpose_list:
                        receive_opportunities = True
                        receive_resumes = True

                # Tạo cài đặt với các giá trị đã xác định
                new_setting = request.env['vit.setting'].sudo().create({
                    'user_id': db_user.id,
                    'receive_mail': True,  # Luôn bật email mặc định
                    'receive_opportunities': receive_opportunities,
                    'receive_resumes': receive_resumes,
                })
            except Exception as e:
                return {'success': False, 'message': 'Failed to create setting', 'details': str(e)}
            return {
            'success': True,
            'message': 'Signup successful',
            'isloggedin': False
            }

    def is_valid_email(self, email):
            pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
            return re.match(pattern, email) is not None

    @http.route('/api/check_email_exists', type='json', auth="public", methods=['POST'])
    def check_email_exists(self):
        data = request.httprequest.get_json(silent=True)
        email = data.get('email')

        if not email:
            return {'success': False, 'message': 'Missing email'}

        # Kiểm tra định dạng email
        if not self.is_valid_email(email):
            return {'success': False, 'message': 'Invalid email format', 'exists': False}

        # Kiểm tra xem email đã tồn tại trong hệ thống chưa
        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email)], limit=1)

        return {
            'success': True,
            'exists': bool(user),
            'message': 'Email already exists' if user else 'Email is available'
        }

    @http.route('/api/email_active', type='json', auth="public", methods=['POST'])
    def email_active(self):
        data = request.httprequest.get_json(silent=True)
        email_to = data.get('email')

        if not email_to:
            return {'success': False, 'message': 'Missing email'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('email', '=', email_to)], limit=1)
        if not user:
            return {'success': False, 'message': 'Email not found'}

        active_token = secrets.token_urlsafe(32)
        expiry_time = datetime.now() + timedelta(minutes=15)

        user.sudo().write({
            'token': active_token,
            'token_expiry': expiry_time,
        })

        reset_link = f"{request.httprequest.host_url.replace('http://', 'https://', 1)}account/verify_active?token={active_token}"

        # Sử dụng template từ database
        
        context_data = {
            'company_name': user.company_id.name if user.company_id else '',
            'user_name': user.username,
            'reset_link': reset_link,
            'user_email': email_to
        }
        
        result = send_email_from_template('Signup: Temporary Activation', context_data)
        
        if not result['success']:
            return {'success': False, 'message': result['message']}

        return {'success': True, 'message': 'Email sent successfully'}

    @http.route('/api/active_account', type='json', auth="public", methods=['POST'])
    def active_account(self):
        data = request.httprequest.get_json(silent=True)
        token = data.get('token')

        if not token:
            return {'success': False, 'message': 'Missing token'}

        user = request.env['vit.users'].with_context(active_test=False).sudo().search([('token', '=', token)], limit=1)
        if not user or not user.token_expiry or user.token_expiry < datetime.now():
            return {'success': False, 'message': 'Invalid or expired token'}

        user.sudo().write({
            'active': True,
            'token': None,
            'token_expiry': None,
        })

        # Gửi email thông báo kích hoạt thành công
        
        context_data = {
            'company_name': user.company_id.name if user.company_id else '',
            'user_name': user.username,
            'login_url': f"{request.httprequest.host_url.replace('http://', 'https://', 1)}login",
            'user_email': user.email
        }
        
        result = send_email_from_template('Signup: Activation Success', context_data)
        
        if not result['success']:
            _logger.warning(f"Failed to send activation success email: {result['message']}")

        return {'success': True, 'message': 'Account activated successfully'}

    def _hash_password(self, password, salt=None):
        #Sử dụng PBKDF2 để băm mật khẩu - không cần thư viện ngoài
        if not salt:
            # Tạo salt 16 bytes
            salt = secrets.token_bytes(16)
        # Sử dụng PBKDF2 với thuật toán SHA-256, 100000 vòng lặp
        # và độ dài key đầu ra 32 bytes
        key = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt,
            100000,  # số vòng lặp, càng cao càng an toàn nhưng càng chậm
            dklen=32  # độ dài key
        )

        # Mã hóa salt và key để có thể lưu vào DB
        salt_b64 = base64.b64encode(salt).decode('utf-8')
        key_b64 = base64.b64encode(key).decode('utf-8')

        # Trả về chuỗi có định dạng: algorithm$iterations$salt$key
        password_hash = f"pbkdf2_sha256$100000${salt_b64}${key_b64}"
        return password_hash

    @http.route('/api/delete_account', type='json', auth="public", methods=['POST'], csrf=False)
    def delete_account(self):
        try:
            data = request.httprequest.get_json(silent=True)
            user_id = data.get('user_id')

            user = request.env['vit.users'].sudo().search([('id', '=', user_id)], limit=1)
            if not user:
                return {'success': False, 'message': 'User not found'}

            # Gửi email thông báo xóa tài khoản
            
            context_data = {
                'company_name': user.company_id.name if user.company_id else '',
                'user_name': user.username,
                'user_email': user.email
            }
            
            result = send_email_from_template('Signup: Delete Account', context_data)
            
            if not result['success']:
                _logger.warning(f"Failed to send delete account email: {result['message']}")

            # Thực hiện xóa user
            user.unlink()
            return {'success': True, 'message': 'Account deleted successfully'}

        except Exception as e:
            return {'success': False, 'message': f'Failed to delete account: {str(e)}'}

