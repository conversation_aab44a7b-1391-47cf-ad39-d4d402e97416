import { createBreadcrumb } from "../../../utils/breadcrumbHelper.js";

const file_upload = {
   template: `
     <main class="margin-header" id="upload-container">
       <div id="breadcrumb-container"></div>
       <div class="container-fluid pt-3">
         <div class="row">
           <div class="col-12">
             <div class="row">
               <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                  <div class="card px-5 pt-5">
                     <div class="alert alert-warning text-center" role="alert">
                        案件への応募には、スキルシート（履歴書・経歴書）のアップロードが必要です。
                     </div>
                     <p class="error-text text-center">
                        ※登録するスキルシートはweb上で多くの登録企業に閲覧されます。個人情報は本名→イニシャルにするなど、マスキングしてください。
                     </p>
                     <div class="mb-5">
                        <form class="dropzone dz-clickable" id="upload-file" enctype="multipart/form-data" @click="triggerFileInput">
                           <div class="dz-default dz-message" id="upload-area" style="cursor: pointer;" v-if="!existingFile">
                              <i class="material-icons md-grey md-36">cloud_upload</i>
                              <span class="font-weight-bold grey-text mt-3" style="display: block;">
                                 ここにファイルをドロップしてください。
                              </span>
                           </div>
                           <input type="file" id="file-input" ref="fileInput" style="display: none;" multiple @change="handleFileChange">
                           <div v-if="existingFile" class="dz-preview dz-image-preview dz-complete">
                              <div class="dz-image"><img :src="getFileIcon(existingFile.name)"></div>
                              <div class="dz-details">
                                 <div class="dz-size"><span data-dz-size=""><strong>{{ existingFile.size }}</strong></span></div>
                                 <div class="dz-filename"><span data-dz-name="">{{ existingFile.name }}</span></div>
                              </div>
                              <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress="" style="width: 100%;"></span></div>
                              <div class="dz-success-mark"></div>
                              <a class="dz-remove" @click="removeFile(existingFile.id, $event)">削除する</a>
                           </div>
                        </form>
                        <p class="custom-grey-text mt-3 mb-0">対応ファイル形式：PDF、DOC、DOCX、TXT（最大10MBまで）</p>
                        <p class="custom-grey-text">掲載可能なファイルは１枚のみです。</p>
                     </div>
                  </div>
               </div>
             </div>
           </div>
         </div>
       </div>
     </main>
     <link rel="stylesheet" href="/custom_frontend/static/css/resumes/manage/file_upload.css"/>
     <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
   `,
   data() {
      return {
         selectedFiles: [],
         existingFile: null,
         isDuplicatePage: false
      };
   },
   mounted() {
      this.loadExternalScript("/custom_frontend/static/js/pages/signup-extra.js");
      this.fetchExistingFile();

      // Kiểm tra xem người dùng đến từ trang duplicate hay không
      const referrer = document.referrer;
      if (referrer && referrer.includes('/duplicate')) {
         this.isDuplicatePage = true;
      } else {
         // Kiểm tra thêm từ localStorage nếu referrer không có thông tin
         const lastPage = localStorage.getItem('lastPage');
         if (lastPage && lastPage.includes('/duplicate')) {
            this.isDuplicatePage = true;
         }
      }

      // Render breadcrumb sau khi xác định isDuplicatePage
      this.$nextTick(() => {
         const id = this.$route.params.id;
         const breadcrumbItems = [
            { text: 'サービスメニュー', link: null },
            { text: '登録・管理', link: null },
            { text: '登録データ管理', link: null },
            { text: '人財管理一覧', link: '/resumes/manage/index' },
            {
               text: this.isDuplicatePage ? "人財登録" : "人財編集",
               link: this.isDuplicatePage
                  ? `/resumes/${id}/manage/duplicate`
                  : `/resumes/${id}/manage/edit`
            },
            { text: 'スキルシート', link: null, current: true }
         ];
         const container = document.getElementById('breadcrumb-container');
         if (container) {
            container.innerHTML = createBreadcrumb(breadcrumbItems);
         }
      });
   },
   methods: {
      triggerFileInput() {
         this.$refs.fileInput.click();
      },

      async fetchExistingFile() {
         try {
            const id = this.$route.params.id;
            const response = await fetch(`/api/resume/get_cv?resume_id=${id}`);
            const result = await response.json();
            console.log(result);
            if (result.success) {
               this.existingFile = {
                  id: result.cv_id,
                  name: result.file_name,
                  size: (result.file_size / 1024 / 1024).toFixed(1) + " MB",
                  url: result.file_url
               };
            }
         } catch (error) {
            console.error("Lỗi khi lấy file:", error);
         }
      },

      async handleFileChange(event) {
         const files = event.target.files;
         if (files.length > 0) {
            for (let file of files) {
               // Check file size (10MB = 10 * 1024 * 1024 bytes)
               if (file.size > 10485760) {
                  alert(`ファイル ${file.name} のサイズが10MBを超えています。`);
                  return;
               }
               // Check file type
               const allowedTypes = ['.pdf', '.doc', '.docx', '.txt'];
               const fileName = file.name.toLowerCase();
               const isValidType = allowedTypes.some(type => fileName.endsWith(type));
               if (!isValidType) {
                  alert(`ファイル ${file.name} の形式が対応していません。PDF、DOC、DOCX、TXTファイルのみアップロード可能です。`);
                  return;
               }
            }
            if (this.existingFile) {
               alert("アップロードできるファイルは 1 つだけです。新しいファイルをアップロードする前に、現在のファイルを削除してください。");
               return;
            }
            this.selectedFiles = Array.from(files);
            await this.uploadFile(this.selectedFiles[0]);
            event.target.value = ''; // Reset input file
         }
      },

      async removeFile(_, event) {
         event.stopPropagation();
         try {
            const id = this.$route.params.id;
            const response = await fetch(`/api/resume/delete_cv`, {
               method: "POST",
               headers: {
                  "Content-Type": "application/json"
               },
               body: JSON.stringify({ resume_id: id })
            });

            const result = await response.json();
            console.log("Delete: ", result);
            if (result.result.success) {
               window.toastr.success('削除に成功しました');
               this.existingFile = null;
            } else {
               alert("Lỗi khi xóa: " + result.error);
            }
         } catch (error) {
            console.error("Lỗi khi xóa file:", error);
            alert("Lỗi kết nối đến server.");
         }
      },

      getFileIcon(filename) {
         const ext = filename.split('.').pop().toLowerCase();
         if (ext === 'pdf') return 'https://img.icons8.com/color/48/000000/pdf.png';
         if (['doc', 'docx'].includes(ext)) return 'https://img.icons8.com/color/48/000000/microsoft-word-2019.png';
         return 'https://img.icons8.com/color/48/000000/file.png';
      },

      async uploadFile(file) {
         const id = this.$route.params.id;
         const formData = new FormData();
         formData.append("file", file);
         formData.append("name", file.name);
         formData.append("resume_id", id);

         try {
            const response = await fetch("/api/resume/upload_cv", {
               method: "POST",
               body: formData,
               headers: { "X-Requested-With": "XMLHttpRequest" }
            });

            const result = await response.json();
            console.log("Upload:", result);
            if (result.success) {
               window.toastr.success('アップロードに成功しました');
               await this.fetchExistingFile();
            } else {
               window.toastr.error(result.error);
            }
         } catch (error) {
            console.error("Lỗi khi tải file lên:", error);
            window.toastr.error(error);
         }
      },

      loadExternalScript(src) {
         const toastrCSS = document.createElement("link");
         toastrCSS.rel = "stylesheet";
         toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
         toastrCSS.onload = function () {
            console.log("Toast css loaded successfully!");
            const jQuery = document.createElement("script");
            jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
            jQuery.onload = function () {
               console.log("jQuery loaded successfully!");
               const toastrJS = document.createElement("script");
               toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
               toastrJS.onload = function () {
                  console.log("Toastr loaded successfully!");
                  const script = document.createElement("script");
                  script.src = src;
                  script.async = true;
                  script.onload = function () {
                     console.log("External script loaded successfully!");
                  }
                  document.body.appendChild(script);
               };
               document.body.appendChild(toastrJS);
            };
            document.body.appendChild(jQuery);
         };
         document.body.appendChild(toastrCSS);
      },
   }
};

export default file_upload;
