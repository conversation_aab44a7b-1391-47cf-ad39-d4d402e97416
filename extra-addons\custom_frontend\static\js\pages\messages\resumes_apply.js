import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const resumes_apply = {
    'template': `
        <main class="margin-header" style="background-color: white">
        ${createBreadcrumb([
            { text: 'サービスメニュー', link: null },
            { text: '案件を探す', link: '/opportunities/active' },
            { text: '{{ Opp.subject }}', link: '/opportunities/126307/detail' },
            { text: '応募人財選択', link: null, current: true }
        ])}
        <div class="container-fluid grabient pt-3">
            <div class="row">
                <div class="col-12">
                    <div class="row justify-content-center">
                    <div class="col-12 col-md-8 col-lg-6 mx-auto">
                        <div class="pt-2 pb-2 font-middle text-left">応募する案件</div>
                        <div class="mb-3">
                            <div class="mb-3 d-inline-block font-middle"><a target="_blank" :href="'/opportunities/' + opp_id + '/detail'">{{ Opp.subject }}</a></div>
                        </div>
                        <div class="font-middle mb-2">応募する人財を選択しましょう。一度に複数の人財の選択が可能です。</div>
                        <form novalidate="novalidate" id="select_resumes" action="/messages/126307/apply_message" accept-charset="UTF-8" method="post">
                            <input name="utf8" type="hidden" value="✓" autocomplete="off"><input type="hidden" name="authenticity_token" value="+PSN1yFCw5WAfJ/dMpXQ3T9kLytZsuAdcT51E4PSmMnmxhozIqAn3xL4f5Y6EmkODXPocaE+f63rnmqrpG2WyA==" autocomplete="off"><input value="40" class="action_count" autocomplete="off" type="hidden" name="action_count"><input type="hidden" name="message_room[opportunity_id]" id="message_room_opportunity_id" value="126307" autocomplete="off"><input type="hidden" name="message[resume_id][]" id="message_resume_id_" autocomplete="off" value="">
                            <div class="row pb-1 justify-content-center">
                                <div class="col-12 mb-3">
                                <table class="table message-table">
                                    <thead class="stylish-color white-text">
                                        <tr class="d-none d-md-table-row">
                                            <th class="vertical-middle border-0"></th>
                                            <th class="vertical-middle border-0 text-center">人財名</th>
                                            <th class="vertical-middle border-0 text-center">稼働可能状況</th>
                                        </tr>
                                    </thead>
                                    <tbody v-for="resume in paginatedResumes" :key="resume.id" v-if="paginatedResumes.length">
                                        <tr class="d-block d-md-table-row px-md-0 mb-3 mb-md-0" :class="{ 'disable' : workFlow.includes(resume.id) }">
                                            <td class="th-sm d-block d-md-table-cell pb-0 pb-md-3 pt-3 select-record">
                                                <div class="d-inline-block bold px-md-1 px-3">
                                                    <div class="selecting-form custom-control custom-checkbox z-2">
                                                        <input value="false" autocomplete="off" type="hidden" name="choose_resume">
                                                        <input :id="'resume_' + resume.id" v-model="selectedResumes" :value="resume.id" class="custom-control-input " type="checkbox" :name="resume.id">
                                                        <label id="choose_resume_field_label" class="custom-control-label" :for="'resume_' + resume.id">選択</label>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="th-lg d-block d-md-table-cell px-3 pt-3 text-center">
                                                <a target="_blank" :href="'/resumes/' + resume.id + '/detail'">
                                                    {{ resume.initial_name.split(',').join('.') }}.
                                                    （{{ resume.user_name }}）さん
                                                    {{ resume.gender === 'male' ? '男' : '女'}}性
                                                    {{ calculateAge(resume.birthday) }}歳
                                                </a>
                                            </td>
                                            <td class="th-lg d-block d-md-table-cell p-3 status-cell text-center">
                                                <span v-if="resume.status === 'work_available'" class="text-green">即日可</span>
                                                <span v-else-if="resume.status === 'will_be_available'" class="text-blue">今後可 <br>
                                                     （{{ new Date(resume.date_period).toLocaleString("ja-JP", { year: '2-digit', month: '2-digit', day: '2-digit', hour12: false }) }}~）</span>
                                                <span v-else-if="resume.status === 'depends_on_opportunities'" class="text-orange">要相談</span>
                                                <span v-else-if="resume.status === 'not_corresponding'" class="text-red">対応不可</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- Pagination -->
                                <nav aria-label="pagination example">
                                    <nav role="navigation" class="pagination">
                                        <ul class="pagination pageul">
                                            <li :class="['page-item', { disabled: currentPage === 1 }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)">← 前</a>
                                            </li>

                                            <li v-for="page in totalPages" :key="page" :class="['page-item', { active: currentPage === page }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                                            </li>

                                            <li :class="['page-item', { disabled: currentPage === totalPages }]">
                                                <a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)">次 →</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </nav>
                                </div>
                            </div>
                            <div class="row px-1">
                                <div class="col-12 pt-4"><a class="btn btn-blue-grey waves-effect waves-light" :href="'/opportunities/' + opp_id + '/detail'">案件詳細に戻る</a></div>
                            </div>
                        </form>
                    </div>
                    </div>
                    <div class="fix_right_buttom d-md-flex justify-content-end align-items-center px-3 py-4 text-right w-100">
                        <span class="vertical-baseline">
                            <button name="button" class="btn btn-default font-middle m-0 waves-effect waves-light" id="input_apply_message" @click="apply">応募</button>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        </main>
        <div class="container-fluid pt-3" style="background-color: white">
            <div class="row">
                <div class="col-12">
                    <div class="text-right pb-4"><a class="pagetop" href="javascript:void(0)" @click="scrollToTop"><i class="material-icons md-blue-grey md-36">arrow_upward</i></a></div>
                </div>
            </div>
        </div>
        <link rel="stylesheet" href="/custom_frontend/static/css/messages/resumes_apply.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            Resumes: [],
            selectedResumes: [],
            Opp: [],
            workFlow: [],
            UserId: "",

            currentPage: 1,
            itemsPerPage: 24,
            isSubmitted: false,
        }
    },
    mounted() {
        this.UserId = userInfo ? userInfo.user_id : null;
        this.loadExternalScript("/custom_frontend/static/js/pages/signup-extra.js");
        this.getOpp();
        this.get_resumes();
        this.getOppWorkflow();
    },

    computed: {
        totalPages() {
            return Math.ceil(this.Resumes.length / this.itemsPerPage);
        },

        paginatedResumes() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            return this.Resumes.slice(start, start + this.itemsPerPage);
        }
    },

    props: ["opp_id"],

    methods: {
        scrollToTop(event) {
            event.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        },
        closeWindow() {
            window.close();
        },

        async get_resumes() {
            const response = await fetch(`/api/resumes?user_id=${this.UserId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })


            if (!response.ok) {
                throw new Error('Failed to fetch resumes')
            }

            const data = await response.json()
            console.log("Data:", data);
            if (data.success) {
                this.Resumes = data.resumes;
            } else {
                this.errMsg = data.message;
            }
        },

        async getOpp() {
            try {
                const response = await fetch(`/api/opp_edit?id=${this.opp_id}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })

                const result = await response.json();
                if (result.success) {
                    console.log(`Get opp info: ${result.message}`);
                    console.log(result.data);
                    this.Opp = result.data;
                    console.log(this.Opp.subject);
                } else {
                    console.log(`Get opp info failed: ${result.message}`);
                }

            } catch (error) {
                console.log(`Error: ${error.message}`);
            }
        },

        async getOppWorkflow() {
            try {
                const res = await fetch(`/api/get_opp_workflow?opp_id=${this.opp_id}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })

                const result = await res.json();
                if (result.success) {
                    this.workFlow = result.data;
                } else {
                    console.log(`Get opp workflow failed: ${result.message}`);
                }
            } catch (error) {
                console.log(`Error: ${error.message}`);
            }
        },

        async apply() {
            if (this.isSubmitted) return;
            this.isSubmitted = true;

            try {
                const res = await fetch(`/api/apply`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        opportunities_id: this.opp_id,
                        expire_date: new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000),
                        resumes_apply: this.selectedResumes,
                        created_by: this.UserId,
                        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                    })
                })
                const result = await res.json();
                console.log(result);
                if (result.result.success) {
                    const res_interview = await fetch(`/api/create_booking`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            opportunities_id: this.opp_id,
                            resumes_apply: this.selectedResumes,
                            created_by: this.UserId,
                            created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
                        })
                    })
                    const result_interview = await res_interview.json();
                    if (result_interview.result.success) {
                        window.location.href = "/mypage";
                    }
                    else {
                        window.toastr.warning(`Apply failed: ${result_interview.result.message}`);
                    }

                } else {
                    window.toastr.warning(`Apply failed: ${result.result.message}`);
                }
            } catch (error) {
                console.log(error.message);
            }
        },

        calculateAge(birthday) {
            if (!birthday) return "N/A"; // Nếu không có ngày sinh, trả về "N/A"

            // Xử lý chuỗi có dạng "YYYY-MM-DD HH:MM:SS"
            if (typeof birthday === "string") {
                birthday = birthday.split(" ")[0]; // Lấy phần "YYYY-MM-DD", bỏ giờ
            }

            const birthDate = new Date(birthday);
            if (isNaN(birthDate.getTime())) return "N/A"; // Kiểm tra nếu ngày không hợp lệ

            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();

            // Kiểm tra nếu chưa tới sinh nhật trong năm nay thì trừ đi 1 tuổi
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            return age;
        },

        changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        }
    }
}
export default resumes_apply;