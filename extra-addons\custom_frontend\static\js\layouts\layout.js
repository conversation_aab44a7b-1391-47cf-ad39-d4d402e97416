import { userInfo } from "../router/router.js";

const Layout = {
    'template': `
    <header class="fixed-top header-bg" id="header-app" data-v-app="">
    <div class="custom-side-nav yellow" :class="{ 'd-block': isMenuOpen }">
        <ul class="account-side-scrollbar" id="side-menu">
            <li>
                <ul class="collapsible mt-3">
                    <div class="text-right pb-2"><i class="material-icons custom custom-grey-6-text mr-3 slide-close" @click="closeMenu">close</i>
                    </div>
                    <li class="pb-3">
                        <div class="px-3"><img class="user-icon d-inline-block float-left"
                                               src="/custom_frontend/static/img/user.png" style="width: 40px; height: 40px;">
                            <div class="pl-7">
                                <div class="mb-0 font-default">{{this.company_name}}</div>
                                <div class="font-middle d-inline-block">{{this.user_name}}</div>
                            </div>
                            <div class="clear"></div>
                        </div>
                    </li>
                </ul>
            </li>
            <li class="px-3 pt-3 mb-5">
                <ul class="collapsible mt-0">
                    <div class="side-nav-title border-bottom d-xl-none"><a class="d-block pl-2 pb-0" href="/my"><span
                            class="font-weight-bold">マイページ</span></a></div>
                    <div class="side-nav-title border-bottom d-xl-none"><a class="d-block pl-2 pb-0"
                                                                           href="/opportunities/active"><span
                            class="font-weight-bold">案件を探す</span></a></div>
                    <div class="side-nav-title border-bottom d-xl-none"><a class="d-block pl-2 pb-0"
                                                                           href="/resumes/active"><span
                            class="font-weight-bold">人財を探す</span></a></div>
                    <div class="side-nav-title border-bottom d-xl-none"><a class="d-block pl-2 pb-0"
                                                                           href="/events"><span
                            class="font-weight-bold">イベントを探す</span></a></div>
                    <div class="side-nav-title border-bottom d-xl-none"><a class="d-block pl-2 pb-0"
                                                                           href="javascript:void(0)"><span
                            class="font-weight-bold">各種管理</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close d-xl-none" style="display: none;">
                        <ul>
                            <li class="border-bottom"><a class="d-block waves-effect active" aria-current="page"
                                                         href="/opportunities/manage/index"><span>案件管理</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/resumes/manage/index"><span>人財管理</span></a></li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom" @click="toggleAccordion($event)"><a class="d-block pl-2 pb-0"
                                                                 href="javascript:void(0)"><span
                            class="font-weight-bold">便利機能</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close" style="display: none;">
                        <ul>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/bookmark_opportunities"><span>案件ブックマーク</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/bookmark_resumes"><span>人財ブックマーク</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/bookmark_users"><span>会社ブックマーク</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/message_templates"><span>メッセージテンプレート一覧</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/block_users"><span>ブロックユーザ一ー覧</span></a>
                            </li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom" @click="toggleAccordion($event)"><a class="d-block pl-2 pb-0"
                                                                 href="javascript:void(0)"><span
                            class="font-weight-bold">申込み・支払い</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close" style="display: none;">
                        <ul>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/contacts/matching"><span>企業マッチングのお申込み</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/fixed_rate_applies/select_plan"><span>年間プランのお申込み</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/buy_additional_actions/index"><span>チケットパックの追加購入</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/fixed_rate_applies"><span>年間プランのお申込み一覧</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/invoices"><span>請求一覧</span></a></li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/credit_payments"><span>クレジットカード</span></a>
                            </li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom" @click="toggleAccordion($event)"><a class="d-block pl-2 pb-0"
                                                                 href="javascript:void(0)"><span
                            class="font-weight-bold">会社設定</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close" style="display: none;">
                        <ul>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/companies/7436/manage/edit"><span>会社データ</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/users/manage/users"><span>サブアカウント管理</span></a>
                            </li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom" @click="toggleAccordion($event)"><a class="d-block pl-2 pb-0"
                                                                 href="javascript:void(0)"><span
                            class="font-weight-bold">会社データ管理</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close" style="display: none;">
                        <ul>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/users/edit"><span>プロフィール</span></a></li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/users/profile/edit_email"><span>メールアドレス</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/users/profile/edit_password"><span>パスワード</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/users/profile/edit_mail_reception_settings"><span>メール受信設定</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/events/manage/scheduled_join_event"><span>参加予定イベント</span></a>
                            </li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom" @click="toggleAccordion($event)"><a class="d-block pl-2 pb-0"
                                                                 href="javascript:void(0)"><span
                            class="font-weight-bold">サービスについて</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close" style="display: none;">
                        <ul>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/our_services/talent"><span>人財をお探しの方</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/our_services/project"><span>案件をお探しの方</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/our_services/freelance"><span>フリーランスの方</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/our_services/matching"><span>パートナー企業をお探しの方</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" target="_blank"
                                                         href="https://assign-navi.jp/hint/category/interview/"><span>活用事例</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect" href="/plan"><span>料金</span></a>
                            </li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom" @click="toggleAccordion($event)"><a class="d-block pl-2 pb-0"
                                                                 href="javascript:void(0)"><span
                            class="font-weight-bold">サポート</span><i
                            class="material-icons custom-grey-6-text float-right pt-1">keyboard_arrow_down</i></a></div>
                    <li class="side-nav-contents accordion_close" style="display: none;">
                        <ul>
                            <li class="border-bottom">
                                <div class="d-block waves-effect" data-target="#trouble_confirm" data-toggle="modal"
                                     id="confirm_trouble_list"><span>トラブル確認</span></div>
                            </li>
                            <li class="border-bottom">
                                <div class="d-block waves-effect" data-target="#trouble_report" data-toggle="modal"
                                     id="report_trouble_list"><span>トラブル連絡</span></div>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/faq"><span>よくある質問</span></a></li>
                            <li class="border-bottom"><a class="d-block waves-effect" target="_blank"
                                                         href="https://guideline.assign-navi.jp/"><span>利用方法</span></a>
                            </li>
                            <li class="border-bottom"><a class="d-block waves-effect"
                                                         href="/contacts/new"><span>お問い合わせ</span></a></li>
                        </ul>
                    </li>
                    <div class="side-nav-title border-bottom"><a id="sign_out_link" class="d-block pl-2 pb-0"
                                                                 rel="nofollow" data-method="delete"
                                                                 href="/users/sign_out" @click="sign_out"><span class="font-weight-bold">ログアウト</span></a>
                    </div>
                </ul>
            </li>
        </ul>
    </div>
    <div class="progress md-progress primary-color-dark mb-0 progress-zindex" id="progress-bar" style="display: none;">
        <div class="indeterminate"></div>
    </div>
    <div class="navbar navbar-toggleable-md navbar-expand-md scrolling-navbar">
        <!-- Dòng 1: Logo + Nút đăng nhập (chỉ trên mobile khi chưa đăng nhập) -->
        <div class="navbar-top-row d-flex d-md-none" v-if="!isloggedin">
            <a class="navbar-brand" href="/"><img class="logo"  src="/custom_frontend/static/img/logo.png"></a>
            <div class="ml-auto">
                <div class="float-right">
                    <ul class="navbar-nav header-notification align-items-center">
                        <li class="pr-3"><router-link to="/signup" id="registration_link"
                                                                    class="fancy-button btn p-2 m-0"
                                                                    style="width: 140px"
                                                                    >無料会員登録</router-link></li>
                        <li class=""><router-link to="/login" id="login_link"
                                                                class="fancy-button btn p-2 m-0 btn-login"
                                                                >ログイン</router-link></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Layout desktop hoặc khi đã đăng nhập -->
        <a class="navbar-brand d-none d-md-block" href="/" v-if="!isloggedin"><img class="logo"  src="/custom_frontend/static/img/logo.png"></a>
        <a class="navbar-brand" href="/" v-if="isloggedin"><img class="logo"  src="/custom_frontend/static/img/logo.png"></a>

        <!-- Dòng 2: Slogan (mobile) hoặc cùng dòng (desktop) -->
        <div class="d-flex flex-column aligns-item-center">
            <img src="/custom_frontend/static/img/ロゴ横2.png" class="slogan">
        </div>

        <!-- Nút đăng nhập cho desktop -->
        <div class="ml-auto d-none d-md-block" v-if="!isloggedin">
            <div class="float-right">
                <ul class="navbar-nav header-notification align-items-center">
                    <li class="pr-3"><router-link to="/signup" id="registration_link"
                                                                class="fancy-button btn p-2 m-0"
                                                                style="width: 140px"
                                                                >無料会員登録</router-link></li>
                    <li class=""><router-link to="/login" id="login_link"
                                                            class="fancy-button btn p-2 m-0 btn-login"
                                                            >ログイン</router-link></li>
                </ul>
            </div>
        </div>
    </div>
    <nav class="navbar navbar-toggleable-md navbar-expand-md navbar_boxshadow pb-0" v-if="isloggedin">
        <div style="margin-left: -4px;width: 100%;" class="mr-auto navbar-left">
            <ul class="d-nonehome d-xl-block" v-if="isloggedin" style="width: 100%;">
                <li class="header-search-menu" :class="{ 'active' : activeTab === 1 }" style="width: 16%;">
                    <span class="menu-content">
                        <span class="find header-color font-default">サービスメニュー</span>
                        <i class="material-icons custom-grey-6-text menu-arrow">keyboard_arrow_down</i>
                    </span>
                    <ul id="big_ul_hover_1" class="big_ul">
                        <div id="div-surround-1">
                            <li class="menu-item-parent"><span class="item header-color font-default">探す <i class="material-icons custom-grey-6-text">keyboard_arrow_right</i></span>
                                <ul id="submenu1" class="submenu">
                                    <li><a class="item header-color font-default" href="/opportunities/active" @click="setActive(1)">案件・人財を探す</a></li>
                                    <li><a class="item header-color font-default" href="/bookmark_list" @click="setActive(1)">ブックマーク</a></li>
                                    <li><a class="item header-color font-default" href="/mypage" @click="setActive(1)">マッチング状況</a></li>
                                    <!--<li id="concierge-service" style="background-color: #e9ba01;"><a class="item header-color font-default" href="/matching_request/opportunity" @click="setActive(1)">コンシェルジュサービス</a></li>-->
                                </ul>
                            </li>
                        </div>
                        <div id="div-surround-2">
                            <li class="menu-item-parent"><span class="item header-color font-default">登録・管理 <i class="material-icons custom-grey-6-text">keyboard_arrow_right</i></span>
                                <ul id="submenu2" class="submenu">
                                    <li><a class="item header-color font-default" href="/opportunities/manage/new" @click="setActive(1)">案件・人財登録</a></li>
                                    <li><a class="item header-color font-default" href="/opportunities/manage/index" @click="setActive(1)">登録データ管理</a></li>
                                    <li><a class="item header-color font-default" href="/companies/manage/edit" @click="setActive(1)">会社データ管理</a></li>
                                </ul>
                            </li>
                        </div>
                        <li><a class="item header-color font-default logout-item" href="/users/sign_out" @click="sign_out">ログアウト</a></li>
                    </ul>
                </li>
                <li class="header-search-menu " :class="{ 'active': activeTab === 2 }" style="width: 16%;">
                    <span class="menu-content">
                        <span class="find header-color font-default">利用方法</span>
                        <i class="material-icons custom-grey-6-text menu-arrow">keyboard_arrow_down</i>
                    </span>
                    <ul class="big_ul">
                        <div class="menu_right_item">
                            <li><a class="item header-color font-default" @click="setActive(2)" href="/plan">料金プラン</a></li>
                        </div>
                        <div class="menu_right_item">
                            <li><a class="item header-color font-default" @click="setActive(2)" href="/guideline">利用方法</a></li>
                        </div>
                        <div class="menu_right_item">
                            <li><a class="item header-color font-default" @click="setActive(2)" href="/contact_new">お問合せ</a></li>
                        </div>
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
</header>
<div id="sidenav-overlay" v-if="isMenuOpen"></div>
<router-view></router-view>



    <footer class="page-footer py-4">
    <div class="container-fluid">
        <div class="row home-footer">
            <div class="col-6ft col-md-12 col-lg-6 d-flex align-items-center justify-content-start home-logo" style="flex: 0 0 40%">
                <img src="/custom_frontend/static/img/vit_logo.png" alt="logo">
                <img src="/custom_frontend/static/img/logo.png" alt="logo">
            </div>
            <div class="col-7 d-flexft justify-content-around home-directive">
                <div class="col-12 col-md-6 col-lg-3" style="flex: 0 0 20%">
                    <dl class="mb-5">
                        <dt class="mb-3 font-middle">サービスメニュー</dt>
                        <dd><a href="/opportunities/active">案件・人財を探す</a></dd>
                        <dd><a href="/bookmark_list">ブックマーク</a></dd>
                        <dd><a href="/mypage">マッチング状況</a></dd>
                        <!-- <dd><a href="/matching_request/opportunity">コンシェルジュサービス</a></dd>-->
                        <dd><a href="/opportunities/manage/new">案件・人財登録</a></dd>
                        <dd><a href="/opportunities/manage/index">データ管理</a></dd>
                        <dd><a href="/companies/manage/edit">会社データ管理</a></dd>
                    </dl>
                </div>
                <div class="col-12 col-md-6 col-lg-3" style="flex: 0 0 20%">
                    <dl class="mb-5">
                        <dt class="mb-3 font-middle">利用方法</dt>
                        <dd><a href="/plan">料金プラン</a></dd>
                        <dd><a href="/guideline">利用方法</a></dd>
                        <dd><a href="/contact_new">お問合せ</a></dd>
                    </dl>
                </div>
                <div class="col-12 col-md-6 col-lg-3" style="flex: 0 0 20%">
                    <dl class="mb-5">
                        <dt class="mb-3 font-middle">Mi52について</dt>
                        <dd><a href="/our_services/agreement">利用規約</a></dd>
                        <dd><a href="/our_services/userguide">ご利用ガイドライン</a></dd>
                        <dd><a href="/our_services/law">特定商取引法に基づく表記</a></dd>
                        <dd><a href="https://verticallimit.co.jp/personal">個人情報保護方針</a></dd>
                        <dd><a href="https://verticallimit.co.jp">運営会社</a></dd>

                    </dl>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid text-center">
        <p class="mb-0 mt-2 white-text small copyright_wrap">© VerticallimIT Co.,Ltd.</p>
    </div>
</footer>
    `,
    data() {
        return {
            isMenuOpen: false,
            company_name: '',
            isloggedin: userInfo ? userInfo.user_id : '',
            company_id: userInfo ? userInfo.user_company_id : '',
            user_name: userInfo ? userInfo.user_name : '',
            isOpenNotification: false,
            isOpenRelease: false,
            isClickingIcon: false,
            activeTab: "1",
            active: Number(userInfo ? userInfo.user_active : 1),
        }
    },
    methods: {
        initMobileMenuHandlers() {
            // Remove existing mobile handlers
            $('.menu-item-parent > span').off('click.mobile');
            $('.menu-item-parent .submenu a').off('click.mobile');
            $(document).off('click.mobile-submenu');

            if (window.innerWidth <= 767) {
                // Handle click on menu title (span) to toggle submenu
                $('.menu-item-parent > span').on('click.mobile', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle submenu visibility
                    const submenu = $(this).parent().find('.submenu');
                    const isVisible = submenu.hasClass('mobile-show');

                    // Hide all other submenus
                    $('.submenu').removeClass('mobile-show');

                    // Toggle current submenu
                    if (!isVisible) {
                        submenu.addClass('mobile-show');
                    } else {
                        submenu.removeClass('mobile-show');
                    }
                });

                // Handle click on submenu links - allow navigation
                $('.menu-item-parent .submenu a').on('click.mobile', function() {
                    // Close all submenus after clicking a link
                    $('.submenu').removeClass('mobile-show');
                    // Allow the link to navigate normally
                });

                // Close submenu when clicking outside or on submenu links
                $(document).on('click.mobile-submenu', function(e) {
                    // If clicking on a link inside submenu, allow navigation and close menu
                    if ($(e.target).closest('.submenu a').length) {
                        $('.submenu').removeClass('mobile-show');
                        return; // Allow the link to work normally
                    }

                    // If clicking outside menu area, close submenu
                    if (!$(e.target).closest('.menu-item-parent').length &&
                        !$(e.target).closest('.submenu').length) {
                        $('.submenu').removeClass('mobile-show');
                    }
                });
            } else {
                // Desktop behavior - remove mobile classes and rely on CSS hover
                $('.submenu').removeClass('mobile-show');
            }
        },
        toggleAccordion(event) {
            const element = event.currentTarget;
            const content = element.nextElementSibling;
            const icon = element.querySelector('i');

            const isOpen = content.classList.contains('accordion_open');

            if (isOpen) {
                content.style.display = 'none';
                content.classList.remove('accordion_open');
                content.classList.add('accordion_close');
                icon.textContent = 'keyboard_arrow_down';
            } else {
                content.style.display = 'block';
                content.classList.add('accordion_open');
                content.classList.remove('accordion_close');
                icon.textContent = 'keyboard_arrow_up';
            }
        },
        openMenu() {
            this.isMenuOpen = true;
            document.body.classList.add('overflow-hidden');
        },
        closeMenu() {
            this.isMenuOpen = false;
            document.body.classList.remove('overflow-hidden');
        },
        toggleMenu(menu) {
            if (menu === "notification") {
                this.isOpenNotification = !this.isOpenNotification;
                this.isOpenRelease = false;
                this.activeTab = "1";
            } else if (menu === "release") {
                this.isOpenRelease = !this.isOpenRelease;
                this.isOpenNotification = false;
                this.activeTab = "1";
            }
        },
        closeMenus(event) {
            if (
                event.target.closest(".notification_announcement") ||
                event.target.closest(".release_announcement") ||
                event.target.closest(".header-tab-menu-wrapper")
            ) {
                return;
            }
            this.isOpenNotification = false;
            this.isOpenRelease = false;
            this.activeTab = "1";
        },
        setActiveTab(tab) {
            this.activeTab = tab;
        },
        sign_out() {
            localStorage.clear();
            sessionStorage.clear();
        },
        setActive(tabIndex) {
            // Cập nhật giá trị activeTab
            this.activeTab = tabIndex;

            // Lưu giá trị activeTab vào localStorage
            localStorage.setItem('active', tabIndex);
        },
        async getCompanyName(com_id) {
            try {
                const response = await fetch(`/api/get_company_name`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: com_id }),
                });
                const company = await response.json();
                this.company_name = company.result.company_name;
            } catch (error) {
                console.error('Fetch error:', error);
                this.errorMessage = error.message;
            }
        }
    },
    created() {
        this.activeTab = Number(userInfo ? userInfo.user_active : 1)
    },
    mounted() {
        this.activeTab = Number(userInfo ? userInfo.user_active : 1),
            $('.side-nav-contents').css('display', 'none');
        $(document).on('click', '.slide-close', () => {
            this.closeMenu();
        });
        $(document).on('click', (e) => {
            if (!$(e.target).closest('.custom-side-nav').length && !$(e.target).closest('.menu').length) {
                this.closeMenu();
            }
        });
        $('.header-search-menu').on('mouseenter', function () {
            $(this).find('i.menu-arrow').text('keyboard_arrow_up');
        });
        $('.header-search-menu').on('mouseleave', function () {
            $(this).find('i.menu-arrow').text('keyboard_arrow_down');
        });

        // Mobile menu handling for submenu clicks
        this.initMobileMenuHandlers();

        // Handle window resize
        $(window).on('resize', () => {
            this.initMobileMenuHandlers();
        });

        document.addEventListener("click", this.closeMenus);
        this.getCompanyName(this.company_id);
    },
    beforeUnmount() {
        document.removeEventListener("click", this.closeMenus);
    }
}
export default Layout