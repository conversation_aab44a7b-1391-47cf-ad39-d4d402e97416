import { userInfo } from "../router/router.js";
import { createBreadcrumb } from "../utils/breadcrumbHelper.js";

const InterviewResult = {
    template: `
        <main class="margin-header sp_fluid" id="vue-app" data-v-app>
            ${createBreadcrumb([
                { text: 'サービスメニュー', link: null },
                { text: '探す', link: null },
                { text: 'マッチング状況', link: '/mypage' },
                { text: '面談結果（表示）', link: null, current: true }
            ], 'container-fluid', '')}

            <div class="col-12 col-md-10 col-lg-8 mx-auto mb-5">
                <div class="card px-3 px-md-4 mt-2 pt-5 form-card sp_sides_uniter">
                    <div class="d-flex checkbox-container justify-content-start">
                        <div v-show="userAccess !== 'hiring' && userAccess !== ''" class="custom-control custom-checkbox pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field0" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="scout"
                                   v-model="contractTypes" disabled>
                            <label id="contract_types_field_label_0" class="custom-control-label anavi-select-label mb-3" for="contract_types_field0">人財スカウト</label>
                        </div>
                        <div v-show="userAccess !== 'scout' && userAccess !== ''" class="custom-control custom-checkbox pl-4 pr-md-0 font-middle col-12 col-sm-4">
                            <input id="contract_types_field1" class="custom-control-input" type="checkbox" name="resume[contract_types][]" value="case"
                                   v-model="contractTypes" disabled>
                            <label id="contract_types_field_label_1" class="custom-control-label anavi-select-label mb-3" for="contract_types_field1">案件ヒアリング</label>
                        </div>
                    </div>

                    <div class="info-container">
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">案件名:</span>
                                <a class="info-value" :href="'/opportunities/' + opp_id + '/manage/edit'">{{ opportunityName }}</a>
                            </div>
                            <div class="info-item">
                                <span class="info-label">人財名:</span>
                                <a class="info-value"  :href="'/resumes/' + resume_id + '/manage/edit'">{{ partnerName }}</a>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex mb-5">
                        <div class="form-check">
                            <input disabled class="form-check-input" id="result_interview0" required="required" type="radio" value="false" name="result" checked @click="ResultInterviewChange">
                            <label id="result_interview_label0" class="form-check-label" for="result_interview0" required="required" >OK</label>
                        </div>
                        <div class="form-check">
                            <input disabled class="form-check-input" id="result_interview1" required="required" type="radio" value="true" name="result" @click="ResultInterviewChange">
                            <label id="result_interview_label1" class="form-check-label" for="result_interview1" required="required">NG</label>
                        </div>
                    </div>

                    <div class="mb-5">
                        <label class="form-label">メッセージ</label>
                        <div class="textarea-container" id="textareaContainer">
                            <textarea :readonly="true" id="message" rows="5" class="form-control"></textarea>
                            <div class="d-flex justify-content-between">
                                <CharacterCounter targetId="message" :max-length="2000" />
                            </div>
                        </div>
                    </div>

                    <!--
                    <div class="d-flex justify-content-center">
                        <button class="btn mb-5 font-middle white-text btn-default btn-lg width-200 sp-width-150 px-0 submit_change_send_to waves-effect waves-light" @click="submitData">登録する</button>
                    </div>
                    -->
                </div>
            </div>
        </main>
        <link rel="stylesheet" href="/custom_frontend/static/css/interview_result.css"/>
        <link rel="stylesheet" href="/custom_frontend/static/css/layout.css"/>
    `,
    data() {
        return {
            id: '',
            block_date: '',
            contractTypes: [],
            isRejected: false,
            opp_id: '',
            resume_id: '',
            opportunityName: 'xxxxx',
            partnerName: 'xxxxx',
            userAccess: '',
            user_id: userInfo ? userInfo.user_id : null
        }
    },
    methods: {
        ResultInterviewChange(event) {
            this.isRejected = event.target.value === 'true';
        },
        async getInterviewResult() {
            const id = this.$route.params.id;
            let response = await fetch(`/api/interview_result_view?id=${id}`);

            if (!response.ok) {
                console.error("Error fetching interview result:", response.statusText);
                return;
            }

            let result = await response.json();
            if (result.success) {
                this.subject = result.data.subject;

                // Sử dụng filter để loại bỏ các phần tử trống sau khi split
                this.id = result.data.id;
                this.contract_types = result.data.contract_types ? result.data.contract_types.split(",").filter(item => item.trim() !== "") : [];
                this.opp_id = result.data.opp_id;
                this.resume_id = result.data.resume_id;
                this.result = result.data.result;
                this.message = result.data.reject_reason; // Sử dụng reject_reason làm message
                this.opportunityName = result.data.opportunity_subject;
                this.partnerName = result.data.candidate_name;

                document.getElementById('message').value = result.data.reject_reason || '';
                if (result.data.result == 'Fail') {
                    document.getElementById('result_interview1').checked = true;
                    this.isRejected = true;
                } else {
                    document.getElementById('result_interview0').checked = true;
                    this.isRejected = false;
                }
            }
        },
        convertDateToJapanese(date) {
            if (!date) return null;
            const year = date.getFullYear();
            const month = ('0' + (date.getMonth() + 1)).slice(-2);  // Đảm bảo tháng luôn 2 chữ số
            const day = ('0' + date.getDate()).slice(-2);  // Đảm bảo ngày luôn 2 chữ số
            return `${year}年${month}月${day}日`;
        },

        async checkScout() {
            try {
                const res = await fetch('/api/check_scout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        resume_id: this.resume_id,
                        opportunity_id: this.opp_id,
                        user_id: this.user_id
                    })
                });

                const result = await res.json();

                if (result.result && result.result.success) {
                    if (result.result.type === 'scout') {
                        this.userAccess = 'scout';
                        this.contractTypes = ['scout'];
                    } else if (result.result.type === 'hiring') {
                        this.userAccess = 'hiring';
                        if (!this.contractTypes.includes('case')) {
                            this.contractTypes.push('case');
                        }
                    } else {
                        this.userAccess = 'none';
                        this.contractTypes = [];
                    }
                    return true;
                } else {
                    if(result.result && result.result.redirect) {
                        //window.location.href = result.result.redirect;
                    } else {
                        //window.location.href = '/notfound';
                    }
                    return false;
                }
            } catch(error) {
                console.error("Error checking scout:", error);
                //window.location.href = '/notfound';
                return false;
            }
        }
    },
    beforeMount() {
        this.id = this.$route.params.id;
    },
    async mounted() {
        await this.getInterviewResult();
        await this.checkScout();
    }
}

export default InterviewResult;
