[options]
; Database configuration
db_host = postgres
db_port = 5432
db_user = odoo
db_password = Kate02052001@
db_name = MI52_02_06_2025

; Database filtering - only allow MI52_02_06_2025 database for regular access
; Enable dbfilter to auto-select database for regular users
dbfilter = ^MI52_02_06_2025$

; Hide database selector for regular users
; Admin can still access via special routes
list_db = False

; Auto-select database without login for anonymous users
; This allows access to the database without showing selector
without_demo = all

; Allow database manager access (admin can still access via /web/database/manager)
; Admin password is required for database operations

; Server configuration
addons_path = /mnt/extra-addons,/usr/lib/python3/dist-packages/odoo/addons
data_dir = /var/lib/odoo

; Logging
log_level = info
log_handler = :INFO

; Security
admin_passwd = Nkt02052001@

; Performance
workers = 0
max_cron_threads = 1

; Development settings (remove in production)
dev_mode = reload,qweb,werkzeug,xml
proxy_mode  = True