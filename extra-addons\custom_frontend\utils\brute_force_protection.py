import logging
import time
import json
from datetime import datetime, timedelta
from collections import defaultdict
import hashlib
import random

_logger = logging.getLogger(__name__)

class BruteForceProtection:
    def __init__(self):
        # Account lockout tracking
        self.locked_accounts = {}  # email -> lock_info
        
        # Failed attempts per account
        self.account_failed_attempts = defaultdict(list)
        
        # Progressive delay tracking
        self.progressive_delays = defaultdict(int)  # email -> delay_level
        
        # CAPTCHA requirements
        self.captcha_required = set()  # emails that need CAPTCHA
        
        # Configuration
        self.config = {
            'max_attempts_before_lockout': 5,
            'lockout_duration_minutes': 30,
            'progressive_delay_base': 2,  # seconds
            'progressive_delay_max': 60,  # seconds
            'captcha_threshold': 3,  # attempts before CAPTCHA required
            'attempt_window_minutes': 15,  # window for counting attempts
        }
    
    def add_failed_attempt(self, email, ip=None):
        """
        Thêm failed login attempt cho account
        Returns: (is_locked: bool, delay_seconds: int, requires_captcha: bool)
        """
        now = datetime.now()
        
        # Clean old attempts
        self._clean_old_attempts(email, now)
        
        # Add new attempt
        self.account_failed_attempts[email].append({
            'timestamp': now,
            'ip': ip
        })
        
        attempt_count = len(self.account_failed_attempts[email])
        
        # Check if account should be locked
        if attempt_count >= self.config['max_attempts_before_lockout']:
            self._lock_account(email, now)
            return True, 0, False
        
        # Check if CAPTCHA is required
        requires_captcha = attempt_count >= self.config['captcha_threshold']
        if requires_captcha:
            self.captcha_required.add(email)
        
        # Calculate progressive delay
        delay = self._calculate_progressive_delay(email, attempt_count)
        
        # Log the attempt
        self._log_failed_attempt(email, ip, attempt_count, delay, requires_captcha)
        
        return False, delay, requires_captcha
    
    def is_account_locked(self, email):
        """Kiểm tra account có bị lock không"""
        if email not in self.locked_accounts:
            return False, 0
        
        lock_info = self.locked_accounts[email]
        now = datetime.now()
        
        # Check if lock has expired
        if now >= lock_info['unlock_time']:
            self._unlock_account(email)
            return False, 0
        
        remaining_seconds = int((lock_info['unlock_time'] - now).total_seconds())
        return True, remaining_seconds
    
    def requires_captcha(self, email):
        """Kiểm tra có cần CAPTCHA không"""
        return email in self.captcha_required
    
    def verify_captcha(self, email, captcha_response):
        """
        Verify CAPTCHA (simple implementation)
        In production, integrate with Google reCAPTCHA or similar
        """
        # Simple CAPTCHA verification (replace with real implementation)
        if self._verify_simple_captcha(captcha_response):
            # Reset CAPTCHA requirement on successful verification
            self.captcha_required.discard(email)
            return True
        return False
    
    def reset_failed_attempts(self, email):
        """Reset failed attempts sau successful login"""
        self.account_failed_attempts[email] = []
        self.progressive_delays[email] = 0
        self.captcha_required.discard(email)
        
        # Log successful login
        self._log_successful_login(email)
    
    def _lock_account(self, email, timestamp):
        """Lock account"""
        unlock_time = timestamp + timedelta(minutes=self.config['lockout_duration_minutes'])
        
        self.locked_accounts[email] = {
            'locked_at': timestamp,
            'unlock_time': unlock_time,
            'attempt_count': len(self.account_failed_attempts[email])
        }
        
        _logger.warning(f"Account {email} locked until {unlock_time}")
        
        # Log account lockout
        from .security_logger import security_logger
        security_logger.log_security_violation(
            'account_lockout',
            f'Account {email} locked due to {len(self.account_failed_attempts[email])} failed attempts',
            None
        )
    
    def _unlock_account(self, email):
        """Unlock account"""
        if email in self.locked_accounts:
            del self.locked_accounts[email]
            
        # Reset attempts and delays
        self.account_failed_attempts[email] = []
        self.progressive_delays[email] = 0
        self.captcha_required.discard(email)
        
        _logger.info(f"Account {email} unlocked")
    
    def _calculate_progressive_delay(self, email, attempt_count):
        """Calculate progressive delay based on attempt count"""
        if attempt_count <= 1:
            return 0
        
        # Progressive delay: 2^(attempt-1) seconds, max 60 seconds
        delay = min(
            self.config['progressive_delay_base'] ** (attempt_count - 1),
            self.config['progressive_delay_max']
        )
        
        # Add some randomization to prevent timing attacks
        delay += random.uniform(0, 1)
        
        return delay
    
    def _clean_old_attempts(self, email, current_time):
        """Clean attempts older than the window"""
        cutoff_time = current_time - timedelta(minutes=self.config['attempt_window_minutes'])
        
        self.account_failed_attempts[email] = [
            attempt for attempt in self.account_failed_attempts[email]
            if attempt['timestamp'] > cutoff_time
        ]
    
    def _verify_simple_captcha(self, captcha_response):
        """
        Simple CAPTCHA verification
        Replace with Google reCAPTCHA in production
        """
        # For now, just check if response is not empty
        # In production, verify with reCAPTCHA API
        return bool(captcha_response and len(captcha_response) > 0)
    
    def _log_failed_attempt(self, email, ip, attempt_count, delay, requires_captcha):
        """Log failed attempt details"""
        from .security_logger import security_logger
        
        log_data = {
            'event': 'brute_force_attempt',
            'email': email,
            'ip': ip,
            'attempt_count': attempt_count,
            'delay_applied': delay,
            'captcha_required': requires_captcha,
            'timestamp': datetime.now().isoformat()
        }
        
        security_logger.security_logger.warning(json.dumps(log_data))
    
    def _log_successful_login(self, email):
        """Log successful login after failed attempts"""
        from .security_logger import security_logger
        
        log_data = {
            'event': 'login_success_after_failures',
            'email': email,
            'timestamp': datetime.now().isoformat()
        }
        
        security_logger.security_logger.info(json.dumps(log_data))
    
    def get_account_status(self, email):
        """Get comprehensive account status"""
        is_locked, remaining_time = self.is_account_locked(email)
        
        return {
            'is_locked': is_locked,
            'remaining_lockout_seconds': remaining_time,
            'failed_attempts': len(self.account_failed_attempts.get(email, [])),
            'requires_captcha': self.requires_captcha(email),
            'max_attempts': self.config['max_attempts_before_lockout']
        }
    
    def get_statistics(self):
        """Get brute force protection statistics"""
        return {
            'locked_accounts_count': len(self.locked_accounts),
            'accounts_requiring_captcha': len(self.captcha_required),
            'total_tracked_accounts': len(self.account_failed_attempts),
            'config': self.config
        }

# Singleton instance
brute_force_protection = BruteForceProtection()
