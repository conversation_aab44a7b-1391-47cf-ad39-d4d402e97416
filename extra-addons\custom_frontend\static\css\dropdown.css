.select-wrapper .select-dropdown {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.select-wrapper {
  position: relative;
}

.select-wrapper:not(.md-outline) .select-dropdown:focus {
  border-bottom: 1px solid #4285f4;
  box-shadow: 0 1px 0 0 #4285f4;
}

.select-wrapper input.select-dropdown {
  position: relative;
  z-index: 2;
  display: block;
  width: 100%;
  height: 40px;
  padding: 0;
  margin: 0 0 0.94rem 0;
  font-size: 1rem;
  line-height: 2.9rem;
  text-overflow: ellipsis;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0);
  border: none;
  border-bottom: 1px solid #ced4da;
  outline: none;
}

.select-wrapper input.select-dropdown:disabled {
  color: rgba(0, 0, 0, 0.3);
  cursor: default;
  border-bottom-color: rgba(0, 0, 0, 0.2);
}

.select-wrapper span.caret {
  position: absolute;
  top: 0.8rem;
  right: 0;
  font-size: 0.63rem;
  color: #495057;
}

.select-wrapper ul {
  padding-left: 0;
  list-style-type: none;
}

select {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

select.mdb-select.initialized {
  display: none !important;
}

select:disabled {
  color: rgba(0, 0, 0, 0.3);
}

.dropdown-content {
  position: absolute;
  top: 0 !important;
  z-index: 1021;
  display: none;
  min-width: 6.25rem;
  max-height: 40.625rem;
  margin: 0;
  overflow-y: auto;
  background-color: #fff;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16),
    0 2px 10px 0 rgba(0, 0, 0, 0.12);
  opacity: 0;
  will-change: width, height;
}

.dropdown-content li {
  width: 100%;
  clear: both;
  line-height: 1.3rem;
  color: #000;
  text-align: left;
  text-transform: none;
  cursor: pointer;
}

.dropdown-content li>span {
  display: block;
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #4285f4;
}

.dropdown-content li>span {
  color: #595959 !important;
}

.dropdown-content li.active,
.dropdown-content li:hover {
  background-color: #1072e9;
}

.dropdown-content li.active>span,
.dropdown-content li:hover>span {
  color: #fff;
}

.select-wrapper.anavi-select {
  background-color: #f4f4f4;
  border-radius: 3px 3px 0 0;
}

.select-wrapper.anavi-select input.select-dropdown {
  padding-right: 0;
  height: 3rem;
  border-color: #1072e9;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1rem;
  text-indent: 1rem;
}

.select-wrapper.anavi-select span.caret {
  top: 1rem;
  right: 1rem;
}

.select-wrapper.anavi-select span.caret.material-icons {
  font-size: 16px;
}

.sort-select {
  width: 10.25rem;
}

/* Mobile CSS cho dropdown trong mypage */
@media (max-width: 767px) {
  .dropdown-content {
    top: 100% !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 10000 !important;
  }
}

/* iPhone 12 Pro responsive styles */
@media screen and (min-width: 375px) and (max-width: 390px) {
  /* Add your iPhone 12 Pro specific styles here */
  .select-wrapper.anavi-select span.caret {
    top: 0rem;
    right: 1rem;
  }
}

/* iPad Pro responsive styles */
@media screen and (min-width: 1024px) and (max-width: 1279px) {
  .select-wrapper.anavi-select span.caret {
    top: -20%;
    right: 1rem;
  }
}

/* Desktop large screens (min 1280px) responsive styles */
@media screen and (min-width: 1280px) {
  .select-wrapper.anavi-select span.caret {
    top: 0%;
    right: 1rem;
  }
}

