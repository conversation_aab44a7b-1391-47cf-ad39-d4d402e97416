import pickadate from '/custom_frontend/static/js/directives/pickadate.js';
import { userInfo } from "../../router/router.js";
import { createBreadcrumb } from "../../utils/breadcrumbHelper.js";

const Search = {
    'template': `
<div style="margin-top: 79px;">
    ${createBreadcrumb([
        { text: 'Mi52TOP', link: '/' },
        { text: '案件を探す', link: null, current: true }
    ])}
    <div class="container-fluid title py-2 py-md-4">
        <h1 class="mb-0">案件を探す</h1>
    </div>
    <div class="container-fluid grabient pt-5">
        <div class="row">
            <div class="d-none d-md-block col-12 col-md-4 col-lg-3" id="side-search">
                <div class="card py-4 side-card"><i
                        class="material-icons md-dark md-18 d-md-none search-toggle search-close-btn">close</i>
                        <div class="mb-3" v-if="isSaveQuery"><div class="reset_link_area-pc mx-3 d-none d-md-block"><a class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light" :href="query"><span>保存済み条件で検索</span></a></div></div>
                    <div class="mb-3">
                        <div class="reset_link_area-pc mx-3 d-none"><a
                                class="btn btn-outline-default w-100 reset_search_condition mx-0 mt-0 waves-effect waves-light"
                                href="/opportunities/active"><span>保存済み条件で検索</span></a></div>
                        <div class="reset_link_area-sp d-none"><a class="reset_search_condition mx-0"
                                                                  href="/opportunities/active"><span>保存条件で検索</span></a>
                        </div>
                    </div>
                    <form class="new_opportunity_search_condition" id="opportunity_search_condition_form" novalidate=""
                          @submit.prevent="opp_search" accept-charset="UTF-8" method="get" @input="handleInputChange">
                        <div class="container">
                            <div class="row">
                                <div class="col-12 px-0 px-md-3"><label
                                        class="font-middle mb-3 ex-bold">フリーワード</label>
                                    <div class="mb-4">
                                        <div class="vertical-top d-inline-block w-100"
                                                               data-html="true"
                                                               data-toggle="tooltip" title=""
                                                               data-original-title="類語/関連語でも検索してみましょう (例:セールスフォース → Sales Force や SFDCなど、ERP導入→SAPなど)">
                                            <div class="mb-1"><input class="form-control"
                                                                     autocomplete="off" id="free_keyword_field"
                                                                     type="text"
                                                                     v-model="free_keyword"
                                                                     name="opportunity_search_condition[free_keyword]">
                                            </div>
                                        </div>
                                        <div class="mb-3"><span class="font-middle">を含む</span></div>
                                        <div class="mb-1"><input class="form-control"
                                                                 autocomplete="off" id="negative_keyword_field"
                                                                 type="text"
                                                                 v-model="negative_keyword"
                                                                 name="opportunity_search_condition[negative_keyword]">
                                        </div>
                                        <div class="mb-3"><span class="font-middle">を除く</span></div>
                                    </div>
                                    <div class="mb-5"><label class="font-middle mb-3 ex-bold">得意領域</label>
                                        <div class="design_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion">
                                            <span class="font-size-middle ex-bold">設計</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{ isOpen ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_design" v-show="isOpen">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categories" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'design' + index"
                                                            id_params="design"
                                                            type="checkbox"
                                                            v-model="opp_categories"
                                                            name="opportunity_search_condition[opp_categories][]"
                                                            :value="category.value"
                                                        />
                                                        <label
                                                            :id="'opp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'design' + index"
                                                        >
                                                            {{ category.label }}
                                                        </label>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="dev_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                                @click="toggleAccordion1"><span
                                                class="font-size-middle ex-bold">開発</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{ isOpen1 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_dev" v-show="isOpen1">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categoriesDev" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'dev' + index"
                                                            id_params="dev"
                                                            type="checkbox"
                                                            v-model="opp_categories"
                                                            name="opportunity_search_condition[opp_categories][]"
                                                            :value="category.value"
                                                        />
                                                        <label
                                                            :id="'opp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'dev' + index"
                                                        >
                                                            {{ category.label }}
                                                        </label>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="infra_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion2">
                                            <span class="font-size-middle ex-bold">インフラ</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{ isOpen2 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_infra" v-show="isOpen2">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categoriesInfra" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'infra' + index"
                                                            id_params="infra"
                                                            type="checkbox"
                                                            v-model="opp_categories"
                                                            name="opportunity_search_condition[opp_categories][]"
                                                            :value="category.value"
                                                        />
                                                        <label
                                                            :id="'opp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'infra' + index"
                                                        >
                                                            {{ category.label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="operation_details accordion_open py-1 bg-grey-1 pl-3 mb-2 d-flex clear"
                                            @click="toggleAccordion3">
                                            <span class="font-size-middle ex-bold">運用・保守</span><i
                                                class="material-icons md-dark d-inline-block ml-auto mr-2 align-middle">
                                                {{ isOpen3 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                        </div>
                                        <div class="accordion_contents_operation" v-show="isOpen3">
                                            <div class="mx-auto py-3 pl-3">
                                                <div class="selecting-form row px-3">
                                                    <div v-for="(category, index) in categories3" :key="index" class="custom-control custom-checkbox pl-4 col-12 font-middle">
                                                        <input
                                                            class="custom-control-input"
                                                            :id="'category' + index"
                                                            id_params="category"
                                                            type="checkbox"
                                                            v-model="opp_categories"
                                                            name="opportunity_search_condition[opp_categories][]"
                                                            :value="category.value"
                                                        />
                                                        <label
                                                            :id="'opp_categories_field_label_' + index"
                                                            class="custom-control-label anavi-select-label mb-3"
                                                            :for="'category' + index"
                                                        >
                                                            {{ category.label }}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="font-middle mb-3 ex-bold">単価</label>
                                    <PriceRange
											:min="0"
											:max="300"
											:step="5"
											:startValues="[priceMin, priceMax]"
											@update:price_min="updatePriceMin"
											@update:price_max="updatePriceMax"
										/>
                                    <input type="hidden" :value="priceMin !== null ? priceMin : ''" name="opportunity_search_condition[unit_price_min]">
                                    <input type="hidden" :value="priceMax !== null ? priceMax : ''" name="opportunity_search_condition[unit_price_max]">
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">稼働率</label>
                                        <div class="selecting-form row px-3 with-title">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="utilization_rate_field_opportunity_search_condition_0"
                                                        type="checkbox"
                                                        v-model="utilization_rate"
                                                        name="opportunity_search_condition[utilization_rate][]"
                                                        value="100"><label
                                                    id="utilization_rate_field_label_0"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="utilization_rate_field_opportunity_search_condition_0">100%（フル稼働）</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="utilization_rate_field_opportunity_search_condition_1"
                                                        type="checkbox"
                                                        v-model="utilization_rate"
                                                        name="opportunity_search_condition[utilization_rate][]"
                                                        value="75"><label id="utilization_rate_field_label_1"
                                                                              class="custom-control-label anavi-select-label mb-3"
                                                                              for="utilization_rate_field_opportunity_search_condition_1">75%</label></div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="utilization_rate_field_opportunity_search_condition_2"
                                                        type="checkbox"
                                                        v-model="utilization_rate"
                                                        name="opportunity_search_condition[utilization_rate][]"
                                                        value="50"><label id="utilization_rate_field_label_2"
                                                                              class="custom-control-label anavi-select-label mb-3"
                                                                              for="utilization_rate_field_opportunity_search_condition_2">50%</label></div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="utilization_rate_field_opportunity_search_condition_3"
                                                        type="checkbox"
                                                        v-model="utilization_rate"
                                                        name="opportunity_search_condition[utilization_rate][]"
                                                        value="25"><label id="utilization_rate_field_label_3"
                                                                              class="custom-control-label anavi-select-label mb-3"
                                                                              for="utilization_rate_field_opportunity_search_condition_3">25%</label></div>
                                        </div>
                                    </div>
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">案件の商流</label>
                                        <div class="selecting-form row px-3 with-title">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="opp_type_id_field_opportunity_search_condition_0"
                                                        type="checkbox"
                                                        v-model="opp_type_id"
                                                        name="opportunity_search_condition[opp_type_id][]" value="clnt"><label
                                                    id="opp_type_id_field_label_0"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="opp_type_id_field_opportunity_search_condition_0">エンド</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="opp_type_id_field_opportunity_search_condition_1"
                                                        type="checkbox"
                                                        v-model="opp_type_id"
                                                        name="opportunity_search_condition[opp_type_id][]" value="prim"><label
                                                    id="opp_type_id_field_label_1"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="opp_type_id_field_opportunity_search_condition_1">元請</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="opp_type_id_field_opportunity_search_condition_2"
                                                        type="checkbox"
                                                        v-model="opp_type_id"
                                                        name="opportunity_search_condition[opp_type_id][]" value="subc"><label
                                                    id="opp_type_id_field_label_2"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="opp_type_id_field_opportunity_search_condition_2">一次請</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="opp_type_id_field_opportunity_search_condition_3"
                                                        type="checkbox"
                                                        v-model="opp_type_id"
                                                        name="opportunity_search_condition[opp_type_id][]"
                                                        value="msubc"><label
                                                    id="opp_type_id_field_label_3"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="opp_type_id_field_opportunity_search_condition_3">二次請以降</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">商流への関与</label>
                                        <div class="selecting-form row px-3 with-title">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="involvements_field_opportunity_search_condition_0"
                                                        type="checkbox"
                                                        v-model="involvements"
                                                        name="opportunity_search_condition[involvements]"
                                                        value="enter_sales_channels"><label
                                                    id="involvements_field_label_0"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="involvements_field_opportunity_search_condition_0">掲載企業が商流に入る案件のみ表示する</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">出社頻度</label>
                                        <div class="selecting-form row px-3 with-title">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="work_frequencies_field_opportunity_search_condition_0"
                                                        type="checkbox"
                                                        v-model="work_frequencies"
                                                        name="opportunity_search_condition[work_frequencies][]"
                                                        value="5days"><label id="work_frequencies_field_label_0"
                                                                             class="custom-control-label anavi-select-label mb-3"
                                                                             for="work_frequencies_field_opportunity_search_condition_0">週5日出社</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="work_frequencies_field_opportunity_search_condition_1"
                                                        type="checkbox"
                                                        v-model="work_frequencies"
                                                        name="opportunity_search_condition[work_frequencies][]"
                                                        value="2to4days"><label id="work_frequencies_field_label_1"
                                                                                class="custom-control-label anavi-select-label mb-3"
                                                                                for="work_frequencies_field_opportunity_search_condition_1">週4
                                                〜 2日出社</label></div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="work_frequencies_field_opportunity_search_condition_2"
                                                        type="checkbox"
                                                        v-model="work_frequencies"
                                                        name="opportunity_search_condition[work_frequencies][]"
                                                        value="less_than_1day"><label
                                                    id="work_frequencies_field_label_2"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="work_frequencies_field_opportunity_search_condition_2">週1日以下出社</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="work_frequencies_field_opportunity_search_condition_3"
                                                        type="checkbox"
                                                        v-model="work_frequencies"
                                                        name="opportunity_search_condition[work_frequencies][]"
                                                        value="full_remote"><label id="work_frequencies_field_label_3"
                                                                                   class="custom-control-label anavi-select-label mb-3"
                                                                                   for="work_frequencies_field_opportunity_search_condition_3">フルリモート</label>
                                            </div>
                                        </div>
                                    </div>
                                    <workplace-selector :required-label="false" :inputName="'opportunity_search_condition[workplaces][]'" v-model="specifies_workplaces"></workplace-selector>
                                    <div class="mb-5">
                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                         for="">契約形態</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                    <input class="custom-control-input"
                                                           id="contract_types_field_opportunity_search_condition_0"
                                                           type="checkbox"
                                                           v-model="contract_types"
                                                           name="opportunity_search_condition[contract_types][]"
                                                           value="quas"><label id="contract_types_field_label_0"
                                                                               class="custom-control-label anavi-select-label mb-3"
                                                                               for="contract_types_field_opportunity_search_condition_0">業務委託（準委任）</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                    <input class="custom-control-input"
                                                           id="contract_types_field_opportunity_search_condition_1"
                                                           type="checkbox"
                                                           v-model="contract_types"
                                                           name="opportunity_search_condition[contract_types][]"
                                                           value="subc"><label id="contract_types_field_label_1"
                                                                               class="custom-control-label anavi-select-label mb-3"
                                                                               for="contract_types_field_opportunity_search_condition_1">業務委託（請負）</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                    <input class="custom-control-input"
                                                           id="contract_types_field_opportunity_search_condition_2"
                                                           type="checkbox"
                                                           v-model="contract_types"
                                                           name="opportunity_search_condition[contract_types][]"
                                                           value="temp"><label id="contract_types_field_label_2"
                                                                               class="custom-control-label anavi-select-label mb-3"
                                                                               for="contract_types_field_opportunity_search_condition_2">派遣契約</label>
                                                </div>
                                            </div>
                                        </div>
                                        <label class="font-middle mb-3 ex-bold">案件の特徴</label>
                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                         for="">国籍</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
                                                    <input class="form-check-input"
                                                                                         id="nationality0"
                                                                                         type="radio"
                                                                                         value="nationality"
                                                                                         name="opportunity_search_condition[nationality]"><label
                                                    class="form-check-label" for="nationality0">日本人のみ</label>
                                                </div>
                                                <div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
                                                    <input class="form-check-input"
                                                                                         id="nationality1"
                                                                                         type="radio"
                                                                                         v-model="nationality"
                                                                                         name="opportunity_search_condition[nationality]"><label
                                                    class="form-check-label" for="nationality1">国籍問わず</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                         for="">在留資格有無</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
                                                    <input class="form-check-input"
                                                                                         id="status_resident0"
                                                                                         type="radio"
                                                                                         value="status_resident"
                                                                                         name="opportunity_search_condition[status_resident]"><label
                                                    class="form-check-label" for="status_resident0">有</label>
                                                </div>
                                                <div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
                                                    <input class="form-check-input"
                                                                                         id="status_resident1"
                                                                                         type="radio"
                                                                                         value="status_resident"
                                                                                         name="opportunity_search_condition[status_resident]"><label
                                                    class="form-check-label" for="status_resident1">無し</label>
                                                </div>
                                                <div class="pl-4 col-6 col-md-12 ml-md-3" id="radio-options">
                                                    <input class="form-check-input"
                                                                                         id="status_resident2"
                                                                                         type="radio"
                                                                                         value="status_resident"
                                                                                         name="opportunity_search_condition[status_resident]"><label
                                                    class="form-check-label" for="status_resident2">必須資格</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                     for="">募集対象</label>
                                        <div class="selecting-form row px-3 with-title">
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="trading_restriction_field_opportunity_search_condition_0"
                                                        type="checkbox"
                                                        v-model="trading_restriction"
                                                        name="opportunity_search_condition[trading_restriction][]"
                                                        value="allow_own_employee"><label
                                                    id="trading_restriction_field_label_0"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="trading_restriction_field_opportunity_search_condition_0">自社社員</label>
                                            </div>
                                            <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                <input
                                                        class="custom-control-input"
                                                        id="trading_restriction_field_opportunity_search_condition_1"
                                                        type="checkbox"
                                                        v-model="trading_restriction"
                                                        name="opportunity_search_condition[trading_restriction][]"
                                                        value="allow_via_another_company"><label
                                                    id="trading_restriction_field_label_1"
                                                    class="custom-control-label anavi-select-label mb-3"
                                                    for="trading_restriction_field_opportunity_search_condition_1">協力会社社員</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="accordion_details accordion_close py-3 px-2 clear border-top"
                                            @click="toggleAccordion4"><span
                                            class="float-left pr-7">詳細検索条件</span><i
                                            class="material-icons md-dark float-right d-block">
                                            {{ isOpen4 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                                    </div>
                                    <div class="accordion_contents_details mb-4 pt-3" v-show="isOpen4">
                                        <div class="mx-auto mb-3"><label class="font-middle mb-3 ex-bold"
                                                                         for="">案件内容の確定状況</label>
                                            <div class="selecting-form row px-3 with-title">
                                                <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                    <input class="custom-control-input"
                                                           id="order_accuracy_id_field_opportunity_search_condition_0"
                                                           type="checkbox"
                                                           v-model="order_accuracy_ids"
                                                           name="opportunity_search_condition[order_accuracy_id][]"
                                                           value="afte"><label id="order_accuracy_id_field_label_0"
                                                                               class="custom-control-label anavi-select-label mb-3"
                                                                               for="order_accuracy_id_field_opportunity_search_condition_0">確定済み</label>
                                                </div>
                                                <div class="custom-control custom-checkbox pl-4 col-6 col-md-12 ml-md-3">
                                                    <input class="custom-control-input"
                                                           id="order_accuracy_id_field_opportunity_search_condition_1"
                                                           type="checkbox"
                                                           v-model="order_accuracy_ids"
                                                           name="opportunity_search_condition[order_accuracy_id][]"
                                                           value="befo"><label id="order_accuracy_id_field_label_1"
                                                                               class="custom-control-label anavi-select-label mb-3"
                                                                               for="order_accuracy_id_field_opportunity_search_condition_1">確定前</label>
                                                </div>
                                            </div>
                                        </div>
                                        <label class="font-middle mb-3 ex-bold">契約開始日</label>
                                        <div class="row pl-3">
                                            <div class="col-9">
                                                <div class="mx-auto mb-3"><input class="form-control picker__input"
                                                                                 autocomplete="off"
                                                                                 v-pickadate="{ model: 'contract_startdate_at' }"
                                                                                 v-model="contract_startdate_at"
                                                                                 id="contract_startdate_at_field"
                                                                                 type="text"
                                                                                 name="opportunity_search_condition[contract_startdate_at]"
                                                                                 readonly="" aria-haspopup="true"
                                                                                 aria-expanded="false"
                                                                                 aria-readonly="false"
                                                                                 aria-owns="contract_startdate_at_field_root">

                                                </div>
                                            </div>
                                            <div class="col-1 p-0"><i
                                                    class="material-icons md-grey md-18 inline-unit-icon calender-icon">date_range</i>
                                            </div>
                                        </div>
                                        <label class="font-middle mb-3 ex-bold">募集人数</label>
                                        <div class="row pl-3">
                                            <div class="col-8">
                                                <div class="mx-auto mb-5"><input type="number" min="0"
                                                                                 class="form-control"
                                                                                 autocomplete="off"
                                                                                 v-model="participants"
                                                                                 id="participants_field"
                                                                                 name="opportunity_search_condition[participants]">
                                                </div>
                                            </div>
                                            <div class="col pl-0"><label class="inline-unit-label">人</label></div>
                                        </div>
                                        <div class="mx-auto mb-5"><label
                                                class="font-middle mb-3 ex-bold">面談回数</label>
                                            <div v-dropdown="{modelValue: '', listType: 'interview_count_id'}" @selected="select_interview"><input type="hidden" v-model="interview_count_id" name="opportunity_search_condition[interview_count_id]"></div>
                                        </div>
                                        <div class="accordion-close-area text-center" @click="toggleAccordion4">閉じる<i
                                                class="material-icons md-dark md-18">close</i></div>
                                    </div>
                                    <div class="border-bottom mb-5"></div>
                                    <div class="text-center d-none d-md-block search-area py-2">
										<button name="button" id="resume-search-button" class="btn btn-default font-middle w-100 mx-0 waves-effect waves-light" data-disable-with="検索中" @click="opp_search">
                                            <div id="btn-text" v-if="isFinding" class="">検索中</div>
											<div class="py-2" id="loader" v-else-if="isLoading">
												<div class="loader"></div>
											</div>
											<div id="btn-text" v-else class=""><span class="font-extralarge" id="search-count">{{opp_count}}</span> 件<br>この条件で検索</div>
										</button>
										<div class="py-2"><a href="/opportunities/active?search_reset=true"><span>条件をリセット</span></a></div>
									</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-12 col-md-8 col-lg-9">
                <div class="row">
                    <div class="col-12 d-none d-md-block mb-4">
                        <div class="d-flex"><span class="mr-3 nowrap">よく検索されるキーワード</span>
                            <div>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('Java')"><span>Java</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('SAP')"><span>SAP</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('PHP')"><span>PHP</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('COBOL')"><span>COBOL</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('C#')"><span>C#</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('PMO')"><span>PMO</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('VB.NET')"><span>VB.NET</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('PL/SQL')"><span>PL/SQL</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('Linux')"><span>Linux</span></a>
								<a class="mr-4 default-main-color font-middle d-inline-block" @click="opp_keyword('Android')"><span>Android</span></a>
							</div>
                        </div>
                    </div>
                </div>
                <div class="row mr-0 ml-0">
					<div class="col-12 mt-3 border p-3 px-md-4 bg-grey-2 rounded-sm" id="border-light">
						<div class="position-relative">
							<div class="d-sm-flex search-condition-area line-height-180">
								<div class="text-nowrap mb-2 mb-sm-n2 ml-3 ml-sm-0">検索条件</div>
								<div class="pl-3">
									<div v-if="this.categorizedExps && Object.keys(this.categorizedExps || {}).length > 0" class="d-sm-inline border-left">
										<span class="mr-3 custom-grey-5-text">得意領域</span>
										<div v-for="(values, category) in this.categorizedExps" :key="category" class="d-sm-inline mb-2 mb-sm-0">
										<span class="border-grey-3 bg-white mr-2 px-2">{{ category }}</span>
										<div class="d-sm-inline">
											<span v-for="value in values" :key="value" class="mr-3">{{ value }}</span>
										</div>
										</div>
									</div>

									<div v-if="validConditions.length">
										<div v-for="condition in validConditions" :key="condition.key" class="d-sm-inline border-left">
										<span class="mr-3 custom-grey-5-text">{{ condition.label }}</span>
										<div class="d-sm-inline">
											<span class="mr-3">{{ condition.value }}</span>
										</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="text-center">
							<a v-if="this.isloggedin.length > 0" class="btn btn-outline-default px-5 btn-white mt-3 mb-0 font-default table-responsive-sm save_search_condition_btn waves-effect waves-light" href="javascript:void(0);" @click="saveQuery"> この条件を保存 </a>
							<a v-if="isSaveQuery" class="btn btn-outline-blue-grey px-5 btn-white mt-3 mb-0 font-default table-responsive-sm delete_search_condition_btn waves-effect waves-light" rel="nofollow" href="/opportunities/active" @click="unSaveQuery"> 保存条件を破棄 </a>
						</div>
					</div>
				</div>
                <hr class="mt-0 mb-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
					<div class="font-middle"><span class="font-ll ex-bold" id="total-count">{{this.totalRecord}}</span> 件中 {{totalRecord.length ? 1 : 0 }}〜{{ displayedRecords }}件</div>
					<div class="position-relative pl-3" ref="dropdown">
                        <!-- Khu vực hiển thị và bấm vào -->
                        <div class="d-flex justify-content-end sort-display-area p-2" @click="isOpen5 = !isOpen5">
                            <label class="pr-2 mb-0">{{ selectedOption }}</label>
                            <i class="material-icons custom-grey-6-text pl-1">
                            {{ isOpen5 ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}
                            </i>
                        </div>

                        <!-- Danh sách hiển thị khi bấm vào -->
                        <ul class="bg-white sort-options-area text-right" v-show="isOpen5">
                            <li v-for="option in options" :key="option"
                                :class="{ 'sort-active': option === selectedOption }"
                                @click="selectOption(option)">
                            {{ option }}
                            <i v-if="option === selectedOption" class="material-icons custom-grey-6-text">done</i>
                            </li>
                        </ul>
                    </div>
				</div>
                <div class="row">
                    <div class="col-12">
                        <div v-for="opp in opps" :key="opp.id">
                            <a class="card mb-4 w-100 hoverable d-block" :href="'/opportunities/' + opp.id + '/detail?prev_next_display=display'" @click="add_viewer(opp.id, this.isloggedin)">
                                <div class="card-header default-bg-color-opacity-10 d-md-flex justify-content-start">
                                    <div v-if="viewedStatuses[opp.id] === true" class="viewed-flag-pc"><span class="font-small">閲覧済</span></div>
                                    <div v-else class="new-flag-pc"><span class="font-small">NEW!</span></div>
                                    <h5 class="mb-0 ml-5">
                                        <div class="mr-3 mb-2 mb-md-0 d-flex align-items-center position-relative">
                                            <div class="default-main-color">{{opp.subject}}</div>
                                        </div>
                                        <div class="d-md-inline-block mt-1 font-small custom-grey-6-text">
                                            <span>更新</span><span class="px-1">:</span><span>{{ formatDate(opp.updated_at) }}</span>
                                        </div>
                                        <div class="d-md-inline-block ml-1 mt-1 font-small custom-grey-6-text">
                                            <span>登録</span><span class="px-1">:</span><span>{{ formatDate(opp.created_at) }}</span>
                                        </div>
                                    </h5>
                                </div>
                                <div class="pt-3 pb-4 px-3 px-md-4" style="color: black;">
                                    <div class="mb-2">
                                        <div class="d-md-inline-block mt-1 ex-bold">
                                            {{ formatDate(opp.contract_startdate_at) }} 〜 {{ formatDate(opp.contract_enddate_at) }}
                                        </div>
                                        <span class="badge-pill font-small ml-md-2 grey">一次請</span>
                                    </div>
                                    <p class="mt-1">●案件概要： {{opp.requirements}}</p>
                                    <div class="row mb-2">
                                        <div class="col-12 col-lg-6 d-flex align-items-start mb-2">
                                            <label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">応募期限</label>
                                            <span>{{ formatDate(opp.expired_at) }}</span>
                                        </div>
                                        <div class="col-12 col-lg-6 mb-2">
                                            <label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">会社名</label>
                                            <span>{{ opp.publish_company_name_status_id === 'public' ? opp.company_name : '非公開' }}</span>
                                        </div>
                                        <div class="col-12 col-lg-6 mb-2">
                                            <label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">単価</label>
                                            <span>{{opp.unit_price_min}}万円 〜 {{opp.unit_price_max}}万円 / 月</span>
                                        </div>
                                        <div class="col-12 col-lg-6 d-flex mb-2">
                                            <label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">稼働率</label>
                                            <span>{{ (opp?.utilization_rate || '').split(',').map(rate => categoryMap[rate] || '').join('／') || '' }}</span>
                                        </div>
                                        <div class="col-12 col-lg-6 d-flex mb-2">
                                            <label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">出社頻度</label>
                                            <span>{{ (opp?.work_frequencies || '').split(',').map(fr => categoryMap[fr] || '').join('／') || '' }}</span>
                                        </div>
                                        <div class="col-12 col-lg-6 d-flex mb-2">
                                            <label class="custom-grey-text col-3 col-xl-2 pl-0 ex-bold mb-0">就業場所</label>
                                            <span>{{opp.specifies_workplaces}}</span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <!-- ブロックモーダルのアイコン -->
                                        <div class="d-inline-block">
                                            <div class="d-inline-block" data-toggle="tooltip" data-original-title="テキストで表示">
                                                <a class="mdb-modal-form custom-grey-6-text z-2 p-2" :data-target="'#display_text_format_' + opp.id" data-toggle="modal" @click.stop.prevent="openModal(opp.id)">
                                                <i class="material-icons ml-3 mr-1 align-text-bottom">file_copy</i>
                                                <span>テキスト表示</span></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </a>
                            <div aria-labelledby="display_text_format_modal" class="modal" :id="'display_text_format_' + opp.id" tabindex="-1" aria-modal="true" role="dialog">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title w-100">案件詳細</h4>
                                            <button aria-label="Close" class="close" data-dismiss="modal" type="button"><span aria-hidden="true"><i class="material-icons md-dark mb-36">clear</i></span></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-4">**********************************************************<br>
                                            ◆案件ID: {{opp.id}}<br>
                                            ◆案件名: {{opp.subject}}<br>
                                            ◆案件への関わり: {{categoryMap[opp.involvements]}}<br>
                                            ◆案件の商流: {{categoryMap[opp.opp_type_id] || opp.opp_type_id}}<br>
                                            ◆案件内容: <br>{{opp.requirements}}<br>
                                            ◆人財要件: <br>{{opp.skill_requirements}}<br>
                                            ◆単価: {{opp.unit_price_min}}万円 〜 {{opp.unit_price_max}}万円 / 月<br>
                                            ◆稼働率: {{(opp?.utilization_rate || '').split(',').map(rate => categoryMap[rate] || '').join('／')}}<br>
                                            ◆出社頻度: {{ (opp?.work_frequencies || '').split(',').map(fr => categoryMap[fr] || '').join('／') || '' }}<br>
                                            ◆就業場所: {{opp.specifies_workplaces}}<br>
                                            ◆契約形態: {{ (opp?.contract_types || '').split(',').map(type => categoryMap[type] || '').join('／') || '' }}<br>
                                            ◆募集人数: {{opp.participants}}人<br>
                                            ◆面談回数: {{ (opp?.interview_count_id || '').split(',').map(it => categoryMap[it] || '').join('／') || '' }}<br>
                                            ◆契約期間: {{convertDateToJapanese(opp.contract_startdate_at)}} 〜 {{convertDateToJapanese(opp.contract_enddate_at)}}<br>
                                            ◆募集対象: {{ (opp?.trading_restriction || '').split(',').map(tr => categoryMap[tr] || '').join('／') || '' }}<br>
                                            **********************************************************
                                            </div>
                                            <div class="text-center"><a aria-label="Close"
                                                                        class="btn btn-blue-grey waves-effect waves-light"
                                                                        data-dismiss="modal">閉じる</a></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <nav role="navigation" class="pagination">
					<ul class="pagination">
						<li class="page-item" :class="{ disabled: currentPage === 1 }">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage - 1)"></a>
						</li>
						<li v-for="page in visiblePages" :key="page" class="page-item" :class="{ active: page === currentPage }">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(page)">{{ page }}</a>
						</li>
						<li class="page-item disabled" v-if="totalPages > 5 && currentPage < totalPages - 2">
							<a class="page-link waves-effect" href="#">…</a>
						</li>
						<li class="page-item" v-if="totalPages > 5">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(totalPages)">{{ totalPages }}</a>
						</li>
						<li class="page-item" :class="{ disabled: currentPage === totalPages }">
							<a class="page-link waves-effect" href="#" @click.prevent="changePage(currentPage + 1)"></a>
						</li>
					</ul>
				</nav>
            </div>
        </div>
    </div>
    <div style="margin-top: 79px;">
        <link rel="stylesheet" href="/custom_frontend/static/css/opportunities/active.css"/>
        <link rel="stylesheet" type="text/css" href="/custom_frontend/static/css/dropdown.css"/>
    `,
    data() {
        return {
            isOpen: false,
            isOpen1: false,
            isOpen2: false,
            isOpen3: false,
            isOpen4: false,
            isOpen5: false,
            isLoading: false,
            priceMin: null,
            priceMax: null,
            sortType: '',
            params_search: {},
            categorizedExps: {},
            viewedStatuses: {},
            isloggedin: userInfo ? userInfo.user_id : '',
            isSaveQuery: localStorage.getItem("query") !== null,
            query: localStorage.getItem('query') || '',
            opps: [],
            currentPage: 1,
            totalPages: 1,
            totalRecord: 0,
            opp_count: 0,
            free_keyword: '',
            negative_keyword: '',
            nationality: '',
            status_resident: '',
            opp_categories: [],
            opp_type_id: [],
            order_accuracy_ids: [],
            contract_types: [],
            utilization_rate: [],
            work_frequencies: [],
            specifies_workplaces: [],
            involvements: null,
            trading_restriction: [],
            contract_startdate_at: '',
            participants: null,
            interview_count_id: '',
            searchConditions: [
                { key: 'opportunity_search_condition[free_keyword]', label: 'フリーキーワード' },
                { key: 'opportunity_search_condition[negative_keyword]', label: '除外キーワード' },
                { key: 'opportunity_search_condition[unit_price_min]', label: '単価' },
                { key: 'opportunity_search_condition[unit_price_max]', label: '単価' },
                { key: 'opportunity_search_condition[utilization_rate][]', label: '稼働率' },
                { key: 'opportunity_search_condition[opp_type_id][]', label: '案件の商流' },
                { key: 'opportunity_search_condition[involvements]', label: '商流への関与' },
                { key: 'opportunity_search_condition[work_frequencies][]', label: '出社頻度' },
                { key: 'opportunity_search_condition[workplaces][]', label: '就業場所' },
                { key: 'opportunity_search_condition[contract_types][]', label: '契約形態' },
                { key: 'opportunity_search_condition[trading_restriction][]', label: '募集対象' },
                { key: 'opportunity_search_condition[order_accuracy_id][]', label: '案件内容の確定状況' },
                { key: 'opportunity_search_condition[contract_startdate_at]', label: '契約開始日' },
                { key: 'opportunity_search_condition[interview_count_id]', label: '面談回数' },
                { key: 'opportunity_search_condition[participants]', label: '募集人数' }
            ],
            categoryMapping: {
                コンサル: 'design',
                開発: 'dev_',
                インフラ: 'infra_',
                運用・保守: 'operation_'
            },
            selectedOption: "新着（降順）", // Mặc định
            options: ["新着（降順）", "更新（降順）", "単価（降順）"],
            sortMap: {
                "新着（降順）": "created_at",
                "更新（降順）": "updated_at",
                "単価（降順）": "unit_price_max"
            },
            categories: [
                { value: "design_pmo", label: "PMO" },
                { value: "design_pmpl", label: "PM・PL" },
                { label: "DX", value: "design_DX" },
                { label: "クラウド", value: "design_cloud" },
                { label: "モダナイゼション", value: "design_strategy" },
                { label: "セキュリティ", value: "design_work" },
                { label: "ITインフラ", value: "design_it" },
                { label: "AI", value: "design_ai" },
            ],
            categoriesDev: [
                { value: "dev_pmo", label: "PMO" },
                { value: "dev_pmpl", label: "PM・PL" },
                { value: "dev_web", label: "Webシステム" },
                { value: "dev_ios", label: "IOS" },
                { value: "dev_android", label: "Android" },
                { value: "dev_control", label: "制御" },
                { value: "dev_embedded", label: "組込" },
                { value: "dev_ai", label: "AI・DL・ML" },
                { value: "dev_test", label: "テスト" },
                { value: "dev_cloud", label: "クラウド" },
                { value: "dev_architect", label: "サーバ" },
                { value: "dev_bridge_se", label: "データベース" },
                { value: "dev_network", label: "ネットワーク" },
                { value: "dev_mainframe", label: "メインフレーム" },
            ],
            categoriesInfra: [
                { value: "infra_pmo", label: "PMO" },
                { value: "infra_pmpl", label: "PM・PL" },
                { value: "infra_server", label: "サーバー" },
                { value: "infra_network", label: "ネットワーク" },
                { value: "infra_db", label: "データベース" },
                { value: "infra_cloud", label: "クラウド" },
                { value: "infra_virtualized", label: "仮想化" },
                { value: "infra_mainframe", label: "メインフレーム" },
            ],
            categories3: [
                { value: "operation_pmo", label: "業務システム" },
                { value: "operation_pmpl", label: "オープン" },
                { label: "クラウド", value: "operation_DX" },
                { label: "メインフレーム", value: "operation_mainframe" },
                { label: "ヘルプデスク", value: "operation_strategy" },
            ],
            isFinding: false,
        }
    },
    methods: {
        toggleAccordion() {
            this.isOpen = !this.isOpen;
        },
        toggleAccordion1() {
            this.isOpen1 = !this.isOpen1;
        },
        toggleAccordion2() {
            this.isOpen2 = !this.isOpen2;
        },
        toggleAccordion3() {
            this.isOpen3 = !this.isOpen3;
        },
        toggleAccordion4() {
            this.isOpen4 = !this.isOpen4;
        },
        selectOption(option) {
            this.selectedOption = option; // Cập nhật nội dung hiển thị
            this.isOpen5 = false; // Đóng dropdown
            const sort_type = this.sortMap[option]; // Lấy giá trị sort_type từ map
            this.updateURLAndReload(1, sort_type); // Cập nhật URL & load lại trang
        },
        updateURLAndReload(page, sort_type) {
            const url = new URL(window.location.href);
            url.searchParams.set('page', page);
            url.searchParams.set('sort_type', sort_type);
            window.location.href = url.toString(); // Cập nhật URL và reload trang
        },
        closeDropdown(e) {
            if (!this.$refs.dropdown.contains(e.target)) this.isOpen5 = false;
        },
        async fetch_search(page = 1, sort_type = 'created_at') {
            try {
                let params = new URLSearchParams();
                params.append("opportunity_search_condition[free_keyword]", this.free_keyword || '');
                params.append("opportunity_search_condition[negative_keyword]", this.negative_keyword || '');
                params.append("opportunity_search_condition[interview_count_id]", this.interview_count_id === "未選択" ? '' : this.interview_count_id);
                params.append("opportunity_search_condition[participants]", this.participants || '');
                params.append("opportunity_search_condition[contract_startdate_at]", this.contract_startdate_at || '');
                params.append("opportunity_search_condition[unit_price_min]", this.priceMin || '');
                params.append("opportunity_search_condition[unit_price_max]", this.priceMax || '');
                params.append("opportunity_search_condition[involvements]", this.involvements || '');


                // Xử lý mảng status[]
                if (this.trading_restriction.length > 0) {
                    this.trading_restriction.forEach(value => {
                        params.append("opportunity_search_condition[trading_restriction][]", value);
                    });
                }

                // Xử lý mảng exp_categories[]
                if (this.opp_categories.length > 0) {
                    this.opp_categories.forEach(value => {
                        params.append("opportunity_search_condition[opp_categories][]", value);
                    });
                }

                if (this.order_accuracy_ids.length > 0) {
                    this.order_accuracy_ids.forEach(value => {
                        params.append("opportunity_search_condition[order_accuracy_id][]", value);
                    });
                }

                if (this.opp_type_id.length > 0) {
                    this.opp_type_id.forEach(value => {
                        params.append("opportunity_search_condition[opp_type_id][]", value);
                    });
                }

                if (this.contract_types.length > 0) {
                    this.contract_types.forEach(value => {
                        params.append("opportunity_search_condition[contract_types][]", value);
                    });
                }

                if (this.utilization_rate.length > 0) {
                    this.utilization_rate.forEach(value => {
                        params.append("opportunity_search_condition[utilization_rate][]", value);
                    });
                }

                if (this.work_frequencies.length > 0) {
                    this.work_frequencies.forEach(value => {
                        params.append("opportunity_search_condition[work_frequencies][]", value);
                    });
                }

                if (this.specifies_workplaces.length > 0) {
                    this.specifies_workplaces.forEach(value => {
                        params.append("opportunity_search_condition[workplaces][]", value);
                    });
                }
                // Gọi API lấy dữ liệu
                const response = await fetch(`/api/opportunity_search_condition/search?${params.toString()}&page=${page}&sort_type=${sort_type}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                });
                const data = await response.json();
                if (data.success === true) {
                    this.opps = data.data;
                    this.currentPage = data.current_page;
                    this.totalPages = data.total_pages;
                    this.totalRecord = data.total_records;
                } else {
                    console.warn("API data is not in the correct format:", data);
                }
                this.$nextTick(() => {
                    pickadate.updatePickadate('contract_startdate_at_field', this.contract_startdate_at);
                });
            } catch (error) {
                console.error('Error fetching API:', error.message);
                this.errorMessage = `Error fetching opportunities: ${error.message}`;
            }
        },
        async opp_search() {
            this.isFinding = true;
            let params = new URLSearchParams();
            params.append("opportunity_search_condition[free_keyword]", this.free_keyword || '');
            params.append("opportunity_search_condition[negative_keyword]", this.negative_keyword || '');
            params.append("opportunity_search_condition[interview_count_id]", this.interview_count_id === "未選択" ? '' : this.interview_count_id);
            params.append("opportunity_search_condition[participants]", this.participants || '');
            params.append("opportunity_search_condition[contract_startdate_at]", this.contract_startdate_at || '');
            params.append("opportunity_search_condition[unit_price_min]", this.priceMin || '');
            params.append("opportunity_search_condition[unit_price_max]", this.priceMax || '');
            params.append("opportunity_search_condition[involvements]", this.involvements || '');
            params.append("opportunity_search_condition[accordion_open_design]", this.isOpen ? "yes" : "no");
            params.append("opportunity_search_condition[accordion_open_dev]", this.isOpen1 ? "yes" : "no");
            params.append("opportunity_search_condition[accordion_open_infra]", this.isOpen2 ? "yes" : "no");
            params.append("opportunity_search_condition[accordion_open_operation]", this.isOpen3 ? "yes" : "no");
            params.append("opportunity_search_condition[accordion_open_details]", this.isOpen4 ? "yes" : "no");


            // Xử lý mảng status[]
            if (this.trading_restriction.length > 0) {
                this.trading_restriction.forEach(value => {
                    params.append("opportunity_search_condition[trading_restriction][]", value);
                });
            }

            // Xử lý mảng opp_categories[]
            if (this.opp_categories.length > 0) {
                this.opp_categories.forEach(value => {
                    params.append("opportunity_search_condition[opp_categories][]", value);
                });
            }

            if (this.order_accuracy_ids.length > 0) {
                this.order_accuracy_ids.forEach(value => {
                    params.append("opportunity_search_condition[order_accuracy_id][]", value);
                });
            }

            if (this.opp_type_id.length > 0) {
                this.opp_type_id.forEach(value => {
                    params.append("opportunity_search_condition[opp_type_id][]", value);
                });
            }

            if (this.contract_types.length > 0) {
                this.contract_types.forEach(value => {
                    params.append("opportunity_search_condition[contract_types][]", value);
                });
            }

            if (this.utilization_rate.length > 0) {
                this.utilization_rate.forEach(value => {
                    params.append("opportunity_search_condition[utilization_rate][]", value);
                });
            }

            if (this.work_frequencies.length > 0) {
                this.work_frequencies.forEach(value => {
                    params.append("opportunity_search_condition[work_frequencies][]", value);
                });
            }

            if (this.specifies_workplaces.length > 0) {
                this.specifies_workplaces.forEach(value => {
                    params.append("opportunity_search_condition[workplaces][]", value);
                });
            }

            window.location.href = `/opportunity_search_condition/search?${params.toString()}`;
        },
        async count_opp() {
            this.isLoading = true;
            try {
                let params = new URLSearchParams();
                params.append("opportunity_search_condition[free_keyword]", this.free_keyword || '');
                params.append("opportunity_search_condition[negative_keyword]", this.negative_keyword || '');
                params.append("opportunity_search_condition[interview_count_id]", this.interview_count_id === "未選択" ? '' : this.interview_count_id);
                params.append("opportunity_search_condition[participants]", this.participants || '');
                params.append("opportunity_search_condition[contract_startdate_at]", this.contract_startdate_at || '');
                params.append("opportunity_search_condition[unit_price_min]", this.priceMin || '');
                params.append("opportunity_search_condition[unit_price_max]", this.priceMax || '');
                params.append("opportunity_search_condition[involvements]", this.involvements || '');


                // Xử lý mảng status[]
                if (this.trading_restriction.length > 0) {
                    this.trading_restriction.forEach(value => {
                        params.append("opportunity_search_condition[trading_restriction][]", value);
                    });
                }

                // Xử lý mảng exp_categories[]
                if (this.opp_categories.length > 0) {
                    this.opp_categories.forEach(value => {
                        params.append("opportunity_search_condition[opp_categories][]", value);
                    });
                }

                if (this.order_accuracy_ids.length > 0) {
                    this.order_accuracy_id.forEach(value => {
                        params.append("opportunity_search_condition[order_accuracy_id][]", value);
                    });
                }

                if (this.opp_type_id.length > 0) {
                    this.opp_type_id.forEach(value => {
                        params.append("opportunity_search_condition[opp_type_id][]", value);
                    });
                }

                if (this.contract_types.length > 0) {
                    this.contract_types.forEach(value => {
                        params.append("opportunity_search_condition[contract_types][]", value);
                    });
                }

                if (this.utilization_rate.length > 0) {
                    this.utilization_rate.forEach(value => {
                        params.append("opportunity_search_condition[utilization_rate][]", value);
                    });
                }

                if (this.work_frequencies.length > 0) {
                    this.work_frequencies.forEach(value => {
                        params.append("opportunity_search_condition[work_frequencies][]", value);
                    });
                }

                if (this.specifies_workplaces.length > 0) {
                    this.specifies_workplaces.forEach(value => {
                        params.append("opportunity_search_condition[workplaces][]", value);
                    });
                }
                // Gọi API lấy dữ liệu
                const response = await fetch(`/api/opportunity_count?${params.toString()}`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                });
                const data = await response.json();
                if (data.success === true) {
                    this.opp_count = data.total_records;
                } else {
                    console.warn("API data is not in the correct format:", data);
                }
            } catch (error) {
                console.error('Error fetching API:', error.message);
                this.errorMessage = `Error fetching opportunities: ${error.message}`;
            } finally {
                this.isLoading = false; // Dừng loading khi API trả về kết quả
            }
        },
        // Hàm gọi khi có sự thay đổi của form
        handleInputChange() {
            // Nếu đã có một timeout trước đó, xóa nó đi để tránh gọi API quá nhiều
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            // Thiết lập timeout mới để trì hoãn việc gọi API
            this.searchTimeout = setTimeout(() => {
                this.count_opp(); // Gọi API sau khi người dùng ngừng thay đổi trong 500ms
            }, 500); // 500ms là khoảng thời gian trì hoãn
        },
        async opp_keyword(keyword) {
            let params = new URLSearchParams();
            params.append("opportunity_search_condition[free_keyword]", keyword);
            window.location.href = `/opportunity_search_condition/search?${params.toString()}`;
        },
        updatePriceMin(value) {
            if (this.priceMin !== value) {
                this.priceMin = value;
            }
        },
        updatePriceMax(value) {
            if (this.priceMax !== value) {
                this.priceMax = value;
            }
        },
        loadQueryParams() {
            const urlParams = new URLSearchParams(window.location.search);

            this.free_keyword = urlParams.get("opportunity_search_condition[free_keyword]") || "";
            this.negative_keyword = urlParams.get("opportunity_search_condition[negative_keyword]") || "";
            this.interview_count_id = urlParams.get("opportunity_search_condition[interview_count_id]") || "";
            this.participants = urlParams.get("opportunity_search_condition[participants]") || "";
            this.contract_startdate_at = urlParams.get("opportunity_search_condition[contract_startdate_at]") || "";
            this.priceMin = urlParams.get("opportunity_search_condition[unit_price_min]" || "");
            this.priceMax = urlParams.get("opportunity_search_condition[unit_price_max]")?.trim() || null;
            this.involvements = urlParams.get("opportunity_search_condition[involvements]" || "")
            this.isOpen = urlParams.get("opportunity_search_condition[accordion_open_design]") === "yes";
            this.isOpen1 = urlParams.get("opportunity_search_condition[accordion_open_dev]") === "yes";
            this.isOpen2 = urlParams.get("opportunity_search_condition[accordion_open_infra]") === "yes";
            this.isOpen3 = urlParams.get("opportunity_search_condition[accordion_open_operation]") === "yes";
            this.isOpen4 = urlParams.get("opportunity_search_condition[accordion_open_details]") === "yes";


            // Xử lý các tham số dạng mảng
            this.trading_restriction = urlParams.getAll("opportunity_search_condition[trading_restriction][]");
            this.opp_categories = urlParams.getAll("opportunity_search_condition[opp_categories][]")
            this.order_accuracy_ids = urlParams.getAll("opportunity_search_condition[order_accuracy_id][]");
            this.contract_types = urlParams.getAll("opportunity_search_condition[contract_types][]");
            this.utilization_rate = urlParams.getAll("opportunity_search_condition[utilization_rate][]");
            this.work_frequencies = urlParams.getAll("opportunity_search_condition[work_frequencies][]");
            this.specifies_workplaces = urlParams.getAll("opportunity_search_condition[workplaces][]");
            this.opp_type_id = urlParams.getAll("opportunity_search_condition[opp_type_id][]");
            console.log('opp_type_id', this.opp_type_id);
        },
        changePage(page) {
            if (page >= 1 && page <= this.totalPages) {
                window.location.href = `?page=${page}`; // Cập nhật URL và reload trang
            }
        },
        restoreSelection() {
            const urlParams = new URLSearchParams(window.location.search);
            const sort_type = urlParams.get('sort_type') || 'created_at';
            this.selectedOption = Object.keys(this.sortMap).find(key => this.sortMap[key] === sort_type) || "新着（降順）";
        },
        restoreSelections() {
            const urlParams = new URLSearchParams(window.location.search);
            const interview_count_idEN = urlParams.get("opportunity_search_condition[interview_count_id]");
            // Chuyển đổi giá trị từ EN → JP
            this.interview_count_id = Object.keys(optionsMappings.interview_count_id).find(
                key => optionsMappings.interview_count_id[key] === interview_count_idEN
            ) || "未選択";

            // Gửi sự kiện để directive cập nhật dropdown
            document.dispatchEvent(new CustomEvent("restoreDropdown", { detail: { key: "interview_count_id", value: this.interview_count_id } }));
        },
        select_interview(event) {
            this.interview_count_id = event.detail; // Nhận giá trị từ dropdown
        },
        saveQuery() {
            this.isSaveQuery = true
            localStorage.setItem('query', window.location.href)
        },
        unSaveQuery() {
            this.isSaveQuery = false
            localStorage.removeItem('query')
        },
        openModal(oppId, event) {
            // Ngừng hành động mặc định của thẻ <a> khi nhấn nút mở modal
            const modalId = '#display_text_format_' + oppId;

            // Dùng jQuery để mở modal
            $(modalId).modal('show');
        },
        convertDateToJapanese(date) {
            if (!date) return "日付なし"; // Nếu giá trị rỗng/null, trả về "Không có ngày"

            // Nếu date là chuỗi, chuyển thành Date object
            if (typeof date === "string") {
                date = new Date(date);
            }

            // Kiểm tra nếu date không hợp lệ
            if (!(date instanceof Date) || isNaN(date)) {
                return "無効な日付"; // Trả về "Ngày không hợp lệ"
            }

            // Chuyển đổi sang format Nhật Bản
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        },
        async add_viewer(opp_id, user_id) {
            try {
                // Tạo URL với user_id nếu có
                let url = `/api/view_opp?opp_id=${encodeURIComponent(opp_id)}`;
                if (user_id) {
                    url += `&user_id=${encodeURIComponent(user_id)}`;
                }

                // Gửi yêu cầu GET đến API
                const response = await fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                });

                // Kiểm tra nếu có lỗi từ phía server
                if (!response.ok) {
                    throw new Error('Error: ' + response.statusText);
                }

                // Xử lý kết quả
                const data = await response.json();
                console.log('Response:', data);

            } catch (error) {
                console.error('Fetch error:', error);
                this.errorMessage = error.message;
            }
        },
        async isViewed(oppId) {
            if (this.viewedStatuses[oppId] === undefined) {
                await this.fetchViewedStatus(oppId); // Chỉ gọi API nếu chưa có trong viewedStatuses
            }
            return this.viewedStatuses[oppId] || false; // Trả về trạng thái đã xem nếu có, false nếu chưa xem
        },

        // Lấy trạng thái đã xem của opp từ API
        async fetchViewedStatus(oppId) {
            try {
                const response = await fetch(`/api/check_view_opp?user_id=${this.isloggedin}&opp_id=${oppId}`);
                const data = await response.json();
                this.viewedStatuses[oppId] = data.success ? data.viewed : false; // Lưu kết quả vào viewedStatuses
            } catch (error) {
                console.error('Error fetching viewed status:', error);
                this.viewedStatuses[oppId] = false; // Mặc định là chưa xem nếu có lỗi
            }
        },

        // Kiểm tra tất cả các opp đã được xem hay chưa (chỉ gọi API 1 lần cho mỗi opp)
        async checkAllViewStatuses() {
            const oppIds = this.opps.map(opp => opp.id);
            await Promise.all(oppIds.map(id => this.isViewed(id))); // Kiểm tra tất cả oppId trong danh sách resumes
        },
        formatCondition(key) {
            if (!this.params_search[key] || this.params_search[key].length === 0) return null;

            // Xử lý giá (asking_unit_price_min, asking_unit_price_max)
            if (key === 'opportunity_search_condition[unit_price_min]' || key === 'opportunity_search_condition[unit_price_max]') {
                const minPrice = this.params_search['opportunity_search_condition[unit_price_min]']?.[0];
                const maxPrice = this.params_search['opportunity_search_condition[unit_price_max]']?.[0];

                if (minPrice && maxPrice) return `${minPrice}万円 ~ ${maxPrice}万円`; // Cả min & max
                if (minPrice) return `${minPrice}万円以上`; // Chỉ có min
                if (maxPrice) return `${maxPrice}万円以下`; // Chỉ có max
                return null; // Không có giá trị hợp lệ
            }

            // Trả về các giá trị khác (nếu có)
            const value = this.params_search[key].join(' / ');
            return value ? value : null;
        },
        params() {
            const urlParams = new URLSearchParams(window.location.search);
            const paramObj = {};

            urlParams.forEach((value, key) => {
                if (!paramObj[key]) {
                    paramObj[key] = [];
                }
                paramObj[key].push(value);
            });
            this.params_search = paramObj;
            console.log('params_search', this.params_search);
            this.categorizedExp();
        },

        // Nhóm các exp_categories vào category chính
        categorizedExp() {
            if (!this.params_search || !this.params_search["opportunity_search_condition[opp_categories][]"]) {
                this.categorizedExps = {};
                return;
            }
            const expCategories = this.params_search['opportunity_search_condition[opp_categories][]'] || [];
            const grouped = {};

            for (const category in this.categoryMapping) {
                const prefix = this.categoryMapping[category];
                const matchedValues = expCategories.filter(value => value.startsWith(prefix));

                if (matchedValues.length) {
                    grouped[category] = matchedValues.map(val => val.replace(prefix, ''));
                }
            }
            this.categorizedExps = grouped;
            console.log('categories', this.categorizedExps);
        },

        // format date
        formatDate(dateStr) {
            if (!dateStr) return 'N/A';
            const date = new Date(dateStr);
            const yyyy = date.getFullYear();
            const mm = String(date.getMonth() + 1).padStart(2, '0');
            const dd = String(date.getDate()).padStart(2, '0');
            return `${yyyy}/${mm}/${dd}`;
        },
    },
    async mounted() {
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        }),
            this.loadQueryParams();
        document.addEventListener("click", this.closeDropdown);
        const params = new URLSearchParams(window.location.search);
        this.sortType = params.get('sort_type') || 'created_at';
        const page = parseInt(params.get('page')) || 1;
        await this.fetch_search(page, this.sortType);
        this.params();
        this.count_opp();
        if (this.opps.length > 0) {
            this.checkAllViewStatuses();
        } else {
            console.log('No resumes data available.');
        }
        this.restoreSelection();
        this.restoreSelections();
    },
    computed: {
        visiblePages() {
            let pages = [];
            if (this.totalPages <= 5) {
                pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
            } else if (this.currentPage <= 3) {
                pages = [1, 2, 3, 4, 5];
            } else if (this.currentPage >= this.totalPages - 2) {
                pages = [this.totalPages - 4, this.totalPages - 3, this.totalPages - 2, this.totalPages - 1, this.totalPages];
            } else {
                pages = [this.currentPage - 2, this.currentPage - 1, this.currentPage, this.currentPage + 1, this.currentPage + 2];
            }
            return pages;
        },
        validConditions() {
            if (!this.params_search) return [];

            const uniqueConditions = new Map(); // Dùng Map để tránh trùng lặp key-value

            this.searchConditions.forEach(condition => {
                const value = this.formatCondition(condition.key);
                if (value) {
                    uniqueConditions.set(condition.label, { ...condition, value });
                }
            });

            return Array.from(uniqueConditions.values()); // Trả về danh sách đã loại bỏ trùng lặp
        },
        displayedRecords() {
            return this.totalRecord < 24 ? this.totalRecord : 24;
        },
        categoryMap() {
            return {
                // categories
                "design_pmo": "PMO",
                "design_pmpl": "PM・PL",
                "design_DX": "DX",
                "design_cloud": "クラウド",
                "design_strategy": "モダナイゼション",
                "design_work": "セキュリティ",
                "design_it": "ITインフラ",
                "design_ai": "AI",

                // categoriesDev
                "dev_pmo": "PMO",
                "dev_pmpl": "PM・PL",
                "dev_web": "Webシステム",
                "dev_ios": "IOS",
                "dev_android": "Android",
                "dev_control": "制御",
                "dev_embedded": "組込",
                "dev_ai": "AI・DL・ML",
                "dev_test": "テスト",
                "dev_cloud": "クラウド",
                "dev_architect": "サーバ",
                "dev_bridge_se": "データベース",
                "dev_network": "ネットワーク",
                "dev_mainframe": "メインフレーム",

                // categoriesInfra
                "infra_pmo": "PMO",
                "infra_pmpl": "PM・PL",
                "infra_server": "サーバー",
                "infra_network": "ネットワーク",
                "infra_db": "データベース",
                "infra_cloud": "クラウド",
                "infra_virtualized": "仮想化",
                "infra_mainframe": "メインフレーム",

                // categories3
                "operation_pmo": "業務システム",
                "operation_pmpl": "オープン",
                "operation_DX": "クラウド",
                "operation_mainframe": "メインフレーム",
                "operation_strategy": "ヘルプデスク",

                // contract type
                "quas": "業務委託（準委任）",
                "subc": "業務委託（請負）",
                "temp": "派遣契約",

                //invol
                "enter_sales_channels": "商流に入る",
                "agent_only": "仲介のみ",

                //opp_qualities
                "700thousand_or_more": "70万円以上",
                "1million_or_more": "100万円以上",
                "less_1year_ok": "1年未満OK",
                "newcomer_ok": "新人でもOK",
                "over_50years_old_ok": "50代以上OK",
                "foreign_nationality_ok": "外国籍OK",
                "leader_recruitment": "リーダー募集",
                "english_skill": "英語力",
                "interview_once": "面談1回",
                "take_home_project": "持ち帰りOK",
                "team_proposal_ok": "チーム提案OK",
                "web_interview_ok": "ウェブ面談可能",
                "remote__location_ok": "案件場所から遠隔地居住でもOK",
                "long_term": "日本以外の居住者OK",
                "overseas_resident_ok": "長期（２年以上継続可能性）",

                //restriction
                "own_employee": "自社社員",
                "one_subcontract_employee": "協力会社社員（一社先）",
                "more_subcontract_employee": "協力会社社員（二社先以降）",
                "freelance_person": "フリーランス（本人）",
                "subcontract_freelance": "フリーランス（一社先）",
                "more_subcontract_freelance": "フリーランス（二社先以降）",

                //order_accuracy_ids
                "afte": "確定済み",
                "befo": "確定前",

                //opp_type_id
                "clnt": "エンド",
                "prim": "元請",
                "subc": "一次請",
                "msubc": "二次請以降",

                //utilization_rate
                '100': '100%（フル稼働）',
                '75': '75%',
                '50': '50%',
                '25': '25%',

                //work_frequencies
                '5days': '週5日出社',
                '4days': '週4日出社',
                '3days': '週3日出社',
                '2days': '週2日出社',
                '2to4days': '週4 〜 2日出社',
                'less_than_1day': '週1日未満出社',
                'full_remote': 'フルリモート',
                '1day': '週1日出社',

                //interview_count_id
                "once": "1回",
                "few": "1〜2回",
                "twice": "2回",
                "over_three_times": "3回以上"
            };
        }
    }
}

export default Search