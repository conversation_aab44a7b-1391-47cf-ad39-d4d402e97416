from odoo import http
from odoo.http import request, Response
import base64
import json
import logging

# Import security modules
from ..utils import AntivirusScanner, security_logger

_logger = logging.getLogger(__name__)

class ResumeController(http.Controller):

    @http.route('/api/resume/upload_cv', type='http', auth='public', methods=['POST'], csrf=False)
    def upload_cv(self, **kwargs):
        file = request.httprequest.files.get('file')  # Get from request.files
        resume_id = kwargs.get('resume_id')

        if not file or not resume_id:
            return json.dumps({"success": False, "error": "Required fields or id are missing."})

        # ✅ ZEUS SECURITY: File validation và antivirus scanning
        try:
            # Đọc file content
            file_content_bytes = file.read()
            file_name = file.filename

            # 1. Kiểm tra file type
            if not AntivirusScanner.is_allowed_file_type(file_name):
                security_logger.log_security_violation(
                    'invalid_file_type',
                    f'File type not allowed: {file_name}',
                    resume_id
                )
                return json.dumps({
                    "success": False,
                    "error": "ファイル形式が許可されていません。PDF、DOC、DOCXファイルのみ許可されています。"
                })

            # 2. Kiểm tra file size
            if not AntivirusScanner.is_allowed_file_size(len(file_content_bytes)):
                security_logger.log_security_violation(
                    'file_too_large',
                    f'File too large: {file_name}, size: {len(file_content_bytes)} bytes',
                    resume_id
                )
                return json.dumps({
                    "success": False,
                    "error": "ファイルサイズが大きすぎます。最大10MBまで許可されています。"
                })

            # 3. Antivirus scan
            is_clean, scan_result = AntivirusScanner.scan_file(file_content_bytes, file_name)

            # Log scan result
            security_logger.log_antivirus_scan(
                file_name,
                scan_result,
                resume_id,
                len(file_content_bytes)
            )

            if not is_clean:
                security_logger.log_security_violation(
                    'virus_detected',
                    f'Virus scan failed: {file_name}, result: {scan_result}',
                    resume_id
                )
                return json.dumps({
                    "success": False,
                    "error": "セキュリティスキャンによりファイルが拒否されました。"
                })

            _logger.info(f"File passed security validation: {file_name}")

        except Exception as e:
            _logger.error(f"Security validation error: {str(e)}")
            security_logger.log_security_violation(
                'security_scan_error',
                f'Security scan error: {str(e)}',
                resume_id
            )
            return json.dumps({
                "success": False,
                "error": "ファイルセキュリティスキャンに失敗しました。"
            })

        # ✅ LOGIC CŨ KHÔNG THAY ĐỔI - tiếp tục từ đây
        try:
            resume_id = int(resume_id)

            # Check resume_id exists
            existing_cv = request.env["vit.curriculum_vitae"].sudo().search([("resume_id", "=", resume_id)], limit=1)
            if existing_cv:
                return json.dumps({"success": False, "error": "Resume already has a CV."})

            # Encode file content to base64 (sử dụng content đã đọc ở trên)
            file_content = base64.b64encode(file_content_bytes).decode("utf-8")

            # Tạo ir.attachment public trước
            attachment = None
            try:
                attachment = request.env['ir.attachment'].sudo().create({
                    'name': file_name,
                    'datas': base64.b64encode(file_content_bytes),
                    'res_model': 'vit.curriculum_vitae',  # sẽ cập nhật res_id sau
                    'type': 'binary',
                    'mimetype': file.content_type,
                    'public': True,
                })
                _logger.info(f"Created attachment: id={attachment.id}, name={file_name}, public=True")
            except Exception as e:
                _logger.error(f"Failed to create attachment: {str(e)}", exc_info=True)

            # Tạo bản ghi CV, gán file_attachment_id
            cv = request.env["vit.curriculum_vitae"].sudo().create({
                "name": file_name,
                "resume_id": resume_id,
                "file_attachment_id": attachment.id if attachment else False
            })
            # Cập nhật lại res_id cho attachment (nếu cần)
            if attachment:
                attachment.sudo().write({'res_id': cv.id})
            partner = request.env["vit.partner"].sudo().search([("id", "=", resume_id)], limit=1)
            if partner:
                partner.sudo().write({"cv_id": cv.id})

            # Log successful upload
            security_logger.log_file_upload(
                file_name,
                len(file_content_bytes),
                resume_id,
                success=True,
                reason="Upload successful"
            )

            return json.dumps({
                "success": True,
                "cv_id": cv.id,
                "attachment_id": attachment.id if attachment else None,
                "file_url": f"/web/content/{attachment.id}?download=true" if attachment else None
            })
        except Exception as e:
            # Log failed upload
            security_logger.log_file_upload(
                file_name if 'file_name' in locals() else 'unknown',
                len(file_content_bytes) if 'file_content_bytes' in locals() else 0,
                resume_id,
                success=False,
                reason=str(e)
            )
            return json.dumps({"success": False, "error": str(e)})

    @http.route('/api/resume/delete_cv', type='json', auth='public', methods=['POST'], csrf=False)
    def delete_cv(self):
        data = request.httprequest.get_json(silent=True)
        resume_id = data.get('resume_id')

        if not resume_id:
            return {"success": False, "error": "Required id is missing."}

        try:
            resume_id = int(resume_id)

            # Find CV
            cv = request.env["vit.curriculum_vitae"].sudo().search([("resume_id", "=", resume_id)], limit=1)
            if not cv:
                return {"success": False, "error": "CV is not found"}

            # Delete attachments
            attachments = request.env["ir.attachment"].sudo().search([
                ("res_model", "=", "vit.curriculum_vitae"),
                ("res_id", "=", cv.id)
            ])
            if attachments:
                attachments.unlink()

            # Delete CV
            cv.sudo().unlink()

            return {"success": True, "message": "Delete CV and attachments successfully"}
        except Exception as e:
            return {"success": False, "error": str(e)}


    @http.route('/api/resume/get_cv', type='http', auth='public', methods=['GET'], csrf=False)
    def get_cv(self, **kwargs):
        resume_id = kwargs.get('resume_id')

        if not resume_id:
            return Response(json.dumps({"success": False, "message": "Required id is missing."}), content_type='application/json', status=400)

        try:
            resume_id = int(resume_id)
        except ValueError:
            return Response(json.dumps({"success": False, "message": "Resume ID is invalid."}), content_type='application/json', status=400)

        # Query lấy id từ bảng vit.curriculum_vitae
        query_cv = "SELECT id FROM vit_curriculum_vitae WHERE resume_id = %s LIMIT 1;"
        request.env.cr.execute(query_cv, (resume_id,))
        cv_record = request.env.cr.fetchone()

        cv = request.env["vit.curriculum_vitae"].sudo().search([("resume_id", "=", resume_id)], limit=1)

        if not cv_record:
            return Response(json.dumps({"success": False, "message": "CV not found."}), content_type='application/json', status=200)

        cv_id = cv_record[0]  # Lấy ID của CV

        # Query lấy file từ bảng ir.attachment
        query_attachment = """
            SELECT id, name, file_size
            FROM ir_attachment
            WHERE res_id = %s AND res_model = 'vit.curriculum_vitae'
            ORDER BY create_date DESC
            LIMIT 1;
        """

        request.env.cr.execute(query_attachment, (cv_id,))
        attachment = request.env.cr.fetchone()

        if attachment:
            attachment_id, name, file_size = attachment
            return Response(json.dumps({
                "success": True,
                "cv_id": attachment_id,
                "file_name": cv.name,
                "file_size": file_size,
                "file_url": f"/web/content/{attachment_id}?download=true"
            }), content_type='application/json', status=200)

        return Response(json.dumps({"success": False, "message": "File not found."}), content_type='application/json', status=200)





