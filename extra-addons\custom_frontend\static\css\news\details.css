/*! CSS Used from: https://assign-navi.jp/assets/application-5c62c0bf10dc71ffa28617d13da03c81eeb71d47f2b1b1f657b8b6641e347cff.css ; media=screen */
@media screen{
*,::after,::before{box-sizing:border-box;}
main{display:block;}
h1{margin-top:0;margin-bottom:.5rem;}
a{color:#007bff;text-decoration:none;background-color:transparent;}
a:hover{color:#0056b3;text-decoration:underline;}
h1{margin-bottom:.5rem;font-weight:500;line-height:1.2;}
h1{font-size:2.5rem;}
.container-fluid{width:100%;padding-right:15px;padding-left:15px;margin-right:auto;margin-left:auto;}
.row{display:flex;flex-wrap:wrap;margin-right:-15px;margin-left:-15px;}
.col-lg-8,.col-md-10,.col-sm-12{position:relative;width:100%;padding-right:15px;padding-left:15px;}
@media (min-width: 576px){
.col-sm-12{flex:0 0 100%;max-width:100%;}
}
@media (min-width: 768px){
.col-md-10{flex:0 0 83.333333%;max-width:83.333333%;}
}
@media (min-width: 992px){
.col-lg-8{flex:0 0 66.666667%;max-width:66.666667%;}
}
.btn{display:inline-block;font-weight:400;color:#fff !important;text-align:center;vertical-align:middle;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:transparent;border:1px solid transparent;padding:.375rem .75rem;font-size:1rem;line-height:1.5;border-radius:.25rem;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}
@media (prefers-reduced-motion: reduce){
.btn{transition:none;}
}
.btn:hover{color:#fff !important;text-decoration:none;}
.btn:focus{outline:0;box-shadow:0 0 0 0.2rem rgba(0,123,255,0.25);}
.btn:disabled{opacity:.65;}
.btn:not(:disabled):not(.disabled){cursor:pointer;}
.bg-white{background-color:#fff!important;}
.d-inline-block{display:inline-block!important;}
.d-flex{display:flex!important;}
.flex-shrink-0{flex-shrink:0!important;}
.align-items-start{align-items:flex-start!important;}
.align-self-end{align-self:flex-end!important;}
.mx-0{margin-right:0!important;}
.mb-0{margin-bottom:0!important;}
.mx-0{margin-left:0!important;}
.mr-2{margin-right:0.5rem!important;}
.py-2{padding-top:0.5rem!important;}
.py-2{padding-bottom:0.5rem!important;}
.pb-3{padding-bottom:1rem!important;}
.p-4{padding:1.5rem!important;}
.pt-5{padding-top:3rem!important;}
.mx-auto{margin-right:auto!important;}
.mx-auto{margin-left:auto!important;}
@media (min-width: 768px){
.mr-md-3{margin-right:1rem!important;}
.py-md-4{padding-top:1.5rem!important;}
.py-md-4{padding-bottom:1.5rem!important;}
}
@media print{
*,::after,::before{text-shadow:none!important;box-shadow:none!important;}
}
:disabled{pointer-events:none!important;}
a{color:#007bff;text-decoration:none;cursor:pointer;transition:all .2s ease-in-out;}
a:hover{color:#0056b3;text-decoration:none;transition:all .2s ease-in-out;}
a:disabled:hover{color:#007bff;}
h1{font-weight:300;}
.waves-effect{position:relative;overflow:hidden;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
a.waves-effect,a.waves-light{display:inline-block;}
.btn{margin:.375rem;color:inherit;text-transform:uppercase;word-wrap:break-word;white-space:normal;cursor:pointer;border:0;border-radius:.25rem;box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;padding:.84rem 2.14rem;font-size:.81rem;}
.btn:hover,.btn:focus,.btn:active{outline:0;box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn.btn-md{padding:.7rem 1.6rem;font-size:.7rem;}
.btn:disabled:hover,.btn:disabled:focus,.btn:disabled:active{box-shadow:0 2px 5px 0 rgba(0,0,0,0.16),0 2px 10px 0 rgba(0,0,0,0.12);}
.btn-blue-grey{color:#fff;background-color:#78909c!important;}
.btn-blue-grey:hover{color:#fff;background-color:#879ca7;}
.btn-blue-grey:focus{box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn-blue-grey:focus,.btn-blue-grey:active{background-color:#4a5b64;}
.btn-blue-grey:not([disabled]):not(.disabled):active{background-color:#4a5b64!important;box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.btn-blue-grey:not([disabled]):not(.disabled):active:focus{box-shadow:0 5px 11px 0 rgba(0,0,0,0.18),0 4px 15px 0 rgba(0,0,0,0.15);}
.font-default{font-size:.875rem!important;}
.font-middle{font-size:1rem!important;}
.font-extralarge{font-size:1.25rem!important;}
.custom-grey-5-text{color:#738a97;}
.mx-0{margin-right:0!important;}
.mb-0{margin-bottom:0!important;}
.mx-0{margin-left:0!important;}
.py-2{padding-top:.5rem!important;}
.py-2{padding-bottom:.5rem!important;}
.mr-2{margin-right:.5rem!important;}
.pb-3{padding-bottom:1rem!important;}
.p-4{padding:1.5rem!important;}
.pt-5{padding-top:2rem!important;}
.mt-6{margin-top:2.5rem!important;}
@media (min-width: 768px){
.mr-md-3{margin-right:1rem!important;}
.py-md-4{padding-top:1.5rem!important;}
.py-md-4{padding-bottom:1.5rem!important;}
}
.bg-light-green{background-color:#eaf8f7;}
.btn{line-height:1;text-transform:none;}
.btn:hover,.btn:active,.btn:focus{opacity:.7;}
main{margin-top:79px;}
main .grabient{min-height:50vh;}
@media (max-width: 767px){
main{margin-top:64px;}
}
.title{background-image:url(https://assign-navi.jp/assets/img/common/bg-title-1afb32d6753617ed3284b711315764d1894c70aeff8a945eab5bd541d5c5ff2a.png);background-size:cover;}
.title h1{color:#fff;}
@media (max-width: 767px){
.title h1{font-size:1.8rem;}
}
:focus{outline:0;}
}
/*! CSS Used from: Embedded */
.detail-card-wrapper{border-radius:4px;overflow:hidden;}
.icon{width:36px;height:36px;border-radius:50%;background-size:cover;background-position:center;}
.icon.other_news{background-image:url(https://assign-navi.jp/packs/2930012a68986fba81b9.png);}
.body{white-space:pre-wrap;}
