@font-face {
  font-family: "Roboto";
  src: url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");
  font-weight: normal;
  font-style: normal;
}
body {
  font-family: "Roboto", sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  text-decoration: none;
  list-style: none;
}

.header-custom {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1010;
  box-shadow: #6c757d 0px 0px 10px 0px;
  background-color: #fff;
  height: 80px;
  display: flex;
}
.header-custom ul, .header-custom p {
  margin-bottom: 0;
}
.header-custom .navbar-custom {
  width: 60%;
  display: flex;
  justify-items: flex-start;
  align-items: center;
  padding: 2rem;
}
.header-custom .navbar-custom .logo {
  width: 8.5rem;
  margin-right: 1.5rem;
  margin-bottom: 0;
}
.header-custom .navbar-custom .nav-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-custom .navbar-custom .nav-links li {
  margin: 0 1rem;
}
.header-custom .navbar-custom .nav-links li a {
  color: #343a40;
  line-height: 20px;
  transition: all ease-in-out 0.3s;
}
.header-custom .navbar-custom .nav-links li a:hover {
  color: #6c757d;
}
.header-custom .navbar-custom .nav-links li a:hover::before {
  transform: rotate(135deg);
  color: #6c757d;
}
.header-custom .navbar-custom .nav-links li .nav-menu {
  position: relative;
}
.header-custom .navbar-custom .nav-links li .nav-menu::before {
  content: "";
  position: absolute;
  width: 7px;
  height: 7px;
  border-left: #343a40 solid 2px;
  border-bottom: #343a40 solid 2px;
  right: -15px;
  top: 3px;
  transition: all ease-in-out 0.3s;
  transform: rotate(315deg);
}
.header-custom .navbar-custom .nav-links .dropdown {
  position: relative;
}
.header-custom .navbar-custom .nav-links .dropdown .dropdown-menu {
  display: none;
  position: absolute;
  right: -25px;
  top: 12px;
  background-color: #fff;
  z-index: 1000;
  margin: 0.5rem 0;
}
.header-custom .navbar-custom .nav-links .dropdown .dropdown-menu a {
  padding: 10px 20px;
  display: block;
  color: #343a40;
}
.header-custom .navbar-custom .nav-links .dropdown .dropdown-menu a:hover {
  color: #6c757d;
}
.header-custom .navbar-custom .nav-links .dropdown:hover .dropdown-menu {
  display: block;
}
.header-custom .contact-info-cus {
  width: 40%;
  margin-top: 3px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 2rem;
}
.header-custom .contact-info-cus li {
  margin: 0 1rem;
}
.header-custom .contact-info-cus li a, .header-custom .contact-info-cus li button {
  background-color: rgba(0, 0, 0, 0);
  border: none;
}
.header-custom .contact-info-cus li a i, .header-custom .contact-info-cus li button i {
  color: #343a40;
  font-size: 32px;
}
.header-custom .contact-info-cus .phone {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 18px;
  font-family: "Roboto", sans-serif;
}
.header-custom .contact-info-cus .phone a {
  display: block;
  color: #007bff;
}
.header-custom .contact-info-cus .phone a i {
  font-size: 18px;
  color: #007bff;
}
.header-custom .contact-info-cus .phone p {
  display: block;
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0;
}

.footer-custom {
  background-color: #343a40;
  padding: 4rem;
  color: #fff;
}
.footer-custom a {
  text-decoration: none;
  font-size: 14px;
  transition: all ease-in-out 0.3s;
  color: #fff;
  font-weight: 300;
}
.footer-custom a:hover {
  color: #17a2b8;
  text-decoration: none;
}

body {
  position: relative;
}
body #list-route {
  background-color: #17a2b8;
  border: none;
  position: fixed;
  width: 70px;
  display: flex;
  height: 70px;
  bottom: 70px;
  right: 30px;
  color: #fff;
  font-size: 20px;
  align-content: center;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  z-index: 100000000;
  transition: all ease-in-out 0.3s;
}
body #list-route:hover {
  background-color: #1900ff;
}
body #list-route:focus-visible {
  outline: none;
}
body #list-route:focus {
  outline: none;
}

/*# sourceMappingURL=header.css.map */
