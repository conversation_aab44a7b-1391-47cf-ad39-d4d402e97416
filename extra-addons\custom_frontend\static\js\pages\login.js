import { checkToastrMessage } from "/custom_frontend/static/js/common/Toastr.js";
const Login = {
    template: `
    <main class="pb-3 margin-header">
        <div class="container-fluid grabient pt-5">
            <div class="row">
                <div class="col-sm-12 col-md-10 col-lg-8 mx-auto">
                    <div class="row">
                        <div class="col-12">
                            <h1 class="font-extralarge text-center my-2">ログイン</h1>
                        </div>
                    </div>
                    <div class="row justify-content-center mt-3 mt-md-5">
                        <div class="col-12 col-xl-6 col-md-7">
                            <div class="card">
                                <div class="card-body px-3 py-5 p-md-5">
                                    <form class="mt-2" id="new_user" @submit.prevent="loginUser">
                                        <div class="mx-auto mb-3">
                                            <input v-model="email" class="form-control" autocomplete="off" type="email"/>
                                            <div v-if="!check_email" class="text-danger text-left">{{ email_error }}</div>
                                        </div>
                                        <div class="mx-auto mb-4 password-field-container">
                                            <input v-model="password" class="form-control" autocomplete="off" :type="passwordFieldType"/>
                                            <i class="material-icons password-toggle-icon" @click="togglePasswordVisibility">{{ passwordFieldIcon }}</i>
                                            <div v-if="!check_password" class="text-danger text-left">{{ password_error }}</div>
                                        </div>
                                        <div class="mx-auto text-center">
                                            <div class="selecting-form custom-control custom-checkbox z-2">
                                                <input id="remember_me_field" class="custom-control-input" type="checkbox" v-model="rememberMe">
                                                <label id="remember_me_field_label" class="custom-control-label" for="remember_me_field">次回から入力を省略する</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <button type="submit" class="btn btn-default btn-block btn-lg font-middle mt-3 mb-5 waves-effect waves-light">ログイン</button>
                                            </div>
                                        </div>
                                    </form>
                                    <div v-if="errorMessage" class="text-danger text-center mt-3">
                                        {{ errorMessage }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row justify-content-center mt-5 mx-3">
                        <div class="col-12 col-md-6 px-0 px-md-5">
                            <a class="btn btn-white btn-outline-default btn-lg btn-block waves-effect waves-light" href="/signup">
                                <span class="overflow-visible">会員登録はこちら</span>
                            </a>
                        </div>
                        <div class="col-12 text-center mt-2">
                            <a id="pass_forget_link" class="green-text font-middle" href="/users/password/new">パスワードをお忘れの方はこちら</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <link rel="stylesheet" href="/custom_frontend/static/css/login.css"/>
    <link rel="stylesheet" href="/custom_frontend/static/css/password-toggle.css"/>
    `,

    data() {
        return {
            email: '',
            password: '',
            rememberMe: false,
            errorMessage: '',
            check_email: true,
            check_password: true,
            email_error: '',
            password_error: '',
            passwordFieldType: 'password', // Thêm biến để theo dõi loại trường mật khẩu
            passwordFieldIcon: 'visibility_off', // Biểu tượng mặc định là ẩn mật khẩu
        };
    },

    mounted() {
        this.loadExternalScript("/custom_frontend/static/js/pages/login-extra.js");
        const toastrCSS = document.createElement("link");
        toastrCSS.rel = "stylesheet";
        toastrCSS.href = "/custom_frontend/static/css/Toastr.css";
        toastrCSS.onload = function () {
            console.log("Toastr CSS loaded successfully!");
        };
        document.head.appendChild(toastrCSS);
        checkToastrMessage();
    },

    methods: {
        // Phương thức để chuyển đổi hiển thị/ẩn mật khẩu
        togglePasswordVisibility() {
            this.passwordFieldType = this.passwordFieldType === 'password' ? 'text' : 'password';
            this.passwordFieldIcon = this.passwordFieldType === 'password' ? 'visibility_off' : 'visibility';
        },

        async loginUser() {
            if (!this.validateInput()) {
                return;
            }
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: this.email,
                        password: this.password,
                        remember_me: this.rememberMe
                    })
                });

                const data = await response.json();
                console.log('Ruturned result:', data);

                if (data.result.success) {
                    if (data.result.active == true) {
                        if (data.result.token != null)
                            localStorage.setItem('authToken', data.result.token);
                        localStorage.setItem('token_login', data.result.token_login);
                        window.location.href = '/mypage';
                    }
                    else {
                        this.active_account();
                        localStorage.setItem('mask_email', this.maskEmail(this.email));
                        window.location.href = '/account/wait_active';
                    }
                } else {
                    console.log('Login failed:', data.result.message);
                    // this.errorMessage = data.result.message; // This will show the error message on the page
                    window.toastr.warning(data.result.message);
                }
            } catch (error) {
                console.log('Error API:', data.result.message);
                // this.errorMessage = data.result.message; // This will show the error message on the page
            }
        },

        loadExternalScript(src) {
            const toastrCSS = document.createElement("link");
            toastrCSS.rel = "stylesheet";
            toastrCSS.href = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css";
            toastrCSS.onload = function () {
                console.log("Toast css loaded successfully!");
                const jQuery = document.createElement("script");
                jQuery.src = "https://code.jquery.com/jquery-3.6.0.min.js";
                jQuery.onload = function () {
                    console.log("jQuery loaded successfully!");
                    const toastrJS = document.createElement("script");
                    toastrJS.src = "https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js";
                    toastrJS.onload = function () {
                        console.log("Toastr loaded successfully!");
                        const script = document.createElement("script");
                        script.src = src;
                        script.async = true;
                        script.onload = function () {
                            console.log("External script loaded successfully!");
                        }
                        document.body.appendChild(script);
                    };
                    document.body.appendChild(toastrJS);
                };
                document.body.appendChild(jQuery);
            };
            document.body.appendChild(toastrCSS);
        },
        async active_account() {
            try {
                await fetch('/api/email_active', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: this.email }),
                }).catch(error => {
                    console.error('Error:', error);
                });
            } catch (error) {

            }
        },
        maskEmail(email) {
            const [_, domain] = email.split('@');
            if (!domain) return '<EMAIL>';

            if (domain === 'gmail.com') {
                return `<EMAIL>`;
            }

            // Nếu không phải gmail → che phần trước dấu .
            const domainParts = domain.split('.');
            if (domainParts.length < 2) return `xxx@xxx`;

            domainParts[0] = 'xxx'; // thay phần đầu tiên của domain
            return `xxx@${domainParts.join('.')}`;
        },

        validateInput() {
            let isValid = true;

            // Validate email
            if (!this.email || this.email.trim() === '') {
                this.check_email = false;
                this.email_error = 'メールアドレスを入力してください。';
                isValid = false;
            } else {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(this.email.trim())) {
                    this.check_email = false;
                    this.email_error = 'メールアドレスを正しい形式で入力してください。';
                    isValid = false;
                } else {
                    this.check_email = true;
                    this.email_error = '';
                }
            }

            // Validate password
            if (!this.password || this.password.trim() === '') {
                this.check_password = false;
                this.password_error = 'パスワードを入力してください。';
                isValid = false;
            } else {
                this.check_password = true;
                this.password_error = '';
            }

            return isValid;
        },
    },
    watch: {
        email: {
            handler() {
                if (this.email && this.email.trim() !== '') {
                    this.check_email = true;
                    this.email_error = '';
                }
            }
        },
        password: {
            handler() {
                if (this.password && this.password.trim() !== '') {
                    this.check_password = true;
                    this.password_error = '';
                }
            }
        }
    }
};

export default Login;
