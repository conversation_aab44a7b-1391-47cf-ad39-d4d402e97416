const FrequencySlider = {
    props: {
        min: { type: Number, default: 0 },
        max: { type: Number, default: 6 },
        step: { type: Number, default: 1 },
        startValue: {
            type: [Array, String],
            default: () => "5days"
        },
    },
    data() {
        return {
            selectedValue: 6, // Default to "5days"
            slider: null,
            frequencyMap: {
                "full_remote": 0,
                "less_than_1day": 1,
                "1day": 2,
                "2days": 3,
                "3days": 4,
                "4days": 5,
                "5days": 6
            },
            displayMap: {
                0: "フルリモート",
                1: "1日未満",
                2: "1日",
                3: "2日",
                4: "3日",
                5: "4日",
                6: "5日"
            },
            frequencyValues: ["full_remote", "less_than_1day", "1day", "2days", "3days", "4days", "5days"]
        };
    },
    watch: {
        startValue: {
            handler(newValue) {
                // Handle both array (legacy) and single value
                let value = 6; // default to "5days"
                if (Array.isArray(newValue)) {
                    // If array, take the first value or default to "5days"
                    if (newValue.length > 0) {
                        value = this.frequencyMap.hasOwnProperty(newValue[0]) ? this.frequencyMap[newValue[0]] : 6;
                    }
                } else if (typeof newValue === 'string') {
                    value = this.frequencyMap.hasOwnProperty(newValue) ? this.frequencyMap[newValue] : 6;
                }

                if (value !== this.selectedValue) {
                    this.selectedValue = value;
                    if (this.slider) {
                        this.slider.set(value);
                    }
                }
            },
            immediate: true,
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (!this.slider && this.$refs.slider) {
                // Initialize single-handle slider
                this.slider = noUiSlider.create(this.$refs.slider, {
                    start: this.selectedValue,
                    connect: [true, false], // Connect from start to handle
                    range: { min: this.min, max: this.max },
                    step: this.step,
                });

                // Handle slider update events
                this.slider.on("update", (values) => {
                    const newValue = Math.round(parseFloat(values[0]));
                    this.selectedValue = newValue;
                    // Emit single frequency value instead of array
                    const frequencyKey = this.frequencyValues[newValue];
                    this.$emit("update:frequency", frequencyKey);
                });
            }
        });
    },
    methods: {
        getDisplayValue(value) {
            return this.displayMap[value] || "フルリモート";
        }
    },
    computed: {
        displayValue() {
            return this.getDisplayValue(this.selectedValue);
        },
    },
    template: `
      <div class="mb-5">
        <p class="d-flex justify-content-center" id="frequency-range">
          {{ displayValue }}
        </p>
        <div class="slider-styled mx-4" id="slider-handles" ref="slider"></div>
        <div class="d-flex justify-content-between font-small custom-grey-text my-2">
          <span>フルリモート</span><span>5日</span>
        </div>
      </div>
    `,
};
export default FrequencySlider;
